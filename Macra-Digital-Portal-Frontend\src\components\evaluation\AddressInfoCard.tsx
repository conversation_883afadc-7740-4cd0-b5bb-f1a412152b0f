import React, { useState, useEffect } from 'react';
import DataDisplayCard from './DataDisplayCard';
import { Application } from '@/types/license';
import { addressService } from '@/hooks/useAddressing';

interface AddressInfoCardProps {
  application: Application | null;
  className?: string;
  showEmptyFields?: boolean;
  defaultCollapsed?: boolean;
}

const AddressInfoCard: React.FC<AddressInfoCardProps> = ({
  application,
  className = '',
  showEmptyFields = true,
  defaultCollapsed = false
}) => {
  const [addresses, setAddresses] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchAddresses = async () => {
      if (!application?.applicant_id) return;

      try {
        setLoading(true);
        const addressResponse = await addressService.getAddressesByEntity('applicant', application.applicant_id);
        const addressData = addressResponse?.data || addressResponse || [];
        setAddresses(addressData);
      } catch (err) {
        console.warn('Could not load addresses:', err);
        setAddresses([]);
      } finally {
        setLoading(false);
      }
    };

    fetchAddresses();
  }, [application?.applicant_id]);

  // Return empty fragment if no application or no addresses
  if (!application || loading || !addresses || addresses.length === 0) {
    return <></>;
  }

  return (
    <>
      {addresses.map((address, index) => (
        <DataDisplayCard
          key={index}
          title={`${address.address_type || 'Address'} ${addresses.length > 1 ? `#${index + 1}` : ''}`}
          icon="ri-map-pin-line"
          className={className}
          showEmptyFields={showEmptyFields}
          defaultCollapsed={defaultCollapsed}
          creatorEmail={application?.applicant?.email}
          creatorName={application?.applicant?.name}
          showEmailButton={true}
          fields={[
            {
              label: 'Street Address',
              value: address.street_address,
              icon: 'ri-road-map-line'
            },
            {
              label: 'City',
              value: address.city,
              icon: 'ri-building-line'
            },
            {
              label: 'State/Province',
              value: address.state_province,
              icon: 'ri-map-line'
            },
            {
              label: 'Postal Code',
              value: address.postal_code,
              icon: 'ri-mail-line'
            },
            {
              label: 'Country',
              value: address.country,
              icon: 'ri-earth-line'
            },
            {
              label: 'Address Type',
              value: address.address_type,
              icon: 'ri-home-line'
            },
            {
              label: 'Full Address',
              value: `${address.street_address || ''} ${address.city || ''} ${address.state_province || ''} ${address.postal_code || ''}`.trim(),
              fullWidth: true,
              icon: 'ri-map-pin-2-line'
            }
          ]}
        />
      ))}
    </>
  );
};

export default AddressInfoCard;
