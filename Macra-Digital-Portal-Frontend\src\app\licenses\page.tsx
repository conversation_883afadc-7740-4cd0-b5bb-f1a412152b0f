'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { licenseService } from '@/services/licenseService';
import { License, LicenseStatus, LicenseType } from '@/types';
import { licenseTypeService } from '@/services/licenseTypeService';
import DataTable from '@/components/common/DataTable';
import Select from '@/components/common/Select';
import CertificateModal from '@/components/certificates/CertificateModal';
import toast from 'react-hot-toast';
import { PaginatedResponse, PaginateQuery } from '@/types';

export default function LicensesPage() {
  const [licensesData, setLicensesData] = useState<PaginatedResponse<License> | null>(null);
  const [licenseTypes, setLicenseTypes] = useState<LicenseType[]>([]);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [licenseTypeFilter, setLicenseTypeFilter] = useState<string>('');
  const [dateRangeFilter, setDateRangeFilter] = useState<string>('');

  // Certificate modal state
  const [selectedLicense, setSelectedLicense] = useState<License | null>(null);
  const [isCertificateModalOpen, setIsCertificateModalOpen] = useState(false);

  // Load licenses function for DataTable
  const loadLicenses = useCallback(async (query: PaginateQuery) => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        page: query.page,
        limit: query.limit,
        search: query.search || undefined,
        sortBy: Array.isArray(query.sortBy) ? query.sortBy[0] : query.sortBy,
        sortOrder: Array.isArray(query.sortBy) && query.sortBy.length > 1 ? query.sortBy[1] as 'ASC' | 'DESC' : undefined,
        status: statusFilter || undefined,
        licenseType: licenseTypeFilter || undefined,
        dateRange: dateRangeFilter || undefined,
      };

      const response = await licenseService.getLicenses(params);
      setLicensesData(response);
    } catch (err: unknown) {
      setError('Failed to load licenses');
      console.error('Error loading licenses:', err);
      toast.error('Failed to load licenses');
    } finally {
      setLoading(false);
    }
  }, [statusFilter, licenseTypeFilter, dateRangeFilter]);

  // Load license types on component mount
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        // Fetch license types
        const typesResponse = await licenseTypeService.getAllLicenseTypes();
        const types = Array.isArray(typesResponse) ? typesResponse : (typesResponse?.data || []);
        setLicenseTypes(types);
      } catch (error) {
        console.error('Error fetching initial data:', error);
      }
    };

    fetchInitialData();
  }, []);

  // Load licenses on component mount and when filters change
  useEffect(() => {
    loadLicenses({ page: 1, limit: 10 });
  }, [loadLicenses]);

  const getStatusBadge = (status: string) => {
    const statusClasses: Record<string, string> = {
      [LicenseStatus.ACTIVE]: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
      [LicenseStatus.EXPIRED]: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
      [LicenseStatus.SUSPENDED]: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
      [LicenseStatus.REVOKED]: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
      [LicenseStatus.UNDER_REVIEW]: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClasses[status] || statusClasses[LicenseStatus.ACTIVE]}`}>
        {status.replace('_', ' ').toUpperCase()}
      </span>
    );
  };

  const handleViewCertificate = (license: License) => {
    setSelectedLicense(license);
    setIsCertificateModalOpen(true);
  };

  const handleCloseCertificateModal = () => {
    setIsCertificateModalOpen(false);
    setSelectedLicense(null);
  };

  const handleFilterChange = (filterType: string, value: string) => {
    switch (filterType) {
      case 'status':
        setStatusFilter(value);
        break;
      case 'licenseType':
        setLicenseTypeFilter(value);
        break;
      case 'dateRange':
        setDateRangeFilter(value);
        break;
    }
    // Reload licenses with new filters
    loadLicenses({ page: 1, limit: 10 });
  };

  // Define table columns
  const licenseColumns = [
    {
      key: 'license_number',
      label: 'License Number',
      sortable: true,
      render: (value: unknown, item: License) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center">
              <i className="ri-award-line text-blue-600 dark:text-blue-400"></i>
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {item.license_number}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {item.code || 'N/A'}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'applicant',
      label: 'Applicant',
      render: (value: unknown, item: License) => (
        <div>
          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
            {item.application.applicant?.name || 'N/A'}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {item.application.applicant?.email || 'N/A'}
          </div>
        </div>
      ),
    },
    {
      key: 'license_type',
      label: 'License Type',
      render: (value: unknown, item: License) => (
        <div>
          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
            {item?.description || 'N/A'}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {item?.code || 'N/A'}
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value: unknown, item: License) => getStatusBadge(item.status),
    },
    {
      key: 'issue_date',
      label: 'Issue Date',
      sortable: true,
      render: (value: unknown, item: License) => (
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {new Date(item.issue_date).toLocaleDateString()}
        </span>
      ),
    },
    {
      key: 'expiry_date',
      label: 'Expiry Date',
      sortable: true,
      render: (value: unknown, item: License) => {
        const expiryDate = new Date(item.expiry_date);
        const isExpired = expiryDate < new Date();
        const isExpiringSoon = expiryDate < new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
        
        return (
          <span className={`text-sm ${isExpired ? 'text-red-600 dark:text-red-400' : isExpiringSoon ? 'text-yellow-600 dark:text-yellow-400' : 'text-gray-500 dark:text-gray-400'}`}>
            {expiryDate.toLocaleDateString()}
          </span>
        );
      },
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: unknown, item: License) => (
        <div className="flex items-center justify-end space-x-2">
          <button
            type="button"
            onClick={() => handleViewCertificate(item)}
            className="inline-flex items-center px-3 py-1 text-xs font-medium text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md hover:bg-green-100 dark:hover:bg-green-900/40 transition-colors"
          >
            <i className="ri-eye-line mr-1"></i>
            View Certificate
          </button>
        </div>
      ),
    },
  ];

  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: LicenseStatus.ACTIVE, label: 'Active' },
    { value: LicenseStatus.EXPIRED, label: 'Expired' },
    { value: LicenseStatus.SUSPENDED, label: 'Suspended' },
    { value: LicenseStatus.REVOKED, label: 'Revoked' },
    { value: LicenseStatus.UNDER_REVIEW, label: 'Under Review' },
  ];

  const licenseTypeOptions = [
    { value: '', label: 'All License Types' },
    ...licenseTypes.map(type => ({
      value: type.license_type_id,
      label: type.name
    }))
  ];

  const dateRangeOptions = [
    { value: '', label: 'All Time' },
    { value: 'today', label: 'Today' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
    { value: 'quarter', label: 'This Quarter' },
    { value: 'year', label: 'This Year' },
    { value: 'expiring_30', label: 'Expiring in 30 Days' },
    { value: 'expiring_90', label: 'Expiring in 90 Days' },
  ];

  return (
    <div className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Page header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Licenses</h1>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                Manage and view all issued licenses in the system
              </p>
            </div>
          </div>
        </div>



        {/* Filters */}
        <div className="mb-6 bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Status
              </label>
              <Select
                value={statusFilter}
                onChange={(value) => handleFilterChange('status', value)}
                options={statusOptions}
                placeholder="Filter by status"
              />
            </div>

            {/* <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                License Type
              </label>
              <Select
                value={licenseTypeFilter}
                onChange={(value) => handleFilterChange('licenseType', value)}
                options={licenseTypeOptions}
                placeholder="Filter by license type"
              />
            </div> */}

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Date Range
              </label>
              <Select
                value={dateRangeFilter}
                onChange={(value) => handleFilterChange('dateRange', value)}
                options={dateRangeOptions}
                placeholder="Filter by date range"
              />
            </div>
          </div>
        </div>

        {/* Error message */}
        {error && (
          <div className="mb-6">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <i className="ri-error-warning-line text-red-400"></i>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                    Error loading licenses
                  </h3>
                  <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                    <p>{error}</p>
                  </div>
                </div>
                <div className="ml-auto pl-3">
                  <button
                    type="button"
                    onClick={() => setError(null)}
                    className="inline-flex bg-red-50 dark:bg-red-900/20 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/40 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800"
                  >
                    <span className="sr-only">Dismiss</span>
                    <i className="ri-close-line text-sm"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Licenses Table */}
        <DataTable<License>
          columns={licenseColumns}
          data={licensesData}
          loading={loading}
          onQueryChange={loadLicenses}
          searchPlaceholder="Search licenses by number, applicant, or type..."
          emptyStateIcon="ri-award-line"
          emptyStateMessage="No licenses found."
        />
      </div>

      {/* Certificate Modal */}
      {selectedLicense && (
        <CertificateModal
          license={selectedLicense}
          isOpen={isCertificateModalOpen}
          onClose={handleCloseCertificateModal}
        />
      )}
    </div>
  );
}
