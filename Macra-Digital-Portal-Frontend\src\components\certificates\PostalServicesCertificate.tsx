'use client';

import React, { useEffect, useRef } from 'react';
import { License } from '@/types';

interface PostalServicesCertificateProps {
  license: License;
}

export default function PostalServicesCertificate({ license }: PostalServicesCertificateProps) {
  const qrCodeRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Generate QR code
    if (qrCodeRef.current && typeof window !== 'undefined') {
      import('qrcode').then((QRCode) => {
        const qrData = `MACRA Postal License Verification
Ref: ${license.license_number}
License No: ${license.license_number}
Licensee: ${license.application.applicant?.name || 'N/A'}
Valid: ${license.issue_date} - ${license.expiry_date}
Verify at: https://macra.mw/verify/${license.license_number}`;

        QRCode.toCanvas(qrCodeRef.current!, qrData, {
          width: 96,
          margin: 1,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        }).catch((error) => {
          console.error('QR Code generation failed:', error);
          if (qrCodeRef.current) {
            qrCodeRef.current.innerHTML = 'QR Code<br>Error';
          }
        });
      });
    }
  }, [license]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getValidityPeriod = () => {
    const issueDate = new Date(license.issue_date);
    const expiryDate = new Date(license.expiry_date);
    const years = expiryDate.getFullYear() - issueDate.getFullYear();
    return `${years} Years (${formatDate(license.issue_date)} - ${formatDate(license.expiry_date)})`;
  };

  return (
    <div className="postal-certificate">
      <style jsx>{`
        .postal-certificate {
          font-family: 'Times New Roman', serif;
          background: white;
          border: 4px solid #dc2626;
          border-radius: 8px;
          padding: 40px;
          min-height: 842px;
          position: relative;
          overflow: hidden;
        }
        
        .inner-border {
          border: 2px solid #16a34a;
          padding: 30px;
          min-height: 100%;
          position: relative;
        }
        
        .watermark {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) rotate(-15deg);
          opacity: 0.05;
          pointer-events: none;
          z-index: 0;
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
        }
        
        .watermark-text {
          font-size: 36px;
          font-weight: bold;
          color: #dc2626;
          letter-spacing: 3px;
          line-height: 1.2;
        }
        
        .content-wrapper {
          position: relative;
          z-index: 1;
        }
        
        .header {
          text-align: center;
          margin-bottom: 30px;
        }
        
        .logo-section {
          margin-bottom: 20px;
        }
        
        .authority-name {
          border: 2px solid #dc2626;
          display: inline-block;
          padding: 12px 30px;
          margin-bottom: 20px;
        }
        
        .authority-title {
          color: #dc2626;
          font-weight: bold;
          font-size: 16px;
          letter-spacing: 1px;
        }
        
        .reference-section {
          display: flex;
          justify-content: space-between;
          margin-bottom: 15px;
          font-size: 14px;
          color: #2563eb;
        }
        
        .certificate-type {
          background-color: #16a34a;
          color: white;
          padding: 10px 20px;
          display: inline-block;
          font-weight: bold;
          font-size: 18px;
          margin-bottom: 10px;
        }
        
        .certification-text {
          font-size: 14px;
          color: #374151;
          margin-bottom: 25px;
        }
        
        .license-details {
          margin-bottom: 25px;
          line-height: 1.8;
        }
        
        .detail-row {
          display: flex;
          margin-bottom: 12px;
          align-items: flex-start;
        }
        
        .detail-label {
          font-weight: 600;
          min-width: 200px;
          color: #374151;
          font-size: 14px;
        }
        
        .detail-colon {
          margin: 0 10px;
          font-weight: bold;
        }
        
        .detail-value {
          font-weight: bold;
          color: #111827;
          flex: 1;
          font-size: 14px;
        }
        
        .services-section {
          margin-bottom: 20px;
        }
        
        .services-title {
          font-weight: bold;
          color: #374151;
          margin-bottom: 8px;
          font-size: 14px;
        }
        
        .services-list {
          list-style: none;
          padding-left: 20px;
        }
        
        .services-list li {
          margin-bottom: 4px;
          font-size: 13px;
          position: relative;
        }
        
        .services-list li:before {
          content: "•";
          color: #16a34a;
          font-weight: bold;
          position: absolute;
          left: -15px;
        }
        
        .compliance-section {
          text-align: justify;
          line-height: 1.6;
          margin-bottom: 30px;
          font-size: 14px;
        }
        
        .footer-section {
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
          margin-top: 40px;
        }
        
        .signature-area {
          flex: 1;
        }
        
        .issue-location {
          font-size: 14px;
          margin-bottom: 60px;
        }
        
        .signature-line {
          text-align: center;
          border-top: 1px solid black;
          padding-top: 8px;
          max-width: 200px;
        }
        
        .dg-name {
          font-weight: bold;
          font-size: 14px;
        }
        
        .dg-title {
          font-size: 14px;
        }
        
        .qr-section {
          flex-shrink: 0;
          margin-left: 40px;
          text-align: center;
        }
        
        .qr-code {
          width: 100px;
          height: 100px;
          border: 2px solid #9ca3af;
          background: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          color: #6b7280;
          margin-bottom: 8px;
        }
        
        .qr-text {
          font-size: 10px;
          color: #4b5563;
          max-width: 100px;
        }
        
        .security-footer {
          background-color: #06b6d4;
          color: white;
          text-align: center;
          padding: 8px;
          font-size: 12px;
          font-weight: 600;
          margin-top: 20px;
          margin: 20px -30px -30px -30px;
        }
      `}</style>

      <div className="inner-border">
        {/* Watermark */}
        <div className="watermark">
          <div className="watermark-text">
            MALAWI COMMUNICATIONS<br />
            REGULATORY AUTHORITY
          </div>
        </div>

        <div className="content-wrapper">
          {/* Header */}
          <div className="header">
            {/* Authority Name */}
            <div className="authority-name">
              <h1 className="authority-title">
                MALAWI COMMUNICATIONS REGULATORY AUTHORITY
              </h1>
            </div>

            {/* Reference and License Numbers */}
            <div className="reference-section">
              <span>Ref: PS - {new Date().getFullYear()}</span>
              <span>License No. {license.license_number}</span>
            </div>

            {/* Certificate Type */}
            <div className="certificate-type">
              POSTAL SERVICES LICENSE
            </div>
            <p className="certification-text">
              This license authorizes the holder to operate postal services in Malawi
            </p>
          </div>

          {/* License Details */}
          <div className="license-details">
            <div className="detail-row">
              <span className="detail-label">LICENSEE NAME</span>
              <span className="detail-colon">:</span>
              <span className="detail-value">{license.application.applicant?.name || 'N/A'}</span>
            </div>

            <div className="detail-row">
              <span className="detail-label">BUSINESS REGISTRATION NO.</span>
              <span className="detail-colon">:</span>
              <span className="detail-value">{license.application.applicant?.business_registration_number || 'N/A'}</span>
            </div>

            <div className="detail-row">
              <span className="detail-label">TAX IDENTIFICATION NO.</span>
              <span className="detail-colon">:</span>
              <span className="detail-value">{license.application.applicant?.tpin || 'N/A'}</span>
            </div>

            <div className="detail-row">
              <span className="detail-label">REGISTERED ADDRESS</span>
              <span className="detail-colon">:</span>
              <div className="detail-value">
                <div>{license.application.applicant?.name || 'N/A'}</div>
                {/* <div>{license.application.applicant?.city || ''}, {license.application.applicant?.country || 'Malawi'}</div> */}
              </div>
            </div>

            <div className="detail-row">
              <span className="detail-label">SERVICE COVERAGE AREA</span>
              <span className="detail-colon">:</span>
              <span className="detail-value">National</span>
            </div>

            <div className="detail-row">
              <span className="detail-label">LICENSE VALIDITY PERIOD</span>
              <span className="detail-colon">:</span>
              <span className="detail-value">{getValidityPeriod()}</span>
            </div>
          </div>

          {/* Authorized Services */}
          <div className="services-section">
            <div className="services-title">AUTHORIZED POSTAL SERVICES:</div>
            <ul className="services-list">
              <li>Domestic mail collection, processing, and delivery</li>
              <li>International mail services (inbound and outbound)</li>
              <li>Express and courier services</li>
              <li>Parcel and package delivery services</li>
              <li>Postal financial services (money orders, postal banking)</li>
              <li>Philatelic services</li>
            </ul>
          </div>

          {/* Compliance Statement */}
          <div className="compliance-section">
            <p>
              {license.conditions || 
                `This license is issued under the Communications Act, 2016, and authorizes the licensee 
                to provide postal services in Malawi subject to compliance with all applicable laws, 
                regulations, and license conditions. The licensee shall maintain adequate infrastructure, 
                qualified personnel, and service standards as prescribed by the Malawi Communications 
                Regulatory Authority. This license is non-transferable and must be renewed before expiration.`
              }
            </p>
          </div>

          {/* Footer with Signature and QR Code */}
          <div className="footer-section">
            {/* Signature Section */}
            <div className="signature-area">
              <div className="issue-location">
                <p>
                  Issued at Lilongwe, this {formatDate(license.issue_date)}
                </p>
              </div>

              <div className="signature-line">
                <p className="dg-name">Daud Suleman</p>
                <p className="dg-title">Director General</p>
              </div>
            </div>

            {/* QR Code Section */}
            <div className="qr-section">
              <div className="qr-code">
                <div ref={qrCodeRef}></div>
              </div>
              <p className="qr-text">
                Scan for verification
              </p>
            </div>
          </div>

          {/* Security Footer */}
          <div className="security-footer">
            This licence is issued without any alterations and remains the property of MACRA
          </div>
        </div>
      </div>
    </div>
  );
}
