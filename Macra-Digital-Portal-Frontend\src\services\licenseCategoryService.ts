import { processApiResponse } from '@/lib';
import { apiClient } from '../lib/apiClient';
import { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';
import { LicenseCategory, PaginatedResponse, PaginateQuery, CreateLicenseCategoryDto, UpdateLicenseCategoryDto, LicenseType } from '@/types';

// Utility functions for category codes
export const generateCategoryCode = (name: string): string => {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    .substring(0, 50); // Limit length
};

export const addCodesToCategories = (categories: LicenseCategory[]): LicenseCategory[] => {
  return categories.map(category => ({
    ...category,
    code: generateCategoryCode(category.name),
    children: category.children ? addCodesToCategories(category.children) : undefined
  }));
};

export const findCategoryByCode = (categories: LicenseCategory[], code: string): LicenseCategory | null => {
  for (const category of categories) {
    if (category?.license_type?.code === code) {
      return category;
    }
    if (category.children) {
      const found = findCategoryByCode(category.children, code);
      if (found) return found;
    }
  }
  return null;
};

export const findCategoryById = (categories: LicenseCategory[], id: string): LicenseCategory | null => {
  for (const category of categories) {
    if (category.license_category_id === id) {
      return category;
    }
    if (category.children) {
      const found = findCategoryById(category.children, id);
      if (found) return found;
    }
  }
  return null;
};


export type LicenseCategoriesResponse = PaginatedResponse<LicenseCategory>;


export const licenseCategoryService = {
  // Get all license categories with pagination
  async getLicenseCategories(query: PaginateQuery = {}): Promise<LicenseCategoriesResponse> {
    const params = new URLSearchParams();

    if (query.page) params.set('page', query.page.toString());
    if (query.limit) params.set('limit', query.limit.toString());
    if (query.search) params.set('search', query.search);
    if (query.sortBy) {
      query.sortBy.forEach(sort => params.append('sortBy', sort));
    }
    if (query.searchBy) {
      query.searchBy.forEach(search => params.append('searchBy', search));
    }
    if (query.filter) {
      Object.entries(query.filter).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(`filter.${key}`, v));
        } else {
          params.set(`filter.${key}`, value);
        }
      });
    }

    const response = await apiClient.get(`/license-categories?${params.toString()}`);
    return processApiResponse(response);
  },

  // Get license category by ID with timeout and retry handling
  async getLicenseCategory(id: string): Promise<LicenseCategory> {
    try {
      const response = await apiClient.get(`/license-categories/${id}`, {
        timeout: 30000, // 30 second timeout for individual requests
      });
      return processApiResponse(response);
    } catch (error: any) {
      console.error('Error fetching license category:', error);
      if (error.code === 'ECONNABORTED') {
        throw new Error('Request timeout - please try again');
      }
      throw error;
    }
  },

  // Get license categories by license type with improved error handling
  async getLicenseCategoriesByType(licenseTypeId: string): Promise<any> {
    try {
      const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`, {
        timeout: 30000, // 30 second timeout
      });
      return processApiResponse(response);
    } catch (error: any) {
      console.error('Error fetching license categories by type:', error);
      if (error.code === 'ECONNABORTED') {
        throw new Error('Request timeout - please try again');
      }
      if (error.response?.status === 429) {
        throw new Error('Too many requests - please wait a moment and try again');
      }
      throw error;
    }
  },

  // Create new license category
  async createLicenseCategory(licenseCategoryData: CreateLicenseCategoryDto): Promise<LicenseCategory> {
    const response = await apiClient.post('/license-categories', licenseCategoryData);
    return response.data;
  },

  // Update license category
  async updateLicenseCategory(id: string, licenseCategoryData: UpdateLicenseCategoryDto): Promise<LicenseCategory> {
    const response = await apiClient.put(`/license-categories/${id}`, licenseCategoryData);
    return processApiResponse(response);
  },

  // Delete license category
  async deleteLicenseCategory(id: string): Promise<{ message: string }> {
    const response = await apiClient.delete(`/license-categories/${id}`);
    return processApiResponse(response);
  },

  // Get all license categories (simple list for dropdowns) with caching
  async getAllLicenseCategories(): Promise<any> {
    return cacheService.getOrSet(
      CACHE_KEYS.LICENSE_CATEGORIES,
      async () => {
        console.log('Fetching license categories from API...');
        // Reduce limit to avoid rate limiting
        const response = await this.getLicenseCategories({ limit: 100 });
        return addCodesToCategories(processApiResponse(response));
      },
      CACHE_TTL.LONG // Cache for 15 minutes
    );
  },

  // Get hierarchical tree of categories for a license type with caching
  async getCategoryTree(licenseTypeId: string): Promise<LicenseCategory[]> {
    return cacheService.getOrSet(
      `category-tree-${licenseTypeId}`,
      async () => {
        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/tree`);
        return addCodesToCategories(processApiResponse(response));
      },
      CACHE_TTL.MEDIUM // Cache for 5 minutes
    );
  },

  // Get root categories (no parent) for a license type with caching
  async getRootCategories(licenseTypeId: string): Promise<LicenseCategory[]> {
    return cacheService.getOrSet(
      `root-categories-${licenseTypeId}`,
      async () => {
        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/root`);
        return processApiResponse(response);
      },
      CACHE_TTL.MEDIUM // Cache for 5 minutes
    );
  },

  // Get license categories for parent selection dropdown
  async getCategoriesForParentSelection(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {
    try {
      const params = excludeId ? { excludeId } : {};
      // Try the new endpoint first
      try {
        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/for-parent-selection`, { params });
          const data = processApiResponse(response).data;
        if (data) {
          return data.data;
        } else {
          return [];
        }
      } catch (newEndpointError) {
        // Fallback to existing endpoint
        const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`);
        // Filter out the excluded category if specified
        let categories : LicenseCategory[] = processApiResponse(response);
        if (excludeId) {
          categories = categories.filter(cat => cat.license_category_id !== excludeId);
        }
        return categories;
  
      }
    } catch (error) {

      return [];
    }
  },

  // Get potential parent categories for a license type
  async getPotentialParents(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {
    const params = excludeId ? { excludeId } : {};
    const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/potential-parents`, { params });
    return processApiResponse(response);
  },
};
