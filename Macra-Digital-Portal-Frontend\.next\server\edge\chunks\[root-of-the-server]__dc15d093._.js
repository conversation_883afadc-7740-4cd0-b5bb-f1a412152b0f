(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__dc15d093._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "config": ()=>config,
    "middleware": ()=>middleware
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
;
/**
 * Check if a JWT token is expired
 * @param token - JWT token to check
 * @returns true if token is expired, false otherwise
 */ const isTokenExpired = (token)=>{
    if (!token) return true;
    try {
        // Decode JWT payload (without verification - just for expiry check)
        const payload = JSON.parse(atob(token.split('.')[1]));
        const currentTime = Math.floor(Date.now() / 1000);
        // Check if token has expired
        return payload.exp < currentTime;
    } catch (error) {
        console.error('Error decoding token:', error);
        return true; // Treat invalid tokens as expired
    }
};
/**
 * Check if user is authorized for admin/staff routes
 * @param user - User object with email and roles
 * @returns true if user is authorized for admin routes, false otherwise
 */ const isAuthorizedForAdminRoutes = (user)=>{
    if (!user) return false;
    // Check if user email ends with @macra.mw
    const hasValidEmail = user.email && user.email.endsWith('@macra.mw');
    // Check if user has customer role
    const hasStaffRole = user.roles && !user.roles.includes('customer');
    return hasValidEmail || hasStaffRole;
};
function middleware(request) {
    const url = request.nextUrl.clone();
    // Get auth tokens from cookies
    const authToken = request.cookies.get('auth_token');
    const authUser = request.cookies.get('auth_user');
    // Parse user data if available
    let user = null;
    // Try to get user from customer auth first, then staff auth
    if (authUser) {
        try {
            user = JSON.parse(authUser.value);
        } catch (error) {
            console.error('Failed to parse user data:', error);
        }
    }
    // Always allow auth routes and public routes without redirection
    if (url.pathname.startsWith('/customer/auth/') || url.pathname.startsWith('/auth/') || url.pathname.startsWith('/public/')) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    // Always allow auth routes and public routes without redirection
    if (url.pathname.endsWith('/customer') && !user) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    // Handle root path redirections
    if (url.pathname === '/') {
        if (user && user.roles && user.roles.includes('customer')) {
            // Check if customer token is expired
            if (authToken && isTokenExpired(authToken.value)) {
                url.pathname = '/customer/auth/login';
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            } else {
                url.pathname = '/customer';
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
        } else if (authToken && user && !user.roles.includes('customer')) {
            url.pathname = '/dashboard';
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
        } else if (authToken && user && user.roles) {
            // Check if token is expired
            if (isTokenExpired(authToken.value)) {
                // Redirect to appropriate login based on user role
                if (user.roles.includes('customer')) {
                    url.pathname = '/customer/auth/login';
                } else {
                    url.pathname = '/auth/login';
                }
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            } else {
                // Redirect to appropriate dashboard based on user role
                if (user.roles.includes('customer')) {
                    url.pathname = '/customer';
                } else if (isAuthorizedForAdminRoutes(user)) {
                    url.pathname = '/dashboard';
                } else {
                    // User is not authorized for admin routes, redirect to customer login
                    console.warn('Unauthorized user attempting to access admin routes from root:', {
                        email: user.email,
                        roles: user.roles
                    });
                    url.pathname = '/customer/auth/login';
                }
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
        } else {
            console.log('No auth token or invalid user data');
            // Only redirect if not already on a login page to prevent redirect loops
            if (!url.pathname.startsWith('/customer/auth/') && !url.pathname.startsWith('/auth/')) {
                url.pathname = '/customer/auth/login';
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
            // If already on auth page, allow through to prevent redirect loops
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
        }
    }
    // Handle customer routes (excluding auth routes which are handled above)
    if (url.pathname.startsWith('/customer') && !url.pathname.startsWith('/customer/auth/')) {
        // Check if user exists and has customer role
        if (!user || !user.roles || !user.roles.includes('customer')) {
            url.pathname = '/customer/auth/login';
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
        }
        // For other customer routes, check authentication and token expiry
        if (!authToken || !user) {
            url.pathname = '/customer/auth/login';
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
        }
        // Check if token is expired
        if (isTokenExpired(authToken.value)) {
            url.pathname = '/customer/auth/login';
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
        }
        // Allow authenticated customer users to access customer portal
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    // Handle admin/staff dashboard routes
    if (url.pathname.startsWith('/dashboard')) {
        // If user is authenticated and is a customer, redirect to customer portal
        if (user && user.roles && user.roles.includes('customer')) {
            url.pathname = '/customer';
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
        }
        // If user is not authenticated, check referrer to determine which login page
        if (!user || !user.roles) {
            const referer = request.headers.get('referer');
            // If coming from customer auth pages, redirect to customer login
            if (referer && referer.includes('/customer/auth')) {
                url.pathname = '/customer/auth/login';
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
            // Default to admin login for dashboard access
            url.pathname = '/auth/login';
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
        }
        // Check authentication and token expiry for admin users
        if (!authToken) {
            url.pathname = '/auth/login';
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
        }
        // Check if token is expired
        if (isTokenExpired(authToken.value)) {
            url.pathname = '/auth/login';
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
        }
        // Extra security check: Verify user is authorized for admin routes
        if (!isAuthorizedForAdminRoutes(user)) {
            console.warn('Unauthorized access attempt to admin routes:', {
                email: user.email,
                roles: user.roles,
                path: url.pathname
            });
            url.pathname = '/auth/login';
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
        }
        // Allow authenticated and authorized admin/staff users to access dashboard
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    // Handle dashboard route specifically (root path is handled above)
    if (url.pathname === '/dashboard') {
        if (!user || !user.roles) {
            // Not authenticated, redirect to admin login
            url.pathname = '/auth/login';
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
        }
        // Redirect based on user role
        if (user.roles.includes('customer')) {
            url.pathname = '/customer';
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
        } else if (isAuthorizedForAdminRoutes(user)) {
            // Authorized admin/staff user - allow access to dashboard
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
        } else {
            // User is not authorized for admin routes
            console.warn('Unauthorized access attempt to dashboard:', {
                email: user.email,
                roles: user.roles,
                path: url.pathname
            });
            url.pathname = '/auth/login';
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
        }
    }
    // For other routes, allow access
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
}
const config = {
    matcher: [
        /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images (public images)
     */ '/((?!api|_next/static|_next/image|favicon.ico|images).*)'
    ]
};
}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__dc15d093._.js.map