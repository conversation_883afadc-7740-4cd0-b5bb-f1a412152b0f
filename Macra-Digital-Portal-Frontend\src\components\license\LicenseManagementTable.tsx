'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { applicationService } from '../../services/applicationService';
import { licenseCategoryService } from '../../services/licenseCategoryService';
import { licenseTypeService } from '../../services/licenseTypeService';
import AssignButton from '../common/AssignButton';
import ApplicationViewModal from './ApplicationViewModal';
import DataTable from '../common/DataTable';
import Select from '../common/Select';
import { Application, ApplicationStatus, LicenseCategory, PaginatedResponse, PaginateQuery } from '@/types';
import { usePathname } from 'next/navigation';
import { formatStatus } from '@/utils/formatters';


interface LicenseManagementTableProps {
  licenseTypeId?: string;
  licenseTypeCode?: string; // Primary filter by license type code
  licenseTypeFilter?: string; // Filter by license type name (fallback)
  title: string;
  description: string;
  searchPlaceholder: string;
  emptyStateIcon: string;
  emptyStateMessage?: string;
  departmentType?: string; // Department type for navigation
}

export default function LicenseManagementTable({
  licenseTypeId,
  licenseTypeCode,
  licenseTypeFilter,
  title,
  description,
  searchPlaceholder,
  emptyStateIcon,
  emptyStateMessage,
  departmentType,
}: LicenseManagementTableProps): React.JSX.Element {
  const pathname = usePathname();
  const [applicationsData, setApplicationsData] = useState<PaginatedResponse<Application> | null>(null);
  const [licenseCategories, setLicenseCategories] = useState<LicenseCategory[]>([]);
  const [resolvedLicenseTypeId, setResolvedLicenseTypeId] = useState<string | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [selectedLicenseCategory, setSelectedLicenseCategory] = useState('');
  const [statusFilter, setStatusFilter] = useState<ApplicationStatus | ''>('');
  const [dateRangeFilter, setDateRangeFilter] = useState('');

  // View modal state
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedApplicationId, setSelectedApplicationId] = useState<string | null>(null);

  // Function for viewing applications - opens modal
  const handleViewApplication = (applicationId: string) => {
    setSelectedApplicationId(applicationId);
    setShowViewModal(true);
  };

  const handleCloseViewModal = () => {
    setShowViewModal(false);
    setSelectedApplicationId(null);
  };

  const handleViewModalUpdate = () => {
    // Refresh the applications list when modal updates data
    loadApplications({ page: 1, limit: 10 });
  };

  // Helper function to determine if this is a postal application
  const isPostalApplication = () => {
    return (
      licenseTypeCode?.toLowerCase().includes('postal') ||
      pathname?.toLowerCase().includes('postal') ||
      departmentType?.toLowerCase().includes('postal') ||
      title?.toLowerCase().includes('postal')
    );
  };

  // Load applications function for DataTable
  const loadApplications = useCallback(async (query: PaginateQuery) => {
    try {
      setLoading(true);
      setError(null);

      const effectiveLicenseTypeId = licenseTypeId || resolvedLicenseTypeId;

      // Build filters object
      const filters: Record<string, string> = {};

      // Add license category filter if selected
      if (selectedLicenseCategory) {
        filters.licenseCategoryId = selectedLicenseCategory;
      }

      // Add license type filter - prioritize licenseTypeCode search
      if (effectiveLicenseTypeId) {
        filters.licenseTypeId = effectiveLicenseTypeId;
      } else if (licenseTypeCode) {
        // If we have a license type code but no resolved ID yet, add it as a filter
        filters.licenseTypeCode = licenseTypeCode;
      }

      // Add status filter if selected
      if (statusFilter) {
        filters.status = statusFilter;
      }

      // Handle draft inclusion logic
      if (statusFilter === ApplicationStatus.DRAFT) {
        // If user specifically selected "Draft" status, include drafts
        filters.include_draft = 'true';
      } else {
        // For all other cases, exclude drafts by default
        filters.include_draft = 'false';
      }

      const params = {
        page: query.page,
        limit: query.limit,
        search: query.search || undefined,
        filters: Object.keys(filters).length > 0 ? filters : undefined,
      };
      const response = await applicationService.getApplications(params);
      setApplicationsData(response);
    } catch (err: unknown) {
      setError('Failed to load applications');
      // Set empty data structure to prevent undefined errors
    } finally {
      setLoading(false);
    }
  }, [licenseTypeId, resolvedLicenseTypeId, selectedLicenseCategory, statusFilter, licenseTypeCode]);



  // Load license types and resolve license type ID from code or filter name
  useEffect(() => {
    const fetchLicenseTypes = async () => {
      try {
        const response = await licenseTypeService.getAllLicenseTypes();
        const types = Array.isArray(response) ? response : (response?.data || []);

        if (!Array.isArray(types)) {
          console.warn('License types response is not an array:', types);
          return;
        }

        // Priority 1: If we have a licenseTypeCode, find the matching license type ID
        if (licenseTypeCode && types.length > 0) {
          const matchingType = types.find(type =>
            type.code === licenseTypeCode
          );
          if (matchingType) {
            setResolvedLicenseTypeId(matchingType.license_type_id);
          }
        }
        // Priority 2: If we have a licenseTypeFilter (fallback), find the matching license type ID
        else if (licenseTypeFilter && types.length > 0) {
          const matchingType = types.find(type =>
            type.name.toLowerCase().includes(licenseTypeFilter.toLowerCase())
          );
          if (matchingType) {
            setResolvedLicenseTypeId(matchingType.license_type_id);
          }
        }
      } catch (error) {
        console.error('Error fetching license types:', error);
      }
    };

    fetchLicenseTypes();
  }, [licenseTypeCode, licenseTypeFilter]);

  // Fetch license categories for the dropdown
  useEffect(() => {
    const fetchLicenseCategories = async () => {
      try {
        let categories: LicenseCategory[] = [];
        const effectiveLicenseTypeId = licenseTypeId || resolvedLicenseTypeId;

        if (effectiveLicenseTypeId) {
          // Fetch categories for specific license type
          const response = await licenseCategoryService.getLicenseCategoriesByType(effectiveLicenseTypeId);
          categories = Array.isArray(response) ? response : (response?.data || []);
        } else {
          // Fetch all categories
          const response = await licenseCategoryService.getAllLicenseCategories();
          categories = Array.isArray(response) ? response : (response?.data || []);
        }

        if (!Array.isArray(categories)) {
          console.warn('License categories response is not an array:', categories);
          categories = [];
        }

        setLicenseCategories(categories);
      } catch (error) {
        console.error('Error fetching license categories:', error);
        setLicenseCategories([]);
      }
    };

    // Fetch categories if we have a specific license type OR if no license type filters are provided (fetch all)
    if (resolvedLicenseTypeId || licenseTypeId || (!licenseTypeCode && !licenseTypeFilter)) {
      fetchLicenseCategories();
    }
  }, [licenseTypeId, resolvedLicenseTypeId]);

  // Load applications on component mount and when filters change
  useEffect(() => {
    // Load applications if we have a specific license type OR if no license type filters are provided (show all)
    if (resolvedLicenseTypeId || licenseTypeId || (!licenseTypeCode && !licenseTypeFilter)) {
      loadApplications({ page: 1, limit: 10 });
    }
  }, [loadApplications, resolvedLicenseTypeId, licenseTypeId, licenseTypeCode, licenseTypeFilter]);

  const getStatusBadge = (status: ApplicationStatus) => {
    const statusClasses: Record<ApplicationStatus, string> = {
      [ApplicationStatus.DRAFT]: 'bg-gray-100 text-gray-800',
      [ApplicationStatus.PENDING]: 'bg-orange-100 text-orange-800',
      [ApplicationStatus.SUBMITTED]: 'bg-blue-100 text-blue-800',
      [ApplicationStatus.UNDER_REVIEW]: 'bg-yellow-100 text-yellow-800',
      [ApplicationStatus.EVALUATION]: 'bg-purple-100 text-purple-800',
      [ApplicationStatus.WAITING_FOR_APPROVAL]: 'bg-purple-100 text-purple-800',
      [ApplicationStatus.PENDING_PAYMENT]: 'bg-amber-100 text-amber-800',
      [ApplicationStatus.APPROVED]: 'bg-green-100 text-green-800',
      [ApplicationStatus.REJECTED]: 'bg-red-100 text-red-800',
      [ApplicationStatus.WITHDRAWN]: 'bg-gray-100 text-gray-800',
      [ApplicationStatus.PASS_EVALUATION]: 'bg-blue-100 text-blue-800',
    };

    const statusLabels: Record<ApplicationStatus, string> = {
      [ApplicationStatus.DRAFT]: 'Draft',
      [ApplicationStatus.PENDING]: 'Pending',
      [ApplicationStatus.SUBMITTED]: 'Submitted',
      [ApplicationStatus.UNDER_REVIEW]: 'Under Review',
      [ApplicationStatus.EVALUATION]: 'Evaluation',
      [ApplicationStatus.WAITING_FOR_APPROVAL]: 'Waiting for Approval',
      [ApplicationStatus.PENDING_PAYMENT]: 'Pending Payment',
      [ApplicationStatus.APPROVED]: 'Approved',
      [ApplicationStatus.REJECTED]: 'Rejected',
      [ApplicationStatus.WITHDRAWN]: 'Withdrawn',
      [ApplicationStatus.PASS_EVALUATION]: 'Pass Evaluation',
    };

    function formatHumanReadable(arg0: string): React.ReactNode {
      throw new Error('Function not implemented.');
    }

    return (
      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClasses[status]}`}>
        { formatStatus(statusLabels[status] )}
      </span>
    );
  };

  // Define columns for applications table
  const applicationColumns = [
    {
      key: 'application_number',
      label: 'Application Number',
      sortable: true,
      render: (value: unknown) => (
        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
          {value as string}
        </div>
      ),
    },
    {
      key: 'applicant',
      label: 'Applicant',
      render: (value: unknown, item: Application) => {
        const application = item;
        return (
          <div className="flex items-center">
            <div className="flex-shrink-0 h-10 w-10">
              <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <i className="ri-building-line text-blue-600 dark:text-blue-400"></i>
              </div>
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {application.applicant?.name || 'N/A'}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {application.applicant?.email || 'No email provided'}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      key: 'license_category',
      label: 'License Category',
      render: (value: unknown, item: Application) => {
        const application = item;
        return (
          <div>
            <div className="text-sm text-gray-900 dark:text-gray-100">
              {application.license_category?.name || 'N/A'}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {application.license_category?.license_type?.name || 'N/A'}
            </div>
          </div>
        );
      },
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: unknown) => getStatusBadge(value as ApplicationStatus),
    },
    {
      key: 'submitted_at',
      label: 'Submitted Date',
      sortable: true,
      render: (value: unknown, item: Application) => {
        const application = item;
        return (
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {application.submitted_at
              ? new Date(application.submitted_at).toLocaleDateString()
              : new Date(application.created_at).toLocaleDateString()
            }
          </span>
        );
      },
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: unknown, item: Application) => {
        const application = item;
        return (
          <div className="flex items-center justify-end space-x-2">
            <button
              type="button"
              onClick={() => handleViewApplication(application.application_id)}
              className="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors"
            >
              <i className="ri-eye-line mr-1"></i>
              View
            </button>
            
            {![
              ApplicationStatus.REJECTED,  
              ApplicationStatus.APPROVED
            ].includes(application.status as ApplicationStatus) && (
              <AssignButton
                itemId={application.application_id}
                isAssigned={application.assigned_to ? true: false}
                itemType="application"
                itemTitle={application.application_number}
                onAssignSuccess={handleAssignSuccess}
                className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/40"
              />
            )}
          </div>
        );
      },
    },
  ];



  const handleAssignSuccess = () => {
    // Refresh the applications list when assignment is successful
    console.log('Assignment successful, refreshing applications...');
    loadApplications({ page: 1, limit: 10 });
  };

  const handleFilterChange = (filterType: string, value: string) => {
    switch (filterType) {
      case 'licenseCategory':
        setSelectedLicenseCategory(value);
        break;
      case 'status':
        setStatusFilter(value as ApplicationStatus | '');
        break;
      case 'dateRange':
        setDateRangeFilter(value);
        break;
    }
    // Reload applications with new filters
    loadApplications({ page: 1, limit: 10 });
  };

  return (
    <div className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Page header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{title}</h1>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                {description}
              </p>
            </div>
          </div>
        </div>

        {/* Filters Section */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Filters</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* License Category Filter */}
            <Select
              label="License Category"
              value={selectedLicenseCategory}
              onChange={(value) => handleFilterChange('licenseCategory', value)}
              options={[
                { value: '', label: 'All Categories' },
                ...licenseCategories.map(category => ({
                  value: category.license_category_id,
                  label: category.name
                }))
              ]}
            />

            {/* Status Filter */}
            <Select
              label="Application Status"
              value={statusFilter}
              onChange={(value) => handleFilterChange('status', value)}
              options={[
                { value: '', label: 'All Statuses' },
                { value: ApplicationStatus.DRAFT, label: 'Draft' },
                { value: ApplicationStatus.PENDING, label: 'Pending' },
                { value: ApplicationStatus.SUBMITTED, label: 'Submitted' },
                { value: ApplicationStatus.UNDER_REVIEW, label: 'Under Review' },
                { value: ApplicationStatus.EVALUATION, label: 'Evaluation' },
                { value: ApplicationStatus.PENDING_PAYMENT, label: 'Pending Payment' },
                { value: ApplicationStatus.APPROVED, label: 'Approved' },
                { value: ApplicationStatus.REJECTED, label: 'Rejected' },
                { value: ApplicationStatus.WITHDRAWN, label: 'Withdrawn' }
              ]}
            />

            {/* Date Range Filter */}
            <Select
              label="Date Range"
              value={dateRangeFilter}
              onChange={(value) => handleFilterChange('dateRange', value)}
              options={[
                { value: '', label: 'All Time' },
                { value: 'last-30', label: 'Last 30 Days' },
                { value: 'last-90', label: 'Last 90 Days' },
                { value: 'last-year', label: 'Last Year' }
              ]}
            />
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <i className="ri-error-warning-line text-red-400 text-xl"></i>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                  Error Loading Applications
                </h3>
                <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                  {error}
                </div>
              </div>
              <div className="ml-auto pl-3">
                <div className="-mx-1.5 -my-1.5">
                  <button
                    type="button"
                    onClick={() => setError(null)}
                    className="inline-flex bg-red-50 dark:bg-red-900/20 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/40 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800"
                  >
                    <span className="sr-only">Dismiss</span>
                    <i className="ri-close-line text-sm"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Applications Table */}
        <DataTable<Application>
          columns={applicationColumns}
          data={applicationsData}
          loading={loading}
          onQueryChange={loadApplications}
          searchPlaceholder={searchPlaceholder}
          emptyStateIcon={emptyStateIcon}
          emptyStateMessage={emptyStateMessage}
        />

        {/* Application View Modal */}
        <ApplicationViewModal
          isOpen={showViewModal}
          onClose={handleCloseViewModal}
          applicationId={selectedApplicationId}
          departmentType={departmentType}
          licenseTypeCode={licenseTypeCode}
          onUpdate={handleViewModalUpdate}
        />


      </div>
    </div>
  );
}
