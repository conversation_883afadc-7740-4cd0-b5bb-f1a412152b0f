import React from 'react';
import Link from 'next/link';
import { formatStatus, getStatusColor } from '@/utils/formatters';

interface StatusCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  bgColor: string;
  iconBgColor: string;
  iconTextColor: string;
  linkText: string;
  linkHref: string;
}

const StatusCard: React.FC<StatusCardProps> = ({
  title,
  value,
  icon,
  bgColor,
  iconBgColor,
  iconTextColor,
  linkText,
  linkHref
}) => {

  return (
    <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700 transition-colors duration-200">
      <div className="p-5">
        <div className="flex items-center">
          <div className={`flex-shrink-0 ${iconBgColor} dark:bg-opacity-20 rounded-md p-3 transition-colors duration-200`}>
            <div className={`h-6 w-6 ${iconTextColor}`}>
              {icon}
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate transition-colors duration-200">{title}</dt>
              <dd>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(value)}`}>
                  { formatStatus(value)}
                </span>
              </dd>
            </dl>
          </div>
        </div>
      </div>
      <div className="bg-gray-50 dark:bg-gray-700 px-5 py-3 border-t border-gray-200 dark:border-gray-600 transition-colors duration-200">
        <div className="text-sm">
          <Link href={linkHref} className="font-medium text-primary hover:text-primary-dark dark:text-primary-light dark:hover:text-primary transition-colors duration-200">
            {linkText}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default StatusCard;
