'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { taskAssignmentService, taskService } from '@/services/task-assignment';
import { useToast } from '@/contexts/ToastContext';
import PaymentTaskComponent from '@/components/tasks/PaymentTaskComponent';
import ApplicationTaskComponent from '@/components/tasks/ApplicationTaskComponent';
import GeneralTaskComponent from '@/components/tasks/GeneralTaskComponent';
import ConfirmationModal from '@/components/common/ConfirmationModal';
import { Task, TaskType } from '@/types';
import ActivityNotesModal from '@/components/evaluation/ActivityNotesModal';
import { useAuth } from '@/contexts/AuthContext';

export default function TaskViewPage() {
  const params = useParams();
  const router = useRouter();
  const { showToast } = useToast();
  const { user } = useAuth();
  const taskId = params.taskId as string;

  const [task, setTask] = useState<Task | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [closingTask, setClosingTask] = useState(false);
  const [isCommunicationModalOpen, setIsCommunicationModalOpen] = useState(false);
  const [showCloseTaskModal, setShowCloseTaskModal] = useState(false);

  useEffect(() => {
    if (taskId) {
      loadTask();
    }
  }, [taskId]);

  const loadTask = async () => {
    try {
      setLoading(true);
      setError(null);      
      const task = await taskAssignmentService.getTaskById(taskId);
      setTask(task);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load task';
      showToast(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleTaskUpdate = async () => {
    // Reload task data after updates
    await loadTask();
  };

  const handleCloseTaskClick = () => {
    setShowCloseTaskModal(true);
  };

  const handleCloseTask = async () => {
    if (!task) return;

    try {
      setClosingTask(true);

      const response = await taskService.updateTask(task.task_id, {
        status: 'completed'
      });

      if (response) {
        showToast('Task closed successfully', 'success');
        await loadTask(); // Reload to show updated status
        setShowCloseTaskModal(false);
      } else {
        throw new Error('Failed to close task');
      }
    } catch (error) {
      console.error('Error closing task:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to close task';
      showToast(errorMessage, 'error');
    } finally {
      setClosingTask(false);
    }
  };

  const handleEmailClient = () => {
    setIsCommunicationModalOpen(true);
  };

  const renderTaskComponent = () => {
    if (!task) return null;

    switch (task.task_type) {
      case TaskType.PAYMENT_VERIFICATION:
        return (
          <PaymentTaskComponent
            task={task}
            onTaskUpdate={handleTaskUpdate}
          />
        );

      case 'application':
      case 'evaluation':
        return (
          <ApplicationTaskComponent
            task={task}
            onTaskUpdate={handleTaskUpdate}
          />
        );

      default:
        return (
          <GeneralTaskComponent
            task={task}
            onTaskUpdate={handleTaskUpdate}
          />
        );
    }
  };

  const getTaskTypeIcon = (taskType: string) => {
    switch (taskType) {
      case 'payment_verification': return 'ri-money-dollar-circle-line';
      case 'application': return 'ri-file-list-line';
      case 'evaluation': return 'ri-search-line';
      case 'document_review': return 'ri-file-text-line';
      case 'compliance_check': return 'ri-shield-check-line';
      default: return 'ri-task-line';
    }
  };

  const getTaskTypeColor = (taskType: string) => {
    switch (taskType) {
      case 'payment_verification': return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';
      case 'application': return 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900/20';
      case 'evaluation': return 'text-purple-600 bg-purple-100 dark:text-purple-400 dark:bg-purple-900/20';
      case 'document_review': return 'text-orange-600 bg-orange-100 dark:text-orange-400 dark:bg-orange-900/20';
      case 'compliance_check': return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';
      default: return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-700';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'in_progress': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'cancelled': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'on_hold': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading task...</p>
        </div>
      </div>
    );
  }

  if (error || !task) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="text-center max-w-md mx-auto">
          <div className="bg-red-100 dark:bg-red-900/20 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <i className="ri-error-warning-line text-2xl text-red-600 dark:text-red-400"></i>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">Task Not Found</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {error || 'The requested task could not be found.'}
          </p>
          <button
            onClick={() => router.back()}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
          >
            <i className="ri-arrow-left-line mr-2"></i>
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Breadcrumb */}
      <nav className="flex mb-6" aria-label="Breadcrumb">
        <ol className="inline-flex items-center space-x-1 md:space-x-3">
          <li className="inline-flex items-center">
            <a
              href="/"
              className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400"
            >
              <i className="ri-home-line mr-2"></i>
              Admin
            </a>
          </li>
          <li>
            <div className="flex items-center">
              <i className="ri-arrow-right-s-line text-gray-400"></i>
              <a
                href="/tasks"
                className="ml-1 text-sm font-medium text-gray-700 hover:text-red-600 md:ml-2 dark:text-gray-400 dark:hover:text-red-400"
              >
                Tasks
              </a>
            </div>
          </li>
          <li aria-current="page">
            <div className="flex items-center">
              <i className="ri-arrow-right-s-line text-gray-400"></i>
              <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">
                {task.title}
              </span>
            </div>
          </li>
        </ol>
      </nav>

      {/* Task Header Card */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
        <div className="px-6 py-4">
          {/* Mobile-first responsive layout */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Left section - Task info */}
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Go back"
              >
                <i className="ri-arrow-left-line text-xl"></i>
              </button>
              <div className="flex items-center space-x-4">
                <div className={`p-3 rounded-lg ${getTaskTypeColor(task.task_type)}`}>
                  <i className={`${getTaskTypeIcon(task.task_type)} text-xl`}></i>
                </div>
                <div>
                  <h1 className="text-xl lg:text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {task.title}
                  </h1>
                  <div className="flex flex-wrap items-center gap-2 lg:gap-4 mt-1">
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Task #{task.task_number}
                    </p>
                    {task.assigned_to && (
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Assigned to: {task.assignee?.first_name}
                      </p>
                    )}
                    {task.due_date && (
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Due: {new Date(task.due_date).toLocaleDateString()}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Right section - Status, badges, and actions */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
              {/* Status and Priority badges */}
              <div className="flex items-center space-x-2">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(task.status)}`}>
                  {task.status.replace('_', ' ').toUpperCase()}
                </span>
              </div>

              {/* Action buttons */}
              <div className="flex items-center space-x-2">
                {/* Email Client Button - Show for application, invoice, and payment tasks */}
                {(task.entity_type === 'application' || task.entity_type === 'invoice' || task.entity_type === 'payment' ||
                  task.task_type === 'application' || task.task_type === 'payment_verification') && task.entity_id && (
                  <button
                    onClick={handleEmailClient}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                    title={`Email ${task.entity_type === 'application' ? 'Applicant' : 'Client'}`}
                  >
                    <i className="ri-mail-line mr-2"></i>
                  </button>
                )}

                {/* Close Task Button - Only show if task is not already completed */}
                {task.status.toLowerCase() !== 'completed' && user?.isAdmin && (
                  <button
                    onClick={handleCloseTaskClick}
                    disabled={closingTask}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    title="Close Task"
                  >
                    {closingTask ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-1"></div>
                        <span className="hidden sm:inline ml-1">Closing...</span>
                      </>
                    ) : (
                      <>
                        <span className="hidden sm:inline">Close Task</span>
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>

          {task.description && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <p className="text-gray-600 dark:text-gray-400 leading-relaxed">{task.description}</p>
            </div>
          )}
        </div>
      </div>

      {/* Task Content */}
      <div className="space-y-6">
        {renderTaskComponent()}
      </div>

      {/* Communication Center Modal */}
      {task && task.entity_id && (
        <ActivityNotesModal
          isOpen={isCommunicationModalOpen}
          onClose={() => setIsCommunicationModalOpen(false)}
          entityType={task.entity_type || 'task'}
          entityId={task.entity_id}
          initialEmails={task.assigner?.email}
        />  
      )}
      {/* Close Task Confirmation Modal */}
      <ConfirmationModal
        isOpen={showCloseTaskModal}
        onClose={() => setShowCloseTaskModal(false)}
        onConfirm={handleCloseTask}
        title="Close Task"
        message={
          <div className="space-y-3">
            <p>Are you sure you want to close this task?</p>
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3 text-sm">
              <p className="font-medium text-green-900 dark:text-green-100 mb-2">This action will:</p>
              <ul className="text-green-700 dark:text-green-300 space-y-1">
                <li>• Mark the task as completed</li>
                <li>• Update the task status permanently</li>
                <li>• Create an activity log entry</li>
                <li>• Notify relevant stakeholders</li>
              </ul>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              This action cannot be undone. Make sure all work is completed before closing.
            </p>
          </div>
        }
        confirmText="Close Task"
        confirmVariant="primary"
        loading={closingTask}
        icon={
          <div className="flex-shrink-0">
            <div className="h-10 w-10 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
              <i className="ri-check-line text-green-600 dark:text-green-400 text-xl"></i>
            </div>
          </div>
        }
      />
    </div>
  );
}
