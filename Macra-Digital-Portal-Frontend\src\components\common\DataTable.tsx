'use client';

import { useState, useEffect, useCallback } from 'react';
import { PaginateQuery } from '../../types';
import Pagination from './Pagination';
import '../../styles/DataTable.css';

// Generic paginated response interface to handle different response types
interface GenericPaginatedResponse<T> {
  data: T[];
  meta: {
    itemsPerPage: number;
    totalItems?: number;
    currentPage?: number;
    totalPages?: number;
    sortBy: [string, string][];
    searchBy: string[];
    search: string;
    select?: string[];
    filter?: Record<string, string | string[]>;
  };
  links: {
    first?: string;
    previous?: string;
    current: string;
    next?: string;
    last?: string;
  };
}

interface Column<T> {
  key: keyof T | string;
  label: string;
  sortable?: boolean;
  searchable?: boolean;
  render?: (value: unknown, item: T) => React.ReactNode;
  className?: string;
}

interface DataTableProps<T> {
  columns: Column<T>[];
  data: GenericPaginatedResponse<T> | null;
  loading?: boolean;
  onQueryChange: (query: PaginateQuery) => void;
  searchPlaceholder?: string;
  className?: string;
  emptyStateIcon?: string;
  emptyStateMessage?: string;
}

export default function DataTable<T extends Record<string, unknown>>({
  columns,
  data,
  loading = false,
  onQueryChange,
  searchPlaceholder = "Search...",
  className = "",
  emptyStateIcon = "ri-inbox-line",
  emptyStateMessage = "No data found",
}: DataTableProps<T>) {
  const [query, setQuery] = useState<PaginateQuery>({
    page: 1,
    limit: 10,
    search: '',
    sortBy: [],
  });
  const [searchInput, setSearchInput] = useState('');

  const handleSearch = useCallback((search: string) => {
    try {
      const newQuery = { ...query, search, page: 1 };
      setQuery(newQuery);
      onQueryChange(newQuery);
    } catch (error) {
      console.error('Error handling search:', error);
    }
  }, [query, onQueryChange]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchInput !== query.search) {
        handleSearch(searchInput);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchInput, query.search, handleSearch]);

  const handleSort = (columnKey: string) => {
    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));
    let newSortBy: string[] = [];

    if (!currentSort) {
      newSortBy = [`${columnKey}:ASC`];
    } else if (currentSort.endsWith(':ASC')) {
      newSortBy = [`${columnKey}:DESC`];
    } else {
      newSortBy = [];
    }

    const newQuery = { ...query, sortBy: newSortBy, page: 1 };
    setQuery(newQuery);
    onQueryChange(newQuery);
  };

  const handlePageChange = (page: number) => {
    const newQuery = { ...query, page };
    setQuery(newQuery);
    onQueryChange(newQuery);
  };

  const getSortDirection = (columnKey: string): 'asc' | 'desc' | null => {
    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));
    if (!currentSort) return null;
    return currentSort.endsWith(':ASC') ? 'asc' : 'desc';
  };

  // Handle null data case early
  if (!data) {
    return (
      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>
        <div className="p-6 text-center text-gray-500 dark:text-gray-400">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-600"></div>
            <span className="ml-2">Loading...</span>
          </div>
        </div>
      </div>
    );
  }

  const handlePageSizeChange = (newPageSize: number) => {
    const newQuery = { ...query, limit: newPageSize, page: 1 };
    setQuery(newQuery);
    onQueryChange(newQuery);
  };

  return (
    <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>
      {/* Search Bar */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <i className="ri-search-line text-gray-400 dark:text-gray-500"></i>
          </div>
          <input
            type="text"
            placeholder={searchPlaceholder}
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 dark:text-gray-100"
          />
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto data-table-container">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-900">
            <tr>
              {columns.map((column) => (
                <th
                  key={String(column.key)}
                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${
                    column.sortable ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800' : ''
                  } ${column.className || ''}`}
                  onClick={() => column.sortable && handleSort(String(column.key))}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.label}</span>
                    {column.sortable && (
                      <div className="flex flex-col">
                        <i className={`ri-arrow-up-s-line text-xs ${
                          getSortDirection(String(column.key)) === 'asc' ? 'text-red-600' : 'text-gray-400'
                        }`}></i>
                        <i className={`ri-arrow-down-s-line text-xs -mt-1 ${
                          getSortDirection(String(column.key)) === 'desc' ? 'text-red-600' : 'text-gray-400'
                        }`}></i>
                      </div>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {loading ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-600"></div>
                    <span className="ml-2">Loading...</span>
                  </div>
                </td>
              </tr>
            ) : !data?.data || data.data.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                  <div className="flex flex-col items-center justify-center py-8">
                    <i className={`${emptyStateIcon} text-4xl mb-2`}></i>
                    <p>{emptyStateMessage}</p>
                  </div>
                </td>
              </tr>
            ) : (
              data.data.map((item, index) => (
                <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  {columns.map((column) => (
                    <td key={String(column.key)} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      {column.render
                        ? column.render(item[column.key as keyof T], item)
                        : String(item[column.key as keyof T] || '')
                      }
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {data?.meta && data.meta.totalItems !== undefined && data.meta.currentPage !== undefined && data.meta.totalPages !== undefined && (
        <Pagination
          meta={{
            ...data.meta,
            totalItems: data.meta.totalItems,
            currentPage: data.meta.currentPage,
            totalPages: data.meta.totalPages,
          }}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          showFirstLast={true}
          showPageSizeSelector={true}
          showInfo={true}
          maxVisiblePages={7}
          pageSizeOptions={[10, 25, 50, 100]}
        />
      )}
    </div>
  );
}
