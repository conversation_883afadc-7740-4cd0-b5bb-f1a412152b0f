'use client';

import { useState, useEffect } from 'react';
import { taskService } from '@/services/task-assignment';
import { useAuth } from '@/contexts/AuthContext';
import { TaskStatus } from '@/types';

interface UseTaskCountReturn {
  pendingCount: number;
  loading: boolean;
  error: string | null;
  refreshCount: () => Promise<void>;
}

export const useTaskCount = (): UseTaskCountReturn => {
  const { user } = useAuth();
  const [pendingCount, setPendingCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTaskCount = async () => {
    if (!user) {
      setPendingCount(0);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      if (user.isAdmin) {
        // Administrators see all pending tasks
        const stats = await taskService.getTaskStats();
        setPendingCount(stats.pending || 0);
      } else {
        // Other users see only tasks assigned to them with pending status
        const response = await taskService.getMyTasks({
          status: TaskStatus.PENDING,
          limit: 1, // We only need the count, not the actual tasks
          page: 1
        });
        // The total count should be in the response metadata
        setPendingCount(response.meta?.totalItems || 0);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch task count');
      setPendingCount(0);
    } finally {
      setLoading(false);
      setTimeout(()=>{
        fetchTaskCount();
      },600000)
    }
  };

  const refreshCount = async () => {
    await fetchTaskCount();
  };

  // Initial fetch
  useEffect(() => {
    fetchTaskCount();
  }, [user]);

  // Poll for updates every 60 seconds (reduced frequency to avoid too many API calls)
  useEffect(() => {
    if (!user) return;

    const interval = setInterval(fetchTaskCount, 60000);
    return () => clearInterval(interval);
  }, [user]);

  // Listen for task updates via custom events
  useEffect(() => {
    const handleTaskUpdate = () => {
      fetchTaskCount();
    };

    // Listen for custom events that indicate task updates
    window.addEventListener('taskUpdated', handleTaskUpdate);
    window.addEventListener('taskCompleted', handleTaskUpdate);
    window.addEventListener('taskAssigned', handleTaskUpdate);
    window.addEventListener('taskRefresh', handleTaskUpdate);

    return () => {
      window.removeEventListener('taskUpdated', handleTaskUpdate);
      window.removeEventListener('taskCompleted', handleTaskUpdate);
      window.removeEventListener('taskAssigned', handleTaskUpdate);
      window.removeEventListener('taskRefresh', handleTaskUpdate);
    };
  }, []);

  return {
    pendingCount,
    loading,
    error,
    refreshCount
  };
};
