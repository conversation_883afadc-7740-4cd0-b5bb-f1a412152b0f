'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { ApplicationStatus, Application, StatusAction } from '@/types/license';
import { applicationStatusService } from '@/services/applicationStatusService';
import { activityNotesService } from '@/services/activityNotesService';
import { licenseService } from '@/services/licenseService';
import { useToast } from '@/contexts/ToastContext';
import { useAuth } from '@/contexts/AuthContext';

interface ApplicationStatusButtonsProps {
  application: Application;
  onStatusChange?: (newStatus: string, updatedApplication: Application) => void;
  disabled?: boolean;
  className?: string;
}


const ApplicationStatusButtons: React.FC<ApplicationStatusButtonsProps> = ({
  application,
  onStatusChange,
  disabled = false,
  className = ''
}) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState<StatusAction | null>(null);
  const [localApplication, setLocalApplication] = useState<Application>(application);
  const [statusComment, setStatusComment] = useState<string>('');

  // Toast notifications
  const { showSuccess, showError } = useToast();
  const { user } = useAuth();

  // Sync local state when application prop changes
  useEffect(() => {
    console.log('📥 ApplicationStatusButtons: Application prop changed, updating local state to status:', application.status);
    setLocalApplication(application);
  }, [application]);

  // Define available status actions based on current status
  const getAvailableActions = (status: string): StatusAction[] => {
    const actions: StatusAction[] = [];

    switch (status.toLowerCase()) {
      case 'submitted':
        actions.push({
          status: 'evaluation' as ApplicationStatus,
          label: 'Start Review',
          icon: 'ri-eye-line',
          roles: ['officer', 'administrator'],
          color: 'bg-blue-600 hover:bg-blue-700',
          hoverColor: 'hover:bg-blue-700',
          description: 'Move application to under review status'
        });
        break;

      case 'under_review':
        actions.push({
          status: 'evaluation' as ApplicationStatus,
          label: 'Start Evaluation',
          icon: 'ri-file-list-3-line',
          roles: ['officer', 'administrator'],
          color: 'bg-purple-600 hover:bg-purple-700',
          hoverColor: 'hover:bg-purple-700',
          description: 'Move application to evaluation phase'
        });
        actions.push({
          status: 'rejected' as ApplicationStatus,
          label: 'Reject',
          icon: 'ri-close-circle-line',
          color: 'bg-red-600 hover:bg-red-700',
          hoverColor: 'hover:bg-red-700',
          roles: ['officer', 'administrator'],
          description: 'Reject the application',
          confirmMessage: 'Are you sure you want to reject this application? This action cannot be undone.'
        });
        break;

      case 'evaluation':
        actions.push({
          status: 'pass_evaluation' as ApplicationStatus,
          label: 'Pass Evaluation',
          icon: 'ri-checkbox-circle-line',
          roles: ['officer', 'administrator'],
          color: 'bg-green-600 hover:bg-green-700',
          hoverColor: 'hover:bg-green-700',
          description: 'Mark evaluation as passed and proceed to payment'
        });
        actions.push({
          status: 'rejected' as ApplicationStatus,
          label: 'Fail Evaluation',
          icon: 'ri-close-circle-line',
          roles: ['officer', 'administrator'],
          color: 'bg-red-600 hover:bg-red-700',
          hoverColor: 'hover:bg-red-700',
          description: 'Fail the evaluation and reject application',
          confirmMessage: 'Are you sure you want to fail this evaluation? This will reject the application.'
        });
        break;

      // case 'pass_evaluation':
      //   actions.push({
      //     status: 'pending_payment' as ApplicationStatus,
      //     label: 'Request Payment',
      //     roles: ['finance', 'administrator'],
      //     icon: 'ri-money-dollar-circle-line',
      //     color: 'bg-yellow-600 hover:bg-yellow-700',
      //     hoverColor: 'hover:bg-yellow-700',
      //     description: 'Request payment from applicant'
      //   });
      //   break;

      case 'waiting_for_approval':
        actions.push({
          status: 'approved' as ApplicationStatus,
          label: 'Approve',
          icon: 'ri-check-double-line',
          color: 'bg-green-600 hover:bg-green-700',
          roles: ['finance', 'administrator'],
          hoverColor: 'hover:bg-green-700',
          description: 'Approve the application (payment confirmed)',
          confirmMessage: 'Are you sure you want to approve this application? This action cannot be undone.'
        });
        break;

      default:
        // No actions available for other statuses
        break;
    }

    return actions;
  };

  // Filter actions based on user roles
  const filterActionsByUserRole = (actions: StatusAction[]): StatusAction[] => {
    if (!user || !user.roles) {
      console.log('ApplicationStatusButtons: No user or user roles found');
      return [];
    }

    const filteredActions = actions.filter(action => {
      // If no roles specified for the action, show to all users
      if (!action.roles || action.roles.length === 0) {
        console.log(`ApplicationStatusButtons: Action "${action.label}" has no role restrictions, showing to all users`);
        return true;
      }

      // Check if user has any of the required roles
      const hasRequiredRole = action.roles.some(requiredRole =>
        user.roles?.some(userRole =>
          userRole.toLowerCase() === requiredRole.toLowerCase()
        ) || false
      );

      return hasRequiredRole;
    });
    return filteredActions;
  };

  const handleStatusChange = async (action: StatusAction) => {
    // Check if comment is required
    if (!statusComment.trim()) {
      const errorMessage = 'Please provide a comment for this status change';
      showError(errorMessage, 5000);
      return;
    }

    if (action.confirmMessage) {
      setShowConfirmDialog(action);
      return;
    }

    await executeStatusChange(action);
  };

  const executeStatusChange = async (action: StatusAction) => {
    try {
      setIsUpdating(true);

      // Validate comment if required
      if (!statusComment.trim()) {
        const errorMessage = 'Please provide a comment for this status change';
        showError(errorMessage, 5000);
        return;
      }

      // Update the application status via comprehensive tracking API
      console.log(`🔄 Updating application ${localApplication.application_id} status to: ${action.status}`);

      await applicationStatusService.updateApplicationStatus(
        localApplication.application_id,
        {
          status: action.status,
          comments: statusComment.trim(),
          reason: action.description,
          send_email: true, // Always send email notification
          changed_by: undefined // Will be set from auth token
        }
      );

      console.log(`✅ Application status updated successfully to: ${action.status}`);

      // Special handling for approved status
      if (action.status === 'approved') {
        console.log(`🏷️ Application approved - license generation should be triggered automatically by backend`);

        // Wait a moment for license generation, then verify
        setTimeout(async () => {
          try {
            const license = await licenseService.getLicenseByApplication(localApplication.application_id);
            if (license) {
              console.log(`✅ License verified: ${license.license_number}`);
              showSuccess(
                `Application approved successfully! License ${license.license_number} has been generated and the applicant will be notified.`,
                10000
              );
            } else {
              console.warn(`⚠️ License not found after approval for application: ${localApplication.application_id}`);
              showSuccess(
                'Application approved successfully! License generation is in progress and the applicant will be notified.',
                8000
              );
            }
          } catch (error) {
            console.error('Error verifying license creation:', error);
            showSuccess(
              'Application approved successfully! License generation is in progress and the applicant will be notified.',
              8000
            );
          }
        }, 2000); // Wait 2 seconds for backend processing
      }

      // Create activity note with status update (this will email the applicant)
      await activityNotesService.createStatusUpdate(
        localApplication.application_id,
        `Application status changed from "${localApplication.status.replace('_', ' ')}" to "${action.status.replace('_', ' ')}"${statusComment.trim() ? `: ${statusComment.trim()}` : ''}`,
        {
          previous_status: localApplication.status,
          new_status: action.status,
          action_taken: action.label,
          comment: statusComment.trim()
        }
      );

      // Update local state
      const newApplication = { ...localApplication, status: action.status };
      console.log('✅ ApplicationStatusButtons: Updating local application status from', localApplication.status, 'to', action.status);
      setLocalApplication(newApplication);

      // Clear the comment field
      setStatusComment('');

      // Call the callback if provided
      if (onStatusChange) {
        onStatusChange(action.status, newApplication);
      }

      // Show success toast (unless it's approval which has its own message)
      if (action.status !== 'approved') {
        showSuccess(
          `Application status successfully changed to "${action.status.replace('_', ' ')}" and notification sent to applicant.`,
          6000
        );
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update application status';
      console.error('Error updating application status:', err);

      // Special error handling for approval failures
      if (action.status === 'approved') {
        showError(
          `Failed to approve application: ${errorMessage}. This may be due to license generation issues. Please check the application details and try again.`,
          10000
        );
      } else {
        showError(
          `Failed to update application status: ${errorMessage}`,
          8000
        );
      }
    } finally {
      setIsUpdating(false);
      setShowConfirmDialog(null);
    }
  };

  // Calculate available actions based on current status and user roles
  const availableActions = useMemo(() => {
    console.log('🔄 ApplicationStatusButtons: Recalculating available actions for status:', localApplication.status);
    const allActions = getAvailableActions(localApplication.status);
    const filtered = filterActionsByUserRole(allActions);
    console.log('🔄 ApplicationStatusButtons: Available actions after filtering:', filtered.map(a => a.label));
    return filtered;
  }, [localApplication.status, user]);

  console.log('🎯 ApplicationStatusButtons: Rendering with status:', localApplication.status, 'Available actions:', availableActions.length);

  if (availableActions.length === 0) {
    console.log('❌ ApplicationStatusButtons: No available actions, hiding component');
    return null;
  }

  if (!user || user.isCustomer) {
    console.log('❌ ApplicationStatusButtons: User is customer or not logged in, hiding component');
    return (<></>);
  }

  return (
    <div className={`space-y-4 ${className}`}>

      {/* Status Action Buttons */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
          <i className="ri-settings-3-line mr-2"></i>
          Application Status Actions
        </h3>

        {/* Comment Field */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Status Change Comment <span className="text-red-500">*</span>
          </label>
          <textarea
            value={statusComment}
            onChange={(e) => setStatusComment(e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
            placeholder="Provide a comment explaining the reason for this status change. This will be emailed to the applicant and saved as an activity note."
            disabled={disabled || isUpdating}
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            <i className="ri-mail-line mr-1"></i>
            This comment will be emailed to the applicant and saved in the application activity log.
          </p>
        </div>

        <div className="flex flex-wrap gap-3">
          {availableActions.map((action, index) => (
            <button
              key={index}
              onClick={() => handleStatusChange(action)}
              disabled={disabled || isUpdating || !statusComment.trim()}
              className={`inline-flex items-center px-4 py-2.5 rounded-lg border border-transparent transition-all duration-200 ${action.color} text-white disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-lg hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-opacity-50 text-sm font-medium`}
              title={action.description}
            >
              <i className={`${action.icon} text-base mr-2`}></i>
              <span>{action.label}</span>

              {isUpdating ? (
                <i className="ri-loader-4-line animate-spin text-base ml-2"></i>
              ) : (
                <i className="ri-arrow-right-line text-base ml-2 opacity-70"></i>
              )}
            </button>
          ))}
        </div>

        {/* Action descriptions */}
        <div className="mt-4 space-y-2">
          {availableActions.map((action, index) => (
            <div key={index} className="flex items-start space-x-2 text-sm text-gray-600 dark:text-gray-400">
              <i className={`${action.icon} text-base mt-0.5 flex-shrink-0`}></i>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">{action.label}:</span>
                <span className="ml-1">{action.description}</span>
              </div>
            </div>
          ))}
        </div>

        {/* Help text when comment is missing */}
        {!statusComment.trim() && (
          <div className="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <div className="flex items-center text-yellow-800 dark:text-yellow-200 text-sm">
              <i className="ri-information-line mr-2"></i>
              <span>Please provide a comment above to enable status change actions.</span>
            </div>
          </div>
        )}

        {/* Current Status Display */}
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">Current Status:</span>
            <span className="font-medium text-gray-900 dark:text-gray-100 capitalize">
              {localApplication.status.replace('_', ' ')}
            </span>
          </div>
          <div className="flex items-center justify-between text-sm mt-2">
            <span className="text-gray-600 dark:text-gray-400">Application Number:</span>
            <span className="font-mono text-xs text-gray-900 dark:text-gray-100">
              {localApplication.application_number}
            </span>
          </div>
          <div className="flex items-center justify-between text-sm mt-2">
            <span className="text-gray-600 dark:text-gray-400">Applicant:</span>
            <span className="text-gray-900 dark:text-gray-100">
              {localApplication.applicant?.name}
            </span>
          </div>
        </div>
      </div>

      {/* Confirmation Dialog */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <i className={`${showConfirmDialog.icon} text-2xl mr-3 text-yellow-500`}></i>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Confirm Action
              </h3>
            </div>
            
            <div className="mb-6">
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {showConfirmDialog.confirmMessage}
              </p>

              {statusComment.trim() && (
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Comment to be sent to applicant:
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400 italic">
                    "{statusComment.trim()}"
                  </p>
                </div>
              )}
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  setShowConfirmDialog(null);
                  // Don't clear comment on cancel - user might want to try again
                }}
                className="flex-1 inline-flex items-center justify-center px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                <i className="ri-close-line mr-2"></i>
                Cancel
              </button>
              <button
                onClick={() => executeStatusChange(showConfirmDialog)}
                disabled={isUpdating}
                className={`flex-1 inline-flex items-center justify-center px-4 py-2.5 rounded-lg text-sm font-medium text-white transition-all duration-200 ${showConfirmDialog.color} disabled:opacity-50 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-opacity-50`}
              >
                {isUpdating ? (
                  <>
                    <i className="ri-loader-4-line animate-spin mr-2"></i>
                    Updating...
                  </>
                ) : (
                  <>
                    <i className="ri-check-line mr-2"></i>
                    Confirm
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ApplicationStatusButtons;
