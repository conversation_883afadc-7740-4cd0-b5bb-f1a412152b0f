{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAee,SAAS,aAAa,KAOjB;QAPiB,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU,GAPiB;;IAQnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,AAAC,GAAgB,OAAd,YAAW,KAAyB,OAAtB,oBAAmB,KAAgD,OAA7C,YAAY,SAAS,kBAAkB,IAAG,KAAa,OAAV;IAEzG,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAwB;gCACH,CAAA,iBAAA,2BAAA,KAAM,UAAU,IAAG,AAAC,KAAoB,OAAhB,KAAK,UAAU,IAAK;gCAAG;;;;;;;;;;;;kCAInF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,6LAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD;GAvJwB;;QAUG,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAZN", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/utils/imageUtils.ts"], "sourcesContent": ["/**\r\n * Utility functions for handling profile images\r\n */\r\n\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\n\r\n/**\r\n * Get the full URL for a profile image\r\n * @param profileImage - The profile image path from the API\r\n * @returns Full URL for the profile image\r\n */\r\nexport const getProfileImageUrl = (profileImage?: string): string | null => {\r\n  if (!profileImage) return null;\r\n  \r\n  // If it's already a full URL (starts with http), return as is\r\n  if (profileImage.startsWith('http://') || profileImage.startsWith('https://')) {\r\n    return profileImage;\r\n  }\r\n  \r\n  // If it's a relative path, prepend the API base URL\r\n  if (profileImage.startsWith('/')) {\r\n    return `${API_BASE_URL}${profileImage}`;\r\n  }\r\n  \r\n  // If it's just a filename, assume it's in the uploads directory\r\n  return `${API_BASE_URL}/uploads/avatars/${profileImage}`;\r\n};\r\n\r\n/**\r\n * Get user initials for fallback display\r\n * @param firstName - User's first name\r\n * @param lastName - User's last name\r\n * @returns Initials string (e.g., \"JD\")\r\n */\r\nexport const getUserInitials = (firstName?: string, lastName?: string): string => {\r\n  const first = firstName?.charAt(0)?.toUpperCase() || '';\r\n  const last = lastName?.charAt(0)?.toUpperCase() || '';\r\n  return `${first}${last}` || 'U';\r\n};\r\n\r\n/**\r\n * Validate image file for upload\r\n * @param file - File to validate\r\n * @returns Validation result with error message if invalid\r\n */\r\nexport const validateImageFile = (file: File): { valid: boolean; error?: string } => {\r\n  // Check file type\r\n  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\r\n  if (!allowedTypes.includes(file.type)) {\r\n    return {\r\n      valid: false,\r\n      error: 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'\r\n    };\r\n  }\r\n\r\n  // Check file size (5MB max)\r\n  const maxSize = 5 * 1024 * 1024; // 5MB\r\n  if (file.size > maxSize) {\r\n    return {\r\n      valid: false,\r\n      error: 'File size too large. Maximum size is 5MB.'\r\n    };\r\n  }\r\n\r\n  return { valid: true };\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAEoB;AAArB,MAAM,eAAe,6DAAmC;AAOjD,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,cAAc,OAAO;IAE1B,8DAA8D;IAC9D,IAAI,aAAa,UAAU,CAAC,cAAc,aAAa,UAAU,CAAC,aAAa;QAC7E,OAAO;IACT;IAEA,oDAAoD;IACpD,IAAI,aAAa,UAAU,CAAC,MAAM;QAChC,OAAO,AAAC,GAAiB,OAAf,cAA4B,OAAb;IAC3B;IAEA,gEAAgE;IAChE,OAAO,AAAC,GAAkC,OAAhC,cAAa,qBAAgC,OAAb;AAC5C;AAQO,MAAM,kBAAkB,CAAC,WAAoB;QACpC,mBACD;IADb,MAAM,QAAQ,CAAA,sBAAA,iCAAA,oBAAA,UAAW,MAAM,CAAC,gBAAlB,wCAAA,kBAAsB,WAAW,OAAM;IACrD,MAAM,OAAO,CAAA,qBAAA,gCAAA,mBAAA,SAAU,MAAM,CAAC,gBAAjB,uCAAA,iBAAqB,WAAW,OAAM;IACnD,OAAO,AAAC,GAAU,OAAR,OAAa,OAAL,SAAU;AAC9B;AAOO,MAAM,oBAAoB,CAAC;IAChC,kBAAkB;IAClB,MAAM,eAAe;QAAC;QAAc;QAAa;QAAa;KAAa;IAC3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACrC,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,MAAM,UAAU,IAAI,OAAO,MAAM,MAAM;IACvC,IAAI,KAAK,IAAI,GAAG,SAAS;QACvB,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/license.ts"], "sourcesContent": ["import { LicenseCategory } from \".\";\r\nimport { User } from \"./user\";\r\n\r\nexport interface LicenseType {\r\n  license_type_id: string;\r\n  name: string;\r\n  validity?: number;\r\n  code?: string;\r\n  description?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  categories?: LicenseCategory[];\r\n  iconColor?: string;\r\n  icon?: string;\r\n  iconBg?: string;\r\n}\r\n\r\nexport interface StatusAction {\r\n  status: ApplicationStatus;\r\n  label: string;\r\n  icon: string;\r\n  color: string;\r\n  hoverColor: string;\r\n  roles?: string[],\r\n  description: string;\r\n  confirmMessage?: string;\r\n}\r\n\r\n\r\nexport interface Applicant {\r\n  applicant_id: string;\r\n  name: string;\r\n  business_registration_number: string;\r\n  tpin: string;\r\n  website: string;\r\n  email: string;\r\n  phone: string;\r\n  fax?: string;\r\n  level_of_insurance_cover?: string;\r\n  address_id?: string;\r\n  contact_id?: string;\r\n  date_incorporation: string;\r\n  place_incorporation: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Application extends Record<string, unknown> {\r\n  application_id: string;\r\n  application_number: string;\r\n  applicant_id: string;\r\n  license_category_id: string;\r\n  status: string;\r\n  current_step: number;\r\n  progress_percentage: number;\r\n  submitted_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  assigned_to?: string;\r\n  assigned_at?: string;\r\n  application_data?: any;\r\n  applicant?: Applicant;\r\n  license_category?:LicenseCategory;\r\n  assignee?:User;\r\n}\r\n\r\nexport enum ApplicationStatus {\r\n  DRAFT = 'draft',\r\n  PENDING = 'pending',\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  EVALUATION = 'evaluation',\r\n  PENDING_PAYMENT = 'pending_payment',\r\n  APPROVED = 'approved',\r\n  REJECTED = 'rejected',\r\n  WITHDRAWN = 'withdrawn',\r\n  PASS_EVALUATION = 'pass_evaluation',\r\n  WAITING_FOR_APPROVAL = 'waiting_for_approval',\r\n}\r\n\r\nexport interface ApplicationFilters {\r\n  licenseTypeId?: string;\r\n  licenseCategoryId?: string;\r\n  status?: ApplicationStatus | '';\r\n  dateRange?: string;\r\n  search?: string;\r\n}\r\n\r\nexport interface License extends Record<string, unknown> {\r\n  license_id: string;\r\n  license_number: string;\r\n  description?: string;\r\n  application_id: string;\r\n  applicant_id: string;\r\n  license_type_id: string;\r\n  status: LicenseStatus;\r\n  issue_date: string;\r\n  expiry_date: string;\r\n  issued_by: string;\r\n  code?: string;\r\n  conditions?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  application: Application;\r\n  issuer?:User;\r\n}\r\n\r\nexport enum LicenseStatus {\r\n  ACTIVE = 'active',\r\n  EXPIRED = 'expired',\r\n  SUSPENDED = 'suspended',\r\n  REVOKED = 'revoked',\r\n  UNDER_REVIEW = 'under_review',\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAkEO,IAAA,AAAK,2CAAA;;;;;;;;;;;;WAAA;;AAyCL,IAAA,AAAK,uCAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/notificationService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { ApplicationStatus } from '../types/license';\r\nimport { AppNotification, NotificationSummary } from '@/types/notification';\r\n\r\n\r\nexport const notificationService = {\r\n  // Get user notifications\r\n  async getUserNotifications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    type?: string;\r\n    status?: 'read' | 'unread';\r\n  }): Promise<NotificationSummary> {\r\n    try {\r\n      const queryParams = new URLSearchParams();\r\n      \r\n      if (params?.page) queryParams.append('page', params.page.toString());\r\n      if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n      if (params?.type) queryParams.append('type', params.type);\r\n      if (params?.status) queryParams.append('status', params.status);\r\n\r\n      const endpoint = `/notifications/my-notifications?${queryParams.toString()}`;\r\n      const response = await apiClient.get(endpoint);\r\n      const data = processApiResponse(response).data;\r\n      \r\n      // Validate response structure\r\n      if (!data || typeof data !== 'object') {\r\n        console.warn('Invalid notification response format:', data);\r\n        return {\r\n          total_count: 0,\r\n          unread_count: 0,\r\n          notifications: []\r\n        };\r\n      }\r\n      \r\n      return {\r\n        total_count: data.length || 0,\r\n        unread_count: data.length || 0,\r\n        notifications: Array.isArray(data) ? data : []\r\n      };\r\n    } catch (error: any) {\r\n      // Convert params to string format for URLSearchParams\r\n      const stringParams: Record<string, string> = {};\r\n      if (params) {\r\n        if (params.page) stringParams.page = params.page.toString();\r\n        if (params.limit) stringParams.limit = params.limit.toString();\r\n        if (params.type) stringParams.type = params.type;\r\n        if (params.status) stringParams.status = params.status;\r\n      }\r\n\r\n      console.error('Error fetching user notifications:', {\r\n        error: error.message || 'Unknown error',\r\n        response: error.response?.data || null,\r\n        status: error.response?.status || null,\r\n        code: error.code || null,\r\n        params,\r\n        endpoint: `/notifications/my-notifications?${new URLSearchParams(stringParams).toString()}`\r\n      });\r\n      \r\n      // Return empty result on error instead of throwing\r\n      return {\r\n        total_count: 0,\r\n        unread_count: 0,\r\n        notifications: []\r\n      };\r\n    }\r\n  },\r\n\r\n  // Mark notification as read\r\n  async markAsRead(notificationId: string): Promise<void> {\r\n    const response = await apiClient.patch(`/notifications/${notificationId}/mark-read`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Mark all notifications as read (implemented client-side by marking each notification individually)\r\n  async markAllAsRead(): Promise<void> {\r\n    // This is handled by the useNotifications hook by calling markAsRead for each unread notification\r\n    // We don't need a separate backend endpoint for this\r\n    throw new Error('markAllAsRead should be handled by useNotifications hook');\r\n  },\r\n\r\n  // Create a status change notification (usually called from backend)\r\n  async createStatusChangeNotification(\r\n    applicationId: string,\r\n    oldStatus: ApplicationStatus,\r\n    newStatus: ApplicationStatus,\r\n    step?: number,\r\n    progressPercentage?: number\r\n  ): Promise<Notification> {\r\n    const response = await apiClient.post('/notifications/status-change', {\r\n      application_id: applicationId,\r\n      old_status: oldStatus,\r\n      new_status: newStatus,\r\n      step,\r\n      progress_percentage: progressPercentage\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete notification\r\n  async deleteNotification(notificationId: string): Promise<void> {\r\n    const response = await apiClient.delete(`/notifications/${notificationId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get notification count\r\n  async getNotificationCount(): Promise<{ total: number; unread: number }> {\r\n    const response = await apiClient.get('/notifications/count');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Manual notification trigger for testing (will be removed in production)\r\n  async triggerTestNotification(\r\n    applicationId: string,\r\n    applicationNumber: string,\r\n    licenseCategoryName: string,\r\n    type: 'status_change' | 'reminder' | 'document_required' | 'approval' | 'rejection',\r\n    status: ApplicationStatus\r\n  ): Promise<Notification> {\r\n    const response = await apiClient.post('/notifications/test', {\r\n      application_id: applicationId,\r\n      application_number: applicationNumber,\r\n      license_category_name: licenseCategoryName,\r\n      type,\r\n      status\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Send activity note notification (creates notification which auto-creates activity note)\r\n  async sendActivityNoteNotification(data: {\r\n    applicationId: string;\r\n    message: string;\r\n    noteType?: string;\r\n    category?: string;\r\n    priority?: 'low' | 'normal' | 'high';\r\n    attachmentsCount?: number;\r\n  }): Promise<any> {\r\n    const response = await apiClient.post('/notifications/activity-note', {\r\n      application_id: data.applicationId,\r\n      message: data.message,\r\n      note_type: data.noteType || 'evaluation_comment',\r\n      category: data.category || 'communication',\r\n      priority: data.priority || 'normal',\r\n      attachments_count: data.attachmentsCount || 0,\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n};\r\n\r\n// Status change message templates\r\nexport const getStatusChangeMessage = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  oldStatus: string,\r\n  newStatus: string,\r\n  step?: number,\r\n  progressPercentage?: number\r\n): { title: string; message: string; type: 'info' | 'success' | 'warning' | 'error' } => {\r\n  const progressText = progressPercentage ? ` (${progressPercentage}% complete)` : '';\r\n  const stepText = step ? ` - Step ${step}` : '';\r\n  \r\n  const messages = {\r\n    [ApplicationStatus.DRAFT]: {\r\n      title: 'Application Draft Saved',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been saved as a draft. You can continue editing and submit it when ready.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.PENDING]: {\r\n      title: 'Application Pending',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is pending initial review. We'll notify you once the review begins.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.SUBMITTED]: {\r\n      title: 'Application Submitted',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been successfully submitted and is now being processed.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.UNDER_REVIEW]: {\r\n      title: 'Application Under Review',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is now under review by our team${progressText}${stepText}. We'll notify you of any updates.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.EVALUATION]: {\r\n      title: 'Application Being Evaluated',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is currently being evaluated${progressText}${stepText}. This may take several business days.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.PENDING_PAYMENT]: {\r\n      title: 'Payment Required',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been approved for payment. Please complete the payment to proceed with license issuance.`,\r\n      type: 'warning' as const\r\n    },\r\n    [ApplicationStatus.APPROVED]: {\r\n      title: 'Application Approved! 🎉',\r\n      message: `Congratulations! Your ${licenseCategoryName} application (${applicationNumber}) has been approved. You can now download your license.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.REJECTED]: {\r\n      title: 'Application Status Update',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) requires attention. Please review the feedback and resubmit if needed.`,\r\n      type: 'warning' as const\r\n    },\r\n    [ApplicationStatus.WITHDRAWN]: {\r\n      title: 'Application Withdrawn',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been withdrawn as requested.`,\r\n      type: 'info' as const\r\n    }\r\n  };\r\n\r\n  const defaultMessage = {\r\n    title: 'Application Status Update',\r\n    message: `Your ${licenseCategoryName} application (${applicationNumber}) status has been updated to ${newStatus.replace('_', ' ')}.`,\r\n    type: 'info' as const\r\n  };\r\n\r\n  return messages[newStatus as keyof typeof messages] || defaultMessage;\r\n};\r\n\r\n// Helper function to manually trigger notification for testing\r\nexport const createTestNotification = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  status: ApplicationStatus\r\n): AppNotification => {\r\n  const now = new Date().toISOString();\r\n  const notificationId = `test-${Date.now()}`;\r\n  \r\n  return {\r\n    notification_id: notificationId,\r\n    user_id: 'current-user',\r\n    application_id: `app-${applicationNumber}`,\r\n    application_number: applicationNumber,\r\n    license_category_name: licenseCategoryName,\r\n    title: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).title,\r\n    message: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).message,\r\n    type: 'status_change',\r\n    status: 'unread',\r\n    priority: 'medium',\r\n    created_at: now,\r\n    metadata: {\r\n      old_status: ApplicationStatus.SUBMITTED,\r\n      new_status: status,\r\n      step: 2,\r\n      progress_percentage: 50\r\n    }\r\n  };\r\n};"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAIO,MAAM,sBAAsB;IACjC,yBAAyB;IACzB,MAAM,sBAAqB,MAK1B;QACC,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;YACxD,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAE9D,MAAM,WAAW,AAAC,mCAAyD,OAAvB,YAAY,QAAQ;YACxE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI;YAE9C,8BAA8B;YAC9B,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;gBACrC,QAAQ,IAAI,CAAC,yCAAyC;gBACtD,OAAO;oBACL,aAAa;oBACb,cAAc;oBACd,eAAe,EAAE;gBACnB;YACF;YAEA,OAAO;gBACL,aAAa,KAAK,MAAM,IAAI;gBAC5B,cAAc,KAAK,MAAM,IAAI;gBAC7B,eAAe,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;YAChD;QACF,EAAE,OAAO,OAAY;gBAYP,iBACF;YAZV,sDAAsD;YACtD,MAAM,eAAuC,CAAC;YAC9C,IAAI,QAAQ;gBACV,IAAI,OAAO,IAAI,EAAE,aAAa,IAAI,GAAG,OAAO,IAAI,CAAC,QAAQ;gBACzD,IAAI,OAAO,KAAK,EAAE,aAAa,KAAK,GAAG,OAAO,KAAK,CAAC,QAAQ;gBAC5D,IAAI,OAAO,IAAI,EAAE,aAAa,IAAI,GAAG,OAAO,IAAI;gBAChD,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,GAAG,OAAO,MAAM;YACxD;YAEA,QAAQ,KAAK,CAAC,sCAAsC;gBAClD,OAAO,MAAM,OAAO,IAAI;gBACxB,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,IAAI,KAAI;gBAClC,QAAQ,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,KAAI;gBAClC,MAAM,MAAM,IAAI,IAAI;gBACpB;gBACA,UAAU,AAAC,mCAA+E,OAA7C,IAAI,gBAAgB,cAAc,QAAQ;YACzF;YAEA,mDAAmD;YACnD,OAAO;gBACL,aAAa;gBACb,cAAc;gBACd,eAAe,EAAE;YACnB;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,YAAW,cAAsB;QACrC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,AAAC,kBAAgC,OAAf,gBAAe;QACxE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qGAAqG;IACrG,MAAM;QACJ,kGAAkG;QAClG,qDAAqD;QACrD,MAAM,IAAI,MAAM;IAClB;IAEA,oEAAoE;IACpE,MAAM,gCACJ,aAAqB,EACrB,SAA4B,EAC5B,SAA4B,EAC5B,IAAa,EACb,kBAA2B;QAE3B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC;YACpE,gBAAgB;YAChB,YAAY;YACZ,YAAY;YACZ;YACA,qBAAqB;QACvB;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,oBAAmB,cAAsB;QAC7C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,kBAAgC,OAAf;QAC1D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0EAA0E;IAC1E,MAAM,yBACJ,aAAqB,EACrB,iBAAyB,EACzB,mBAA2B,EAC3B,IAAmF,EACnF,MAAyB;QAEzB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;YAC3D,gBAAgB;YAChB,oBAAoB;YACpB,uBAAuB;YACvB;YACA;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0FAA0F;IAC1F,MAAM,8BAA6B,IAOlC;QACC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC;YACpE,gBAAgB,KAAK,aAAa;YAClC,SAAS,KAAK,OAAO;YACrB,WAAW,KAAK,QAAQ,IAAI;YAC5B,UAAU,KAAK,QAAQ,IAAI;YAC3B,UAAU,KAAK,QAAQ,IAAI;YAC3B,mBAAmB,KAAK,gBAAgB,IAAI;QAC9C;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA,WACA,WACA,MACA;IAEA,MAAM,eAAe,qBAAqB,AAAC,KAAuB,OAAnB,oBAAmB,iBAAe;IACjF,MAAM,WAAW,OAAO,AAAC,WAAe,OAAL,QAAS;IAE5C,MAAM,WAAW;QACf,CAAC,0HAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,EAAE;YACzB,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,OAAO,CAAC,EAAE;YAC3B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,EAAE;YAChC,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAqE,OAArD,mBAAkB,qCAAkD,OAAf,cAAwB,OAAT,UAAS;YAClI,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,EAAE;YAC9B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkE,OAAlD,mBAAkB,kCAA+C,OAAf,cAAwB,OAAT,UAAS;YAC/H,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,eAAe,CAAC,EAAE;YACnC,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,AAAC,yBAA4D,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACxF,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAO;QACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAiE,OAAjD,mBAAkB,iCAA2D,OAA5B,UAAU,OAAO,CAAC,KAAK,MAAK;QAClI,MAAM;IACR;IAEA,OAAO,QAAQ,CAAC,UAAmC,IAAI;AACzD;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA;IAEA,MAAM,MAAM,IAAI,OAAO,WAAW;IAClC,MAAM,iBAAiB,AAAC,QAAkB,OAAX,KAAK,GAAG;IAEvC,OAAO;QACL,iBAAiB;QACjB,SAAS;QACT,gBAAgB,AAAC,OAAwB,OAAlB;QACvB,oBAAoB;QACpB,uBAAuB;QACvB,OAAO,uBAAuB,mBAAmB,qBAAqB,0HAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,KAAK;QAChH,SAAS,uBAAuB,mBAAmB,qBAAqB,0HAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,OAAO;QACpH,MAAM;QACN,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,UAAU;YACR,YAAY,0HAAA,CAAA,oBAAiB,CAAC,SAAS;YACvC,YAAY;YACZ,MAAM;YACN,qBAAqB;QACvB;IACF;AACF", "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useNotifications.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { notificationService } from '@/services/notificationService';\r\nimport { AppNotification } from '@/types/notification';\r\n\r\nexport interface NotificationCounts {\r\n  total: number;\r\n  unread: number;\r\n}\r\n\r\nexport interface UseNotificationsReturn {\r\n  notifications: AppNotification[];\r\n  unreadCount: number;\r\n  totalCount: number;\r\n  loading: boolean;\r\n  error: string | null;\r\n  fetchNotifications: () => Promise<void>;\r\n  markAsRead: (id: string) => Promise<void>;\r\n  markAllAsRead: () => Promise<void>;\r\n  refreshNotifications: () => Promise<void>;\r\n  getNotificationCounts: () => Promise<void>;\r\n}\r\n\r\nexport const useNotifications = (): UseNotificationsReturn => {\r\n  const [notifications, setNotifications] = useState<AppNotification[]>([]);\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [totalCount, setTotalCount] = useState(0);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const { user, isAuthenticated } = useAuth();\r\n  const { showError } = useToast();\r\n\r\n  const fetchNotifications = useCallback(async () => {\r\n    if (!isAuthenticated || !user) {\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      console.log('🔍 Fetching notifications for user:', {\r\n        userId: user.user_id,\r\n        email: user.email\r\n      });\r\n\r\n      const data = await notificationService.getUserNotifications({\r\n        page: 1,\r\n        limit: 50 // Get more notifications for better UX\r\n      });\r\n\r\n      if (data && data.notifications) {\r\n        setNotifications(data.notifications);\r\n        setUnreadCount(data.unread_count);\r\n        setTotalCount(data.total_count);\r\n      } else {\r\n        setNotifications([]);\r\n        setUnreadCount(0);\r\n        setTotalCount(0);\r\n      }\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch notifications';\r\n      setError(errorMessage);\r\n      console.error('Error fetching notifications:', err);\r\n      // Don't show toast error for initial load failures\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [isAuthenticated, user]);\r\n\r\n  const markAsRead = useCallback(async (id: string) => {\r\n    if (!isAuthenticated) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await notificationService.markAsRead(id);\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notification =>\r\n          notification.notification_id === id\r\n            ? { ...notification, status: 'read', read_at: new Date().toISOString() }\r\n            : notification\r\n        )\r\n      );\r\n\r\n      // Update unread count\r\n      setUnreadCount(prev => Math.max(0, prev - 1));\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to mark notification as read';\r\n      setError(errorMessage);\r\n      showError('Failed to mark notification as read');\r\n      console.error('Error marking notification as read:', err);\r\n    }\r\n  }, [isAuthenticated, showError]);\r\n\r\n  const markAllAsRead = useCallback(async () => {\r\n    if (!isAuthenticated || notifications.length === 0) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Mark all unread notifications as read\r\n      const unreadNotifications = notifications.filter(n => n.status === 'unread');\r\n\r\n      for (const notification of unreadNotifications) {\r\n        await markAsRead(notification.notification_id);\r\n      }\r\n\r\n      setUnreadCount(0);\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to mark all notifications as read';\r\n      setError(errorMessage);\r\n      showError('Failed to mark all notifications as read');\r\n      console.error('Error marking all notifications as read:', err);\r\n    }\r\n  }, [isAuthenticated, notifications, markAsRead, showError]);\r\n\r\n  const getNotificationCounts = useCallback(async () => {\r\n    if (!isAuthenticated) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const counts = await notificationService.getNotificationCount();\r\n      setUnreadCount(counts.unread);\r\n      setTotalCount(counts.total);\r\n    } catch (err) {\r\n      console.error('Error getting notification counts:', err);\r\n    }\r\n  }, [isAuthenticated]);\r\n\r\n  const refreshNotifications = useCallback(async () => {\r\n    await fetchNotifications();\r\n  }, [fetchNotifications]);\r\n\r\n  // Initial fetch when component mounts and user is authenticated\r\n  useEffect(() => {\r\n    if (isAuthenticated && user) {\r\n      fetchNotifications();\r\n    }\r\n  }, [isAuthenticated, user, fetchNotifications]);\r\n\r\n  // Set up polling for new notifications every 30 seconds\r\n  useEffect(() => {\r\n    if (!isAuthenticated || !user) {\r\n      return;\r\n    }\r\n\r\n    const interval = setInterval(() => {\r\n      fetchNotifications();\r\n    }, 30000); // 30 seconds\r\n\r\n    return () => clearInterval(interval);\r\n  }, [isAuthenticated, user, fetchNotifications]);\r\n\r\n  return {\r\n    notifications,\r\n    unreadCount,\r\n    totalCount,\r\n    loading,\r\n    error,\r\n    fetchNotifications,\r\n    markAsRead,\r\n    markAllAsRead,\r\n    refreshNotifications,\r\n    getNotificationCounts,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;;AALA;;;;;AA0BO,MAAM,mBAAmB;;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAE7B,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACrC,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B;YACF;YAEA,WAAW;YACX,SAAS;YAET,IAAI;gBACF,QAAQ,GAAG,CAAC,uCAAuC;oBACjD,QAAQ,KAAK,OAAO;oBACpB,OAAO,KAAK,KAAK;gBACnB;gBAEA,MAAM,OAAO,MAAM,yIAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC;oBAC1D,MAAM;oBACN,OAAO,GAAG,uCAAuC;gBACnD;gBAEA,IAAI,QAAQ,KAAK,aAAa,EAAE;oBAC9B,iBAAiB,KAAK,aAAa;oBACnC,eAAe,KAAK,YAAY;oBAChC,cAAc,KAAK,WAAW;gBAChC,OAAO;oBACL,iBAAiB,EAAE;oBACnB,eAAe;oBACf,cAAc;gBAChB;YACF,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,mDAAmD;YACrD,SAAU;gBACR,WAAW;YACb;QACF;2DAAG;QAAC;QAAiB;KAAK;IAE1B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,OAAO;YACpC,IAAI,CAAC,iBAAiB;gBACpB;YACF;YAEA,IAAI;gBACF,MAAM,yIAAA,CAAA,sBAAmB,CAAC,UAAU,CAAC;gBAErC,qBAAqB;gBACrB;gEAAiB,CAAA,OACf,KAAK,GAAG;wEAAC,CAAA,eACP,aAAa,eAAe,KAAK,KAC7B;oCAAE,GAAG,YAAY;oCAAE,QAAQ;oCAAQ,SAAS,IAAI,OAAO,WAAW;gCAAG,IACrE;;;gBAIR,sBAAsB;gBACtB;gEAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;;YAC5C,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,UAAU;gBACV,QAAQ,KAAK,CAAC,uCAAuC;YACvD;QACF;mDAAG;QAAC;QAAiB;KAAU;IAE/B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAChC,IAAI,CAAC,mBAAmB,cAAc,MAAM,KAAK,GAAG;gBAClD;YACF;YAEA,IAAI;gBACF,wCAAwC;gBACxC,MAAM,sBAAsB,cAAc,MAAM;uFAAC,CAAA,IAAK,EAAE,MAAM,KAAK;;gBAEnE,KAAK,MAAM,gBAAgB,oBAAqB;oBAC9C,MAAM,WAAW,aAAa,eAAe;gBAC/C;gBAEA,eAAe;YACjB,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,UAAU;gBACV,QAAQ,KAAK,CAAC,4CAA4C;YAC5D;QACF;sDAAG;QAAC;QAAiB;QAAe;QAAY;KAAU;IAE1D,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE;YACxC,IAAI,CAAC,iBAAiB;gBACpB;YACF;YAEA,IAAI;gBACF,MAAM,SAAS,MAAM,yIAAA,CAAA,sBAAmB,CAAC,oBAAoB;gBAC7D,eAAe,OAAO,MAAM;gBAC5B,cAAc,OAAO,KAAK;YAC5B,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,sCAAsC;YACtD;QACF;8DAAG;QAAC;KAAgB;IAEpB,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACvC,MAAM;QACR;6DAAG;QAAC;KAAmB;IAEvB,gEAAgE;IAChE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,mBAAmB,MAAM;gBAC3B;YACF;QACF;qCAAG;QAAC;QAAiB;QAAM;KAAmB;IAE9C,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B;YACF;YAEA,MAAM,WAAW;uDAAY;oBAC3B;gBACF;sDAAG,QAAQ,aAAa;YAExB;8CAAO,IAAM,cAAc;;QAC7B;qCAAG;QAAC;QAAiB;QAAM;KAAmB;IAE9C,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAlJa;;QAMuB,kIAAA,CAAA,UAAO;QACnB,mIAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 786, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Modal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ReactNode, useEffect } from 'react';\r\n\r\ninterface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string;\r\n  children: ReactNode;\r\n  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';\r\n  showCloseButton?: boolean;\r\n  closeOnOverlayClick?: boolean;\r\n}\r\n\r\nexport default function Modal({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  size = 'md',\r\n  showCloseButton = true,\r\n  closeOnOverlayClick = true,\r\n}: ModalProps) {\r\n  // Handle escape key press\r\n  useEffect(() => {\r\n    const handleEscape = (event: KeyboardEvent) => {\r\n      if (event.key === 'Escape' && isOpen) {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      // Prevent body scroll when modal is open\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [isOpen, onClose]);\r\n\r\n  if (!isOpen) return null;\r\n\r\n  const getSizeClasses = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'sm:max-w-sm';\r\n      case 'md':\r\n        return 'sm:max-w-md';\r\n      case 'lg':\r\n        return 'sm:max-w-lg';\r\n      case 'xl':\r\n        return 'sm:max-w-xl';\r\n      case '2xl':\r\n        return 'sm:max-w-2xl';\r\n      default:\r\n        return 'sm:max-w-md';\r\n    }\r\n  };\r\n\r\n  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {\r\n    if (closeOnOverlayClick && e.target === e.currentTarget) {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4\">\r\n      <div\r\n        className=\"fixed inset-0\"\r\n        onClick={handleOverlayClick}\r\n        aria-hidden=\"true\"\r\n      />\r\n      \r\n      <div className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full ${getSizeClasses()} mx-4 transform transition-all max-h-[90vh] flex flex-col`}>\r\n        {/* Header - Fixed */}\r\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n            {title}\r\n          </h3>\r\n          {showCloseButton && (\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 rounded-md p-1\"\r\n              aria-label=\"Close modal\"\r\n            >\r\n              <i className=\"ri-close-line text-xl\"></i>\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {/* Scrollable Content */}\r\n        <div className=\"flex-1 overflow-hidden\">\r\n          <div className=\"overflow-y-auto h-full\">\r\n            <div className=\"p-6\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAce,SAAS,MAAM,KAQjB;QARiB,EAC5B,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACX,kBAAkB,IAAI,EACtB,sBAAsB,IAAI,EACf,GARiB;;IAS5B,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,MAAM;gDAAe,CAAC;oBACpB,IAAI,MAAM,GAAG,KAAK,YAAY,QAAQ;wBACpC;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,WAAW;gBACrC,yCAAyC;gBACzC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,uBAAuB,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YACvD;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAU;gBACV,SAAS;gBACT,eAAY;;;;;;0BAGd,6LAAC;gBAAI,WAAW,AAAC,kEAAkF,OAAjB,kBAAiB;;kCAEjG,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX;;;;;;4BAEF,iCACC,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;kCAMnB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GA3FwB;KAAA", "debugId": null}}, {"offset": {"line": 943, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/notifications/NotificationItem.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { Notification } from '@/hooks/useNotifications';\r\n\r\ninterface NotificationItemProps {\r\n  notification: Notification;\r\n  onMarkAsRead: (id: string) => void;\r\n  onNotificationClick?: (notification: Notification) => void;\r\n}\r\n\r\nconst NotificationItem: React.FC<NotificationItemProps> = ({\r\n  notification,\r\n  onMarkAsRead,\r\n  onNotificationClick,\r\n}) => {\r\n  const formatTimeAgo = (dateString: string) => {\r\n    const date = new Date(dateString);\r\n    const now = new Date();\r\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n    if (diffInSeconds < 60) {\r\n      return 'Just now';\r\n    } else if (diffInSeconds < 3600) {\r\n      const minutes = Math.floor(diffInSeconds / 60);\r\n      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\r\n    } else if (diffInSeconds < 86400) {\r\n      const hours = Math.floor(diffInSeconds / 3600);\r\n      return `${hours} hour${hours > 1 ? 's' : ''} ago`;\r\n    } else if (diffInSeconds < 604800) {\r\n      const days = Math.floor(diffInSeconds / 86400);\r\n      return `${days} day${days > 1 ? 's' : ''} ago`;\r\n    } else {\r\n      return date.toLocaleDateString();\r\n    }\r\n  };\r\n\r\n  const getNotificationIcon = (type: string) => {\r\n    switch (type.toLowerCase()) {\r\n      case 'email':\r\n        return 'ri-mail-line';\r\n      case 'sms':\r\n        return 'ri-message-3-line';\r\n      case 'in_app':\r\n        return 'ri-notification-3-line';\r\n      case 'application_status':\r\n        return 'ri-file-list-3-line';\r\n      case 'evaluation_assigned':\r\n        return 'ri-user-settings-line';\r\n      case 'payment_due':\r\n        return 'ri-money-dollar-circle-line';\r\n      case 'license_expiry':\r\n        return 'ri-calendar-event-line';\r\n      case 'system_alert':\r\n        return 'ri-alert-line';\r\n      default:\r\n        return 'ri-notification-3-line';\r\n    }\r\n  };\r\n\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority.toLowerCase()) {\r\n      case 'urgent':\r\n        return 'text-red-600 dark:text-red-400';\r\n      case 'high':\r\n        return 'text-orange-600 dark:text-orange-400';\r\n      case 'medium':\r\n        return 'text-yellow-600 dark:text-yellow-400';\r\n      case 'low':\r\n        return 'text-green-600 dark:text-green-400';\r\n      default:\r\n        return 'text-gray-600 dark:text-gray-400';\r\n    }\r\n  };\r\n\r\n  const handleItemClick = () => {\r\n    if (!notification.is_read) {\r\n      onMarkAsRead(notification.notification_id);\r\n    }\r\n    if (onNotificationClick) {\r\n      onNotificationClick(notification);\r\n    }\r\n  };\r\n\r\n  const handleMarkAsReadClick = (e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    onMarkAsRead(notification.notification_id);\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors duration-200 ${\r\n        !notification.is_read ? 'bg-blue-50 dark:bg-blue-900/20' : ''\r\n      }`}\r\n      onClick={handleItemClick}\r\n    >\r\n      <div className=\"flex items-start space-x-3\">\r\n        {/* Notification Icon */}\r\n        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${\r\n          !notification.is_read \r\n            ? 'bg-blue-100 dark:bg-blue-900/50' \r\n            : 'bg-gray-100 dark:bg-gray-700'\r\n        }`}>\r\n          <i className={`${getNotificationIcon(notification.type)} text-sm ${\r\n            !notification.is_read \r\n              ? 'text-blue-600 dark:text-blue-400' \r\n              : 'text-gray-600 dark:text-gray-400'\r\n          }`}></i>\r\n        </div>\r\n\r\n        {/* Notification Content */}\r\n        <div className=\"flex-1 min-w-0\">\r\n          <div className=\"flex items-start justify-between\">\r\n            <div className=\"flex-1\">\r\n              <h4 className={`text-sm font-medium ${\r\n                !notification.is_read \r\n                  ? 'text-gray-900 dark:text-gray-100' \r\n                  : 'text-gray-700 dark:text-gray-300'\r\n              }`}>\r\n                {notification.subject}\r\n              </h4>\r\n              <p className={`mt-1 text-sm ${\r\n                !notification.is_read \r\n                  ? 'text-gray-700 dark:text-gray-300' \r\n                  : 'text-gray-500 dark:text-gray-400'\r\n              }`}>\r\n                {notification.message}\r\n              </p>\r\n            </div>\r\n\r\n            {/* Priority Indicator */}\r\n            {notification.priority && notification.priority !== 'medium' && (\r\n              <div className={`flex-shrink-0 ml-2 ${getPriorityColor(notification.priority)}`}>\r\n                <i className=\"ri-flag-line text-xs\"></i>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Metadata */}\r\n          <div className=\"mt-2 flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400\">\r\n              <span>{formatTimeAgo(notification.created_at)}</span>\r\n              {notification.entity_type && (\r\n                <span className=\"capitalize\">{notification.entity_type}</span>\r\n              )}\r\n            </div>\r\n\r\n            {/* Actions */}\r\n            <div className=\"flex items-center space-x-2\">\r\n              {!notification.is_read && (\r\n                <button\r\n                  onClick={handleMarkAsReadClick}\r\n                  className=\"inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/50 hover:text-blue-800 dark:hover:text-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-colors duration-200\"\r\n                  title=\"Mark as read\"\r\n                >\r\n                  <i className=\"ri-check-line mr-1\"></i>\r\n                  Mark as Read\r\n                </button>\r\n              )}\r\n              {notification.is_read && (\r\n                <span className=\"text-xs text-green-600 dark:text-green-400 flex items-center\">\r\n                  <i className=\"ri-check-line mr-1\"></i>\r\n                  Read\r\n                </span>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Unread Indicator */}\r\n        {!notification.is_read && (\r\n          <div className=\"flex-shrink-0 w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full\"></div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotificationItem;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAWA,MAAM,mBAAoD;QAAC,EACzD,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACpB;IACC,MAAM,gBAAgB,CAAC;QACrB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;QAEpE,IAAI,gBAAgB,IAAI;YACtB,OAAO;QACT,OAAO,IAAI,gBAAgB,MAAM;YAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;YAC3C,OAAO,AAAC,GAAmB,OAAjB,SAAQ,WAAgC,OAAvB,UAAU,IAAI,MAAM,IAAG;QACpD,OAAO,IAAI,gBAAgB,OAAO;YAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;YACzC,OAAO,AAAC,GAAe,OAAb,OAAM,SAA4B,OAArB,QAAQ,IAAI,MAAM,IAAG;QAC9C,OAAO,IAAI,gBAAgB,QAAQ;YACjC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;YACxC,OAAO,AAAC,GAAa,OAAX,MAAK,QAA0B,OAApB,OAAO,IAAI,MAAM,IAAG;QAC3C,OAAO;YACL,OAAO,KAAK,kBAAkB;QAChC;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ,KAAK,WAAW;YACtB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,aAAa,OAAO,EAAE;YACzB,aAAa,aAAa,eAAe;QAC3C;QACA,IAAI,qBAAqB;YACvB,oBAAoB;QACtB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,EAAE,eAAe;QACjB,aAAa,aAAa,eAAe;IAC3C;IAEA,qBACE,6LAAC;QACC,WAAW,AAAC,2IAEX,OADC,CAAC,aAAa,OAAO,GAAG,mCAAmC;QAE7D,SAAS;kBAET,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAW,AAAC,uEAIhB,OAHC,CAAC,aAAa,OAAO,GACjB,oCACA;8BAEJ,cAAA,6LAAC;wBAAE,WAAW,AAAC,GACb,OADe,oBAAoB,aAAa,IAAI,GAAE,aAIvD,OAHC,CAAC,aAAa,OAAO,GACjB,qCACA;;;;;;;;;;;8BAKR,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAW,AAAC,uBAIf,OAHC,CAAC,aAAa,OAAO,GACjB,qCACA;sDAEH,aAAa,OAAO;;;;;;sDAEvB,6LAAC;4CAAE,WAAW,AAAC,gBAId,OAHC,CAAC,aAAa,OAAO,GACjB,qCACA;sDAEH,aAAa,OAAO;;;;;;;;;;;;gCAKxB,aAAa,QAAQ,IAAI,aAAa,QAAQ,KAAK,0BAClD,6LAAC;oCAAI,WAAW,AAAC,sBAA6D,OAAxC,iBAAiB,aAAa,QAAQ;8CAC1E,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAM,cAAc,aAAa,UAAU;;;;;;wCAC3C,aAAa,WAAW,kBACvB,6LAAC;4CAAK,WAAU;sDAAc,aAAa,WAAW;;;;;;;;;;;;8CAK1D,6LAAC;oCAAI,WAAU;;wCACZ,CAAC,aAAa,OAAO,kBACpB,6LAAC;4CACC,SAAS;4CACT,WAAU;4CACV,OAAM;;8DAEN,6LAAC;oDAAE,WAAU;;;;;;gDAAyB;;;;;;;wCAIzC,aAAa,OAAO,kBACnB,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAE,WAAU;;;;;;gDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;gBAS/C,CAAC,aAAa,OAAO,kBACpB,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzB;KArKM;uCAuKS", "debugId": null}}, {"offset": {"line": 1205, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/notifications/NotificationModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Modal from '@/components/common/Modal';\r\nimport NotificationItem from './NotificationItem';\r\nimport Loader from '@/components/Loader';\r\nimport { useNotifications } from '@/hooks/useNotifications';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { AppNotification } from '@/types/notification';\r\n\r\ninterface NotificationModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nconst NotificationModal: React.FC<NotificationModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n}) => {\r\n  const {\r\n    notifications,\r\n    unreadCount,\r\n    loading,\r\n    error,\r\n    markAsRead,\r\n    markAllAsRead,\r\n    refreshNotifications,\r\n  } = useNotifications();\r\n  \r\n  const { showSuccess, showError } = useToast();\r\n  const [filter, setFilter] = useState<'all' | 'unread'>('unread');\r\n  const [localLoading, setLocalLoading] = useState(false);\r\n\r\n  // Filter notifications based on selected filter\r\n  const filteredNotifications = notifications.filter(notification => {\r\n    if (filter === 'unread') {\r\n      return !notification.read_at;\r\n    }\r\n    return true;\r\n  });\r\n\r\n\r\n  // Refresh notifications when modal opens\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      refreshNotifications();\r\n    }\r\n  }, [isOpen, refreshNotifications]);\r\n\r\n  const handleMarkAsRead = async (id: string) => {\r\n    try {\r\n      await markAsRead(id);\r\n      showSuccess('Notification marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking notification as read:', error);\r\n      showError('Failed to mark notification as read');\r\n    }\r\n  };\r\n\r\n  const handleMarkAllAsRead = async () => {\r\n    if (unreadCount === 0) {\r\n      showError('No unread notifications to mark');\r\n      return;\r\n    }\r\n\r\n    setLocalLoading(true);\r\n    try {\r\n      await markAllAsRead();\r\n      showSuccess('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      showError('Failed to mark all notifications as read');\r\n    } finally {\r\n      setLocalLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleNotificationClick = (notification: AppNotification) => {\r\n    // Handle notification click - could navigate to related page\r\n    if (notification.entity_type === 'application' && notification.entity_id) {\r\n      // Navigate to application details\r\n      window.location.href = `/customer/my-licenses?application_id=${notification.entity_id}`;\r\n    }\r\n  };\r\n\r\n  const handleRefresh = async () => {\r\n    setLocalLoading(true);\r\n    try {\r\n      await refreshNotifications();\r\n    } catch (error) {\r\n      console.error('Error refreshing notifications:', error);\r\n      showError('Failed to refresh notifications');\r\n    } finally {\r\n      setLocalLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      title=\"Notifications\"\r\n      size=\"lg\"\r\n    >\r\n      <div className=\"flex flex-col h-96\">\r\n        {/* Header with filters and actions */}\r\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center space-x-4\">\r\n            {/* Filter buttons */}\r\n            <div className=\"flex space-x-2\">\r\n              <button\r\n                onClick={() => setFilter('all')}\r\n                className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${\r\n                  filter === 'all'\r\n                    ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'\r\n                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'\r\n                }`}\r\n              >\r\n                All ({notifications.length})\r\n              </button>\r\n              <button\r\n                onClick={() => setFilter('unread')}\r\n                className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${\r\n                  filter === 'unread'\r\n                    ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'\r\n                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'\r\n                }`}\r\n              >\r\n                Unread ({unreadCount})\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Action buttons */}\r\n          <div className=\"flex items-center space-x-2\">\r\n            <button\r\n              onClick={handleRefresh}\r\n              disabled={loading || localLoading}\r\n              className=\"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 disabled:opacity-50\"\r\n              title=\"Refresh notifications\"\r\n            >\r\n              <i className={`ri-refresh-line ${(loading || localLoading) ? 'animate-spin' : ''}`}></i>\r\n            </button>\r\n            \r\n            {unreadCount > 0 && (\r\n              <button\r\n                onClick={handleMarkAllAsRead}\r\n                disabled={loading || localLoading}\r\n                className=\"px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 disabled:opacity-50\"\r\n              >\r\n                Mark all as read\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Notifications list */}\r\n        <div className=\"flex-1 overflow-y-auto\">\r\n          {loading && notifications.length === 0 ? (\r\n            <div className=\"flex items-center justify-center h-full\">\r\n              <Loader message=\"Loading notifications...\" />\r\n            </div>\r\n          ) : error ? (\r\n            <div className=\"flex flex-col items-center justify-center h-full text-center p-4\">\r\n              <i className=\"ri-error-warning-line text-4xl text-red-500 mb-2\"></i>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-1\">\r\n                Error Loading Notifications\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\r\n                {error}\r\n              </p>\r\n              <button\r\n                onClick={handleRefresh}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200\"\r\n              >\r\n                Try Again\r\n              </button>\r\n            </div>\r\n          ) : filteredNotifications.length === 0 ? (\r\n            <div className=\"flex flex-col items-center justify-center h-full text-center p-4\">\r\n              <i className=\"ri-notification-off-line text-4xl text-gray-400 mb-2\"></i>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-1\">\r\n                {filter === 'unread' ? 'No Unread Notifications' : 'No Notifications'}\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                {filter === 'unread' \r\n                  ? 'All caught up! You have no unread notifications.'\r\n                  : 'You have no notifications at this time.'\r\n                }\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"divide-y divide-gray-200 dark:divide-gray-700\">\r\n              {filteredNotifications.map((notification) => (\r\n                <NotificationItem\r\n                  key={notification.notification_id}\r\n                  notification={notification}\r\n                  onMarkAsRead={handleMarkAsRead}\r\n                  onNotificationClick={handleNotificationClick}\r\n                />\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        {filteredNotifications.length > 0 && (\r\n          <div className=\"p-4 border-t border-gray-200 dark:border-gray-700 text-center\">\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Showing {filteredNotifications.length} of {notifications.length} notifications\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default NotificationModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAeA,MAAM,oBAAsD;QAAC,EAC3D,MAAM,EACN,OAAO,EACR;;IACC,MAAM,EACJ,aAAa,EACb,WAAW,EACX,OAAO,EACP,KAAK,EACL,UAAU,EACV,aAAa,EACb,oBAAoB,EACrB,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gDAAgD;IAChD,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAA;QACjD,IAAI,WAAW,UAAU;YACvB,OAAO,CAAC,aAAa,OAAO;QAC9B;QACA,OAAO;IACT;IAGA,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,QAAQ;gBACV;YACF;QACF;sCAAG;QAAC;QAAQ;KAAqB;IAEjC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW;YACjB,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,UAAU;QACZ;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,gBAAgB,GAAG;YACrB,UAAU;YACV;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM;YACN,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,6DAA6D;QAC7D,IAAI,aAAa,WAAW,KAAK,iBAAiB,aAAa,SAAS,EAAE;YACxE,kCAAkC;YAClC,OAAO,QAAQ,CAAC,IAAI,GAAG,AAAC,wCAA8D,OAAvB,aAAa,SAAS;QACvF;IACF;IAEA,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC,wIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,OAAM;QACN,MAAK;kBAEL,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAEb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,AAAC,+DAIX,OAHC,WAAW,QACP,qEACA;;4CAEP;4CACO,cAAc,MAAM;4CAAC;;;;;;;kDAE7B,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,AAAC,+DAIX,OAHC,WAAW,WACP,qEACA;;4CAEP;4CACU;4CAAY;;;;;;;;;;;;;;;;;;sCAM3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,UAAU,WAAW;oCACrB,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC;wCAAE,WAAW,AAAC,mBAAkE,OAAhD,AAAC,WAAW,eAAgB,iBAAiB;;;;;;;;;;;gCAG/E,cAAc,mBACb,6LAAC;oCACC,SAAS;oCACT,UAAU,WAAW;oCACrB,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAQP,6LAAC;oBAAI,WAAU;8BACZ,WAAW,cAAc,MAAM,KAAK,kBACnC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+HAAA,CAAA,UAAM;4BAAC,SAAQ;;;;;;;;;;mEAEhB,sBACF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAG1E,6LAAC;gCAAE,WAAU;0CACV;;;;;;0CAEH,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;mEAID,sBAAsB,MAAM,KAAK,kBACnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;gCAAG,WAAU;0CACX,WAAW,WAAW,4BAA4B;;;;;;0CAErD,6LAAC;gCAAE,WAAU;0CACV,WAAW,WACR,qDACA;;;;;;;;;;;iFAKR,6LAAC;wBAAI,WAAU;kCACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,6LAAC,0JAAA,CAAA,UAAgB;gCAEf,cAAc;gCACd,cAAc;gCACd,qBAAqB;+BAHhB,aAAa,eAAe;;;;;;;;;;;;;;;gBAW1C,sBAAsB,MAAM,GAAG,mBAC9B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAA2C;4BAC7C,sBAAsB,MAAM;4BAAC;4BAAK,cAAc,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;;;;;AAO9E;GAzMM;;QAYA,mIAAA,CAAA,mBAAgB;QAEe,mIAAA,CAAA,WAAQ;;;KAdvC;uCA2MS", "debugId": null}}, {"offset": {"line": 1546, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/documentService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\n\r\nexport interface DocumentData {\r\n  document_id?: string;\r\n  document_type: string;\r\n  file_name: string;\r\n  entity_type: string;\r\n  entity_id: string;\r\n  file_path: string;\r\n  file_size?: number;\r\n  mime_type: string;\r\n  is_required: boolean;\r\n  created_at?: string;\r\n  updated_at?: string;\r\n  created_by?: string;\r\n  updated_by?: string;\r\n}\r\n\r\nexport interface CreateDocumentData {\r\n  document_type: string;\r\n  file_name: string;\r\n  entity_type: string;\r\n  entity_id: string;\r\n  file_path: string;\r\n  file_size?: number;\r\n  mime_type: string;\r\n  is_required?: boolean;\r\n}\r\n\r\nexport interface LicenseCategoryDocumentData {\r\n  license_category_document_id: string;\r\n  license_category_id: string;\r\n  name: string;\r\n  is_required: boolean;\r\n  created_at?: string;\r\n  updated_at?: string;\r\n}\r\n\r\nexport interface UploadDocumentResponse {\r\n  document: DocumentData;\r\n  message: string;\r\n}\r\n\r\n// Cache for preventing duplicate requests\r\nconst requestCache = new Map<string, Promise<any>>();\r\n\r\nexport const documentService = {\r\n  // Get all documents with pagination and filtering\r\n  async getDocuments(params?: { page?: number; limit?: number; search?: string; sortBy?: string }): Promise<any> {\r\n    try {\r\n      const queryParams = new URLSearchParams();\r\n      if (params?.page) queryParams.append('page', params.page.toString());\r\n      if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n      if (params?.search) queryParams.append('search', params.search);\r\n      if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n\r\n      const cacheKey = `/documents?${queryParams.toString()}`;\r\n\r\n      // Check if we already have a pending request for this exact query\r\n      if (requestCache.has(cacheKey)) {\r\n        console.log('Returning cached request for:', cacheKey);\r\n        return await requestCache.get(cacheKey);\r\n      }\r\n\r\n      // Create new request and cache it\r\n      const requestPromise = apiClient.get(cacheKey).then(response => {\r\n        // Remove from cache after completion\r\n        requestCache.delete(cacheKey);\r\n        return processApiResponse(response);\r\n      }).catch(error => {\r\n        // Remove from cache on error too\r\n        requestCache.delete(cacheKey);\r\n        throw error;\r\n      });\r\n\r\n      requestCache.set(cacheKey, requestPromise);\r\n      return await requestPromise;\r\n    } catch (error) {\r\n      console.error('DocumentService.getDocuments error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get documents by entity (polymorphic relationship)\r\n  async getDocumentsByEntity(entityType: string, entityId: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/documents/by-entity/${entityType}/${entityId}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('DocumentService.getDocumentsByEntity error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get documents by application\r\n  async getDocumentsByApplication(applicationId: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/documents/by-application/${applicationId}`);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get required documents for license category\r\n  async getRequiredDocumentsForLicenseCategory(licenseCategoryId: string): Promise<LicenseCategoryDocumentData[]> {\r\n    try {\r\n      const response = await apiClient.get(`/license-category-documents/category/${licenseCategoryId}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('DocumentService.getRequiredDocumentsForLicenseCategory error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Upload document (using the dedicated upload endpoint)\r\n  async uploadDocument(file: File, documentData: Omit<CreateDocumentData, 'file_name' | 'file_path' | 'file_size' | 'mime_type'>): Promise<UploadDocumentResponse> {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n      formData.append('document_type', documentData.document_type);\r\n      formData.append('entity_type', documentData.entity_type);\r\n      formData.append('entity_id', documentData.entity_id);\r\n      formData.append('is_required', (documentData.is_required || false).toString());\r\n      formData.append('file_name', file.name);\r\n\r\n      const response = await apiClient.post('/documents/upload', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n\r\n      const result = processApiResponse(response);\r\n      return {\r\n        document: result.data,\r\n        message: result.message || 'Document uploaded successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('DocumentService.uploadDocument error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Create document record (without file upload)\r\n  async createDocument(documentData: CreateDocumentData): Promise<DocumentData> {\r\n    try {\r\n      const response = await apiClient.post('/documents', documentData);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('DocumentService.createDocument error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update document\r\n  async updateDocument(documentId: string, updateData: Partial<CreateDocumentData>): Promise<DocumentData> {\r\n    try {\r\n      const response = await apiClient.put(`/documents/${documentId}`, updateData);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('DocumentService.updateDocument error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete document\r\n  async deleteDocument(documentId: string): Promise<void> {\r\n    try {\r\n      await apiClient.delete(`/documents/${documentId}`);\r\n    } catch (error) {\r\n      console.error('DocumentService.deleteDocument error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get document by ID\r\n  async getDocument(documentId: string): Promise<DocumentData> {\r\n    try {\r\n      const response = await apiClient.get(`/documents/${documentId}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('DocumentService.getDocument error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Download document\r\n  async downloadDocument(documentId: string): Promise<Blob> {\r\n    try {\r\n      const response = await apiClient.get(`/documents/${documentId}/download`, {\r\n        responseType: 'blob',\r\n      });\r\n      return response.data; // Return blob directly, not processed\r\n    } catch (error) {\r\n      console.error('DocumentService.downloadDocument error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Preview document\r\n  async previewDocument(documentId: string): Promise<Blob> {\r\n    try {\r\n      const response = await apiClient.get(`/documents/${documentId}/preview`, {\r\n        responseType: 'blob',\r\n      });\r\n      return response.data; // Return blob directly for preview\r\n    } catch (error) {\r\n      console.error('DocumentService.previewDocument error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Check if document type is previewable\r\n  isPreviewable(mimeType: string = \"\"): boolean {\r\n    if (!mimeType) return false;\r\n    const previewableMimeTypes = [\r\n      'application/pdf',\r\n      'image/jpeg',\r\n      'image/jpg',\r\n      'image/png',\r\n      'image/gif',\r\n      'image/webp',\r\n      'text/plain',\r\n      'text/html',\r\n      'text/css',\r\n      'text/javascript',\r\n      'application/json',\r\n    ];\r\n\r\n    return previewableMimeTypes.includes(mimeType.toLowerCase());\r\n  },\r\n\r\n  // Check if all required documents are uploaded for an application\r\n  async checkRequiredDocuments(applicationId: string, licenseCategoryId: string): Promise<{\r\n    allUploaded: boolean;\r\n    missing: LicenseCategoryDocumentData[];\r\n    uploaded: DocumentData[];\r\n  }> {\r\n    try {\r\n      // Get required documents for license category\r\n      const requiredDocs = await this.getRequiredDocumentsForLicenseCategory(licenseCategoryId);\r\n      \r\n      // Get uploaded documents for application\r\n      const data = await this.getDocumentsByApplication(applicationId);\r\n      const uploadedDocs: DocumentData[] = data.data\r\n      \r\n      // Check which required documents are missing\r\n      const uploadedTypes = uploadedDocs.map(doc => doc.document_type);\r\n      const missing = requiredDocs.filter(reqDoc => \r\n        reqDoc.is_required && !uploadedTypes.includes(reqDoc.name.toLowerCase().replace(/\\s+/g, '_'))\r\n      );\r\n      \r\n      return {\r\n        allUploaded: missing.length === 0,\r\n        missing,\r\n        uploaded: uploadedDocs\r\n      };\r\n    } catch (error) {\r\n      console.error('DocumentService.checkRequiredDocuments error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get document types enum\r\n  getDocumentTypes(): string[] {\r\n    return [\r\n      'certificate_incorporation',\r\n      'memorandum_association',\r\n      'shareholding_structure',\r\n      'business_plan',\r\n      'financial_statements',\r\n      'technical_proposal',\r\n      'coverage_plan',\r\n      'network_diagram',\r\n      'equipment_specifications',\r\n      'insurance_certificate',\r\n      'tax_clearance',\r\n      'audited_accounts',\r\n      'bank_statement',\r\n      'cv_document',\r\n      'proof_of_payment',\r\n      'other'\r\n    ];\r\n  },\r\n\r\n  // Format document type for display\r\n  formatDocumentType(type: string): string {\r\n    return type\r\n      .split('_')\r\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\r\n      .join(' ');\r\n  },\r\n\r\n  // Map document name to DocumentType enum value\r\n  mapDocumentNameToType(documentName: string): string {\r\n    const nameToTypeMap: Record<string, string> = {\r\n      'Certificate of Incorporation': 'certificate_incorporation',\r\n      'Memorandum of Association': 'memorandum_association',\r\n      'Shareholding Structure': 'shareholding_structure',\r\n      'Business Plan': 'business_plan',\r\n      'Financial Statements': 'financial_statements',\r\n      'Technical Proposal': 'technical_proposal',\r\n      'Coverage Plan': 'coverage_plan',\r\n      'Network Diagram': 'network_diagram',\r\n      'Equipment Specifications': 'equipment_specifications',\r\n      'Insurance Certificate': 'insurance_certificate',\r\n      'Tax Clearance Certificate': 'tax_clearance',\r\n      'Tax Clearance': 'tax_clearance',\r\n      'Audited Accounts': 'audited_accounts',\r\n      'Bank Statement': 'bank_statement',\r\n      'CV Document': 'cv_document',\r\n      'Other': 'other'\r\n    };\r\n\r\n    // Try exact match first\r\n    if (nameToTypeMap[documentName]) {\r\n      return nameToTypeMap[documentName];\r\n    }\r\n\r\n    // Try case-insensitive match\r\n    const lowerName = documentName.toLowerCase();\r\n    for (const [name, type] of Object.entries(nameToTypeMap)) {\r\n      if (name.toLowerCase() === lowerName) {\r\n        return type;\r\n      }\r\n    }\r\n\r\n    // Fallback: convert name to snake_case\r\n    return documentName.toLowerCase().replace(/\\s+/g, '_');\r\n  },\r\n\r\n  // Validate file type and size\r\n  validateFile(file: File, maxSizeMB: number = 10, allowedTypes: string[] = []): {\r\n    isValid: boolean;\r\n    error?: string;\r\n  } {\r\n    // Check file size (convert MB to bytes)\r\n    const maxSizeBytes = maxSizeMB * 1024 * 1024;\r\n    if (file.size > maxSizeBytes) {\r\n      return {\r\n        isValid: false,\r\n        error: `File size must be less than ${maxSizeMB}MB`\r\n      };\r\n    }\r\n\r\n    // Check file type if specified\r\n    if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {\r\n      return {\r\n        isValid: false,\r\n        error: `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`\r\n      };\r\n    }\r\n\r\n    return { isValid: true };\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA2CA,0CAA0C;AAC1C,MAAM,eAAe,IAAI;AAElB,MAAM,kBAAkB;IAC7B,kDAAkD;IAClD,MAAM,cAAa,MAA4E;QAC7F,IAAI;YACF,MAAM,cAAc,IAAI;YACxB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAC9D,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAE9D,MAAM,WAAW,AAAC,cAAoC,OAAvB,YAAY,QAAQ;YAEnD,kEAAkE;YAClE,IAAI,aAAa,GAAG,CAAC,WAAW;gBAC9B,QAAQ,GAAG,CAAC,iCAAiC;gBAC7C,OAAO,MAAM,aAAa,GAAG,CAAC;YAChC;YAEA,kCAAkC;YAClC,MAAM,iBAAiB,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC,CAAA;gBAClD,qCAAqC;gBACrC,aAAa,MAAM,CAAC;gBACpB,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAC5B,GAAG,KAAK,CAAC,CAAA;gBACP,iCAAiC;gBACjC,aAAa,MAAM,CAAC;gBACpB,MAAM;YACR;YAEA,aAAa,GAAG,CAAC,UAAU;YAC3B,OAAO,MAAM;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;IAEA,qDAAqD;IACrD,MAAM,sBAAqB,UAAkB,EAAE,QAAgB;QAC7D,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,wBAAqC,OAAd,YAAW,KAAY,OAAT;YAC3E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,2BAA0B,aAAqB;QACnD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,6BAA0C,OAAd;YAClE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,MAAM;QACR;IACF;IAEA,8CAA8C;IAC9C,MAAM,wCAAuC,iBAAyB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,wCAAyD,OAAlB;YAC7E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iEAAiE;YAC/E,MAAM;QACR;IACF;IAEA,wDAAwD;IACxD,MAAM,gBAAe,IAAU,EAAE,YAA6F;QAC5H,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,iBAAiB,aAAa,aAAa;YAC3D,SAAS,MAAM,CAAC,eAAe,aAAa,WAAW;YACvD,SAAS,MAAM,CAAC,aAAa,aAAa,SAAS;YACnD,SAAS,MAAM,CAAC,eAAe,CAAC,aAAa,WAAW,IAAI,KAAK,EAAE,QAAQ;YAC3E,SAAS,MAAM,CAAC,aAAa,KAAK,IAAI;YAEtC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,qBAAqB,UAAU;gBACnE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAClC,OAAO;gBACL,UAAU,OAAO,IAAI;gBACrB,SAAS,OAAO,OAAO,IAAI;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,+CAA+C;IAC/C,MAAM,gBAAe,YAAgC;QACnD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,cAAc;YACpD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAe,UAAkB,EAAE,UAAuC;QAC9E,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,cAAwB,OAAX,aAAc;YACjE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAe,UAAkB;QACrC,IAAI;YACF,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,cAAwB,OAAX;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,aAAY,UAAkB;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,cAAwB,OAAX;YACnD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,oBAAoB;IACpB,MAAM,kBAAiB,UAAkB;QACvC,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,cAAwB,OAAX,YAAW,cAAY;gBACxE,cAAc;YAChB;YACA,OAAO,SAAS,IAAI,EAAE,sCAAsC;QAC9D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,UAAkB;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,cAAwB,OAAX,YAAW,aAAW;gBACvE,cAAc;YAChB;YACA,OAAO,SAAS,IAAI,EAAE,mCAAmC;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,MAAM;QACR;IACF;IAEA,wCAAwC;IACxC;YAAc,WAAA,iEAAmB;QAC/B,IAAI,CAAC,UAAU,OAAO;QACtB,MAAM,uBAAuB;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,OAAO,qBAAqB,QAAQ,CAAC,SAAS,WAAW;IAC3D;IAEA,kEAAkE;IAClE,MAAM,wBAAuB,aAAqB,EAAE,iBAAyB;QAK3E,IAAI;YACF,8CAA8C;YAC9C,MAAM,eAAe,MAAM,IAAI,CAAC,sCAAsC,CAAC;YAEvE,yCAAyC;YACzC,MAAM,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC;YAClD,MAAM,eAA+B,KAAK,IAAI;YAE9C,6CAA6C;YAC7C,MAAM,gBAAgB,aAAa,GAAG,CAAC,CAAA,MAAO,IAAI,aAAa;YAC/D,MAAM,UAAU,aAAa,MAAM,CAAC,CAAA,SAClC,OAAO,WAAW,IAAI,CAAC,cAAc,QAAQ,CAAC,OAAO,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ;YAG1F,OAAO;gBACL,aAAa,QAAQ,MAAM,KAAK;gBAChC;gBACA,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iDAAiD;YAC/D,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B;QACE,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,mCAAmC;IACnC,oBAAmB,IAAY;QAC7B,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;IACV;IAEA,+CAA+C;IAC/C,uBAAsB,YAAoB;QACxC,MAAM,gBAAwC;YAC5C,gCAAgC;YAChC,6BAA6B;YAC7B,0BAA0B;YAC1B,iBAAiB;YACjB,wBAAwB;YACxB,sBAAsB;YACtB,iBAAiB;YACjB,mBAAmB;YACnB,4BAA4B;YAC5B,yBAAyB;YACzB,6BAA6B;YAC7B,iBAAiB;YACjB,oBAAoB;YACpB,kBAAkB;YAClB,eAAe;YACf,SAAS;QACX;QAEA,wBAAwB;QACxB,IAAI,aAAa,CAAC,aAAa,EAAE;YAC/B,OAAO,aAAa,CAAC,aAAa;QACpC;QAEA,6BAA6B;QAC7B,MAAM,YAAY,aAAa,WAAW;QAC1C,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,OAAO,OAAO,CAAC,eAAgB;YACxD,IAAI,KAAK,WAAW,OAAO,WAAW;gBACpC,OAAO;YACT;QACF;QAEA,uCAAuC;QACvC,OAAO,aAAa,WAAW,GAAG,OAAO,CAAC,QAAQ;IACpD;IAEA,8BAA8B;IAC9B,cAAa,IAAU;YAAE,YAAA,iEAAoB,IAAI,eAAA,iEAAyB,EAAE;QAI1E,wCAAwC;QACxC,MAAM,eAAe,YAAY,OAAO;QACxC,IAAI,KAAK,IAAI,GAAG,cAAc;YAC5B,OAAO;gBACL,SAAS;gBACT,OAAO,AAAC,+BAAwC,OAAV,WAAU;YAClD;QACF;QAEA,+BAA+B;QAC/B,IAAI,aAAa,MAAM,GAAG,KAAK,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YAChE,OAAO;gBACL,SAAS;gBACT,OAAO,AAAC,yCAAgE,OAAxB,aAAa,IAAI,CAAC;YACpE;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;AACF", "debugId": null}}, {"offset": {"line": 1835, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/activityNotesService.ts"], "sourcesContent": ["import { apiClient } from \"@/lib/apiClient\";\r\nimport { processApiResponse } from \"@/lib/authUtils\";\r\nimport { User } from \"./auth.service\";\r\n\r\nexport interface ActivityNote {\r\n  id: string;\r\n  entity_type: string;\r\n  entity_id: string;\r\n  note: string;\r\n  note_type: 'evaluation_comment' | 'status_update' | 'general_note' | 'system_log' | 'review_note' | 'approval_note' | 'rejection_note';\r\n  status: 'active' | 'archived' | 'deleted';\r\n  category?: string;\r\n  step?: string;\r\n  metadata?: Record<string, any>;\r\n  priority: string;\r\n  is_visible: boolean;\r\n  is_internal: boolean;\r\n  created_by: string;\r\n  updated_by?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  archived_at?: string;\r\n  deleted_at?: string;\r\n  creator?: User;\r\n  updater?: User;\r\n}\r\n\r\nexport interface CreateActivityNoteDto {\r\n  entity_type: string;\r\n  entity_id: string;\r\n  note: string;\r\n  note_type?: 'evaluation_comment' | 'status_update' | 'general_note' | 'system_log' | 'review_note' | 'approval_note' | 'rejection_note';\r\n  category?: string;\r\n  step?: string;\r\n  metadata?: Record<string, any>;\r\n  priority?: string;\r\n  is_visible?: boolean;\r\n  is_internal?: boolean;\r\n}\r\n\r\nexport interface UpdateActivityNoteDto {\r\n  note?: string;\r\n  note_type?: 'evaluation_comment' | 'status_update' | 'general_note' | 'system_log' | 'review_note' | 'approval_note' | 'rejection_note';\r\n  status?: 'active' | 'archived' | 'deleted';\r\n  category?: string;\r\n  step?: string;\r\n  metadata?: Record<string, any>;\r\n  priority?: string;\r\n  is_visible?: boolean;\r\n  is_internal?: boolean;\r\n}\r\n\r\nexport interface ActivityNoteQueryDto {\r\n  entity_type?: string;\r\n  entity_id?: string;\r\n  note_type?: string;\r\n  status?: string;\r\n  category?: string;\r\n  step?: string;\r\n  priority?: string;\r\n  is_internal?: boolean;\r\n  created_by?: string;\r\n}\r\n\r\nclass ActivityNotesService {\r\n  private baseUrl = '/activity-notes';\r\n\r\n  async create(data: CreateActivityNoteDto): Promise<ActivityNote> {\r\n    const response = await apiClient.post(this.baseUrl, data);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async findAll(query?: ActivityNoteQueryDto): Promise<ActivityNote[]> {\r\n    const response = await apiClient.get(this.baseUrl, { params: query });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async findByEntity(entityType: string, entityId: string): Promise<any> {\r\n    const response = await apiClient.get(`${this.baseUrl}/entity/${entityType}/${entityId}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async findByEntityAndStep(entityType: string, entityId: string, step: string): Promise<ActivityNote[]> {\r\n    const response = await apiClient.get(`${this.baseUrl}/entity/${entityType}/${entityId}/step/${step}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async findOne(id: string): Promise<ActivityNote> {\r\n    const response = await apiClient.get(`${this.baseUrl}/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async update(id: string, data: UpdateActivityNoteDto): Promise<ActivityNote> {\r\n    const response = await apiClient.put(`${this.baseUrl}/${id}`, data);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async archive(id: string): Promise<ActivityNote> {\r\n    const response = await apiClient.put(`${this.baseUrl}/${id}/archive`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async softDelete(id: string): Promise<void> {\r\n    await apiClient.delete(`${this.baseUrl}/${id}/soft`);\r\n  }\r\n\r\n  async hardDelete(id: string): Promise<void> {\r\n    await apiClient.delete(`${this.baseUrl}/${id}/hard`);\r\n  }\r\n\r\n  // Specialized methods for evaluation workflow\r\n  async createEvaluationComment(\r\n    applicationId: string,\r\n    step: string,\r\n    comment: string,\r\n    metadata?: Record<string, any>\r\n  ): Promise<ActivityNote> {\r\n    const response = await apiClient.post(`${this.baseUrl}/evaluation-comment`, {\r\n      applicationId,\r\n      step,\r\n      comment,\r\n      metadata,\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createStatusUpdate(\r\n    applicationId: string,\r\n    statusChange: string,\r\n    metadata?: Record<string, any>\r\n  ): Promise<ActivityNote> {\r\n    const response = await apiClient.post(`${this.baseUrl}/status-update`, {\r\n      applicationId,\r\n      statusChange,\r\n      metadata,\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Helper methods for common use cases\r\n  async getEvaluationComments(applicationId: string, step?: string): Promise<ActivityNote[]> {\r\n    const query: ActivityNoteQueryDto = {\r\n      entity_type: 'application',\r\n      entity_id: applicationId,\r\n      note_type: 'evaluation_comment',\r\n      status: 'active',\r\n    };\r\n\r\n    if (step) {\r\n      query.step = step;\r\n    }\r\n\r\n    return this.findAll(query);\r\n  }\r\n\r\n  async getApplicationNotes(applicationId: string): Promise<any> {\r\n    return this.findByEntity('application', applicationId);\r\n  }\r\n\r\n  async getApplicationStatusUpdates(applicationId: string): Promise<ActivityNote[]> {\r\n    return this.findAll({\r\n      entity_type: 'application',\r\n      entity_id: applicationId,\r\n      note_type: 'status_update',\r\n      status: 'active',\r\n    });\r\n  }\r\n}\r\n\r\nexport const activityNotesService = new ActivityNotesService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AA+DA,MAAM;IAGJ,MAAM,OAAO,IAA2B,EAAyB;QAC/D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACpD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,QAAQ,KAA4B,EAA2B;QACnE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE;YAAE,QAAQ;QAAM;QACnE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,aAAa,UAAkB,EAAE,QAAgB,EAAgB;QACrE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAyB,OAAvB,IAAI,CAAC,OAAO,EAAC,YAAwB,OAAd,YAAW,KAAY,OAAT;QAC7E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,oBAAoB,UAAkB,EAAE,QAAgB,EAAE,IAAY,EAA2B;QACrG,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAyB,OAAvB,IAAI,CAAC,OAAO,EAAC,YAAwB,OAAd,YAAW,KAAoB,OAAjB,UAAS,UAAa,OAAL;QAC9F,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,QAAQ,EAAU,EAAyB;QAC/C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAM,OAAH;QACxD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,OAAO,EAAU,EAAE,IAA2B,EAAyB;QAC3E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAM,OAAH,KAAM;QAC9D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,QAAQ,EAAU,EAAyB;QAC/C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAM,OAAH,IAAG;QAC3D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAiB;QAC1C,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAM,OAAH,IAAG;IAC/C;IAEA,MAAM,WAAW,EAAU,EAAiB;QAC1C,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAM,OAAH,IAAG;IAC/C;IAEA,8CAA8C;IAC9C,MAAM,wBACJ,aAAqB,EACrB,IAAY,EACZ,OAAe,EACf,QAA8B,EACP;QACvB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,AAAC,GAAe,OAAb,IAAI,CAAC,OAAO,EAAC,wBAAsB;YAC1E;YACA;YACA;YACA;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,mBACJ,aAAqB,EACrB,YAAoB,EACpB,QAA8B,EACP;QACvB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,AAAC,GAAe,OAAb,IAAI,CAAC,OAAO,EAAC,mBAAiB;YACrE;YACA;YACA;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sCAAsC;IACtC,MAAM,sBAAsB,aAAqB,EAAE,IAAa,EAA2B;QACzF,MAAM,QAA8B;YAClC,aAAa;YACb,WAAW;YACX,WAAW;YACX,QAAQ;QACV;QAEA,IAAI,MAAM;YACR,MAAM,IAAI,GAAG;QACf;QAEA,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,MAAM,oBAAoB,aAAqB,EAAgB;QAC7D,OAAO,IAAI,CAAC,YAAY,CAAC,eAAe;IAC1C;IAEA,MAAM,4BAA4B,aAAqB,EAA2B;QAChF,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,aAAa;YACb,WAAW;YACX,WAAW;YACX,QAAQ;QACV;IACF;;QArGA,+KAAQ,WAAU;;AAsGpB;AAEO,MAAM,uBAAuB,IAAI", "debugId": null}}, {"offset": {"line": 1937, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/ActivityHistory.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo } from 'react';\r\nimport { activityNotesService, ActivityNote } from '@/services/activityNotesService';\r\n\r\ninterface ActivityHistoryProps {\r\n  entityType: string;\r\n  entityId: string;\r\n  title?: string;\r\n  showSearch?: boolean;\r\n  showFilters?: boolean;\r\n  maxHeight?: string;\r\n  className?: string;\r\n  onNotesChange?: (notes: ActivityNote[]) => void;\r\n  refreshTrigger?: number; // For external refresh\r\n}\r\n\r\nconst ActivityHistory: React.FC<ActivityHistoryProps> = ({\r\n  entityType,\r\n  entityId,\r\n  title = 'Activity History',\r\n  showSearch = true,\r\n  showFilters = false,\r\n  maxHeight = 'max-h-96',\r\n  className = '',\r\n  onNotesChange,\r\n  refreshTrigger = 0\r\n}) => {\r\n  const [notes, setNotes] = useState<ActivityNote[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedNoteType, setSelectedNoteType] = useState<string>('all');\r\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\r\n  const [selectedPriority, setSelectedPriority] = useState<string>('all');\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // Load notes when component mounts or when entity changes\r\n  useEffect(() => {\r\n    if (entityType && entityId) {\r\n      loadNotes();\r\n    }\r\n  }, [entityType, entityId, refreshTrigger]);\r\n\r\n  // Notify parent when notes change\r\n  useEffect(() => {\r\n    if (onNotesChange) {\r\n      onNotesChange(notes);\r\n    }\r\n  }, [notes, onNotesChange]);\r\n\r\n  const loadNotes = async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    try {\r\n      const response = await activityNotesService.findByEntity(entityType, entityId);\r\n      const activityNotes = response.data || response;\r\n      \r\n      // Ensure we always have an array\r\n      if (Array.isArray(activityNotes)) {\r\n        setNotes(activityNotes);\r\n      } else {\r\n        console.warn('Unexpected response format for notes:', activityNotes);\r\n        setNotes([]);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load activity notes:', error);\r\n      setError('Failed to load activity history. Please try again.');\r\n      setNotes([]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Filter and search notes\r\n  const filteredNotes = useMemo(() => {\r\n    return notes.filter(note => {\r\n      // Search filter\r\n      const matchesSearch = searchTerm === '' || \r\n        note.note.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        note.creator?.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        note.creator?.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        note.note_type.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        note.category?.toLowerCase().includes(searchTerm.toLowerCase());\r\n\r\n      // Note type filter\r\n      const matchesNoteType = selectedNoteType === 'all' || note.note_type === selectedNoteType;\r\n\r\n      // Category filter\r\n      const matchesCategory = selectedCategory === 'all' || note.category === selectedCategory;\r\n\r\n      // Priority filter\r\n      const matchesPriority = selectedPriority === 'all' || note.priority === selectedPriority;\r\n\r\n      return matchesSearch && matchesNoteType && matchesCategory && matchesPriority;\r\n    });\r\n  }, [notes, searchTerm, selectedNoteType, selectedCategory, selectedPriority]);\r\n\r\n  // Get unique values for filter dropdowns\r\n  const noteTypes = useMemo(() => {\r\n    const types = [...new Set(notes.map(note => note.note_type))];\r\n    return types.sort();\r\n  }, [notes]);\r\n\r\n  const categories = useMemo(() => {\r\n    const cats = [...new Set(notes.map(note => note.category).filter(Boolean))];\r\n    return cats.sort();\r\n  }, [notes]);\r\n\r\n  const priorities = useMemo(() => {\r\n    const prios = [...new Set(notes.map(note => note.priority))];\r\n    return prios.sort();\r\n  }, [notes]);\r\n\r\n  const formatNoteType = (noteType: string) => {\r\n    return noteType.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\r\n  };\r\n\r\n  const getNoteIcon = (noteType: string) => {\r\n    switch (noteType) {\r\n      case 'evaluation_comment': return 'ri-chat-3-line';\r\n      case 'status_update': return 'ri-flag-line';\r\n      case 'system_log': return 'ri-settings-line';\r\n      case 'review_note': return 'ri-eye-line';\r\n      case 'approval_note': return 'ri-check-line';\r\n      case 'rejection_note': return 'ri-close-line';\r\n      default: return 'ri-sticky-note-line';\r\n    }\r\n  };\r\n\r\n  const getNoteTypeColor = (noteType: string) => {\r\n    switch (noteType) {\r\n      case 'evaluation_comment': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\r\n      case 'status_update': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'system_log': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n      case 'review_note': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';\r\n      case 'approval_note': return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200';\r\n      case 'rejection_note': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\r\n      default: return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n    }\r\n  };\r\n\r\n  const clearFilters = () => {\r\n    setSearchTerm('');\r\n    setSelectedNoteType('all');\r\n    setSelectedCategory('all');\r\n    setSelectedPriority('all');\r\n  };\r\n\r\n  return (\r\n    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 ${className}`}>\r\n      {/* Header */}\r\n      <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center\">\r\n            <i className=\"ri-history-line mr-2 text-purple-600\"></i>\r\n            {title} ({filteredNotes.length})\r\n          </h3>\r\n          <button\r\n            onClick={loadNotes}\r\n            disabled={loading}\r\n            className=\"text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 disabled:opacity-50\"\r\n            title=\"Refresh\"\r\n          >\r\n            <i className={`ri-refresh-line ${loading ? 'animate-spin' : ''}`}></i>\r\n          </button>\r\n        </div>\r\n\r\n        {/* Search and Filters */}\r\n        {(showSearch || showFilters) && (\r\n          <div className=\"mt-4 space-y-3\">\r\n            {/* Search */}\r\n            {showSearch && (\r\n              <div className=\"relative\">\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <i className=\"ri-search-line text-gray-400\"></i>\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\r\n                  placeholder=\"Search activity notes...\"\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            {/* Filters */}\r\n            {showFilters && (\r\n              <div className=\"grid grid-cols-1 md:grid-cols-4 gap-3\">\r\n                <select\r\n                  value={selectedNoteType}\r\n                  onChange={(e) => setSelectedNoteType(e.target.value)}\r\n                  className=\"block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                >\r\n                  <option value=\"all\">All Types</option>\r\n                  {noteTypes.map(type => (\r\n                    <option key={type} value={type}>{formatNoteType(type)}</option>\r\n                  ))}\r\n                </select>\r\n\r\n                <select\r\n                  value={selectedCategory}\r\n                  onChange={(e) => setSelectedCategory(e.target.value)}\r\n                  className=\"block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                >\r\n                  <option value=\"all\">All Categories</option>\r\n                  {categories.map(category => (\r\n                    <option key={category} value={category}>{category}</option>\r\n                  ))}\r\n                </select>\r\n\r\n                <select\r\n                  value={selectedPriority}\r\n                  onChange={(e) => setSelectedPriority(e.target.value)}\r\n                  className=\"block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                >\r\n                  <option value=\"all\">All Priorities</option>\r\n                  {priorities.map(priority => (\r\n                    <option key={priority} value={priority}>{priority}</option>\r\n                  ))}\r\n                </select>\r\n\r\n                <button\r\n                  onClick={clearFilters}\r\n                  className=\"px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\r\n                >\r\n                  Clear Filters\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Content */}\r\n      <div className={`${maxHeight} overflow-y-auto`}>\r\n        {error && (\r\n          <div className=\"p-4 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800\">\r\n            <div className=\"flex items-center text-red-600 dark:text-red-400 text-sm\">\r\n              <i className=\"ri-error-warning-line mr-2\"></i>\r\n              {error}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {loading ? (\r\n          <div className=\"flex items-center justify-center py-8\">\r\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3\"></div>\r\n            <span className=\"text-gray-600 dark:text-gray-400\">Loading activity history...</span>\r\n          </div>\r\n        ) : filteredNotes.length === 0 ? (\r\n          <div className=\"text-center py-8\">\r\n            <i className=\"ri-chat-3-line text-4xl text-gray-400 mb-4\"></i>\r\n            <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n              {notes.length === 0 ? 'No Activity Yet' : 'No Matching Results'}\r\n            </h4>\r\n            <p className=\"text-gray-600 dark:text-gray-400\">\r\n              {notes.length === 0 \r\n                ? 'No notes or communications have been recorded for this entity.'\r\n                : 'Try adjusting your search or filter criteria.'\r\n              }\r\n            </p>\r\n          </div>\r\n        ) : (\r\n          <div className=\"p-4 space-y-4\">\r\n            {filteredNotes.map((note) => (\r\n              <div key={note.id} className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600\">\r\n                {/* Note Header */}\r\n                <div className=\"flex items-start justify-between mb-3\">\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <div className=\"flex-shrink-0\">\r\n                      <div className=\"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\">\r\n                        <span className=\"text-sm font-medium text-blue-600 dark:text-blue-400\">\r\n                          {note?.creator?.first_name?.charAt(0)}{note?.creator?.last_name?.charAt(0)}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        <span className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                          {note.creator?.first_name} {note.creator?.last_name}\r\n                        </span>\r\n                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getNoteTypeColor(note.note_type)}`}>\r\n                          <i className={`${getNoteIcon(note.note_type)} mr-1`}></i>\r\n                          {formatNoteType(note.note_type)}\r\n                        </span>\r\n                      </div>\r\n                      <div className=\"flex items-center text-sm text-gray-500 dark:text-gray-400 mt-1\">\r\n                        <i className=\"ri-time-line mr-1\"></i>\r\n                        <span>{new Date(note.created_at).toLocaleString()}</span>\r\n                        {note.step && (\r\n                          <>\r\n                            <span className=\"mx-2\">•</span>\r\n                            <i className=\"ri-footprint-line mr-1\"></i>\r\n                            <span className=\"capitalize\">{note.step.replace(/-/g, ' ')}</span>\r\n                          </>\r\n                        )}\r\n                        {note.category && (\r\n                          <>\r\n                            <span className=\"mx-2\">•</span>\r\n                            <i className=\"ri-bookmark-line mr-1\"></i>\r\n                            <span className=\"capitalize\">{note.category}</span>\r\n                          </>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    {note.priority !== 'normal' && (\r\n                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${\r\n                        note.priority === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :\r\n                        note.priority === 'critical' ? 'bg-red-200 text-red-900 dark:bg-red-800 dark:text-red-100' :\r\n                        'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'\r\n                      }`}>\r\n                        {note.priority}\r\n                      </span>\r\n                    )}\r\n                    {note.metadata?.is_email_message && (\r\n                      <span className=\"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\">\r\n                        <i className=\"ri-mail-line mr-1\"></i>\r\n                        Email Sent\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Note Content */}\r\n                <div className=\"mb-3\">\r\n                  <p className=\"text-gray-900 dark:text-gray-100 whitespace-pre-wrap\">\r\n                    {note.note}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ActivityHistory;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAiBA,MAAM,kBAAkD;QAAC,EACvD,UAAU,EACV,QAAQ,EACR,QAAQ,kBAAkB,EAC1B,aAAa,IAAI,EACjB,cAAc,KAAK,EACnB,YAAY,UAAU,EACtB,YAAY,EAAE,EACd,aAAa,EACb,iBAAiB,CAAC,EACnB;;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,0DAA0D;IAC1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,cAAc,UAAU;gBAC1B;YACF;QACF;oCAAG;QAAC;QAAY;QAAU;KAAe;IAEzC,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,eAAe;gBACjB,cAAc;YAChB;QACF;oCAAG;QAAC;QAAO;KAAc;IAEzB,MAAM,YAAY;QAChB,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,WAAW,MAAM,0IAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC,YAAY;YACrE,MAAM,gBAAgB,SAAS,IAAI,IAAI;YAEvC,iCAAiC;YACjC,IAAI,MAAM,OAAO,CAAC,gBAAgB;gBAChC,SAAS;YACX,OAAO;gBACL,QAAQ,IAAI,CAAC,yCAAyC;gBACtD,SAAS,EAAE;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS;YACT,SAAS,EAAE;QACb,SAAU;YACR,WAAW;QACb;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDAAE;YAC5B,OAAO,MAAM,MAAM;0DAAC,CAAA;wBAIhB,0BAAA,eACA,yBAAA,gBAEA;oBANF,gBAAgB;oBAChB,MAAM,gBAAgB,eAAe,MACnC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,SACvD,gBAAA,KAAK,OAAO,cAAZ,qCAAA,2BAAA,cAAc,UAAU,cAAxB,+CAAA,yBAA0B,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,UACvE,iBAAA,KAAK,OAAO,cAAZ,sCAAA,0BAAA,eAAc,SAAS,cAAvB,8CAAA,wBAAyB,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,QACtE,KAAK,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,SAC5D,iBAAA,KAAK,QAAQ,cAAb,qCAAA,eAAe,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;oBAE9D,mBAAmB;oBACnB,MAAM,kBAAkB,qBAAqB,SAAS,KAAK,SAAS,KAAK;oBAEzE,kBAAkB;oBAClB,MAAM,kBAAkB,qBAAqB,SAAS,KAAK,QAAQ,KAAK;oBAExE,kBAAkB;oBAClB,MAAM,kBAAkB,qBAAqB,SAAS,KAAK,QAAQ,KAAK;oBAExE,OAAO,iBAAiB,mBAAmB,mBAAmB;gBAChE;;QACF;iDAAG;QAAC;QAAO;QAAY;QAAkB;QAAkB;KAAiB;IAE5E,yCAAyC;IACzC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE;YACxB,MAAM,QAAQ;mBAAI,IAAI,IAAI,MAAM,GAAG;0DAAC,CAAA,OAAQ,KAAK,SAAS;;aAAG;YAC7D,OAAO,MAAM,IAAI;QACnB;6CAAG;QAAC;KAAM;IAEV,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE;YACzB,MAAM,OAAO;mBAAI,IAAI,IAAI,MAAM,GAAG;2DAAC,CAAA,OAAQ,KAAK,QAAQ;0DAAE,MAAM,CAAC;aAAU;YAC3E,OAAO,KAAK,IAAI;QAClB;8CAAG;QAAC;KAAM;IAEV,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE;YACzB,MAAM,QAAQ;mBAAI,IAAI,IAAI,MAAM,GAAG;2DAAC,CAAA,OAAQ,KAAK,QAAQ;;aAAG;YAC5D,OAAO,MAAM,IAAI;QACnB;8CAAG;QAAC;KAAM;IAEV,MAAM,iBAAiB,CAAC;QACtB,OAAO,SAAS,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;IACxE;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAsB,OAAO;YAClC,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAkB,OAAO;YAC9B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAsB,OAAO;YAClC,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAkB,OAAO;YAC9B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,eAAe;QACnB,cAAc;QACd,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;IACtB;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,oFAA6F,OAAV;;0BAElG,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAE,WAAU;;;;;;oCACZ;oCAAM;oCAAG,cAAc,MAAM;oCAAC;;;;;;;0CAEjC,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC;oCAAE,WAAW,AAAC,mBAAgD,OAA9B,UAAU,iBAAiB;;;;;;;;;;;;;;;;;oBAK/D,CAAC,cAAc,WAAW,mBACzB,6LAAC;wBAAI,WAAU;;4BAEZ,4BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;kDAEf,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;wCACV,aAAY;;;;;;;;;;;;4BAMjB,6BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAM;;;;;;4CACnB,UAAU,GAAG,CAAC,CAAA,qBACb,6LAAC;oDAAkB,OAAO;8DAAO,eAAe;mDAAnC;;;;;;;;;;;kDAIjB,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAM;;;;;;4CACnB,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;oDAAsB,OAAO;8DAAW;mDAA5B;;;;;;;;;;;kDAIjB,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAM;;;;;;4CACnB,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;oDAAsB,OAAO;8DAAW;mDAA5B;;;;;;;;;;;kDAIjB,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6LAAC;gBAAI,WAAW,AAAC,GAAY,OAAV,WAAU;;oBAC1B,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;;;;;gCACZ;;;;;;;;;;;;oBAKN,wBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAmC;;;;;;;;;;;mEAEnD,cAAc,MAAM,KAAK,kBAC3B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;gCAAG,WAAU;0CACX,MAAM,MAAM,KAAK,IAAI,oBAAoB;;;;;;0CAE5C,6LAAC;gCAAE,WAAU;0CACV,MAAM,MAAM,KAAK,IACd,mEACA;;;;;;;;;;;iFAKR,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC;gCAQL,0BAAA,eAAsC,yBAAA,gBAOtC,gBAA2B,gBAqCjC;iDAnDP,6LAAC;gCAAkB,WAAU;;kDAE3B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;;oEACb,iBAAA,4BAAA,gBAAA,KAAM,OAAO,cAAb,qCAAA,2BAAA,cAAe,UAAU,cAAzB,+CAAA,yBAA2B,MAAM,CAAC;oEAAI,iBAAA,4BAAA,iBAAA,KAAM,OAAO,cAAb,sCAAA,0BAAA,eAAe,SAAS,cAAxB,8CAAA,wBAA0B,MAAM,CAAC;;;;;;;;;;;;;;;;;kEAI9E,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;;6EACb,iBAAA,KAAK,OAAO,cAAZ,qCAAA,eAAc,UAAU;4EAAC;6EAAE,iBAAA,KAAK,OAAO,cAAZ,qCAAA,eAAc,SAAS;;;;;;;kFAErD,6LAAC;wEAAK,WAAW,AAAC,yEAAyG,OAAjC,iBAAiB,KAAK,SAAS;;0FACvH,6LAAC;gFAAE,WAAW,AAAC,GAA8B,OAA5B,YAAY,KAAK,SAAS,GAAE;;;;;;4EAC5C,eAAe,KAAK,SAAS;;;;;;;;;;;;;0EAGlC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;;;;;;kFACb,6LAAC;kFAAM,IAAI,KAAK,KAAK,UAAU,EAAE,cAAc;;;;;;oEAC9C,KAAK,IAAI,kBACR;;0FACE,6LAAC;gFAAK,WAAU;0FAAO;;;;;;0FACvB,6LAAC;gFAAE,WAAU;;;;;;0FACb,6LAAC;gFAAK,WAAU;0FAAc,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM;;;;;;;;oEAGzD,KAAK,QAAQ,kBACZ;;0FACE,6LAAC;gFAAK,WAAU;0FAAO;;;;;;0FACvB,6LAAC;gFAAE,WAAU;;;;;;0FACb,6LAAC;gFAAK,WAAU;0FAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;0DAMrD,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,QAAQ,KAAK,0BACjB,6LAAC;wDAAK,WAAW,AAAC,yEAIjB,OAHC,KAAK,QAAQ,KAAK,SAAS,8DAC3B,KAAK,QAAQ,KAAK,aAAa,8DAC/B;kEAEC,KAAK,QAAQ;;;;;;oDAGjB,EAAA,iBAAA,KAAK,QAAQ,cAAb,qCAAA,eAAe,gBAAgB,mBAC9B,6LAAC;wDAAK,WAAU;;0EACd,6LAAC;gEAAE,WAAU;;;;;;4DAAwB;;;;;;;;;;;;;;;;;;;kDAQ7C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDACV,KAAK,IAAI;;;;;;;;;;;;+BA/DN,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAyE/B;GAlUM;KAAA;uCAoUS", "debugId": null}}, {"offset": {"line": 2651, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/evaluation/ActivityNotesModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { documentService } from '@/services/documentService';\r\nimport ActivityHistory from '@/components/common/ActivityHistory';\r\nimport { activityNotesService } from '@/services/activityNotesService';\r\nimport { userService } from '@/services/userService';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\n\r\ninterface ActivityNotesModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  entityId: string;\r\n  entityType: string;\r\n  applicationNumber?: string;\r\n  initialEmails?: string;\r\n  title?: string;\r\n  isGeneralMessage?: boolean;\r\n}\r\n\r\nconst ActivityNotesModal: React.FC<ActivityNotesModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  entityId,\r\n  entityType,\r\n  initialEmails = '',\r\n  title,\r\n  isGeneralMessage = false\r\n}) => {\r\n  const [message, setMessage] = useState('');\r\n  const [emailsToNotify, setEmailsToNotify] = useState(initialEmails);\r\n  const [emailBadges, setEmailBadges] = useState<string[]>([]);\r\n  const [currentEmailInput, setCurrentEmailInput] = useState('');\r\n  const [emailSuggestions, setEmailSuggestions] = useState<string[]>([]);\r\n  const [showSuggestions, setShowSuggestions] = useState(false);\r\n  const [attachments, setAttachments] = useState<File[]>([]);\r\n  const [sending, setSending] = useState(false);\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [errorMessage, setErrorMessage] = useState('');\r\n  const [refreshTrigger, setRefreshTrigger] = useState(0);\r\n\r\n  const { user } = useAuth();\r\n\r\n  // Initialize email badges from initial emails\r\n  React.useEffect(() => {\r\n    if (initialEmails) {\r\n      const emails = initialEmails.split(',').map(email => email.trim()).filter(email => email);\r\n      setEmailBadges(emails);\r\n    }\r\n  }, [initialEmails]);\r\n\r\n  const handleEmailInputChange = async (value: string) => {\r\n    setCurrentEmailInput(value);\r\n\r\n    if (value.length > 1) {\r\n      try {\r\n        const userEmails = await userService.getUserEmails(value);\r\n        const filtered = userEmails.filter(email =>\r\n          email.toLowerCase().includes(value.toLowerCase()) &&\r\n          !emailBadges.includes(email)\r\n        );\r\n        setEmailSuggestions(filtered);\r\n        setShowSuggestions(filtered.length > 0);\r\n      } catch (error) {\r\n        console.warn('Failed to fetch email suggestions:', error);\r\n        setShowSuggestions(false);\r\n      }\r\n    } else {\r\n      setShowSuggestions(false);\r\n    }\r\n  };\r\n\r\n  const addEmailBadge = (email: string) => {\r\n    const trimmedEmail = email.trim();\r\n    if (trimmedEmail && !emailBadges.includes(trimmedEmail)) {\r\n      const newBadges = [...emailBadges, trimmedEmail];\r\n      setEmailBadges(newBadges);\r\n      setEmailsToNotify(newBadges.join(', '));\r\n      setCurrentEmailInput('');\r\n      setShowSuggestions(false);\r\n    }\r\n  };\r\n\r\n  const removeEmailBadge = (emailToRemove: string) => {\r\n    const newBadges = emailBadges.filter(email => email !== emailToRemove);\r\n    setEmailBadges(newBadges);\r\n    setEmailsToNotify(newBadges.join(', '));\r\n  };\r\n\r\n  const handleEmailInputKeyDown = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter' || e.key === ',') {\r\n      e.preventDefault();\r\n      if (currentEmailInput.trim()) {\r\n        addEmailBadge(currentEmailInput);\r\n      }\r\n    } else if (e.key === 'Backspace' && !currentEmailInput && emailBadges.length > 0) {\r\n      removeEmailBadge(emailBadges[emailBadges.length - 1]);\r\n    }\r\n  };\r\n\r\n  const isValidEmail = (email: string) => {\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    return emailRegex.test(email);\r\n  };\r\n\r\n  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const files = event.target.files;\r\n    if (files) {\r\n      const newFiles = Array.from(files);\r\n      setAttachments(prev => [...prev, ...newFiles]);\r\n    }\r\n  };\r\n\r\n  const removeAttachment = (index: number) => {\r\n    setAttachments(prev => prev.filter((_, i) => i !== index));\r\n  };\r\n\r\n  const handleSendMessage = async () => {\r\n    if (!message.trim()) {\r\n      setErrorMessage('Please enter a message before sending.');\r\n      return;\r\n    }\r\n\r\n    // For general messaging, ensure we have at least one email recipient\r\n    if (isGeneralMessage && emailBadges.length === 0) {\r\n      setErrorMessage('Please add at least one email recipient for general messaging.');\r\n      return;\r\n    }\r\n\r\n    setSending(true);\r\n    setSuccessMessage('');\r\n    setErrorMessage('');\r\n\r\n    try {\r\n      // Step 1: Create activity note for the message\r\n      // Use emailBadges array for more accurate email list\r\n      const additionalEmails = emailBadges.filter(email => email.trim().length > 0);\r\n\r\n      const activityNote = await activityNotesService.create({\r\n        entity_type: entityType,\r\n        entity_id: entityId,\r\n        note: message.trim(),\r\n        note_type: 'evaluation_comment',\r\n        category: 'communication',\r\n        metadata: {\r\n          is_email_message: true,\r\n          attachments_count: attachments.length,\r\n          timestamp: new Date().toISOString(),\r\n          additional_emails: additionalEmails,\r\n        },\r\n        priority: 'normal',\r\n        is_internal: false,\r\n      });\r\n\r\n      // Step 2: Upload attachments and link to the activity note\r\n      const uploadedDocuments = [];\r\n      if (attachments.length > 0) {\r\n        for (const file of attachments) {\r\n          try {\r\n            const uploadResult = await documentService.uploadDocument(file, {\r\n              document_type: 'COMMUNICATION',\r\n              entity_type: 'activity_note',\r\n              entity_id: activityNote.id,\r\n              is_required: false\r\n            });\r\n            uploadedDocuments.push(uploadResult.document);\r\n          } catch (error) {\r\n            console.error('Failed to upload document:', error);\r\n          }\r\n        }\r\n      }\r\n\r\n      // Step 4: Clear form and trigger refresh\r\n      setMessage('');\r\n      setEmailsToNotify('');\r\n      setAttachments([]);\r\n\r\n      const successMsg = additionalEmails.length > 0\r\n        ? `✅ Message sent successfully! Applicant and ${additionalEmails.length} additional email(s) have been notified.`\r\n        : '✅ Message sent successfully and applicant has been notified via email!';\r\n      setSuccessMessage(successMsg);\r\n\r\n      // Trigger refresh of ActivityHistory component\r\n      setRefreshTrigger(prev => prev + 1);\r\n\r\n      // Auto-hide success message after 5 seconds\r\n      setTimeout(() => {\r\n        setSuccessMessage('');\r\n      }, 5000);\r\n\r\n    } catch (error) {\r\n      console.error('Failed to send message:', error);\r\n      setErrorMessage('❌ Failed to send message. Please try again.');\r\n      \r\n      // Auto-hide error message after 5 seconds\r\n      setTimeout(() => {\r\n        setErrorMessage('');\r\n      }, 5000);\r\n    } finally {\r\n      setSending(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col\">\r\n        {/* Header */}\r\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\r\n          <div>\r\n            <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100 flex items-center\">\r\n              <i className=\"ri-mail-line mr-2 text-blue-600\"></i>\r\n              Communication Center\r\n            </h2>\r\n            <p className=\"text-sm text-gray-500 dark:text-gray-400 mt-1\">\r\n              User: {user?.email}\r\n            </p>\r\n          </div>\r\n          <button\r\n            onClick={onClose}\r\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n          >\r\n            <i className=\"ri-close-line text-xl\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        {/* Content */}\r\n        <div className=\"flex-1 overflow-hidden flex flex-col\">\r\n          <div className='overflow-y-auto'>\r\n            {/* Send Message Section */}\r\n            <div className=\"p-6 border-b border-gray-200 dark:border-gray-700\">\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center\">\r\n                <i className=\"ri-send-plane-line mr-2 text-green-600\"></i>\r\n                {title || (isGeneralMessage ? 'Send Message' : 'Send Message to Applicant')}\r\n              </h3>\r\n\r\n              {/* Success Message */}\r\n              {successMessage && (\r\n                <div className=\"mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md\">\r\n                  <div className=\"text-green-600 dark:text-green-400 text-sm font-medium\">\r\n                    {successMessage}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Error Message */}\r\n              {errorMessage && (\r\n                <div className=\"mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md\">\r\n                  <div className=\"text-red-600 dark:text-red-400 text-sm font-medium\">\r\n                    {errorMessage}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Emails to Notify Input */}\r\n              <div className=\"mb-4\">\r\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                  Additional Emails to Notify (Optional)\r\n                </label>\r\n\r\n                {/* Email Badges */}\r\n                {emailBadges.length > 0 && (\r\n                  <div className=\"flex flex-wrap gap-2 mb-2 p-2 bg-gray-50 dark:bg-gray-700 rounded-md\">\r\n                    {emailBadges.map((email, index) => (\r\n                      <span\r\n                        key={index}\r\n                        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\r\n                          isValidEmail(email)\r\n                            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200'\r\n                            : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200'\r\n                        }`}\r\n                      >\r\n                        {email}\r\n                        <button\r\n                          type=\"button\"\r\n                          onClick={() => removeEmailBadge(email)}\r\n                          className=\"ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-black/10 dark:hover:bg-white/10\"\r\n                        >\r\n                          <i className=\"ri-close-line text-xs\"></i>\r\n                        </button>\r\n                      </span>\r\n                    ))}\r\n                  </div>\r\n                )}\r\n\r\n                {/* Email Input with Suggestions */}\r\n                <div className=\"relative\">\r\n                  <input\r\n                    type=\"text\"\r\n                    value={currentEmailInput}\r\n                    onChange={(e) => {\r\n                      handleEmailInputChange(e.target.value);\r\n                      if (successMessage) setSuccessMessage('');\r\n                      if (errorMessage) setErrorMessage('');\r\n                    }}\r\n                    onKeyDown={handleEmailInputKeyDown}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100\"\r\n                    placeholder=\"Type email address and press Enter or comma to add\"\r\n                  />\r\n\r\n                  {/* Email Suggestions Dropdown */}\r\n                  {showSuggestions && emailSuggestions.length > 0 && (\r\n                    <div className=\"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-40 overflow-y-auto\">\r\n                      {emailSuggestions.map((suggestion, index) => (\r\n                        <button\r\n                          key={index}\r\n                          type=\"button\"\r\n                          onClick={() => addEmailBadge(suggestion)}\r\n                          className=\"w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 focus:bg-gray-100 dark:focus:bg-gray-700 focus:outline-none text-sm\"\r\n                        >\r\n                          {suggestion}\r\n                        </button>\r\n                      ))}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Type email addresses and press Enter or comma to add them. {isGeneralMessage ? 'All added recipients will be notified.' : 'The applicant will be notified automatically.'}\r\n                </p>\r\n              </div>\r\n\r\n              {/* Message Input */}\r\n              <div className=\"mb-4\">\r\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                  Message *\r\n                </label>\r\n                <textarea\r\n                  value={message}\r\n                  onChange={(e) => {\r\n                    setMessage(e.target.value);\r\n                    if (successMessage) setSuccessMessage('');\r\n                    if (errorMessage) setErrorMessage('');\r\n                  }}\r\n                  rows={3}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100\"\r\n                  placeholder=\"Enter your message to the applicant...\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              {/* File Upload */}\r\n              <div className=\"mb-4\">\r\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                  Attach Files (Optional)\r\n                </label>\r\n                <div className=\"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-3\">\r\n                  <input\r\n                    type=\"file\"\r\n                    multiple\r\n                    onChange={handleFileUpload}\r\n                    className=\"hidden\"\r\n                    id=\"message-file-upload\"\r\n                    accept=\".pdf,.doc,.docx,.jpg,.jpeg,.png\"\r\n                  />\r\n                  <label\r\n                    htmlFor=\"message-file-upload\"\r\n                    className=\"cursor-pointer flex flex-col items-center justify-center\"\r\n                  >\r\n                    <i className=\"ri-upload-cloud-line text-2xl text-gray-400 mb-1\"></i>\r\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                      Click to upload files\r\n                    </span>\r\n                  </label>\r\n                </div>\r\n\r\n                {/* Uploaded Files List */}\r\n                {attachments.length > 0 && (\r\n                  <div className=\"mt-3\">\r\n                    <div className=\"space-y-2\">\r\n                      {attachments.map((file, index) => (\r\n                        <div key={index} className=\"flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded\">\r\n                          <div className=\"flex items-center\">\r\n                            <i className=\"ri-file-line text-gray-400 mr-2\"></i>\r\n                            <span className=\"text-sm text-gray-700 dark:text-gray-300\">{file.name}</span>\r\n                          </div>\r\n                          <button\r\n                            type=\"button\"\r\n                            onClick={() => removeAttachment(index)}\r\n                            className=\"text-red-500 hover:text-red-700\"\r\n                          >\r\n                            <i className=\"ri-close-line\"></i>\r\n                          </button>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Send Button */}\r\n              <button\r\n                onClick={handleSendMessage}\r\n                disabled={!message.trim() || sending}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"\r\n              >\r\n                {sending ? (\r\n                  <>\r\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                    Sending...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <i className=\"ri-send-plane-line mr-2\"></i>\r\n                    Send Message\r\n                  </>\r\n                )}\r\n              </button>\r\n            </div>\r\n\r\n            {/* Activity History Section */}\r\n            <div className=\"flex-1 overflow-hidden\">\r\n              <ActivityHistory\r\n                entityType={entityType}\r\n                entityId={entityId}\r\n                title=\"Activity History\"\r\n                showSearch={true}\r\n                showFilters={false}\r\n                maxHeight=\"max-h-96\"\r\n                refreshTrigger={refreshTrigger}\r\n                className=\"border-0 rounded-none\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ActivityNotesModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAoBA,MAAM,qBAAwD;QAAC,EAC7D,MAAM,EACN,OAAO,EACP,QAAQ,EACR,UAAU,EACV,gBAAgB,EAAE,EAClB,KAAK,EACL,mBAAmB,KAAK,EACzB;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,8CAA8C;IAC9C,6JAAA,CAAA,UAAK,CAAC,SAAS;wCAAC;YACd,IAAI,eAAe;gBACjB,MAAM,SAAS,cAAc,KAAK,CAAC,KAAK,GAAG;2DAAC,CAAA,QAAS,MAAM,IAAI;0DAAI,MAAM;2DAAC,CAAA,QAAS;;gBACnF,eAAe;YACjB;QACF;uCAAG;QAAC;KAAc;IAElB,MAAM,yBAAyB,OAAO;QACpC,qBAAqB;QAErB,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,IAAI;gBACF,MAAM,aAAa,MAAM,iIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;gBACnD,MAAM,WAAW,WAAW,MAAM,CAAC,CAAA,QACjC,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OAC9C,CAAC,YAAY,QAAQ,CAAC;gBAExB,oBAAoB;gBACpB,mBAAmB,SAAS,MAAM,GAAG;YACvC,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,sCAAsC;gBACnD,mBAAmB;YACrB;QACF,OAAO;YACL,mBAAmB;QACrB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,eAAe,MAAM,IAAI;QAC/B,IAAI,gBAAgB,CAAC,YAAY,QAAQ,CAAC,eAAe;YACvD,MAAM,YAAY;mBAAI;gBAAa;aAAa;YAChD,eAAe;YACf,kBAAkB,UAAU,IAAI,CAAC;YACjC,qBAAqB;YACrB,mBAAmB;QACrB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,YAAY,YAAY,MAAM,CAAC,CAAA,QAAS,UAAU;QACxD,eAAe;QACf,kBAAkB,UAAU,IAAI,CAAC;IACnC;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;YACtC,EAAE,cAAc;YAChB,IAAI,kBAAkB,IAAI,IAAI;gBAC5B,cAAc;YAChB;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,eAAe,CAAC,qBAAqB,YAAY,MAAM,GAAG,GAAG;YAChF,iBAAiB,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE;QACtD;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC;IACzB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,MAAM,MAAM,CAAC,KAAK;QAChC,IAAI,OAAO;YACT,MAAM,WAAW,MAAM,IAAI,CAAC;YAC5B,eAAe,CAAA,OAAQ;uBAAI;uBAAS;iBAAS;QAC/C;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACrD;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,QAAQ,IAAI,IAAI;YACnB,gBAAgB;YAChB;QACF;QAEA,qEAAqE;QACrE,IAAI,oBAAoB,YAAY,MAAM,KAAK,GAAG;YAChD,gBAAgB;YAChB;QACF;QAEA,WAAW;QACX,kBAAkB;QAClB,gBAAgB;QAEhB,IAAI;YACF,+CAA+C;YAC/C,qDAAqD;YACrD,MAAM,mBAAmB,YAAY,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,GAAG,MAAM,GAAG;YAE3E,MAAM,eAAe,MAAM,0IAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC;gBACrD,aAAa;gBACb,WAAW;gBACX,MAAM,QAAQ,IAAI;gBAClB,WAAW;gBACX,UAAU;gBACV,UAAU;oBACR,kBAAkB;oBAClB,mBAAmB,YAAY,MAAM;oBACrC,WAAW,IAAI,OAAO,WAAW;oBACjC,mBAAmB;gBACrB;gBACA,UAAU;gBACV,aAAa;YACf;YAEA,2DAA2D;YAC3D,MAAM,oBAAoB,EAAE;YAC5B,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,KAAK,MAAM,QAAQ,YAAa;oBAC9B,IAAI;wBACF,MAAM,eAAe,MAAM,qIAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,MAAM;4BAC9D,eAAe;4BACf,aAAa;4BACb,WAAW,aAAa,EAAE;4BAC1B,aAAa;wBACf;wBACA,kBAAkB,IAAI,CAAC,aAAa,QAAQ;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC9C;gBACF;YACF;YAEA,yCAAyC;YACzC,WAAW;YACX,kBAAkB;YAClB,eAAe,EAAE;YAEjB,MAAM,aAAa,iBAAiB,MAAM,GAAG,IACzC,AAAC,8CAAqE,OAAxB,iBAAiB,MAAM,EAAC,8CACtE;YACJ,kBAAkB;YAElB,+CAA+C;YAC/C,kBAAkB,CAAA,OAAQ,OAAO;YAEjC,4CAA4C;YAC5C,WAAW;gBACT,kBAAkB;YACpB,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,gBAAgB;YAEhB,0CAA0C;YAC1C,WAAW;gBACT,gBAAgB;YAClB,GAAG;QACL,SAAU;YACR,WAAW;QACb;IACF;IAIA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAE,WAAU;;;;;;wCAAsC;;;;;;;8CAGrD,6LAAC;oCAAE,WAAU;;wCAAgD;wCACpD,iBAAA,2BAAA,KAAM,KAAK;;;;;;;;;;;;;sCAGtB,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;8BAKjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAE,WAAU;;;;;;4CACZ,SAAS,CAAC,mBAAmB,iBAAiB,2BAA2B;;;;;;;oCAI3E,gCACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;;oCAMN,8BACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;;kDAMP,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;4CAKlF,YAAY,MAAM,GAAG,mBACpB,6LAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;wDAEC,WAAW,AAAC,uEAIX,OAHC,aAAa,SACT,qEACA;;4DAGL;0EACD,6LAAC;gEACC,MAAK;gEACL,SAAS,IAAM,iBAAiB;gEAChC,WAAU;0EAEV,cAAA,6LAAC;oEAAE,WAAU;;;;;;;;;;;;uDAbV;;;;;;;;;;0DAqBb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC;4DACT,uBAAuB,EAAE,MAAM,CAAC,KAAK;4DACrC,IAAI,gBAAgB,kBAAkB;4DACtC,IAAI,cAAc,gBAAgB;wDACpC;wDACA,WAAW;wDACX,WAAU;wDACV,aAAY;;;;;;oDAIb,mBAAmB,iBAAiB,MAAM,GAAG,mBAC5C,6LAAC;wDAAI,WAAU;kEACZ,iBAAiB,GAAG,CAAC,CAAC,YAAY,sBACjC,6LAAC;gEAEC,MAAK;gEACL,SAAS,IAAM,cAAc;gEAC7B,WAAU;0EAET;+DALI;;;;;;;;;;;;;;;;0DAYf,6LAAC;gDAAE,WAAU;;oDAAgD;oDACC,mBAAmB,2CAA2C;;;;;;;;;;;;;kDAK9H,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC;oDACT,WAAW,EAAE,MAAM,CAAC,KAAK;oDACzB,IAAI,gBAAgB,kBAAkB;oDACtC,IAAI,cAAc,gBAAgB;gDACpC;gDACA,MAAM;gDACN,WAAU;gDACV,aAAY;gDACZ,QAAQ;;;;;;;;;;;;kDAKZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,QAAQ;wDACR,UAAU;wDACV,WAAU;wDACV,IAAG;wDACH,QAAO;;;;;;kEAET,6LAAC;wDACC,SAAQ;wDACR,WAAU;;0EAEV,6LAAC;gEAAE,WAAU;;;;;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;4CAO9D,YAAY,MAAM,GAAG,mBACpB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;;;;;;sFACb,6LAAC;4EAAK,WAAU;sFAA4C,KAAK,IAAI;;;;;;;;;;;;8EAEvE,6LAAC;oEACC,MAAK;oEACL,SAAS,IAAM,iBAAiB;oEAChC,WAAU;8EAEV,cAAA,6LAAC;wEAAE,WAAU;;;;;;;;;;;;2DAVP;;;;;;;;;;;;;;;;;;;;;kDAoBpB,6LAAC;wCACC,SAAS;wCACT,UAAU,CAAC,QAAQ,IAAI,MAAM;wCAC7B,WAAU;kDAET,wBACC;;8DACE,6LAAC;oDAAI,WAAU;;;;;;gDAAuE;;yEAIxF;;8DACE,6LAAC;oDAAE,WAAU;;;;;;gDAA8B;;;;;;;;;;;;;;0CAQnD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,kJAAA,CAAA,UAAe;oCACd,YAAY;oCACZ,UAAU;oCACV,OAAM;oCACN,YAAY;oCACZ,aAAa;oCACb,WAAU;oCACV,gBAAgB;oCAChB,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1B;GA5ZM;;QAqBa,kIAAA,CAAA,UAAO;;;KArBpB;uCA8ZS", "debugId": null}}, {"offset": {"line": 3316, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\nimport LogoutButton from './LogoutButton';\r\nimport { getUserInitials } from '../utils/imageUtils';\r\nimport { useNotifications } from '../hooks/useNotifications';\r\nimport NotificationModal from './notifications/NotificationModal';\r\nimport ActivityNotesModal from './evaluation/ActivityNotesModal';\r\n\r\ninterface HeaderProps {\r\n  activeTab?: string;\r\n  onTabChange?: (tab: string) => void;\r\n  onMobileMenuToggle?: () => void;\r\n}\r\n\r\nconst Header = ({ activeTab = 'overview', onTabChange, onMobileMenuToggle }: HeaderProps) => {\r\n  const { user } = useAuth();\r\n  const { unreadCount } = useNotifications();\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n  const [notificationModalOpen, setNotificationModalOpen] = useState(false);\r\n  const [activityNotesModalOpen, setActivityNotesModalOpen] = useState(false);\r\n  const pathname = usePathname();\r\n\r\n  // Only show dashboard tabs when on dashboard routes\r\n  const showDashboardTabs = pathname.startsWith('/dashboard');\r\n\r\n  // Routes where the global search bar should be hidden\r\n  const hideSearchBarRoutes = [\r\n    '/tasks',\r\n    '/postal',\r\n    '/spectrum',\r\n    '/consumer-affairs',\r\n    '/data-breach',\r\n    '/resources',\r\n    '/procurement',\r\n    '/applications' // This covers broadcasting, standards, telecommunications license types\r\n  ];\r\n\r\n  // Check if current route should hide the search bar\r\n  const shouldHideSearchBar = hideSearchBarRoutes.some(route => pathname.startsWith(route));\r\n\r\n  const toggleDropdown = () => {\r\n    setDropdownOpen(!dropdownOpen);\r\n  };\r\n\r\n  const toggleNotificationModal = () => {\r\n    setNotificationModalOpen(!notificationModalOpen);\r\n  };\r\n\r\n  const toggleActivityNotesModal = () => {\r\n    setActivityNotesModalOpen(!activityNotesModalOpen);\r\n  };\r\n\r\n  const tabs = [\r\n    { id: 'overview', label: 'Overview' },\r\n    { id: 'licenses', label: 'Licenses' },\r\n    { id: 'users', label: 'Users' },\r\n    { id: 'transactions', label: 'Transactions' },\r\n    { id: 'spectrum', label: 'Spectrum' },\r\n    { id: 'compliance', label: 'Compliance' },\r\n  ];\r\n\r\n  return (\r\n    <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n      <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n        <button\r\n          id=\"mobileMenuBtn\"\r\n          type=\"button\"\r\n          onClick={onMobileMenuToggle}\r\n          className=\"md:hidden text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none\"\r\n        >\r\n          <div className=\"w-6 h-6 flex items-center justify-center\">\r\n            <i className=\"ri-menu-line ri-lg\"></i>\r\n          </div>\r\n        </button>\r\n        {!shouldHideSearchBar && (\r\n          <div className=\"flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start\">\r\n            <div className=\"max-w-lg w-full\">\r\n              {/* <label htmlFor=\"search\" className=\"sr-only\">Search</label>\r\n              <div className=\"relative\">\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <div className=\"w-5 h-5 flex items-center justify-center text-gray-400 dark:text-gray-500\">\r\n                    <i className=\"ri-search-line\"></i>\r\n                  </div>\r\n                </div>\r\n                <input\r\n                  id=\"search\"\r\n                  name=\"search\"\r\n                  className=\"block w-full pl-10 pr-3 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm hover:bg-white dark:hover:bg-gray-600 transition-colors\"\r\n                  placeholder=\"Search for licenses, users, or transactions...\"\r\n                  type=\"search\"\r\n                />\r\n              </div> */}\r\n            </div>\r\n          </div>\r\n        )}\r\n        {shouldHideSearchBar && (\r\n          <div className=\"flex-1\"></div>\r\n        )}\r\n        <div className=\"flex items-center\">\r\n          {/* Message Icon */}\r\n          <button\r\n            type=\"button\"\r\n            onClick={toggleActivityNotesModal}\r\n            disabled={!user}\r\n            className={`flex-shrink-0 p-1 mr-4 rounded-full focus:outline-none relative transition-colors duration-200 ${\r\n              user\r\n                ? 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\r\n                : 'text-gray-300 dark:text-gray-600 cursor-not-allowed'\r\n            }`}\r\n            title={user ? \"Send Message\" : \"Loading...\"}\r\n          >\r\n            <span className=\"sr-only\">Send message</span>\r\n            <div className=\"w-6 h-6 flex items-center justify-center\">\r\n              <i className=\"ri-message-3-line ri-lg\"></i>\r\n            </div>\r\n          </button>\r\n\r\n          {/* Notification Icon */}\r\n          <button\r\n            type=\"button\"\r\n            onClick={toggleNotificationModal}\r\n            className=\"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative transition-colors duration-200\"\r\n            title={`Notifications${unreadCount > 0 ? ` (${unreadCount} unread)` : ''}`}\r\n          >\r\n            <span className=\"sr-only\">View notifications</span>\r\n            <div className=\"w-6 h-6 flex items-center justify-center\">\r\n              <i className=\"ri-notification-3-line ri-lg\"></i>\r\n            </div>\r\n            {unreadCount > 0 && (\r\n              <span className=\"absolute -top-1 -right-1 flex items-center justify-center h-5 w-5 text-xs font-bold text-white bg-red-500 rounded-full ring-2 ring-white dark:ring-gray-800\">\r\n                {unreadCount > 99 ? '99+' : unreadCount}\r\n              </span>\r\n            )}\r\n          </button>\r\n          <div className=\"dropdown relative\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleDropdown}\r\n              className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n            >\r\n              <span className=\"sr-only\">Open user menu</span>\r\n              {user?.profile_image ? (\r\n                <img\r\n                  className=\"h-8 w-8 rounded-full object-cover\"\r\n                  src={user.profile_image}\r\n                  alt=\"Profile\"\r\n                  onError={(e) => {\r\n                    // Fallback to initials if image fails to load\r\n                    const target = e.target as HTMLImageElement;\r\n                    target.style.display = 'none';\r\n                    target.nextElementSibling?.classList.remove('hidden');\r\n                  }}\r\n                />\r\n              ) : null}\r\n              <div className={`h-8 w-8 rounded-full bg-red-600 flex items-center justify-center text-white text-sm font-medium ${user?.profile_image ? 'hidden' : ''}`}>\r\n                {user ? getUserInitials(user.first_name, user.last_name) : 'U'}\r\n              </div>\r\n            </button>\r\n            <div\r\n              className={`dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black dark:ring-gray-600 ring-opacity-5 ${\r\n                dropdownOpen ? 'show' : ''\r\n              }`}\r\n            >\r\n              <div className=\"py-1\">\r\n                <Link\r\n                  href=\"/profile\"\r\n                  className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                >\r\n                  Your Profile\r\n                </Link>\r\n                <Link\r\n                  href=\"/settings\"\r\n                  className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                >\r\n                  Settings\r\n                </Link>\r\n                <div className=\"px-4 py-2\">\r\n                  <LogoutButton\r\n                    variant=\"text\"\r\n                    size=\"sm\"\r\n                    className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                    showConfirmation={true}\r\n                    redirectTo=\"/auth/login\"\r\n                  >\r\n                    Sign out\r\n                  </LogoutButton>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      {/* Secondary navigation - only show on dashboard routes */}\r\n      {showDashboardTabs && (\r\n        <div className=\"border-t border-gray-200 dark:border-gray-700 px-4 sm:px-6\">\r\n          <div className=\"py-3 flex space-x-8\">\r\n            {tabs.map((tab) => (\r\n              <button\r\n                key={tab.id}\r\n                type=\"button\"\r\n                onClick={() => {\r\n                  onTabChange?.(tab.id);\r\n                  // Dispatch custom event for dashboard to listen to\r\n                  window.dispatchEvent(new CustomEvent('tabChange', { detail: { tab: tab.id } }));\r\n                }}\r\n                className={`tab-button text-sm px-1 py-2 ${\r\n                  activeTab === tab.id ? 'active' : 'text-gray-500'\r\n                }`}\r\n              >\r\n                {tab.label}\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Notification Modal */}\r\n      <NotificationModal\r\n        isOpen={notificationModalOpen}\r\n        onClose={() => setNotificationModalOpen(false)}\r\n      />\r\n\r\n      {/* Activity Notes Modal */}\r\n      {user && (\r\n        <ActivityNotesModal\r\n          isOpen={activityNotesModalOpen}\r\n          onClose={() => setActivityNotesModalOpen(false)}\r\n          entityId={user.user_id}\r\n          entityType=\"user\"\r\n          applicationNumber=\"\"\r\n          initialEmails=\"\"\r\n          title=\"Send Message to Users\"\r\n          isGeneralMessage={true}\r\n        />\r\n      )}\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAkBA,MAAM,SAAS;QAAC,EAAE,YAAY,UAAU,EAAE,WAAW,EAAE,kBAAkB,EAAe;;IACtF,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,oDAAoD;IACpD,MAAM,oBAAoB,SAAS,UAAU,CAAC;IAE9C,sDAAsD;IACtD,MAAM,sBAAsB;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA;QACA,gBAAgB,wEAAwE;KACzF;IAED,oDAAoD;IACpD,MAAM,sBAAsB,oBAAoB,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC;IAElF,MAAM,iBAAiB;QACrB,gBAAgB,CAAC;IACnB;IAEA,MAAM,0BAA0B;QAC9B,yBAAyB,CAAC;IAC5B;IAEA,MAAM,2BAA2B;QAC/B,0BAA0B,CAAC;IAC7B;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;QAAW;QACpC;YAAE,IAAI;YAAY,OAAO;QAAW;QACpC;YAAE,IAAI;YAAS,OAAO;QAAQ;QAC9B;YAAE,IAAI;YAAgB,OAAO;QAAe;QAC5C;YAAE,IAAI;YAAY,OAAO;QAAW;QACpC;YAAE,IAAI;YAAc,OAAO;QAAa;KACzC;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,IAAG;wBACH,MAAK;wBACL,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;oBAGhB,CAAC,qCACA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;oBAmBlB,qCACC,6LAAC;wBAAI,WAAU;;;;;;kCAEjB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU,CAAC;gCACX,WAAW,AAAC,kGAIX,OAHC,OACI,kFACA;gCAEN,OAAO,OAAO,iBAAiB;;kDAE/B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;;0CAKjB,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO,AAAC,gBAAiE,OAAlD,cAAc,IAAI,AAAC,KAAgB,OAAZ,aAAY,cAAY;;kDAEtE,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;oCAEd,cAAc,mBACb,6LAAC;wCAAK,WAAU;kDACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;0CAIlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,CAAA,iBAAA,2BAAA,KAAM,aAAa,kBAClB,6LAAC;gDACC,WAAU;gDACV,KAAK,KAAK,aAAa;gDACvB,KAAI;gDACJ,SAAS,CAAC;wDAIR;oDAHA,8CAA8C;oDAC9C,MAAM,SAAS,EAAE,MAAM;oDACvB,OAAO,KAAK,CAAC,OAAO,GAAG;qDACvB,6BAAA,OAAO,kBAAkB,cAAzB,iDAAA,2BAA2B,SAAS,CAAC,MAAM,CAAC;gDAC9C;;;;;2FAEA;0DACJ,6LAAC;gDAAI,WAAW,AAAC,mGAAsI,OAApC,CAAA,iBAAA,2BAAA,KAAM,aAAa,IAAG,WAAW;0DACjJ,OAAO,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,SAAS,IAAI;;;;;;;;;;;;kDAG/D,6LAAC;wCACC,WAAW,AAAC,iIAEX,OADC,eAAe,SAAS;kDAG1B,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qIAAA,CAAA,UAAY;wDACX,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,kBAAkB;wDAClB,YAAW;kEACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUZ,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC;4BAEC,MAAK;4BACL,SAAS;gCACP,wBAAA,kCAAA,YAAc,IAAI,EAAE;gCACpB,mDAAmD;gCACnD,OAAO,aAAa,CAAC,IAAI,YAAY,aAAa;oCAAE,QAAQ;wCAAE,KAAK,IAAI,EAAE;oCAAC;gCAAE;4BAC9E;4BACA,WAAW,AAAC,gCAEX,OADC,cAAc,IAAI,EAAE,GAAG,WAAW;sCAGnC,IAAI,KAAK;2BAXL,IAAI,EAAE;;;;;;;;;;;;;;;0BAmBrB,6LAAC,2JAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,yBAAyB;;;;;;YAIzC,sBACC,6LAAC,yJAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,0BAA0B;gBACzC,UAAU,KAAK,OAAO;gBACtB,YAAW;gBACX,mBAAkB;gBAClB,eAAc;gBACd,OAAM;gBACN,kBAAkB;;;;;;;;;;;;AAK5B;GA/NM;;QACa,kIAAA,CAAA,UAAO;QACA,mIAAA,CAAA,mBAAgB;QAIvB,qIAAA,CAAA,cAAW;;;KANxB;uCAiOS", "debugId": null}}, {"offset": {"line": 3729, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/NavItem.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\n\r\ninterface NavItemProps {\r\n  href: string;\r\n  icon: string;\r\n  label: string;\r\n  isActive?: boolean;\r\n  onClick?: () => void;\r\n  badge?: number | string;\r\n  badgeColor?: 'red' | 'blue' | 'green' | 'yellow' | 'gray';\r\n}\r\n\r\nconst NavItem: React.FC<NavItemProps> = ({\r\n  href,\r\n  icon,\r\n  label,\r\n  isActive = false,\r\n  onClick,\r\n  badge,\r\n  badgeColor = 'red'\r\n}) => {\r\n  const { showLoader } = useLoading();\r\n\r\n  const handleClick = () => {\r\n    // Show loader with specific message based on the page\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/dashboard': 'Loading Dashboard...',\r\n      '/applications/telecommunications': 'Loading Telecommunications...',\r\n      '/applications/postal': 'Loading Postal Services...',\r\n      '/applications/standards': 'Loading Standards...',\r\n      '/applications/clf': 'Loading CLF...',\r\n      '/resources': 'Loading Resources...',\r\n      '/procurement': 'Loading Procurement...',\r\n      '/spectrum': 'Loading Spectrum Management...',\r\n      '/financial': 'Loading Financial Data...',\r\n      '/reports': 'Loading Reports...',\r\n      '/users': 'Loading User Management...',\r\n      '/tasks': 'Loading Tasks...',\r\n      '/audit-trail': 'Loading Audit Trail...',\r\n      '/help': 'Loading Help & Support...'\r\n    };\r\n\r\n    const message = pageMessages[href] || 'Loading page...';\r\n    showLoader(message);\r\n\r\n    if (onClick) {\r\n      onClick();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      onClick={handleClick}\r\n      className={`\r\n        flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200\r\n        ${isActive\r\n          ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n        }\r\n      `}\r\n    >\r\n      <div className={`w-5 h-5 flex items-center justify-center mr-3 ${isActive ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n        <i className={icon}></i>\r\n      </div>\r\n      <span className=\"flex-1\">{label}</span>\r\n      {badge !== undefined && badge !== null && badge !== 0 && (\r\n        <span className={`\r\n          inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full\r\n          ${badgeColor === 'red' ? 'text-white bg-red-500' : ''}\r\n          ${badgeColor === 'blue' ? 'text-white bg-blue-500' : ''}\r\n          ${badgeColor === 'green' ? 'text-white bg-green-500' : ''}\r\n          ${badgeColor === 'yellow' ? 'text-yellow-800 bg-yellow-200' : ''}\r\n          ${badgeColor === 'gray' ? 'text-gray-800 bg-gray-200' : ''}\r\n        `}>\r\n          {typeof badge === 'number' && badge > 99 ? '99+' : badge}\r\n        </span>\r\n      )}\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default NavItem;"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAgBA,MAAM,UAAkC;QAAC,EACvC,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,WAAW,KAAK,EAChB,OAAO,EACP,KAAK,EACL,aAAa,KAAK,EACnB;;IACC,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAEhC,MAAM,cAAc;QAClB,sDAAsD;QACtD,MAAM,eAA0C;YAC9C,cAAc;YACd,oCAAoC;YACpC,wBAAwB;YACxB,2BAA2B;YAC3B,qBAAqB;YACrB,cAAc;YACd,gBAAgB;YAChB,aAAa;YACb,cAAc;YACd,YAAY;YACZ,UAAU;YACV,UAAU;YACV,gBAAgB;YAChB,SAAS;QACX;QAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI;QACtC,WAAW;QAEX,IAAI,SAAS;YACX;QACF;IACF;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,SAAS;QACT,WAAW,AAAC,kHAKT,OAHC,WACE,8GACA,yHACH;;0BAGH,6LAAC;gBAAI,WAAW,AAAC,iDAAiG,OAAjD,WAAW,mCAAmC;0BAC7G,cAAA,6LAAC;oBAAE,WAAW;;;;;;;;;;;0BAEhB,6LAAC;gBAAK,WAAU;0BAAU;;;;;;YACzB,UAAU,aAAa,UAAU,QAAQ,UAAU,mBAClD,6LAAC;gBAAK,WAAW,AAAC,wHAGd,OADA,eAAe,QAAQ,0BAA0B,IAAG,gBAEpD,OADA,eAAe,SAAS,2BAA2B,IAAG,gBAEtD,OADA,eAAe,UAAU,4BAA4B,IAAG,gBAExD,OADA,eAAe,WAAW,kCAAkC,IAAG,gBACN,OAAzD,eAAe,SAAS,8BAA8B,IAAG;0BAE1D,OAAO,UAAU,YAAY,QAAQ,KAAK,QAAQ;;;;;;;;;;;;AAK7D;GApEM;;QASmB,qIAAA,CAAA,aAAU;;;KAT7B;uCAsES", "debugId": null}}, {"offset": {"line": 3828, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/task-assignment.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { AssignApplicationRequest, AssignTaskDto, CreateTaskDto, PaginatedResponse, PaginateQuery, Task, TaskFilters, TaskStats, UpdateTaskDto } from '@/types';\r\n\r\n\r\nexport const taskService = {\r\n  // Main task CRUD operations\r\n  getTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {\r\n    // Handle assignment_status filter by using different endpoints or filters\r\n    if (params?.assignment_status === 'unassigned') {\r\n      // Use the unassigned tasks endpoint\r\n      return taskService.getUnassignedTasks(params);\r\n    } else if (params?.assignment_status === 'assigned') {\r\n      // Use the assigned tasks endpoint instead of recursive call\r\n      return taskService.getAssignedTasks(params);\r\n    }\r\n\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    // Use nestjs-paginate filter format: filter.columnName=value\r\n    if (params?.task_type) searchParams.append('filter.task_type', params.task_type);\r\n    if (params?.status) searchParams.append('filter.status', params.status);\r\n    if (params?.priority) searchParams.append('filter.priority', params.priority);\r\n    if (params?.assigned_to) searchParams.append('filter.assigned_to', params.assigned_to);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks${queryString ? `?${queryString}` : ''}`;\r\n\r\n    console.log('🔗 Making API call to:', url);\r\n    console.log('📊 Request params:', params);\r\n\r\n    const response = await apiClient.get(url);\r\n    console.log('✅ API response:', response.data);\r\n    return response.data;\r\n  },\r\n\r\n  getUnassignedTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    // Use nestjs-paginate filter format: filter.columnName=value\r\n    if (params?.task_type) searchParams.append('filter.task_type', params.task_type);\r\n    if (params?.status) searchParams.append('filter.status', params.status);\r\n    if (params?.priority) searchParams.append('filter.priority', params.priority);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks/unassigned${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  getAssignedTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    // Use nestjs-paginate filter format: filter.columnName=value\r\n    if (params?.task_type) searchParams.append('filter.task_type', params.task_type);\r\n    if (params?.status) searchParams.append('filter.status', params.status);\r\n    if (params?.priority) searchParams.append('filter.priority', params.priority);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks/assigned${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  getMyTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n    if (params?.task_type) searchParams.append('task_type', params.task_type);\r\n    if (params?.status) searchParams.append('status', params.status);\r\n    if (params?.priority) searchParams.append('priority', params.priority);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks/assigned/me${queryString ? `?${queryString}` : ''}`;\r\n    const response = await apiClient.get(url);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  getTaskStats: async (): Promise<TaskStats> => {\r\n    const response = await apiClient.get('/tasks/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  getTask: async (taskId: string): Promise<Task> => {\r\n    const response = await apiClient.get(`/tasks/${taskId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  createTask: async (taskData: CreateTaskDto): Promise<Task> => {\r\n    const response = await apiClient.post('/tasks', taskData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  updateTask: async (taskId: string, taskData: UpdateTaskDto): Promise<Task> => {\r\n    const response = await apiClient.patch(`/tasks/${taskId}`, taskData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  getTaskForApplication: async (applicationId: string): Promise<Task | null> => {\r\n    try {\r\n      const response = await apiClient.get(`/tasks/application/${applicationId}`);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      if (error.response?.status === 404) {\r\n        return null; // No task found for application\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  assignTask: async (taskId: string, assignData: AssignTaskDto): Promise<Task> => {\r\n    const response = await apiClient.put(`/tasks/${taskId}/assign`, assignData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  reassignTask: async (taskId: string, assignData: AssignTaskDto): Promise<Task> => {\r\n    const response = await apiClient.put(`/tasks/${taskId}/reassign`, assignData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  deleteTask: async (taskId: string): Promise<void> => {\r\n    await apiClient.delete(`/tasks/${taskId}`);\r\n  },\r\n\r\n  // Get users for assignment (officers only, exclude customers)\r\n  getUsers: async (): Promise<any> => {\r\n    try {\r\n      const response = await apiClient.get('/users', {\r\n        params: {\r\n          limit: 100,\r\n          filter: {\r\n            exclude_customers: true\r\n          }\r\n        }\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching users:', error);\r\n    }\r\n  },\r\n\r\n  // Get officers specifically (non-customer users)\r\n  getOfficers: async (): Promise<any> => {\r\n    try {\r\n      const response = await apiClient.get('/users/list/officers');\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error fetching officers:', error);\r\n      // Fallback to regular users endpoint with filtering\r\n      try {\r\n        const fallbackResponse = await apiClient.get('/users', {\r\n          params: {\r\n            limit: 100,\r\n            filter: {\r\n              exclude_customers: true\r\n            }\r\n          }\r\n        });\r\n        return processApiResponse(fallbackResponse);\r\n      } catch (fallbackError) {\r\n        console.error('Error fetching users as fallback:', fallbackError);\r\n      }\r\n    }\r\n  },\r\n};\r\n\r\n// Legacy service for backward compatibility\r\nexport const taskAssignmentService = {\r\n  // Generic task management methods\r\n  getUnassignedTasks: taskService.getUnassignedTasks,\r\n  getAssignedTasks: taskService.getAssignedTasks,\r\n  assignTask: taskService.assignTask,\r\n  getTaskById: taskService.getTask,\r\n\r\n  // Legacy application-specific methods (for backward compatibility)\r\n  getUnassignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications/unassigned${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get all applications (including assigned)\r\n  getAllApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications assigned to current user\r\n  getMyAssignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications/assigned/me${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get officers for assignment\r\n  getOfficers: async () => {\r\n    try {\r\n      const response = await apiClient.get('/users');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching officers:', error);\r\n      return { data: [] };\r\n    }\r\n  },\r\n\r\n  // Assign application to officer\r\n  assignApplication: async (applicationId: string, assignData: AssignApplicationRequest) => {\r\n    const response = await apiClient.put(`/applications/${applicationId}/assign`, assignData);\r\n    return response.data;\r\n  },\r\n\r\n  // Get application details\r\n  getApplication: async (applicationId: string) => {\r\n    const response = await apiClient.get(`/applications/${applicationId}`);\r\n    return response.data;\r\n  },\r\n};"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAIO,MAAM,cAAc;IACzB,4BAA4B;IAC5B,UAAU,OAAO;QACf,0EAA0E;QAC1E,IAAI,CAAA,mBAAA,6BAAA,OAAQ,iBAAiB,MAAK,cAAc;YAC9C,oCAAoC;YACpC,OAAO,YAAY,kBAAkB,CAAC;QACxC,OAAO,IAAI,CAAA,mBAAA,6BAAA,OAAQ,iBAAiB,MAAK,YAAY;YACnD,4DAA4D;YAC5D,OAAO,YAAY,gBAAgB,CAAC;QACtC;QAEA,MAAM,eAAe,IAAI;QACzB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,6DAA6D;QAC7D,IAAI,mBAAA,6BAAA,OAAQ,SAAS,EAAE,aAAa,MAAM,CAAC,oBAAoB,OAAO,SAAS;QAC/E,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,aAAa,MAAM,CAAC,iBAAiB,OAAO,MAAM;QACtE,IAAI,mBAAA,6BAAA,OAAQ,QAAQ,EAAE,aAAa,MAAM,CAAC,mBAAmB,OAAO,QAAQ;QAC5E,IAAI,mBAAA,6BAAA,OAAQ,WAAW,EAAE,aAAa,MAAM,CAAC,sBAAsB,OAAO,WAAW;QAErF,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,AAAC,SAA6C,OAArC,cAAc,AAAC,IAAe,OAAZ,eAAgB;QAEvD,QAAQ,GAAG,CAAC,0BAA0B;QACtC,QAAQ,GAAG,CAAC,sBAAsB;QAElC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,QAAQ,GAAG,CAAC,mBAAmB,SAAS,IAAI;QAC5C,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB,OAAO;QACzB,MAAM,eAAe,IAAI;QACzB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,6DAA6D;QAC7D,IAAI,mBAAA,6BAAA,OAAQ,SAAS,EAAE,aAAa,MAAM,CAAC,oBAAoB,OAAO,SAAS;QAC/E,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,aAAa,MAAM,CAAC,iBAAiB,OAAO,MAAM;QACtE,IAAI,mBAAA,6BAAA,OAAQ,QAAQ,EAAE,aAAa,MAAM,CAAC,mBAAmB,OAAO,QAAQ;QAE5E,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,AAAC,oBAAwD,OAArC,cAAc,AAAC,IAAe,OAAZ,eAAgB;QAElE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB,OAAO;QACvB,MAAM,eAAe,IAAI;QACzB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,6DAA6D;QAC7D,IAAI,mBAAA,6BAAA,OAAQ,SAAS,EAAE,aAAa,MAAM,CAAC,oBAAoB,OAAO,SAAS;QAC/E,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,aAAa,MAAM,CAAC,iBAAiB,OAAO,MAAM;QACtE,IAAI,mBAAA,6BAAA,OAAQ,QAAQ,EAAE,aAAa,MAAM,CAAC,mBAAmB,OAAO,QAAQ;QAE5E,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,AAAC,kBAAsD,OAArC,cAAc,AAAC,IAAe,OAAZ,eAAgB;QAEhE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO;QACjB,MAAM,eAAe,IAAI;QACzB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,mBAAA,6BAAA,OAAQ,SAAS,EAAE,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACxE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,mBAAA,6BAAA,OAAQ,QAAQ,EAAE,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ;QAErE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,AAAC,qBAAyD,OAArC,cAAc,AAAC,IAAe,OAAZ,eAAgB;QACnE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,cAAc;QACZ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,UAAgB,OAAP;QAC/C,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,UAAU;QAChD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,AAAC,UAAgB,OAAP,SAAU;QAC3D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB,OAAO;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,sBAAmC,OAAd;YAC3D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;gBACf;YAAJ,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;gBAClC,OAAO,MAAM,gCAAgC;YAC/C;YACA,MAAM;QACR;IACF;IAEA,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,UAAgB,OAAP,QAAO,YAAU;QAChE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,cAAc,OAAO,QAAgB;QACnC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,UAAgB,OAAP,QAAO,cAAY;QAClE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,YAAY,OAAO;QACjB,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,UAAgB,OAAP;IACnC;IAEA,8DAA8D;IAC9D,UAAU;QACR,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,UAAU;gBAC7C,QAAQ;oBACN,OAAO;oBACP,QAAQ;wBACN,mBAAmB;oBACrB;gBACF;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,iDAAiD;IACjD,aAAa;QACX,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,oDAAoD;YACpD,IAAI;gBACF,MAAM,mBAAmB,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,UAAU;oBACrD,QAAQ;wBACN,OAAO;wBACP,QAAQ;4BACN,mBAAmB;wBACrB;oBACF;gBACF;gBACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAC5B,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,qCAAqC;YACrD;QACF;IACF;AACF;AAGO,MAAM,wBAAwB;IACnC,kCAAkC;IAClC,oBAAoB,YAAY,kBAAkB;IAClD,kBAAkB,YAAY,gBAAgB;IAC9C,YAAY,YAAY,UAAU;IAClC,aAAa,YAAY,OAAO;IAEhC,mEAAmE;IACnE,2BAA2B,OAAO;QAChC,MAAM,eAAe,IAAI;QACzB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,AAAC,2BAA+D,OAArC,cAAc,AAAC,IAAe,OAAZ,eAAgB;QAEzE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,4CAA4C;IAC5C,oBAAoB,OAAO;QACzB,MAAM,eAAe,IAAI;QACzB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,AAAC,gBAAoD,OAArC,cAAc,AAAC,IAAe,OAAZ,eAAgB;QAE9D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,4CAA4C;IAC5C,2BAA2B,OAAO;QAChC,MAAM,eAAe,IAAI;QACzB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,AAAC,4BAAgE,OAArC,cAAc,AAAC,IAAe,OAAZ,eAAgB;QAE1E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,aAAa;QACX,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBAAE,MAAM,EAAE;YAAC;QACpB;IACF;IAEA,gCAAgC;IAChC,mBAAmB,OAAO,eAAuB;QAC/C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAA8B,OAAd,eAAc,YAAU;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAA8B,OAAd;QACtD,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 4055, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/applicant_info.ts"], "sourcesContent": ["/**\r\n * Applicant Information Types\r\n * Consolidated interface definitions for applicant-related data\r\n */\r\n\r\n// Main applicant information interface\r\nexport interface ApplicantInfoData {\r\n  applicantName: string;\r\n  postalPoBox: string;\r\n  postalCity: string;\r\n  postalCountry: string;\r\n  physicalStreet: string;\r\n  physicalCity: string;\r\n  physicalCountry: string;\r\n  telephone: string;\r\n  fax?: string; // Optional field\r\n  email: string;\r\n}\r\n\r\n// Shareholder data interface\r\nexport interface ShareholderData {\r\n  name: string;\r\n  nationality: string;\r\n  address: string;\r\n  shareholding: string;\r\n  shares?: number; // Optional for backward compatibility\r\n  percentage?: number; // Optional for backward compatibility\r\n}\r\n\r\n// Director data interface\r\nexport interface DirectorData {\r\n  name: string;\r\n  nationality: string;\r\n  address: string;\r\n  position?: string; // Optional for backward compatibility\r\n  appointmentDate?: string; // Optional for backward compatibility\r\n}\r\n\r\n// Company profile data interface\r\nexport interface CompanyProfileData {\r\n  shareholders: ShareholderData[];\r\n  directors: DirectorData[];\r\n  foreignOwnership: string;\r\n  businessRegistrationNo: string;\r\n  tpin: string;\r\n  website: string;\r\n  dateOfIncorporation: string;\r\n  placeOfIncorporation: string;\r\n}\r\n\r\n// Management data interface\r\nexport interface ManagementData {\r\n  ceoName: string;\r\n  ceoQualifications: string;\r\n  ceoExperience: string;\r\n  technicalManagerName: string;\r\n  technicalManagerQualifications: string;\r\n  technicalManagerExperience: string;\r\n  organizationalStructure: string;\r\n}\r\n\r\n// Professional services data interface\r\nexport interface ProfessionalServicesData {\r\n  auditorsName: string;\r\n  auditorsAddress: string;\r\n  lawyersName: string;\r\n  lawyersAddress: string;\r\n  bankersName: string;\r\n  bankersAddress: string;\r\n}\r\n\r\n// Business information data interface\r\nexport interface BusinessInfoData {\r\n  businessDescription: string;\r\n  operationalAreas: string;\r\n  facilities: string;\r\n  equipment: string;\r\n  businessModel: string;\r\n}\r\n\r\n// Service scope data interface\r\nexport interface ServiceScopeData {\r\n  servicesOffered: string;\r\n  targetMarket: string;\r\n  geographicCoverage: string;\r\n  serviceStandards: string;\r\n}\r\n\r\n// Business plan data interface\r\nexport interface BusinessPlanData {\r\n  marketAnalysis: string;\r\n  financialProjections: string;\r\n  competitiveAdvantage: string;\r\n  riskAssessment: string;\r\n  implementationTimeline: string;\r\n}\r\n\r\n// Legal history data interface\r\nexport interface LegalHistoryData {\r\n  previousViolations: string;\r\n  courtCases: string;\r\n  regulatoryHistory: string;\r\n  complianceRecord: string;\r\n}\r\n\r\n// Complete application form data interface\r\nexport interface ApplicationFormData {\r\n  applicantInfo: ApplicantInfoData;\r\n  companyProfile: CompanyProfileData;\r\n  management: ManagementData;\r\n  professionalServices: ProfessionalServicesData;\r\n  businessInfo: BusinessInfoData;\r\n  serviceScope: ServiceScopeData;\r\n  businessPlan: BusinessPlanData;\r\n  legalHistory: LegalHistoryData;\r\n}\r\n\r\n// Component props interface for application form components\r\nexport interface ApplicationFormComponentProps {\r\n  data: any;\r\n  onChange: (data: any) => void;\r\n  errors?: Record<string, string>;\r\n  disabled?: boolean;\r\n}\r\n\r\n// Form field validation interface\r\nexport interface FieldValidation {\r\n  required?: boolean;\r\n  minLength?: number;\r\n  maxLength?: number;\r\n  pattern?: RegExp;\r\n  custom?: (value: any) => string | null;\r\n}\r\n\r\n// Form validation schema interface\r\nexport interface ValidationSchema {\r\n  [fieldName: string]: FieldValidation;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,uCAAuC", "debugId": null}}, {"offset": {"line": 4070, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/base.ts"], "sourcesContent": ["/**\r\n * Base Types\r\n * Common interfaces and types used across the application\r\n */\r\n\r\n// Base entity interface\r\nexport interface BaseEntity {\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string;\r\n  created_by?: string;\r\n  updated_by?: string;\r\n}\r\n\r\n// User reference interface\r\nexport interface UserReference {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n}\r\n\r\n// Pagination query interface\r\nexport interface PaginateQuery {\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: string;\r\n  sortOrder?: 'ASC' | 'DESC';\r\n  search?: string;\r\n  filter?: Record<string, any>;\r\n}\r\n\r\n// API response wrapper\r\nexport interface ApiResponse<T = any> {\r\n  success: boolean;\r\n  message: string;\r\n  data?: T;\r\n  error?: string;\r\n  timestamp: string;\r\n  path: string;\r\n  statusCode: number;\r\n}\r\n\r\n// File upload interface\r\nexport interface FileUpload {\r\n  file: File;\r\n  name: string;\r\n  type: string;\r\n  size: number;\r\n}\r\n\r\n\r\n// Contact information interface\r\nexport interface ContactInfo {\r\n  contact_id: string;\r\n  phone: string;\r\n  email: string;\r\n  fax?: string;\r\n  website?: string;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB", "debugId": null}}, {"offset": {"line": 4085, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/shortcode.ts"], "sourcesContent": ["/**\r\n * Shortcode Types and Interfaces\r\n * Based on backend DTOs and entity definitions\r\n */\r\n\r\nimport { Application } from \"./license\";\r\nimport { Organization } from \"./organization\";\r\nimport { User } from \"./user\";\r\n\r\n// Enums matching backend\r\nexport enum ShortcodeCategory {\r\n  RESERVED = 'reserved',\r\n  LIFELINE_SERVICES = 'lifeline_services',\r\n  DATA_VOICE_USSD = 'data_voice_ussd',\r\n  CUSTOMER_CARE = 'customer_care',\r\n  LIFE_AND_SAFETY = 'life_and_safety',\r\n  FUTURE_USE = 'future_use',\r\n}\r\n\r\nexport enum ShortcodeStatus {\r\n  ACTIVE = 'active',\r\n  INACTIVE = 'inactive',\r\n}\r\n\r\nexport enum ShortcodeAudience {\r\n  COMMUNITY = 'community',\r\n  NATIONAL = 'national',\r\n  REGIONAL = 'regional',\r\n  DISTRICT = 'district',\r\n}\r\n\r\n// Base shortcode interface matching the entity\r\nexport interface Shortcode {\r\n  shortcode_id: string;\r\n  shortcode: string;\r\n  application_id?: string;\r\n  assigned_to?: string;\r\n  shortcode_length: number;\r\n  audience: string;\r\n  status: string;\r\n  category: string;\r\n  description?: string;\r\n  notes?: string;\r\n  created_at: Date;\r\n  updated_at: Date;\r\n  deleted_at?: Date;\r\n  created_by: string;\r\n  updated_by?: string;\r\n  creator?: User; // User entity\r\n  updater?: User; // User entity\r\n  application?: Application; // Application entity\r\n  assignee?: Organization; // Organization entity\r\n}\r\n\r\n// Create shortcode DTO interface\r\nexport interface CreateShortcodeDto {\r\n  application_id?: string;\r\n  audience: string;\r\n  status: string;\r\n  category: string;\r\n  description: string;\r\n  notes?: string;\r\n  shortcode_length: number;\r\n}\r\n\r\n// Update shortcode DTO interface\r\nexport interface UpdateShortcodeDto {\r\n  application_id?: string;\r\n  audience?: string;\r\n  status?: string;\r\n  category?: string;\r\n  description?: string;\r\n  notes?: string;\r\n  shortcode_length?: number;\r\n  updated_by?: string;\r\n  assigned_to?: string;\r\n  shortcode?: string;\r\n}\r\n\r\n// Shortcode numbering plan category interface\r\nexport interface ShortcodeNumberingCategory {\r\n  category: ShortcodeCategory;\r\n  start: number;\r\n  end: number;\r\n}\r\n\r\n// Shortcode numbering plan length interface\r\nexport interface ShortcodeNumberingPlan {\r\n  length: number;\r\n  categories: ShortcodeNumberingCategory[];\r\n}\r\n\r\n// Shortcode filters for API queries\r\nexport interface ShortcodeFilters {\r\n  status?: ShortcodeStatus;\r\n  category?: ShortcodeCategory;\r\n  audience?: ShortcodeAudience;\r\n  assigned_to?: string;\r\n  application_id?: string;\r\n  search?: string;\r\n}\r\n\r\n// Form data interface for the ShortCodeUsage component\r\nexport interface ShortcodeFormData {\r\n  audience: ShortcodeAudience;\r\n  category: ShortcodeCategory;\r\n  description: string;\r\n  shortcode_length?: 3 | 4;\r\n  notes?: string;\r\n}\r\n\r\n// API response interfaces\r\nexport interface ShortcodeApiResponse {\r\n  success: boolean;\r\n  message: string;\r\n  data: Shortcode;\r\n  timestamp: string;\r\n  path: string;\r\n  statusCode: number;\r\n}\r\n\r\nexport interface ShortcodeListApiResponse {\r\n  success: boolean;\r\n  message: string;\r\n  data: Shortcode[];\r\n  timestamp: string;\r\n  path: string;\r\n  statusCode: number;\r\n}\r\n\r\n// Category display options for forms\r\nexport const SHORTCODE_CATEGORY_OPTIONS = [\r\n  { value: ShortcodeCategory.RESERVED, label: 'Reserved' },\r\n  { value: ShortcodeCategory.LIFELINE_SERVICES, label: 'Lifeline Services' },\r\n  { value: ShortcodeCategory.DATA_VOICE_USSD, label: 'Data/Voice/USSD' },\r\n  { value: ShortcodeCategory.CUSTOMER_CARE, label: 'Customer Care' },\r\n  { value: ShortcodeCategory.LIFE_AND_SAFETY, label: 'Life and Safety' },\r\n  { value: ShortcodeCategory.FUTURE_USE, label: 'Future Use' },\r\n];\r\n\r\n// Audience display options for forms\r\nexport const SHORTCODE_AUDIENCE_OPTIONS = [\r\n  { value: ShortcodeAudience.COMMUNITY, label: 'Community' },\r\n  { value: ShortcodeAudience.NATIONAL, label: 'National' },\r\n  { value: ShortcodeAudience.REGIONAL, label: 'Regional' },\r\n  { value: ShortcodeAudience.DISTRICT, label: 'District' },\r\n];\r\n\r\n\r\n\r\n// Status display options (for admin use)\r\nexport const SHORTCODE_STATUS_OPTIONS = [\r\n  { value: ShortcodeStatus.ACTIVE, label: 'Active' },\r\n  { value: ShortcodeStatus.INACTIVE, label: 'Inactive' },\r\n];\r\n\r\n// Validation rules\r\nexport const SHORTCODE_VALIDATION = {\r\n  DESCRIPTION_MAX_LENGTH: 255,\r\n  NOTES_MAX_LENGTH: 255,\r\n  SHORTCODE_MIN_LENGTH: 3,\r\n  SHORTCODE_MAX_LENGTH: 4,\r\n};\r\n\r\n// Helper functions\r\nexport const getShortcodeCategoryLabel = (category: string): string => {\r\n  const option = SHORTCODE_CATEGORY_OPTIONS.find(opt => opt.value === category);\r\n  return option?.label || category;\r\n};\r\n\r\nexport const getShortcodeAudienceLabel = (audience: string): string => {\r\n  const option = SHORTCODE_AUDIENCE_OPTIONS.find(opt => opt.value === audience);\r\n  return option?.label || audience;\r\n};\r\n\r\nexport const getShortcodeStatusLabel = (status: string): string => {\r\n  const option = SHORTCODE_STATUS_OPTIONS.find(opt => opt.value === status);\r\n  return option?.label || status;\r\n};\r\n\r\n// Shortcode numbering plan (from backend)\r\nexport const shortCodeNumbering: ShortcodeNumberingPlan[] = [\r\n  {\r\n    length: 3,\r\n    categories: [\r\n      { category: ShortcodeCategory.CUSTOMER_CARE, start: 100, end: 109 },\r\n      { category: ShortcodeCategory.LIFELINE_SERVICES, start: 110, end: 119 },\r\n      { category: ShortcodeCategory.RESERVED, start: 120, end: 199 },\r\n      { category: ShortcodeCategory.RESERVED, start: 200, end: 299 },\r\n      { category: ShortcodeCategory.DATA_VOICE_USSD, start: 300, end: 499 },\r\n      { category: ShortcodeCategory.FUTURE_USE, start: 500, end: 899 },\r\n      { category: ShortcodeCategory.LIFE_AND_SAFETY, start: 900, end: 999 },\r\n    ]\r\n  },\r\n  {\r\n    length: 4,\r\n    categories: [\r\n      { category: ShortcodeCategory.RESERVED, start: 1000, end: 1099 },\r\n      { category: ShortcodeCategory.LIFELINE_SERVICES, start: 1100, end: 1199 },\r\n      { category: ShortcodeCategory.RESERVED, start: 1200, end: 1999 },\r\n      { category: ShortcodeCategory.RESERVED, start: 2000, end: 2999 },\r\n      { category: ShortcodeCategory.DATA_VOICE_USSD, start: 3000, end: 3999 },\r\n      { category: ShortcodeCategory.DATA_VOICE_USSD, start: 4000, end: 4999 },\r\n      { category: ShortcodeCategory.RESERVED, start: 5000, end: 8999 }\r\n    ]\r\n  }\r\n];\r\n\r\n// Default form values\r\nexport const DEFAULT_SHORTCODE_FORM_DATA: ShortcodeFormData = {\r\n  audience: ShortcodeAudience.COMMUNITY,\r\n  category: ShortcodeCategory.RESERVED,\r\n  description: '',\r\n  notes: '',\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;AAOM,IAAA,AAAK,2CAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,yCAAA;;;WAAA;;AAKL,IAAA,AAAK,2CAAA;;;;;WAAA;;AA2GL,MAAM,6BAA6B;IACxC;QAAE,KAAK;QAA8B,OAAO;IAAW;IACvD;QAAE,KAAK;QAAuC,OAAO;IAAoB;IACzE;QAAE,KAAK;QAAqC,OAAO;IAAkB;IACrE;QAAE,KAAK;QAAmC,OAAO;IAAgB;IACjE;QAAE,KAAK;QAAqC,OAAO;IAAkB;IACrE;QAAE,KAAK;QAAgC,OAAO;IAAa;CAC5D;AAGM,MAAM,6BAA6B;IACxC;QAAE,KAAK;QAA+B,OAAO;IAAY;IACzD;QAAE,KAAK;QAA8B,OAAO;IAAW;IACvD;QAAE,KAAK;QAA8B,OAAO;IAAW;IACvD;QAAE,KAAK;QAA8B,OAAO;IAAW;CACxD;AAKM,MAAM,2BAA2B;IACtC;QAAE,KAAK;QAA0B,OAAO;IAAS;IACjD;QAAE,KAAK;QAA4B,OAAO;IAAW;CACtD;AAGM,MAAM,uBAAuB;IAClC,wBAAwB;IACxB,kBAAkB;IAClB,sBAAsB;IACtB,sBAAsB;AACxB;AAGO,MAAM,4BAA4B,CAAC;IACxC,MAAM,SAAS,2BAA2B,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;IACpE,OAAO,CAAA,mBAAA,6BAAA,OAAQ,KAAK,KAAI;AAC1B;AAEO,MAAM,4BAA4B,CAAC;IACxC,MAAM,SAAS,2BAA2B,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;IACpE,OAAO,CAAA,mBAAA,6BAAA,OAAQ,KAAK,KAAI;AAC1B;AAEO,MAAM,0BAA0B,CAAC;IACtC,MAAM,SAAS,yBAAyB,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;IAClE,OAAO,CAAA,mBAAA,6BAAA,OAAQ,KAAK,KAAI;AAC1B;AAGO,MAAM,qBAA+C;IAC1D;QACE,QAAQ;QACR,YAAY;YACV;gBAAE,QAAQ;gBAAmC,OAAO;gBAAK,KAAK;YAAI;YAClE;gBAAE,QAAQ;gBAAuC,OAAO;gBAAK,KAAK;YAAI;YACtE;gBAAE,QAAQ;gBAA8B,OAAO;gBAAK,KAAK;YAAI;YAC7D;gBAAE,QAAQ;gBAA8B,OAAO;gBAAK,KAAK;YAAI;YAC7D;gBAAE,QAAQ;gBAAqC,OAAO;gBAAK,KAAK;YAAI;YACpE;gBAAE,QAAQ;gBAAgC,OAAO;gBAAK,KAAK;YAAI;YAC/D;gBAAE,QAAQ;gBAAqC,OAAO;gBAAK,KAAK;YAAI;SACrE;IACH;IACA;QACE,QAAQ;QACR,YAAY;YACV;gBAAE,QAAQ;gBAA8B,OAAO;gBAAM,KAAK;YAAK;YAC/D;gBAAE,QAAQ;gBAAuC,OAAO;gBAAM,KAAK;YAAK;YACxE;gBAAE,QAAQ;gBAA8B,OAAO;gBAAM,KAAK;YAAK;YAC/D;gBAAE,QAAQ;gBAA8B,OAAO;gBAAM,KAAK;YAAK;YAC/D;gBAAE,QAAQ;gBAAqC,OAAO;gBAAM,KAAK;YAAK;YACtE;gBAAE,QAAQ;gBAAqC,OAAO;gBAAM,KAAK;YAAK;YACtE;gBAAE,QAAQ;gBAA8B,OAAO;gBAAM,KAAK;YAAK;SAChE;IACH;CACD;AAGM,MAAM,8BAAiD;IAC5D,QAAQ;IACR,QAAQ;IACR,aAAa;IACb,OAAO;AACT", "debugId": null}}, {"offset": {"line": 4292, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4303, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4314, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4325, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4336, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4347, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4358, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/public.ts"], "sourcesContent": ["// Public License Verification Types\r\nexport interface PublicLicenseInfo {\r\n  licenseNumber: string;\r\n  licenseType: string;\r\n  status: string;\r\n  issueDate: Date;\r\n  expiryDate: Date;\r\n  organizationName: string;\r\n  isValid: boolean;\r\n  verifiedAt: Date;\r\n}\r\n\r\n// Verification Statistics\r\nexport interface VerificationStats {\r\n  totalLicenses: number;\r\n  activeLicenses: number;\r\n  expiredLicenses: number;\r\n  suspendedLicenses: number;\r\n}\r\n\r\n// License Verification Response\r\nexport interface LicenseVerificationResponse {\r\n  success: boolean;\r\n  data?: PublicLicenseInfo;\r\n  message: string;\r\n  error?: string;\r\n}\r\n\r\n// License Status Check Response\r\nexport interface LicenseStatusResponse {\r\n  success: boolean;\r\n  exists: boolean;\r\n  verifiable: boolean;\r\n  message: string;\r\n}\r\n\r\n// Verification Statistics Response\r\nexport interface VerificationStatsResponse {\r\n  success: boolean;\r\n  data: VerificationStats;\r\n  message: string;\r\n}\r\n\r\n// License Status Enum\r\nexport enum PublicLicenseStatus {\r\n  ACTIVE = 'active',\r\n  EXPIRED = 'expired',\r\n  SUSPENDED = 'suspended',\r\n  REVOKED = 'revoked',\r\n  UNDER_REVIEW = 'under_review',\r\n}\r\n\r\n// Verification Form Data\r\nexport interface VerificationFormData {\r\n  licenseNumber: string;\r\n  verificationCode?: string;\r\n}\r\n\r\n// Verification Error Types\r\nexport enum VerificationErrorType {\r\n  INVALID_FORMAT = 'INVALID_FORMAT',\r\n  INVALID_VERIFICATION_CODE = 'INVALID_VERIFICATION_CODE',\r\n  LICENSE_NOT_FOUND = 'LICENSE_NOT_FOUND',\r\n  VERIFICATION_ERROR = 'VERIFICATION_ERROR',\r\n}\r\n\r\n// License Number Validation\r\nexport interface LicenseNumberValidation {\r\n  isValid: boolean;\r\n  error?: string;\r\n}\r\n\r\n// Verification Code Validation\r\nexport interface VerificationCodeValidation {\r\n  isValid: boolean;\r\n  error?: string;\r\n}\r\n\r\n// Public API Error Response\r\nexport interface PublicApiError {\r\n  success: false;\r\n  message: string;\r\n  error: string;\r\n  statusCode?: number;\r\n}\r\n\r\n// Chart Data for Statistics\r\nexport interface LicenseStatsChartData {\r\n  name: string;\r\n  value: number;\r\n  color: string;\r\n}\r\n\r\n// Verification History (for future use)\r\nexport interface VerificationHistory {\r\n  licenseNumber: string;\r\n  verifiedAt: Date;\r\n  ipAddress?: string;\r\n  userAgent?: string;\r\n}\r\n\r\n// Public Page Meta Data\r\nexport interface PublicPageMeta {\r\n  title: string;\r\n  description: string;\r\n  keywords: string[];\r\n  ogTitle?: string;\r\n  ogDescription?: string;\r\n  ogImage?: string;\r\n}\r\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;;AA4C7B,IAAA,AAAK,6CAAA;;;;;;WAAA;;AAeL,IAAA,AAAK,+CAAA;;;;;WAAA", "debugId": null}}, {"offset": {"line": 4387, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/task.ts"], "sourcesContent": ["import { BaseEntity, EntityType, UserReference } from './index';\r\n\r\nexport enum TaskType {\r\n  APPLICATION = 'application',\r\n  COMPLAINT = 'complaint',\r\n  DATA_BREACH = 'data_breach',\r\n  EVALUATION = 'evaluation',\r\n  INSPECTION = 'inspection',\r\n  DOCUMENT_REVIEW = 'document_review',\r\n  COMPLIANCE_CHECK = 'compliance_check',\r\n  FOLLOW_UP = 'follow_up',\r\n  PAYMENT_VERIFICATION = 'payment_verification',\r\n}\r\n\r\nexport enum TaskStatus {\r\n  PENDING = 'pending',\r\n  IN_PROGRESS = 'in_progress',\r\n  COMPLETED = 'completed',\r\n  CANCELLED = 'cancelled',\r\n  ON_HOLD = 'on_hold'\r\n}\r\n\r\nexport enum TaskPriority {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  URGENT = 'urgent'\r\n}\r\n\r\n\r\n// Task interfaces\r\nexport interface Task extends BaseEntity {\r\n  task_id: string;\r\n  task_number: string;\r\n  task_type: TaskType;\r\n  title: string;\r\n  description?: string;\r\n  status: TaskStatus;\r\n  priority: TaskPriority;\r\n  entity_type?: string;\r\n  entity_id?: string;\r\n  assigned_to?: string;\r\n  assigned_by?: string;\r\n  assigned_at?: string;\r\n  due_date?: string;\r\n  completed_at?: string;\r\n  review_notes?: string;\r\n  metadata?: Record<string, any>;\r\n  \r\n  // Relations\r\n  assignee?: UserReference;\r\n  assigner?: UserReference;\r\n  creator?: UserReference;\r\n  updater?: UserReference;\r\n}\r\n\r\nexport interface CreateTaskDto {\r\n  task_type: TaskType;\r\n  title: string;\r\n  description?: string;\r\n  priority?: string;\r\n  status?: string;\r\n  entity_type?: string;\r\n  entity_id?: string;\r\n  assigned_to?: string;\r\n  due_date?: string;\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\nexport interface UpdateTaskDto {\r\n  title?: string;\r\n  description?: string;\r\n  status?: string;\r\n  priority?: string;\r\n  assigned_to?: string;\r\n  due_date?: string;\r\n  review_notes?: string;\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\nexport interface AssignTaskDto {\r\n  assignedTo: string;\r\n  comment?: string;\r\n  assignment_notes?: string;\r\n  due_date?: string;\r\n  priority?: TaskPriority;\r\n}\r\n\r\nexport interface TaskFilters {\r\n  search?: string;\r\n  status?: TaskStatus;\r\n  priority?: TaskPriority;\r\n  task_type?: TaskType;\r\n  entity_type?: string;\r\n  assigned_to?: string;\r\n  assigned_by?: string;\r\n  assignment_status?: string;\r\n  due_date?: string;\r\n  overdue?: boolean;\r\n}\r\n\r\n// Task responses\r\nexport type TasksResponse = {\r\n  data: Task[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems?: number;\r\n    currentPage?: number;\r\n    totalPages?: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    select: string[];\r\n    filter?: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first?: string;\r\n    previous?: string;\r\n    current: string;\r\n    next?: string;\r\n    last?: string;\r\n  };\r\n};\r\n\r\nexport interface TaskStatistics {\r\n  total: number;\r\n  pending: number;\r\n  in_progress: number;\r\n  completed: number;\r\n  overdue: number;\r\n  by_priority: {\r\n    low: number;\r\n    medium: number;\r\n    high: number;\r\n    urgent: number;\r\n  };\r\n  by_type: Record<TaskType, number>;\r\n}\r\n\r\nexport interface TaskNavigationInfo {\r\n  task: Task;\r\n  canNavigateToEntity: boolean;\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n// User interface for task assignments\r\nexport interface TaskUser {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n}\r\n// Legacy interface for backward compatibility\r\nexport interface GenericTask extends Task {}\r\n\r\n// Legacy interface for backward compatibility\r\nexport interface TaskAssignmentApplication {\r\n  application_id: string;\r\n  application_number: string;\r\n  status: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  applicant: {\r\n    applicant_id: string;\r\n    company_name: string;\r\n    first_name: string;\r\n    last_name: string;\r\n  };\r\n  license_category: {\r\n    license_category_id: string;\r\n    name: string;\r\n    license_type: {\r\n      license_type_id: string;\r\n      name: string;\r\n    };\r\n  };\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  assigned_at?: string;\r\n}\r\n\r\nexport interface TaskAssignmentOfficer {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n  department?: string;\r\n}\r\n\r\n\r\nexport interface UpdateTaskDto {\r\n  title?: string;\r\n  description?: string;\r\n  priority?: string;\r\n  status?: string;\r\n  entity_type?: string;\r\n  entity_id?: string;\r\n  due_date?: string;\r\n  review?: string;\r\n  review_notes?: string;\r\n  completion_notes?: string;\r\n}\r\n\r\nexport interface AssignTaskDto {\r\n  assignedTo: string;\r\n  comment?: string;\r\n}\r\n\r\n\r\n\r\nexport interface TaskStats {\r\n  total: number;\r\n  pending: number;\r\n  in_progress: number;\r\n  completed: number;\r\n  cancelled: number;\r\n  on_hold: number;\r\n  unassigned: number;\r\n  assigned: number;\r\n  overdue: number;\r\n}\r\n\r\n// Legacy interfaces for backward compatibility\r\nexport interface AssignApplicationRequest {\r\n  assignedTo: string;\r\n}"], "names": [], "mappings": ";;;;;AAEO,IAAA,AAAK,kCAAA;;;;;;;;;;WAAA;;AAYL,IAAA,AAAK,oCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,sCAAA;;;;;WAAA", "debugId": null}}, {"offset": {"line": 4428, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/audit.ts"], "sourcesContent": ["import { AxiosError } from \"axios\";\r\nimport { BaseEntity, UserReference } from \".\";\r\n\r\n// Audit enums\r\nexport enum AuditAction {\r\n  CREATE = 'CREATE',\r\n  READ = 'READ',\r\n  UPDATE = 'UPDATE',\r\n  DELETE = 'DELETE',\r\n  LOGIN = 'LOGIN',\r\n  LOGOUT = 'LOGOUT',\r\n  EXPORT = 'EXPORT',\r\n  IMPORT = 'IMPORT',\r\n  APPROVE = 'APPROVE',\r\n  REJECT = 'REJECT',\r\n  SUBMIT = 'SUBMIT',\r\n  ASSIGN = 'ASSIGN',\r\n  COMPLETE = 'COMPLETE'\r\n}\r\n\r\nexport enum AuditModule {\r\n  USER_MANAGEMENT = 'USER_MANAGEMENT',\r\n  APPLICATION_MANAGEMENT = 'APPLICATION_MANAGEMENT',\r\n  PAYMENT_MANAGEMENT = 'PAYMENT_MANAGEMENT',\r\n  INVOICE_MANAGEMENT = 'INVOICE_MANAGEMENT',\r\n  TASK_MANAGEMENT = 'TASK_MANAGEMENT',\r\n  ORGANIZATION_MANAGEMENT = 'ORGA<PERSON>ZATION_MANAGEMENT',\r\n  DEPARTMENT_MANAGEMENT = 'DEPARTMENT_MANAGEMENT',\r\n  LICENSE_MANAGEMENT = 'LICENSE_MANAGEMENT',\r\n  NOTIFICATION_MANAGEMENT = 'NOTIFICATION_MANAGEMENT',\r\n  SYSTEM_SETTINGS = 'SYSTEM_SETTINGS',\r\n  AUTHENTICATION = 'AUTHENTICATION'\r\n}\r\n\r\n// Audit interfaces\r\nexport interface AuditLog extends BaseEntity {\r\n  audit_id: string;\r\n  action: AuditAction;\r\n  module: AuditModule;\r\n  resource_type?: string;\r\n  resource_id?: string;\r\n  description?: string;\r\n  ip_address?: string;\r\n  user_agent?: string;\r\n  metadata?: Record<string, any>;\r\n  old_values?: Record<string, any>;\r\n  new_values?: Record<string, any>;\r\n  \r\n  // Relations\r\n  user?: UserReference;\r\n}\r\n\r\nexport interface CreateAuditLogDto {\r\n  action: AuditAction;\r\n  module: AuditModule;\r\n  resource_type?: string;\r\n  resource_id?: string;\r\n  description?: string;\r\n  metadata?: Record<string, any>;\r\n  old_values?: Record<string, any>;\r\n  new_values?: Record<string, any>;\r\n}\r\n\r\nexport interface AuditLogFilters {\r\n  search?: string;\r\n  action?: AuditAction;\r\n  module?: AuditModule;\r\n  resource_type?: string;\r\n  resource_id?: string;\r\n  user_id?: string;\r\n  date_from?: string;\r\n  date_to?: string;\r\n  ip_address?: string;\r\n}\r\n\r\n// Audit responses\r\nexport type AuditLogsResponse = {\r\n  data: AuditLog[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems?: number;\r\n    currentPage?: number;\r\n    totalPages?: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    select: string[];\r\n    filter?: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first?: string;\r\n    previous?: string;\r\n    current: string;\r\n    next?: string;\r\n    last?: string;\r\n  };\r\n};\r\n\r\nexport interface AuditStatistics {\r\n  total_logs: number;\r\n  actions_summary: Record<AuditAction, number>;\r\n  modules_summary: Record<AuditModule, number>;\r\n  top_users: {\r\n    user_id: string;\r\n    user_name: string;\r\n    action_count: number;\r\n  }[];\r\n  recent_activities: AuditLog[];\r\n}\r\n\r\n\r\n// Error handling utility\r\nexport class AuditTrailError extends Error {\r\n  constructor(\r\n    message: string,\r\n    public code?: string,\r\n    public status?: number,\r\n    public details?: any\r\n  ) {\r\n    super(message);\r\n    this.name = 'AuditTrailError';\r\n  }\r\n}\r\n\r\n// Handle API errors consistently\r\nconst handleApiError = (error: any): never => {\r\n  if (error instanceof AxiosError) {\r\n    const status = error.response?.status;\r\n    const message = error.response?.data?.message || error.message;\r\n    const code = error.code;\r\n\r\n    if (status === 401) {\r\n      throw new AuditTrailError('Authentication required', 'UNAUTHORIZED', status);\r\n    } else if (status === 403) {\r\n      throw new AuditTrailError('Access denied', 'FORBIDDEN', status);\r\n    } else if (status === 404) {\r\n      throw new AuditTrailError('Audit trail not found', 'NOT_FOUND', status);\r\n    } else if (status === 429) {\r\n      throw new AuditTrailError('Too many requests', 'RATE_LIMITED', status);\r\n    } else if (status && status >= 500) {\r\n      throw new AuditTrailError('Server error occurred', 'SERVER_ERROR', status);\r\n    } else if (code === 'ERR_NETWORK' || error.message === 'Network Error') {\r\n      throw new AuditTrailError('Network error - please check your connection', 'NETWORK_ERROR');\r\n    } else {\r\n      throw new AuditTrailError(message || 'An unexpected error occurred', code, status, error.response?.data);\r\n    }\r\n  }\r\n\r\n  throw new AuditTrailError(error.message || 'An unexpected error occurred');\r\n};\r\n\r\nexport interface AuditTrail {\r\n  audit_id: string;\r\n  action: string;\r\n  module: string;\r\n  status: string;\r\n  resource_type: string;\r\n  resource_id?: string;\r\n  description?: string;\r\n  old_values?: Record<string, any>;\r\n  new_values?: Record<string, any>;\r\n  metadata?: Record<string, any>;\r\n  ip_address?: string;\r\n  user_agent?: string;\r\n  session_id?: string;\r\n  error_message?: string;\r\n  user_id?: string;\r\n  user?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  created_at: string;\r\n}\r\n\r\nexport interface AuditTrailFilters {\r\n  dateFrom?: string;\r\n  dateTo?: string;\r\n  userId?: string;\r\n  action?: string;\r\n  module?: string;\r\n  status?: string;\r\n  ipAddress?: string;\r\n  resourceType?: string;\r\n  resourceId?: string;\r\n}\r\n\r\n// Standard API response format (matches backend)\r\nexport interface StandardResponse<T = any> {\r\n  success: boolean;\r\n  message?: string;\r\n  data?: T;\r\n  meta?: any;\r\n  timestamp: string;\r\n  path: string;\r\n  statusCode: number;\r\n}"], "names": [], "mappings": ";;;;;;AAAA;;;AAIO,IAAA,AAAK,qCAAA;;;;;;;;;;;;;;WAAA;;AAgBL,IAAA,AAAK,qCAAA;;;;;;;;;;;;WAAA;;AA4FL,MAAM,wBAAwB;IACnC,YACE,OAAe,EACf,AAAO,IAAa,EACpB,AAAO,MAAe,EACtB,AAAO,OAAa,CACpB;QACA,KAAK,CAAC,olBAJC,OAAA,WACA,SAAA,aACA,UAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA,iCAAiC;AACjC,MAAM,iBAAiB,CAAC;IACtB,IAAI,iBAAiB,iJAAA,CAAA,aAAU,EAAE;YAChB,iBACC,sBAAA;QADhB,MAAM,UAAS,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM;QACrC,MAAM,UAAU,EAAA,mBAAA,MAAM,QAAQ,cAAd,wCAAA,uBAAA,iBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI,MAAM,OAAO;QAC9D,MAAM,OAAO,MAAM,IAAI;QAEvB,IAAI,WAAW,KAAK;YAClB,MAAM,IAAI,gBAAgB,2BAA2B,gBAAgB;QACvE,OAAO,IAAI,WAAW,KAAK;YACzB,MAAM,IAAI,gBAAgB,iBAAiB,aAAa;QAC1D,OAAO,IAAI,WAAW,KAAK;YACzB,MAAM,IAAI,gBAAgB,yBAAyB,aAAa;QAClE,OAAO,IAAI,WAAW,KAAK;YACzB,MAAM,IAAI,gBAAgB,qBAAqB,gBAAgB;QACjE,OAAO,IAAI,UAAU,UAAU,KAAK;YAClC,MAAM,IAAI,gBAAgB,yBAAyB,gBAAgB;QACrE,OAAO,IAAI,SAAS,iBAAiB,MAAM,OAAO,KAAK,iBAAiB;YACtE,MAAM,IAAI,gBAAgB,gDAAgD;QAC5E,OAAO;gBAC8E;YAAnF,MAAM,IAAI,gBAAgB,WAAW,gCAAgC,MAAM,SAAQ,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,IAAI;QACzG;IACF;IAEA,MAAM,IAAI,gBAAgB,MAAM,OAAO,IAAI;AAC7C", "debugId": null}}, {"offset": {"line": 4508, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4519, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4530, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4541, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/address_types.ts"], "sourcesContent": ["import { BaseEntity, UserReference } from './index';\r\n\r\n// Core Address interface based on backend entity\r\n\r\n// Address interface\r\nexport interface AddressEntity {\r\n  address_id: string;\r\n  street: string;\r\n  city: string;\r\n  state?: string;\r\n  postal_code: string;\r\n  country: string;\r\n  address_type: 'physical' | 'postal';\r\n}\r\nexport interface Address extends AddressEntity {\r\n  address_id: string;\r\n  entity_type?: string;\r\n  entity_id?: string;\r\n  address_line_1: string;\r\n  address_line_2?: string;\r\n  address_line_3?: string;\r\n  postal_code: string;\r\n  country: string;\r\n  city: string;\r\n  deleted_at?: string;\r\n\r\n  // Related data\r\n  creator?: UserReference;\r\n  updater?: UserReference;\r\n}\r\n\r\nexport interface PostalCodeData {\r\n  postal_code_id: string;\r\n  region: string;\r\n  district: string;\r\n  location: string;\r\n  postal_code: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string | null;\r\n}\r\n\r\n\r\nexport interface SequentialAddressData {\r\n  country: string;\r\n  region: string;\r\n  district: string;\r\n  location: string;\r\n  postal_code: string;\r\n  address_line_1: string;\r\n  address_line_2: string;\r\n}\r\n\r\n\r\nexport interface SequentialAddressBuilderProps {\r\n  onAddressComplete: (address: SequentialAddressData) => void;\r\n  onAddressChange?: (address: Partial<SequentialAddressData>) => void;\r\n  initialData?: Partial<SequentialAddressData>;\r\n  disabled?: boolean;\r\n  className?: string;\r\n  // Additional props for form integration\r\n  onFieldChange?: (field: string, value: string) => void;\r\n  validationErrors?: Record<string, string>;\r\n}\r\n\r\n\r\n// DTO interfaces for API operations\r\nexport interface CreateAddressDto {\r\n  address_type?: string;\r\n  entity_type?: string;\r\n  entity_id?: string;\r\n  address_line_1: string;\r\n  address_line_2?: string;\r\n  address_line_3?: string;\r\n  postal_code: string;\r\n  country: string;\r\n  city: string;\r\n}\r\n\r\nexport interface UpdateAddressDto {\r\n  address_type?: string;\r\n  entity_type?: string;\r\n  entity_id?: string;\r\n  address_line_1?: string;\r\n  address_line_2?: string;\r\n  address_line_3?: string;\r\n  postal_code?: string;\r\n  country?: string;\r\n  city?: string;\r\n}\r\n\r\n// Extended interface for hook usage that includes address_id\r\nexport interface EditAddressData extends UpdateAddressDto {\r\n  address_id: string;\r\n}\r\n\r\n// Search and filter interfaces\r\nexport interface SearchPostcodes {\r\n  region?: string;\r\n  district?: string;\r\n  location?: string;\r\n  postal_code?: string;\r\n}\r\n\r\nexport interface PostalCodeLookupResult {\r\n  postal_code_id: string;\r\n  region: string;\r\n  district: string;\r\n  location: string;\r\n  postal_code: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string | null;\r\n}\r\n\r\n// Address filters for queries\r\nexport interface AddressFilters {\r\n  entity_type?: string;\r\n  entity_id?: string;\r\n  address_type?: string;\r\n  country?: string;\r\n  city?: string;\r\n  postal_code?: string;\r\n}\r\n\r\n// Address service response types\r\nexport interface AddressResponse {\r\n  data: Address[];\r\n  total: number;\r\n  page?: number;\r\n  limit?: number;\r\n}\r\n\r\nexport interface SingleAddressResponse {\r\n  data: Address;\r\n}\r\n\r\n// Address validation types\r\nexport interface AddressValidationResult {\r\n  isValid: boolean;\r\n  errors: Record<string, string>;\r\n}\r\n\r\n// Address type constants\r\nexport const ADDRESS_TYPES = {\r\n  BUSINESS: 'business',\r\n  POSTAL: 'postal',\r\n  PHYSICAL: 'physical',\r\n  BILLING: 'billing',\r\n  SHIPPING: 'shipping',\r\n} as const;\r\n\r\n\r\nexport const AddressType  = {\r\n  APPLICANT: 'applicant',\r\n  APPLICATION: 'application',\r\n  STAKEHOLDER: 'stakeholder',\r\n  CONTACT_PERSON: 'contact_person',\r\n  USER: 'user',\r\n} ;\r\n\r\n\r\n\r\nexport const EntityType  = {\r\n  APPLICANT: 'applicant',\r\n  APPLICATION: 'application',\r\n  STAKEHOLDER: 'stakeholder',\r\n  CONTACT_PERSON: 'contact_person',\r\n  USER: 'user',\r\n} ;\r\n\r\n// Component prop types\r\nexport interface AddressDropdownProps {\r\n  addresses: Address[];\r\n  selectedAddressId: string | null;\r\n  onSelect: (id: string) => void;\r\n  placeholder?: string;\r\n  disabled?: boolean;\r\n  error?: string;\r\n  className?: string;\r\n}\r\n\r\nexport interface AddressModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onSave: (address: Address) => void;\r\n  address?: Address | null;\r\n  entityType?: string;\r\n  entityId?: string;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAgJO,MAAM,gBAAgB;IAC3B,UAAU;IACV,QAAQ;IACR,UAAU;IACV,SAAS;IACT,UAAU;AACZ;AAGO,MAAM,cAAe;IAC1B,WAAW;IACX,aAAa;IACb,aAAa;IACb,gBAAgB;IAChB,MAAM;AACR;AAIO,MAAM,aAAc;IACzB,WAAW;IACX,aAAa;IACb,aAAa;IACb,gBAAgB;IAChB,MAAM;AACR", "debugId": null}}, {"offset": {"line": 4576, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/index.ts"], "sourcesContent": ["// Re-export core types\r\nexport * from './applicant_info';\r\nexport * from './base';\r\nexport * from './shortcode';\r\nexport * from './license'\r\n\r\n// Re-export license types explicitly\r\nexport * from './license';\r\n\r\n// Re-export other types selectively to avoid conflicts\r\nexport * from './user';\r\nexport * from './identification';\r\nexport * from './invoice';\r\nexport * from './notification';\r\nexport * from './department';\r\nexport * from './organization';\r\nexport * from './public';\r\nexport * from './task';\r\nexport * from './audit';\r\nexport * from './payment';\r\nexport * from './equipment';\r\nexport * from './license-category';\r\nexport * from './address_types';\r\n\r\n// Re-export license types with explicit naming to avoid conflicts\r\nexport type {\r\n  LicenseType as LicenseTypeEntity,\r\n  CreateLicenseTypeDto,\r\n  UpdateLicenseTypeDto,\r\n  LicenseTypeFilters,\r\n  LicenseTypesResponse\r\n} from './license-type';\r\n\r\n// Common/Shared types\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems?: number;\r\n    currentPage?: number;\r\n    totalPages?: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    select: string[];\r\n    filter?: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first?: string;\r\n    previous?: string;\r\n    current: string;\r\n    next?: string;\r\n    last?: string;\r\n  };\r\n}\r\n\r\nexport interface PaginateQuery {\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: string[];\r\n  searchBy?: string[];\r\n  search?: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\n// API Response types\r\nexport interface ApiResponse<T = any> {\r\n  data: T;\r\n  message?: string;\r\n  success?: boolean;\r\n  status?: number;\r\n}\r\n\r\nexport interface ApiError {\r\n  message: string;\r\n  error?: string;\r\n  statusCode?: number;\r\n  timestamp?: string;\r\n  path?: string;\r\n}\r\n\r\n// Common entity fields\r\nexport interface BaseEntity {\r\n  created_at: string;\r\n  updated_at: string;\r\n  created_by?: string;\r\n  updated_by?: string;\r\n}\r\n\r\nexport interface SoftDeleteEntity extends BaseEntity {\r\n  deleted_at?: string;\r\n}\r\n\r\n// User reference type (for creator/updater fields)\r\nexport interface UserReference {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n}\r\n\r\n// Navigation types\r\nexport interface NavigationItem {\r\n  id: string;\r\n  name: string;\r\n  code?: string;\r\n  href: string;\r\n  label: string;\r\n  roles?: string[];\r\n  icon?: string;\r\n  children?: NavigationItem[];\r\n}\r\n\r\n// File/Document types\r\nexport interface FileUpload {\r\n  file: File;\r\n  name: string;\r\n  type: string;\r\n  size: number;\r\n}\r\n\r\nexport interface UploadedFile {\r\n  file_id: string;\r\n  filename: string;\r\n  original_name: string;\r\n  mime_type: string;\r\n  size: number;\r\n  path: string;\r\n  url?: string;\r\n  created_at: string;\r\n}\r\n\r\n// Status types\r\nexport type EntityStatus = 'active' | 'inactive' | 'suspended' | 'pending' | 'approved' | 'rejected';\r\n\r\n// Common filter types\r\nexport interface BaseFilters {\r\n  search?: string;\r\n  status?: string;\r\n  dateRange?: string;\r\n  created_by?: string;\r\n  updated_by?: string;\r\n}\r\n\r\n// Form types\r\nexport interface FormField {\r\n  name: string;\r\n  label: string;\r\n  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'checkbox' | 'radio' | 'file' | 'date';\r\n  required?: boolean;\r\n  placeholder?: string;\r\n  options?: { value: string; label: string }[];\r\n  validation?: {\r\n    min?: number;\r\n    max?: number;\r\n    pattern?: string;\r\n    message?: string;\r\n  };\r\n}\r\n\r\nexport interface FormConfig {\r\n  fields: FormField[];\r\n  submitLabel?: string;\r\n  cancelLabel?: string;\r\n}\r\n\r\n// Table/Grid types\r\nexport interface TableColumn<T = any> {\r\n  key: keyof T | string;\r\n  label: string;\r\n  sortable?: boolean;\r\n  filterable?: boolean;\r\n  width?: string;\r\n  align?: 'left' | 'center' | 'right';\r\n  render?: (value: any, row: T) => React.ReactNode;\r\n}\r\n\r\nexport interface TableConfig<T = any> {\r\n  columns: TableColumn<T>[];\r\n  sortable?: boolean;\r\n  filterable?: boolean;\r\n  selectable?: boolean;\r\n  pagination?: boolean;\r\n}\r\n\r\n// Modal/Dialog types\r\nexport interface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title?: string;\r\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';\r\n}\r\n\r\n// Notification/Toast types\r\nexport interface ToastNotification {\r\n  id: string;\r\n  type: 'success' | 'error' | 'warning' | 'info';\r\n  title: string;\r\n  message?: string;\r\n  duration?: number;\r\n  actions?: {\r\n    label: string;\r\n    action: () => void;\r\n  }[];\r\n}"], "names": [], "mappings": "AAAA,uBAAuB;;AACvB;AACA;AACA;AACA;AAKA,uDAAuD;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 4646, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useTaskCount.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { taskService } from '@/services/task-assignment';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { TaskStatus } from '@/types';\r\n\r\ninterface UseTaskCountReturn {\r\n  pendingCount: number;\r\n  loading: boolean;\r\n  error: string | null;\r\n  refreshCount: () => Promise<void>;\r\n}\r\n\r\nexport const useTaskCount = (): UseTaskCountReturn => {\r\n  const { user } = useAuth();\r\n  const [pendingCount, setPendingCount] = useState(0);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const fetchTaskCount = async () => {\r\n    if (!user) {\r\n      setPendingCount(0);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      if (user.isAdmin) {\r\n        // Administrators see all pending tasks\r\n        const stats = await taskService.getTaskStats();\r\n        setPendingCount(stats.pending || 0);\r\n      } else {\r\n        // Other users see only tasks assigned to them with pending status\r\n        const response = await taskService.getMyTasks({\r\n          status: TaskStatus.PENDING,\r\n          limit: 1, // We only need the count, not the actual tasks\r\n          page: 1\r\n        });\r\n        // The total count should be in the response metadata\r\n        setPendingCount(response.meta?.totalItems || 0);\r\n      }\r\n    } catch (err: any) {\r\n      setError(err.message || 'Failed to fetch task count');\r\n      setPendingCount(0);\r\n    } finally {\r\n      setLoading(false);\r\n      setTimeout(()=>{\r\n        fetchTaskCount();\r\n      },20000)\r\n    }\r\n  };\r\n\r\n  const refreshCount = async () => {\r\n    await fetchTaskCount();\r\n  };\r\n\r\n  // Initial fetch\r\n  useEffect(() => {\r\n    fetchTaskCount();\r\n  }, [user]);\r\n\r\n  // Poll for updates every 60 seconds (reduced frequency to avoid too many API calls)\r\n  useEffect(() => {\r\n    if (!user) return;\r\n\r\n    const interval = setInterval(fetchTaskCount, 60000);\r\n    return () => clearInterval(interval);\r\n  }, [user]);\r\n\r\n  // Listen for task updates via custom events\r\n  useEffect(() => {\r\n    const handleTaskUpdate = () => {\r\n      fetchTaskCount();\r\n    };\r\n\r\n    // Listen for custom events that indicate task updates\r\n    window.addEventListener('taskUpdated', handleTaskUpdate);\r\n    window.addEventListener('taskCompleted', handleTaskUpdate);\r\n    window.addEventListener('taskAssigned', handleTaskUpdate);\r\n    window.addEventListener('taskRefresh', handleTaskUpdate);\r\n\r\n    return () => {\r\n      window.removeEventListener('taskUpdated', handleTaskUpdate);\r\n      window.removeEventListener('taskCompleted', handleTaskUpdate);\r\n      window.removeEventListener('taskAssigned', handleTaskUpdate);\r\n      window.removeEventListener('taskRefresh', handleTaskUpdate);\r\n    };\r\n  }, []);\r\n\r\n  return {\r\n    pendingCount,\r\n    loading,\r\n    error,\r\n    refreshCount\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AAAA;;AALA;;;;;AAcO,MAAM,eAAe;;IAC1B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM;YACT,gBAAgB;YAChB;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,IAAI,KAAK,OAAO,EAAE;gBAChB,uCAAuC;gBACvC,MAAM,QAAQ,MAAM,wIAAA,CAAA,cAAW,CAAC,YAAY;gBAC5C,gBAAgB,MAAM,OAAO,IAAI;YACnC,OAAO;oBAQW;gBAPhB,kEAAkE;gBAClE,MAAM,WAAW,MAAM,wIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;oBAC5C,QAAQ,uHAAA,CAAA,aAAU,CAAC,OAAO;oBAC1B,OAAO;oBACP,MAAM;gBACR;gBACA,qDAAqD;gBACrD,gBAAgB,EAAA,iBAAA,SAAS,IAAI,cAAb,qCAAA,eAAe,UAAU,KAAI;YAC/C;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;YACxB,gBAAgB;QAClB,SAAU;YACR,WAAW;YACX,WAAW;gBACT;YACF,GAAE;QACJ;IACF;IAEA,MAAM,eAAe;QACnB,MAAM;IACR;IAEA,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;KAAK;IAET,oFAAoF;IACpF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,MAAM;YAEX,MAAM,WAAW,YAAY,gBAAgB;YAC7C;0CAAO,IAAM,cAAc;;QAC7B;iCAAG;QAAC;KAAK;IAET,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;2DAAmB;oBACvB;gBACF;;YAEA,sDAAsD;YACtD,OAAO,gBAAgB,CAAC,eAAe;YACvC,OAAO,gBAAgB,CAAC,iBAAiB;YACzC,OAAO,gBAAgB,CAAC,gBAAgB;YACxC,OAAO,gBAAgB,CAAC,eAAe;YAEvC;0CAAO;oBACL,OAAO,mBAAmB,CAAC,eAAe;oBAC1C,OAAO,mBAAmB,CAAC,iBAAiB;oBAC5C,OAAO,mBAAmB,CAAC,gBAAgB;oBAC3C,OAAO,mBAAmB,CAAC,eAAe;gBAC5C;;QACF;iCAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;GApFa;;QACM,kIAAA,CAAA,UAAO", "debugId": null}}, {"offset": {"line": 4766, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/Sidebar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { usePathname } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport NavItem from './NavItem';\r\nimport { useTaskCount } from '@/hooks/useTaskCount';\r\n\r\nconst Sidebar: React.FC = () => {\r\n  const { user } = useAuth();\r\n  const pathname = usePathname();\r\n  const [isMobileOpen, setIsMobileOpen] = useState(false);\r\n  const { pendingCount } = useTaskCount();\r\n\r\n  // Close mobile sidebar when route changes\r\n  useEffect(() => {\r\n    setIsMobileOpen(false);\r\n  }, [pathname]);\r\n\r\n  // Load license types for navigation\r\n  useEffect(() => {\r\n  }, []);\r\n\r\n  // Close mobile sidebar when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      const sidebar = document.getElementById('mobile-sidebar');\r\n      const toggleButton = document.getElementById('mobile-sidebar-toggle');\r\n      \r\n      if (\r\n        isMobileOpen &&\r\n        sidebar &&\r\n        !sidebar.contains(event.target as Node) &&\r\n        toggleButton &&\r\n        !toggleButton.contains(event.target as Node)\r\n      ) {\r\n        setIsMobileOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, [isMobileOpen]);\r\n\r\n  const toggleMobileSidebar = () => {\r\n    setIsMobileOpen(!isMobileOpen);\r\n  };\r\n\r\n  // Static navigation items\r\n  const topStaticNavigationItems = [\r\n    {\r\n      href: '/dashboard',\r\n      icon: 'ri-dashboard-line',\r\n      label: 'Dashboard',\r\n      roles: ['administrator', 'officer']\r\n    },\r\n    {\r\n      href: '/tasks',\r\n      icon: 'ri-user-add-line',\r\n      label: 'Tasks',\r\n      roles: ['administrator', 'officer', 'finance']\r\n    }\r\n  ];\r\n\r\n  const staticNavigationItems = [\r\n\r\n    {\r\n      href: '/licenses',\r\n      icon: 'ri-shield-user-line',\r\n      label: 'Licenses',\r\n      roles: ['administrator', 'officer', 'legal']\r\n    },\r\n    {\r\n      href: '/applications',\r\n      icon: 'ri-shield-user-line',\r\n      label: 'Applications',\r\n      roles: ['administrator', 'officer' , 'legal']\r\n    },\r\n    {\r\n      href: '/consumer-affairs',\r\n      icon: 'ri-shield-user-line',\r\n      label: 'Consumer Affairs',\r\n      roles: ['administrator', 'officer']\r\n    },\r\n    {\r\n      href: '/data-breach',\r\n      icon: 'ri-shield-cross-line',\r\n      label: 'Data Breach',\r\n      roles: ['administrator', 'officer']\r\n    },\r\n    {\r\n      href: '/resources',\r\n      icon: 'ri-folder-line',\r\n      label: 'Resources',\r\n      roles: ['administrator', 'officer']\r\n    },\r\n    {\r\n      href: '/procurement',\r\n      icon: 'ri-shopping-bag-line',\r\n      label: 'Procurement',\r\n      roles: ['administrator', 'officer']\r\n    },\r\n    {\r\n      href: '/payments',\r\n      icon: 'ri-money-dollar-circle-line',\r\n      label: 'Invoice & Payments',\r\n      roles: ['administrator', 'officer', 'finance' ]\r\n    },\r\n    {\r\n      href: '/reports',\r\n      icon: 'ri-file-chart-line',\r\n      label: 'Reports & Analytics',\r\n      roles: ['administrator', 'officer']\r\n    }\r\n  ];\r\n\r\n  const settingsNavigationItems = [\r\n    {\r\n      href: '/users',\r\n      icon: 'ri-user-settings-line',\r\n      label: 'User Management',\r\n      roles: ['administrator']\r\n    },\r\n    {\r\n      href: '/settings',\r\n      icon: 'ri-settings-3-line',\r\n      label: 'Management Settings',\r\n      roles: ['administrator']\r\n    },\r\n    {\r\n      href: '/audit-trail',\r\n      icon: 'ri-shield-line',\r\n      label: 'Audit Trail',\r\n      roles: ['administrator', 'officer']\r\n    },\r\n    {\r\n      href: '/help',\r\n      icon: 'ri-question-line',\r\n      label: 'Help & Support',\r\n      roles: ['administrator', 'officer']\r\n    }\r\n  ];\r\n\r\n  // Combine static navigation items with dynamic license types\r\n  const mainNavigationItems = [\r\n    // ...licenseTypeNavItems.map(item => ({\r\n    //   href: item.href,\r\n    //   icon: 'ri-file-list-line', // Generic icon for all license types\r\n    //   label: item.label,\r\n    //   roles: item.roles\r\n    // })),\r\n    ...staticNavigationItems\r\n  ];\r\n\r\n  const filteredMainNavItems = mainNavigationItems.filter(item =>\r\n    user?.roles?.some(role => item.roles.includes(role)) ||\r\n    item.roles.includes('customer')\r\n  );\r\n\r\n  const filteredSettingsNavItems = settingsNavigationItems.filter(item =>\r\n    user?.roles?.some(role => item.roles.includes(role)) ||\r\n    item.roles.includes('customer')\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Mobile Sidebar Toggle Button */}\r\n      <button\r\n        id=\"mobile-sidebar-toggle\"\r\n        onClick={toggleMobileSidebar}\r\n        className=\"lg:hidden fixed top-4 left-4 z-50 p-2 bg-primary text-white rounded-md shadow-lg hover:bg-red-700 transition-colors\"\r\n        aria-label=\"Toggle mobile sidebar\"\r\n      >\r\n        <i className={`fas ${isMobileOpen ? 'fa-times' : 'fa-bars'}`}></i>\r\n      </button>\r\n\r\n      {/* Mobile Overlay */}\r\n      {isMobileOpen && (\r\n        <div \r\n          className=\"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40\"\r\n          onClick={() => setIsMobileOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside\r\n        id=\"mobile-sidebar\"\r\n        className={`\r\n          fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out\r\n          ${isMobileOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n        `}\r\n      >\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <img src=\"/images/macra-logo.png\" alt=\"MACRA Logo\" className=\"max-h-12 w-auto\" />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            {topStaticNavigationItems.length > 0 && (\r\n              <div className=\"mt-2 space-y-1\">\r\n                {topStaticNavigationItems.map((item) => (\r\n                  <NavItem\r\n                    key={item.href}\r\n                    href={item.href}\r\n                    icon={item.icon}\r\n                    label={item.label}\r\n                    isActive={pathname === item.href}\r\n                    onClick={() => setIsMobileOpen(false)}\r\n                    badge={item.href === '/tasks' ? pendingCount : undefined}\r\n                    badgeColor=\"red\"\r\n                  />\r\n              ))}\r\n            </div>\r\n            )}\r\n\r\n            {/* Main Navigation */}\r\n            {filteredMainNavItems.length > 0 && (\r\n              <div className=\"mt-8\">\r\n                <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Main Menu\r\n                </h3>\r\n                <div className=\"mt-2 space-y-1\">\r\n                  {filteredMainNavItems.map((item) => (\r\n                    <NavItem\r\n                      key={item.href}\r\n                      href={item.href}\r\n                      icon={item.icon}\r\n                      label={item.label}\r\n                      isActive={pathname === item.href}\r\n                      onClick={() => setIsMobileOpen(false)}\r\n                    />\r\n                ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Settings Section */}\r\n            {filteredSettingsNavItems.length > 0 && (\r\n              <div className=\"mt-8\">\r\n                <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Settings\r\n                </h3>\r\n                <div className=\"mt-2 space-y-1\">\r\n                  {filteredSettingsNavItems.map((item) => (\r\n                    <NavItem\r\n                      key={item.href}\r\n                      href={item.href}\r\n                      icon={item.icon}\r\n                      label={item.label}\r\n                      isActive={pathname === item.href}\r\n                      onClick={() => setIsMobileOpen(false)}\r\n                    />\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </nav>\r\n\r\n\r\n        </div>\r\n      </aside>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Sidebar;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,MAAM,UAAoB;;IACxB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAEpC,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,gBAAgB;QAClB;4BAAG;QAAC;KAAS;IAEb,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE,KACV;4BAAG,EAAE;IAEL,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;wDAAqB,CAAC;oBAC1B,MAAM,UAAU,SAAS,cAAc,CAAC;oBACxC,MAAM,eAAe,SAAS,cAAc,CAAC;oBAE7C,IACE,gBACA,WACA,CAAC,QAAQ,QAAQ,CAAC,MAAM,MAAM,KAC9B,gBACA,CAAC,aAAa,QAAQ,CAAC,MAAM,MAAM,GACnC;wBACA,gBAAgB;oBAClB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;qCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;4BAAG;QAAC;KAAa;IAEjB,MAAM,sBAAsB;QAC1B,gBAAgB,CAAC;IACnB;IAEA,0BAA0B;IAC1B,MAAM,2BAA2B;QAC/B;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAU;QACrC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;gBAAW;aAAU;QAChD;KACD;IAED,MAAM,wBAAwB;QAE5B;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;gBAAW;aAAQ;QAC9C;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;gBAAY;aAAQ;QAC/C;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAU;QACrC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAU;QACrC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAU;QACrC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAU;QACrC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;gBAAW;aAAW;QACjD;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAU;QACrC;KACD;IAED,MAAM,0BAA0B;QAC9B;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;aAAgB;QAC1B;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;aAAgB;QAC1B;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAU;QACrC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAU;QACrC;KACD;IAED,6DAA6D;IAC7D,MAAM,sBAAsB;QAC1B,wCAAwC;QACxC,qBAAqB;QACrB,qEAAqE;QACrE,uBAAuB;QACvB,sBAAsB;QACtB,OAAO;WACJ;KACJ;IAED,MAAM,uBAAuB,oBAAoB,MAAM,CAAC,CAAA;YACtD;eAAA,CAAA,iBAAA,4BAAA,cAAA,KAAM,KAAK,cAAX,kCAAA,YAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC,WAC9C,KAAK,KAAK,CAAC,QAAQ,CAAC;;IAGtB,MAAM,2BAA2B,wBAAwB,MAAM,CAAC,CAAA;YAC9D;eAAA,CAAA,iBAAA,4BAAA,cAAA,KAAM,KAAK,cAAX,kCAAA,YAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC,WAC9C,KAAK,KAAK,CAAC,QAAQ,CAAC;;IAGtB,qBACE;;0BAEE,6LAAC;gBACC,IAAG;gBACH,SAAS;gBACT,WAAU;gBACV,cAAW;0BAEX,cAAA,6LAAC;oBAAE,WAAW,AAAC,OAA4C,OAAtC,eAAe,aAAa;;;;;;;;;;;YAIlD,8BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,gBAAgB;;;;;;0BAKnC,6LAAC;gBACC,IAAG;gBACH,WAAW,AAAC,iKAE8D,OAAtE,eAAe,kBAAkB,sCAAqC;0BAG1E,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,KAAI;oCAAyB,KAAI;oCAAa,WAAU;;;;;;;;;;;;;;;;sCAKjE,6LAAC;4BAAI,WAAU;;gCAEZ,yBAAyB,MAAM,GAAG,mBACjC,6LAAC;oCAAI,WAAU;8CACZ,yBAAyB,GAAG,CAAC,CAAC,qBAC7B,6LAAC,gIAAA,CAAA,UAAO;4CAEN,MAAM,KAAK,IAAI;4CACf,MAAM,KAAK,IAAI;4CACf,OAAO,KAAK,KAAK;4CACjB,UAAU,aAAa,KAAK,IAAI;4CAChC,SAAS,IAAM,gBAAgB;4CAC/B,OAAO,KAAK,IAAI,KAAK,WAAW,eAAe;4CAC/C,YAAW;2CAPN,KAAK,IAAI;;;;;;;;;;gCAcrB,qBAAqB,MAAM,GAAG,mBAC7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,6LAAC;4CAAI,WAAU;sDACZ,qBAAqB,GAAG,CAAC,CAAC,qBACzB,6LAAC,gIAAA,CAAA,UAAO;oDAEN,MAAM,KAAK,IAAI;oDACf,MAAM,KAAK,IAAI;oDACf,OAAO,KAAK,KAAK;oDACjB,UAAU,aAAa,KAAK,IAAI;oDAChC,SAAS,IAAM,gBAAgB;mDAL1B,KAAK,IAAI;;;;;;;;;;;;;;;;gCAavB,yBAAyB,MAAM,GAAG,mBACjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,6LAAC;4CAAI,WAAU;sDACZ,yBAAyB,GAAG,CAAC,CAAC,qBAC7B,6LAAC,gIAAA,CAAA,UAAO;oDAEN,MAAM,KAAK,IAAI;oDACf,MAAM,KAAK,IAAI;oDACf,OAAO,KAAK,KAAK;oDACjB,UAAU,aAAa,KAAK,IAAI;oDAChC,SAAS,IAAM,gBAAgB;mDAL1B,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBpC;GAtQM;;QACa,kIAAA,CAAA,UAAO;QACP,qIAAA,CAAA,cAAW;QAEH,+HAAA,CAAA,eAAY;;;KAJjC;uCAwQS", "debugId": null}}, {"offset": {"line": 5168, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/tasks/layout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport Header from '../../components/Header';\r\nimport Sidebar from '../../components/Sidebar';\r\n\r\nexport default function TaskAssignmentLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode\r\n}) {\r\n  const [activeTab, setActiveTab] = useState('tasks');\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n\r\n  const toggleMobileSidebar = () => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden bg-gray-50 dark:bg-gray-900\">\r\n      {/* Mobile sidebar overlay */}\r\n      <div\r\n        id=\"mobileSidebarOverlay\"\r\n        className={`mobile-sidebar-overlay ${isMobileSidebarOpen ? 'show' : ''}`}\r\n        onClick={() => setIsMobileSidebarOpen(false)}\r\n      ></div>\r\n\r\n      <Sidebar />\r\n      <div className=\"flex-1 flex flex-col overflow-hidden bg-gray-50 dark:bg-gray-900\">\r\n        <Header\r\n          activeTab={activeTab}\r\n          onTabChange={setActiveTab}\r\n          onMobileMenuToggle={toggleMobileSidebar}\r\n        />\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          {children}\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS,qBAAqB,KAI5C;QAJ4C,EAC3C,QAAQ,EAGT,GAJ4C;;IAK3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,sBAAsB;QAC1B,uBAAuB,CAAC;IAC1B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,IAAG;gBACH,WAAW,AAAC,0BAA2D,OAAlC,sBAAsB,SAAS;gBACpE,SAAS,IAAM,uBAAuB;;;;;;0BAGxC,6LAAC,gIAAA,CAAA,UAAO;;;;;0BACR,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+HAAA,CAAA,UAAM;wBACL,WAAW;wBACX,aAAa;wBACb,oBAAoB;;;;;;kCAEtB,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GAlCwB;KAAA", "debugId": null}}]}