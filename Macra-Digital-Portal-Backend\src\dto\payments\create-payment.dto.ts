import { IsString, IsEnum, IsO<PERSON>al, IsUUID, IsNumber, IsDateString, Min, Length } from 'class-validator';
import { PaymentStatus } from '../../payments/entities/payment.entity';

export class CreatePaymentDto {
  @IsString()
  @Length(1, 255)
  transaction_number: string;

  @IsOptional()
  @IsUUID()
  application_id?: string;

  @IsOptional()
  @IsUUID()
  license_id?: string;

  @IsUUID()
  applicant_id: string;

  @IsString()
  transaction_type: string;

  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  amount: number;

  @IsOptional()
  @IsString()
  @Length(3, 3)
  currency?: string;

  @IsOptional()
  @IsString()
  status?: string;

  @IsOptional()
  @IsString()
  payment_method?: string;

  @IsOptional()
  @IsString()
  @Length(1, 255)
  reference_number?: string;

  @IsString()
  description: string;

  @IsOptional()
  @IsDateString()
  completed_at?: string;
}
