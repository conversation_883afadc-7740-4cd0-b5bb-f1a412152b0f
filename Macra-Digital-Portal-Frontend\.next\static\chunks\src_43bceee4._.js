(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/LogoutButton.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>LogoutButton
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function LogoutButton(param) {
    let { variant = 'primary', size = 'md', className = '', showConfirmation = true, redirectTo = '/auth/login', children } = param;
    _s();
    const [isLoggingOut, setIsLoggingOut] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showConfirm, setShowConfirm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { logout, user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const handleLogout = async ()=>{
        if (showConfirmation && !showConfirm) {
            setShowConfirm(true);
            return;
        }
        setIsLoggingOut(true);
        try {
            // Call logout from context
            logout();
            // Small delay to ensure cleanup is complete
            await new Promise((resolve)=>setTimeout(resolve, 100));
            if (pathname.includes('customer')) {
                // Redirect to specified page
                router.push('/customer/auth/login');
            } else {
                router.push('/auth/login');
            }
        } catch (error) {
            console.error('Logout error:', error);
        } finally{
            setIsLoggingOut(false);
            setShowConfirm(false);
        }
    };
    const handleCancel = ()=>{
        setShowConfirm(false);
    };
    // Base styles for different variants
    const getVariantStyles = ()=>{
        switch(variant){
            case 'primary':
                return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';
            case 'secondary':
                return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';
            case 'text':
                return 'bg-transparent hover:bg-red-50 text-red-600 border-none';
            case 'icon':
                return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';
            default:
                return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';
        }
    };
    // Size styles
    const getSizeStyles = ()=>{
        switch(size){
            case 'sm':
                return 'px-3 py-1.5 text-sm';
            case 'md':
                return 'px-4 py-2 text-base';
            case 'lg':
                return 'px-6 py-3 text-lg';
            default:
                return 'px-4 py-2 text-base';
        }
    };
    const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
    const buttonStyles = "".concat(baseStyles, " ").concat(getVariantStyles(), " ").concat(variant !== 'icon' ? getSizeStyles() : '', " ").concat(className);
    // Confirmation dialog
    if (showConfirm) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center mb-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-shrink-0",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    className: "h-6 w-6 text-red-600",
                                    fill: "none",
                                    viewBox: "0 0 24 24",
                                    stroke: "currentColor",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/LogoutButton.tsx",
                                        lineNumber: 108,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/LogoutButton.tsx",
                                    lineNumber: 107,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/LogoutButton.tsx",
                                lineNumber: 106,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "ml-3",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-medium text-gray-900",
                                    children: "Confirm Logout"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/LogoutButton.tsx",
                                    lineNumber: 112,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/LogoutButton.tsx",
                                lineNumber: 111,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/LogoutButton.tsx",
                        lineNumber: 105,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-gray-500",
                            children: [
                                "Are you sure you want to logout",
                                (user === null || user === void 0 ? void 0 : user.first_name) ? ", ".concat(user.first_name) : '',
                                "? You will need to login again to access your account."
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/LogoutButton.tsx",
                            lineNumber: 117,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/LogoutButton.tsx",
                        lineNumber: 116,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex space-x-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleLogout,
                                disabled: isLoggingOut,
                                className: "flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50",
                                children: isLoggingOut ? 'Logging out...' : 'Yes, Logout'
                            }, void 0, false, {
                                fileName: "[project]/src/components/LogoutButton.tsx",
                                lineNumber: 123,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleCancel,
                                className: "flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200",
                                children: "Cancel"
                            }, void 0, false, {
                                fileName: "[project]/src/components/LogoutButton.tsx",
                                lineNumber: 130,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/LogoutButton.tsx",
                        lineNumber: 122,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/LogoutButton.tsx",
                lineNumber: 104,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/LogoutButton.tsx",
            lineNumber: 103,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        onClick: handleLogout,
        disabled: isLoggingOut,
        className: buttonStyles,
        title: "Logout",
        children: children || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: variant === 'icon' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                className: "h-5 w-5",
                fill: "none",
                viewBox: "0 0 24 24",
                stroke: "currentColor",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                }, void 0, false, {
                    fileName: "[project]/src/components/LogoutButton.tsx",
                    lineNumber: 153,
                    columnNumber: 15
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/LogoutButton.tsx",
                lineNumber: 152,
                columnNumber: 13
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                        className: "h-4 w-4 mr-2",
                        fill: "none",
                        viewBox: "0 0 24 24",
                        stroke: "currentColor",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            strokeWidth: 2,
                            d: "M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                        }, void 0, false, {
                            fileName: "[project]/src/components/LogoutButton.tsx",
                            lineNumber: 158,
                            columnNumber: 17
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/LogoutButton.tsx",
                        lineNumber: 157,
                        columnNumber: 15
                    }, this),
                    isLoggingOut ? 'Logging out...' : 'Logout'
                ]
            }, void 0, true)
        }, void 0, false)
    }, void 0, false, {
        fileName: "[project]/src/components/LogoutButton.tsx",
        lineNumber: 143,
        columnNumber: 5
    }, this);
}
_s(LogoutButton, "GK6euFXnNSahEQDYKCx2YSuMdTI=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
_c = LogoutButton;
var _c;
__turbopack_context__.k.register(_c, "LogoutButton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/types/license.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApplicationStatus": ()=>ApplicationStatus,
    "LicenseStatus": ()=>LicenseStatus
});
var ApplicationStatus = /*#__PURE__*/ function(ApplicationStatus) {
    ApplicationStatus["DRAFT"] = "draft";
    ApplicationStatus["PENDING"] = "pending";
    ApplicationStatus["SUBMITTED"] = "submitted";
    ApplicationStatus["UNDER_REVIEW"] = "under_review";
    ApplicationStatus["EVALUATION"] = "evaluation";
    ApplicationStatus["PENDING_PAYMENT"] = "pending_payment";
    ApplicationStatus["APPROVED"] = "approved";
    ApplicationStatus["REJECTED"] = "rejected";
    ApplicationStatus["WITHDRAWN"] = "withdrawn";
    ApplicationStatus["PASS_EVALUATION"] = "pass_evaluation";
    return ApplicationStatus;
}({});
var LicenseStatus = /*#__PURE__*/ function(LicenseStatus) {
    LicenseStatus["ACTIVE"] = "active";
    LicenseStatus["EXPIRED"] = "expired";
    LicenseStatus["SUSPENDED"] = "suspended";
    LicenseStatus["REVOKED"] = "revoked";
    LicenseStatus["UNDER_REVIEW"] = "under_review";
    return LicenseStatus;
}({});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/notificationService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createTestNotification": ()=>createTestNotification,
    "getStatusChangeMessage": ()=>getStatusChangeMessage,
    "notificationService": ()=>notificationService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/license.ts [app-client] (ecmascript)");
;
;
;
const notificationService = {
    // Get user notifications
    async getUserNotifications (params) {
        try {
            const queryParams = new URLSearchParams();
            if (params === null || params === void 0 ? void 0 : params.page) queryParams.append('page', params.page.toString());
            if (params === null || params === void 0 ? void 0 : params.limit) queryParams.append('limit', params.limit.toString());
            if (params === null || params === void 0 ? void 0 : params.type) queryParams.append('type', params.type);
            if (params === null || params === void 0 ? void 0 : params.status) queryParams.append('status', params.status);
            const endpoint = "/notifications/my-notifications?".concat(queryParams.toString());
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(endpoint);
            const data = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response).data;
            // Validate response structure
            if (!data || typeof data !== 'object') {
                console.warn('Invalid notification response format:', data);
                return {
                    total_count: 0,
                    unread_count: 0,
                    notifications: []
                };
            }
            return {
                total_count: data.length || 0,
                unread_count: data.length || 0,
                notifications: Array.isArray(data) ? data : []
            };
        } catch (error) {
            var _error_response, _error_response1;
            // Convert params to string format for URLSearchParams
            const stringParams = {};
            if (params) {
                if (params.page) stringParams.page = params.page.toString();
                if (params.limit) stringParams.limit = params.limit.toString();
                if (params.type) stringParams.type = params.type;
                if (params.status) stringParams.status = params.status;
            }
            console.error('Error fetching user notifications:', {
                error: error.message || 'Unknown error',
                response: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || null,
                status: ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) || null,
                code: error.code || null,
                params,
                endpoint: "/notifications/my-notifications?".concat(new URLSearchParams(stringParams).toString())
            });
            // Return empty result on error instead of throwing
            return {
                total_count: 0,
                unread_count: 0,
                notifications: []
            };
        }
    },
    // Mark notification as read
    async markAsRead (notificationId) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].patch("/notifications/".concat(notificationId, "/mark-read"));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Mark all notifications as read (implemented client-side by marking each notification individually)
    async markAllAsRead () {
        // This is handled by the useNotifications hook by calling markAsRead for each unread notification
        // We don't need a separate backend endpoint for this
        throw new Error('markAllAsRead should be handled by useNotifications hook');
    },
    // Create a status change notification (usually called from backend)
    async createStatusChangeNotification (applicationId, oldStatus, newStatus, step, progressPercentage) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/notifications/status-change', {
            application_id: applicationId,
            old_status: oldStatus,
            new_status: newStatus,
            step,
            progress_percentage: progressPercentage
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Delete notification
    async deleteNotification (notificationId) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete("/notifications/".concat(notificationId));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get notification count
    async getNotificationCount () {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/notifications/count');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Manual notification trigger for testing (will be removed in production)
    async triggerTestNotification (applicationId, applicationNumber, licenseCategoryName, type, status) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/notifications/test', {
            application_id: applicationId,
            application_number: applicationNumber,
            license_category_name: licenseCategoryName,
            type,
            status
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
};
const getStatusChangeMessage = (applicationNumber, licenseCategoryName, oldStatus, newStatus, step, progressPercentage)=>{
    const progressText = progressPercentage ? " (".concat(progressPercentage, "% complete)") : '';
    const stepText = step ? " - Step ".concat(step) : '';
    const messages = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].DRAFT]: {
            title: 'Application Draft Saved',
            message: "Your ".concat(licenseCategoryName, " application (").concat(applicationNumber, ") has been saved as a draft. You can continue editing and submit it when ready."),
            type: 'info'
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].PENDING]: {
            title: 'Application Pending',
            message: "Your ".concat(licenseCategoryName, " application (").concat(applicationNumber, ") is pending initial review. We'll notify you once the review begins."),
            type: 'info'
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].SUBMITTED]: {
            title: 'Application Submitted',
            message: "Your ".concat(licenseCategoryName, " application (").concat(applicationNumber, ") has been successfully submitted and is now being processed."),
            type: 'success'
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].UNDER_REVIEW]: {
            title: 'Application Under Review',
            message: "Your ".concat(licenseCategoryName, " application (").concat(applicationNumber, ") is now under review by our team").concat(progressText).concat(stepText, ". We'll notify you of any updates."),
            type: 'info'
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].EVALUATION]: {
            title: 'Application Being Evaluated',
            message: "Your ".concat(licenseCategoryName, " application (").concat(applicationNumber, ") is currently being evaluated").concat(progressText).concat(stepText, ". This may take several business days."),
            type: 'info'
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].PENDING_PAYMENT]: {
            title: 'Payment Required',
            message: "Your ".concat(licenseCategoryName, " application (").concat(applicationNumber, ") has been approved for payment. Please complete the payment to proceed with license issuance."),
            type: 'warning'
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].APPROVED]: {
            title: 'Application Approved! 🎉',
            message: "Congratulations! Your ".concat(licenseCategoryName, " application (").concat(applicationNumber, ") has been approved. You can now download your license."),
            type: 'success'
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].REJECTED]: {
            title: 'Application Status Update',
            message: "Your ".concat(licenseCategoryName, " application (").concat(applicationNumber, ") requires attention. Please review the feedback and resubmit if needed."),
            type: 'warning'
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].WITHDRAWN]: {
            title: 'Application Withdrawn',
            message: "Your ".concat(licenseCategoryName, " application (").concat(applicationNumber, ") has been withdrawn as requested."),
            type: 'info'
        }
    };
    const defaultMessage = {
        title: 'Application Status Update',
        message: "Your ".concat(licenseCategoryName, " application (").concat(applicationNumber, ") status has been updated to ").concat(newStatus.replace('_', ' '), "."),
        type: 'info'
    };
    return messages[newStatus] || defaultMessage;
};
const createTestNotification = (applicationNumber, licenseCategoryName, status)=>{
    const now = new Date().toISOString();
    const notificationId = "test-".concat(Date.now());
    return {
        notification_id: notificationId,
        user_id: 'current-user',
        application_id: "app-".concat(applicationNumber),
        application_number: applicationNumber,
        license_category_name: licenseCategoryName,
        title: getStatusChangeMessage(applicationNumber, licenseCategoryName, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].SUBMITTED, status).title,
        message: getStatusChangeMessage(applicationNumber, licenseCategoryName, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].SUBMITTED, status).message,
        type: 'status_change',
        status: 'unread',
        priority: 'medium',
        created_at: now,
        metadata: {
            old_status: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].SUBMITTED,
            new_status: status,
            step: 2,
            progress_percentage: 50
        }
    };
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/common/Modal.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>Modal
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
function Modal(param) {
    let { isOpen, onClose, title, children, size = 'md', showCloseButton = true, closeOnOverlayClick = true } = param;
    _s();
    // Handle escape key press
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Modal.useEffect": ()=>{
            const handleEscape = {
                "Modal.useEffect.handleEscape": (event)=>{
                    if (event.key === 'Escape' && isOpen) {
                        onClose();
                    }
                }
            }["Modal.useEffect.handleEscape"];
            if (isOpen) {
                document.addEventListener('keydown', handleEscape);
                // Prevent body scroll when modal is open
                document.body.style.overflow = 'hidden';
            }
            return ({
                "Modal.useEffect": ()=>{
                    document.removeEventListener('keydown', handleEscape);
                    document.body.style.overflow = 'unset';
                }
            })["Modal.useEffect"];
        }
    }["Modal.useEffect"], [
        isOpen,
        onClose
    ]);
    if (!isOpen) return null;
    const getSizeClasses = ()=>{
        switch(size){
            case 'sm':
                return 'sm:max-w-sm';
            case 'md':
                return 'sm:max-w-md';
            case 'lg':
                return 'sm:max-w-lg';
            case 'xl':
                return 'sm:max-w-xl';
            case '2xl':
                return 'sm:max-w-2xl';
            default:
                return 'sm:max-w-md';
        }
    };
    const handleOverlayClick = (e)=>{
        if (closeOnOverlayClick && e.target === e.currentTarget) {
            onClose();
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0",
                onClick: handleOverlayClick,
                "aria-hidden": "true"
            }, void 0, false, {
                fileName: "[project]/src/components/common/Modal.tsx",
                lineNumber: 71,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full ".concat(getSizeClasses(), " mx-4 transform transition-all max-h-[90vh] flex flex-col"),
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-semibold text-gray-900 dark:text-gray-100",
                                children: title
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Modal.tsx",
                                lineNumber: 80,
                                columnNumber: 11
                            }, this),
                            showCloseButton && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: onClose,
                                className: "text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 rounded-md p-1",
                                "aria-label": "Close modal",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-close-line text-xl"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/Modal.tsx",
                                    lineNumber: 90,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Modal.tsx",
                                lineNumber: 84,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/Modal.tsx",
                        lineNumber: 79,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1 overflow-hidden",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "overflow-y-auto h-full",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "p-6",
                                children: children
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Modal.tsx",
                                lineNumber: 98,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Modal.tsx",
                            lineNumber: 97,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Modal.tsx",
                        lineNumber: 96,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Modal.tsx",
                lineNumber: 77,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/Modal.tsx",
        lineNumber: 70,
        columnNumber: 5
    }, this);
}
_s(Modal, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = Modal;
var _c;
__turbopack_context__.k.register(_c, "Modal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/notifications/NotificationItem.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
'use client';
;
const NotificationItem = (param)=>{
    let { notification, onMarkAsRead, onNotificationClick } = param;
    const formatTimeAgo = (dateString)=>{
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
        if (diffInSeconds < 60) {
            return 'Just now';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return "".concat(minutes, " minute").concat(minutes > 1 ? 's' : '', " ago");
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return "".concat(hours, " hour").concat(hours > 1 ? 's' : '', " ago");
        } else if (diffInSeconds < 604800) {
            const days = Math.floor(diffInSeconds / 86400);
            return "".concat(days, " day").concat(days > 1 ? 's' : '', " ago");
        } else {
            return date.toLocaleDateString();
        }
    };
    const getNotificationIcon = (type)=>{
        switch(type.toLowerCase()){
            case 'email':
                return 'ri-mail-line';
            case 'sms':
                return 'ri-message-3-line';
            case 'in_app':
                return 'ri-notification-3-line';
            case 'application_status':
                return 'ri-file-list-3-line';
            case 'evaluation_assigned':
                return 'ri-user-settings-line';
            case 'payment_due':
                return 'ri-money-dollar-circle-line';
            case 'license_expiry':
                return 'ri-calendar-event-line';
            case 'system_alert':
                return 'ri-alert-line';
            default:
                return 'ri-notification-3-line';
        }
    };
    const getPriorityColor = (priority)=>{
        switch(priority.toLowerCase()){
            case 'urgent':
                return 'text-red-600 dark:text-red-400';
            case 'high':
                return 'text-orange-600 dark:text-orange-400';
            case 'medium':
                return 'text-yellow-600 dark:text-yellow-400';
            case 'low':
                return 'text-green-600 dark:text-green-400';
            default:
                return 'text-gray-600 dark:text-gray-400';
        }
    };
    const handleItemClick = ()=>{
        if (!notification.is_read) {
            onMarkAsRead(notification.notification_id);
        }
        if (onNotificationClick) {
            onNotificationClick(notification);
        }
    };
    const handleMarkAsReadClick = (e)=>{
        e.stopPropagation();
        onMarkAsRead(notification.notification_id);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors duration-200 ".concat(!notification.is_read ? 'bg-blue-50 dark:bg-blue-900/20' : ''),
        onClick: handleItemClick,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-start space-x-3",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ".concat(!notification.is_read ? 'bg-blue-100 dark:bg-blue-900/50' : 'bg-gray-100 dark:bg-gray-700'),
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                        className: "".concat(getNotificationIcon(notification.type), " text-sm ").concat(!notification.is_read ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400')
                    }, void 0, false, {
                        fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                        lineNumber: 104,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                    lineNumber: 99,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex-1 min-w-0",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-start justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                            className: "text-sm font-medium ".concat(!notification.is_read ? 'text-gray-900 dark:text-gray-100' : 'text-gray-700 dark:text-gray-300'),
                                            children: notification.subject
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                                            lineNumber: 115,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "mt-1 text-sm ".concat(!notification.is_read ? 'text-gray-700 dark:text-gray-300' : 'text-gray-500 dark:text-gray-400'),
                                            children: notification.message
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                                            lineNumber: 122,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                                    lineNumber: 114,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                notification.priority && notification.priority !== 'medium' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-shrink-0 ml-2 ".concat(getPriorityColor(notification.priority)),
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                        className: "ri-flag-line text-xs"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                                        lineNumber: 134,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                                    lineNumber: 133,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                            lineNumber: 113,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-2 flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: formatTimeAgo(notification.created_at)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                                            lineNumber: 142,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        notification.entity_type && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "capitalize",
                                            children: notification.entity_type
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                                            lineNumber: 144,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                                    lineNumber: 141,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center space-x-2",
                                    children: [
                                        !notification.is_read && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: handleMarkAsReadClick,
                                            className: "inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/50 hover:text-blue-800 dark:hover:text-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-colors duration-200",
                                            title: "Mark as read",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                    className: "ri-check-line mr-1"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                                                    lineNumber: 156,
                                                    columnNumber: 19
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                "Mark as Read"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                                            lineNumber: 151,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        notification.is_read && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-xs text-green-600 dark:text-green-400 flex items-center",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                    className: "ri-check-line mr-1"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                                                    lineNumber: 162,
                                                    columnNumber: 19
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                "Read"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                                            lineNumber: 161,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                                    lineNumber: 149,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                            lineNumber: 140,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                    lineNumber: 112,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                !notification.is_read && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex-shrink-0 w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full"
                }, void 0, false, {
                    fileName: "[project]/src/components/notifications/NotificationItem.tsx",
                    lineNumber: 172,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/notifications/NotificationItem.tsx",
            lineNumber: 97,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/notifications/NotificationItem.tsx",
        lineNumber: 91,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_c = NotificationItem;
const __TURBOPACK__default__export__ = NotificationItem;
var _c;
__turbopack_context__.k.register(_c, "NotificationItem");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useNotifications.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useNotifications": ()=>useNotifications
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/ToastContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$notificationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/notificationService.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
const useNotifications = ()=>{
    _s();
    const [notifications, setNotifications] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [unreadCount, setUnreadCount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [totalCount, setTotalCount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const { user, isAuthenticated } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const { showError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    const fetchNotifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNotifications.useCallback[fetchNotifications]": async ()=>{
            if (!isAuthenticated || !user) {
                return;
            }
            setLoading(true);
            setError(null);
            try {
                console.log('🔍 Fetching notifications for user:', {
                    userId: user.user_id,
                    email: user.email
                });
                const data = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$notificationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["notificationService"].getUserNotifications({
                    page: 1,
                    limit: 50 // Get more notifications for better UX
                });
                if (data && data.notifications) {
                    setNotifications(data.notifications);
                    setUnreadCount(data.unread_count);
                    setTotalCount(data.total_count);
                } else {
                    setNotifications([]);
                    setUnreadCount(0);
                    setTotalCount(0);
                }
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : 'Failed to fetch notifications';
                setError(errorMessage);
                console.error('Error fetching notifications:', err);
            // Don't show toast error for initial load failures
            } finally{
                setLoading(false);
            }
        }
    }["useNotifications.useCallback[fetchNotifications]"], [
        isAuthenticated,
        user
    ]);
    const markAsRead = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNotifications.useCallback[markAsRead]": async (id)=>{
            if (!isAuthenticated) {
                return;
            }
            try {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$notificationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["notificationService"].markAsRead(id);
                // Update local state
                setNotifications({
                    "useNotifications.useCallback[markAsRead]": (prev)=>prev.map({
                            "useNotifications.useCallback[markAsRead]": (notification)=>notification.notification_id === id ? {
                                    ...notification,
                                    status: 'read',
                                    read_at: new Date().toISOString()
                                } : notification
                        }["useNotifications.useCallback[markAsRead]"])
                }["useNotifications.useCallback[markAsRead]"]);
                // Update unread count
                setUnreadCount({
                    "useNotifications.useCallback[markAsRead]": (prev)=>Math.max(0, prev - 1)
                }["useNotifications.useCallback[markAsRead]"]);
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : 'Failed to mark notification as read';
                setError(errorMessage);
                showError('Failed to mark notification as read');
                console.error('Error marking notification as read:', err);
            }
        }
    }["useNotifications.useCallback[markAsRead]"], [
        isAuthenticated,
        showError
    ]);
    const markAllAsRead = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNotifications.useCallback[markAllAsRead]": async ()=>{
            if (!isAuthenticated || notifications.length === 0) {
                return;
            }
            try {
                // Mark all unread notifications as read
                const unreadNotifications = notifications.filter({
                    "useNotifications.useCallback[markAllAsRead].unreadNotifications": (n)=>n.status === 'unread'
                }["useNotifications.useCallback[markAllAsRead].unreadNotifications"]);
                for (const notification of unreadNotifications){
                    await markAsRead(notification.notification_id);
                }
                setUnreadCount(0);
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : 'Failed to mark all notifications as read';
                setError(errorMessage);
                showError('Failed to mark all notifications as read');
                console.error('Error marking all notifications as read:', err);
            }
        }
    }["useNotifications.useCallback[markAllAsRead]"], [
        isAuthenticated,
        notifications,
        markAsRead,
        showError
    ]);
    const getNotificationCounts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNotifications.useCallback[getNotificationCounts]": async ()=>{
            if (!isAuthenticated) {
                return;
            }
            try {
                const counts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$notificationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["notificationService"].getNotificationCount();
                setUnreadCount(counts.unread);
                setTotalCount(counts.total);
            } catch (err) {
                console.error('Error getting notification counts:', err);
            }
        }
    }["useNotifications.useCallback[getNotificationCounts]"], [
        isAuthenticated
    ]);
    const refreshNotifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNotifications.useCallback[refreshNotifications]": async ()=>{
            await fetchNotifications();
        }
    }["useNotifications.useCallback[refreshNotifications]"], [
        fetchNotifications
    ]);
    // Initial fetch when component mounts and user is authenticated
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useNotifications.useEffect": ()=>{
            if (isAuthenticated && user) {
                fetchNotifications();
            }
        }
    }["useNotifications.useEffect"], [
        isAuthenticated,
        user,
        fetchNotifications
    ]);
    // Set up polling for new notifications every 30 seconds
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useNotifications.useEffect": ()=>{
            if (!isAuthenticated || !user) {
                return;
            }
            const interval = setInterval({
                "useNotifications.useEffect.interval": ()=>{
                    fetchNotifications();
                }
            }["useNotifications.useEffect.interval"], 30000); // 30 seconds
            return ({
                "useNotifications.useEffect": ()=>clearInterval(interval)
            })["useNotifications.useEffect"];
        }
    }["useNotifications.useEffect"], [
        isAuthenticated,
        user,
        fetchNotifications
    ]);
    return {
        notifications,
        unreadCount,
        totalCount,
        loading,
        error,
        fetchNotifications,
        markAsRead,
        markAllAsRead,
        refreshNotifications,
        getNotificationCounts
    };
};
_s(useNotifications, "kkjUCGJ62uTpKH/ARdYUNXxjAuo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/notifications/NotificationModal.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/Modal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$notifications$2f$NotificationItem$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/notifications/NotificationItem.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Loader.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useNotifications$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useNotifications.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/ToastContext.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
const NotificationModal = (param)=>{
    let { isOpen, onClose } = param;
    _s();
    const { notifications, unreadCount, loading, error, markAsRead, markAllAsRead, refreshNotifications } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useNotifications$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotifications"])();
    const { showSuccess, showError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    const [filter, setFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('unread');
    const [localLoading, setLocalLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Filter notifications based on selected filter
    const filteredNotifications = notifications.filter((notification)=>{
        if (filter === 'unread') {
            return !notification.read_at;
        }
        return true;
    });
    // Refresh notifications when modal opens
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NotificationModal.useEffect": ()=>{
            if (isOpen) {
                refreshNotifications();
            }
        }
    }["NotificationModal.useEffect"], [
        isOpen,
        refreshNotifications
    ]);
    const handleMarkAsRead = async (id)=>{
        try {
            await markAsRead(id);
            showSuccess('Notification marked as read');
        } catch (error) {
            console.error('Error marking notification as read:', error);
            showError('Failed to mark notification as read');
        }
    };
    const handleMarkAllAsRead = async ()=>{
        if (unreadCount === 0) {
            showError('No unread notifications to mark');
            return;
        }
        setLocalLoading(true);
        try {
            await markAllAsRead();
            showSuccess('All notifications marked as read');
        } catch (error) {
            console.error('Error marking all notifications as read:', error);
            showError('Failed to mark all notifications as read');
        } finally{
            setLocalLoading(false);
        }
    };
    const handleNotificationClick = (notification)=>{
        // Handle notification click - could navigate to related page
        if (notification.entity_type === 'application' && notification.entity_id) {
            // Navigate to application details
            window.location.href = "/customer/my-licenses?application_id=".concat(notification.entity_id);
        }
    };
    const handleRefresh = async ()=>{
        setLocalLoading(true);
        try {
            await refreshNotifications();
        } catch (error) {
            console.error('Error refreshing notifications:', error);
            showError('Failed to refresh notifications');
        } finally{
            setLocalLoading(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        isOpen: isOpen,
        onClose: onClose,
        title: "Notifications",
        size: "lg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex flex-col h-96",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>setFilter('all'),
                                        className: "px-3 py-1 text-sm rounded-md transition-colors duration-200 ".concat(filter === 'all' ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300' : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'),
                                        children: [
                                            "All (",
                                            notifications.length,
                                            ")"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                                        lineNumber: 111,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>setFilter('unread'),
                                        className: "px-3 py-1 text-sm rounded-md transition-colors duration-200 ".concat(filter === 'unread' ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300' : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'),
                                        children: [
                                            "Unread (",
                                            unreadCount,
                                            ")"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                                        lineNumber: 121,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                                lineNumber: 110,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                            lineNumber: 108,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: handleRefresh,
                                    disabled: loading || localLoading,
                                    className: "p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 disabled:opacity-50",
                                    title: "Refresh notifications",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                        className: "ri-refresh-line ".concat(loading || localLoading ? 'animate-spin' : '')
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                                        lineNumber: 142,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                                    lineNumber: 136,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                unreadCount > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: handleMarkAllAsRead,
                                    disabled: loading || localLoading,
                                    className: "px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 disabled:opacity-50",
                                    children: "Mark all as read"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                                    lineNumber: 146,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                            lineNumber: 135,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                    lineNumber: 107,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex-1 overflow-y-auto",
                    children: loading && notifications.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-center h-full",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            message: "Loading notifications..."
                        }, void 0, false, {
                            fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                            lineNumber: 161,
                            columnNumber: 15
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                        lineNumber: 160,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0)) : error ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col items-center justify-center h-full text-center p-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-error-warning-line text-4xl text-red-500 mb-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                                lineNumber: 165,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium text-gray-900 dark:text-gray-100 mb-1",
                                children: "Error Loading Notifications"
                            }, void 0, false, {
                                fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                                lineNumber: 166,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-gray-600 dark:text-gray-400 mb-4",
                                children: error
                            }, void 0, false, {
                                fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                                lineNumber: 169,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleRefresh,
                                className: "px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200",
                                children: "Try Again"
                            }, void 0, false, {
                                fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                                lineNumber: 172,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                        lineNumber: 164,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0)) : filteredNotifications.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col items-center justify-center h-full text-center p-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-notification-off-line text-4xl text-gray-400 mb-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                                lineNumber: 181,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium text-gray-900 dark:text-gray-100 mb-1",
                                children: filter === 'unread' ? 'No Unread Notifications' : 'No Notifications'
                            }, void 0, false, {
                                fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                                lineNumber: 182,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-gray-600 dark:text-gray-400",
                                children: filter === 'unread' ? 'All caught up! You have no unread notifications.' : 'You have no notifications at this time.'
                            }, void 0, false, {
                                fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                                lineNumber: 185,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                        lineNumber: 180,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "divide-y divide-gray-200 dark:divide-gray-700",
                        children: filteredNotifications.map((notification)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$notifications$2f$NotificationItem$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                notification: notification,
                                onMarkAsRead: handleMarkAsRead,
                                onNotificationClick: handleNotificationClick
                            }, notification.notification_id, false, {
                                fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                                lineNumber: 195,
                                columnNumber: 17
                            }, ("TURBOPACK compile-time value", void 0)))
                    }, void 0, false, {
                        fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                        lineNumber: 193,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                    lineNumber: 158,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                filteredNotifications.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "p-4 border-t border-gray-200 dark:border-gray-700 text-center",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-600 dark:text-gray-400",
                        children: [
                            "Showing ",
                            filteredNotifications.length,
                            " of ",
                            notifications.length,
                            " notifications"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                        lineNumber: 209,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/notifications/NotificationModal.tsx",
                    lineNumber: 208,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/notifications/NotificationModal.tsx",
            lineNumber: 105,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/notifications/NotificationModal.tsx",
        lineNumber: 99,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(NotificationModal, "Km3VnPyh+DdI0NnNdPuesvtaric=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useNotifications$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotifications"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"]
    ];
});
_c = NotificationModal;
const __TURBOPACK__default__export__ = NotificationModal;
var _c;
__turbopack_context__.k.register(_c, "NotificationModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/common/NotificationBell.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$notificationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/notificationService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/ToastContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$notifications$2f$NotificationModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/notifications/NotificationModal.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
const NotificationBell = (param)=>{
    let { className = '' } = param;
    _s();
    const { user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const { showError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    const [unreadCount, setUnreadCount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [isModalOpen, setIsModalOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Fetch notification count
    const fetchNotificationCount = async ()=>{
        if (!user) return;
        try {
            const data = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$notificationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["notificationService"].getNotificationCount();
            setUnreadCount(data.unread);
        } catch (error) {
            console.error('Error fetching notification count:', error);
            showError('Failed to load notification count');
        }
    };
    // Initial fetch
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NotificationBell.useEffect": ()=>{
            fetchNotificationCount();
        }
    }["NotificationBell.useEffect"], [
        user
    ]);
    // Poll for new notifications every 30 seconds
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NotificationBell.useEffect": ()=>{
            const interval = setInterval(fetchNotificationCount, 30000);
            return ({
                "NotificationBell.useEffect": ()=>clearInterval(interval)
            })["NotificationBell.useEffect"];
        }
    }["NotificationBell.useEffect"], [
        user
    ]);
    // Handle modal close and refresh count
    const handleModalClose = ()=>{
        setIsModalOpen(false);
        fetchNotificationCount(); // Refresh count when modal closes
    };
    if (!user) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative ".concat(className),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: ()=>setIsModalOpen(true),
                    className: "relative p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800",
                    title: "Notifications",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-notification-line text-xl"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/NotificationBell.tsx",
                            lineNumber: 60,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        unreadCount > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[1.25rem] h-5 flex items-center justify-center px-1",
                            children: unreadCount > 99 ? '99+' : unreadCount
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/NotificationBell.tsx",
                            lineNumber: 62,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/NotificationBell.tsx",
                    lineNumber: 55,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/common/NotificationBell.tsx",
                lineNumber: 53,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$notifications$2f$NotificationModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                isOpen: isModalOpen,
                onClose: handleModalClose
            }, void 0, false, {
                fileName: "[project]/src/components/common/NotificationBell.tsx",
                lineNumber: 70,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true);
};
_s(NotificationBell, "myj/V4AhEcavWX81LTpIVAVVTB8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"]
    ];
});
_c = NotificationBell;
const __TURBOPACK__default__export__ = NotificationBell;
var _c;
__turbopack_context__.k.register(_c, "NotificationBell");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/customer/CustomerLayout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$LoadingContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/LoadingContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$LogoutButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/LogoutButton.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$NotificationBell$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/NotificationBell.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
const CustomerLayout = (param)=>{
    let { children, breadcrumbs } = param;
    _s();
    const [isMobileSidebarOpen, setIsMobileSidebarOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isUserDropdownOpen, setIsUserDropdownOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const { showLoader } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$LoadingContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLoading"])();
    // Memoize navigation items to prevent unnecessary re-renders
    const navigationItems = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "CustomerLayout.useMemo[navigationItems]": ()=>[
                {
                    name: 'Dashboard',
                    href: '/customer',
                    icon: 'ri-dashboard-line',
                    current: pathname === '/customer'
                },
                {
                    name: 'My Licenses',
                    href: '/customer/my-licenses',
                    icon: 'ri-key-line',
                    current: pathname === '/customer/my-licenses'
                },
                {
                    name: 'New Applications',
                    href: '/customer/applications',
                    icon: 'ri-file-list-3-line',
                    current: pathname === '/customer/applications'
                },
                {
                    name: 'Invoices & Payments',
                    href: '/customer/payments',
                    icon: 'ri-bank-card-line',
                    current: pathname === '/customer/payments'
                },
                {
                    name: 'Documents',
                    href: '/customer/documents',
                    icon: 'ri-file-text-line',
                    current: pathname === '/customer/documents'
                },
                {
                    name: 'Procurement',
                    href: '/customer/procurement',
                    icon: 'ri-auction-line',
                    current: pathname === '/customer/procurement'
                },
                {
                    name: 'Request Resource',
                    href: '/customer/resources',
                    icon: 'ri-hand-heart-line',
                    current: pathname === '/customer/resources'
                }
            ]
    }["CustomerLayout.useMemo[navigationItems]"], [
        pathname
    ]);
    const supportItems = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "CustomerLayout.useMemo[supportItems]": ()=>[
                {
                    name: 'Data Protection',
                    href: '/customer/data-protection',
                    icon: 'ri-shield-keyhole-line'
                },
                {
                    name: 'Help Center',
                    href: '/customer/help',
                    icon: 'ri-question-line'
                }
            ]
    }["CustomerLayout.useMemo[supportItems]"], []);
    // Prefetch customer pages on mount for faster navigation
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CustomerLayout.useEffect": ()=>{
            const prefetchPages = {
                "CustomerLayout.useEffect.prefetchPages": ()=>{
                    const customerPages = [
                        '/customer',
                        '/customer/applications',
                        '/customer/applications/standards',
                        '/customer/payments',
                        '/customer/my-licenses',
                        '/customer/procurement',
                        '/customer/profile',
                        '/customer/data-protection',
                        '/customer/resources',
                        '/customer/help'
                    ];
                    customerPages.forEach({
                        "CustomerLayout.useEffect.prefetchPages": (page)=>{
                            router.prefetch(page);
                        }
                    }["CustomerLayout.useEffect.prefetchPages"]);
                }
            }["CustomerLayout.useEffect.prefetchPages"];
            // Delay prefetching to not interfere with initial page load
            const timer = setTimeout(prefetchPages, 1000);
            return ({
                "CustomerLayout.useEffect": ()=>clearTimeout(timer)
            })["CustomerLayout.useEffect"];
        }
    }["CustomerLayout.useEffect"], [
        router
    ]);
    const toggleMobileSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CustomerLayout.useCallback[toggleMobileSidebar]": ()=>{
            setIsMobileSidebarOpen(!isMobileSidebarOpen);
        }
    }["CustomerLayout.useCallback[toggleMobileSidebar]"], [
        isMobileSidebarOpen
    ]);
    const handleNavClick = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CustomerLayout.useCallback[handleNavClick]": (href, name)=>{
            const pageMessages = {
                '/customer': 'Loading Dashboard...',
                '/customer/my-licenses': 'Loading My Licenses...',
                '/customer/applications': 'Loading Applications...',
                '/customer/applications/apply/': 'Loading Standards License Options...',
                '/customer/payments': 'Loading Payments...',
                '/customer/documents': 'Loading Documents...',
                '/customer/procurement': 'Loading Procurement...',
                '/customer/resources': 'Loading Resources...',
                '/customer/data-protection': 'Loading Data Protection...',
                '/customer/help': 'Loading Help Center...',
                '/customer/profile': 'Loading Profile...',
                '/customer/settings': 'Loading Settings...'
            };
            const message = pageMessages[href] || "Loading ".concat(name, "...");
            showLoader(message);
            setIsMobileSidebarOpen(false);
        }
    }["CustomerLayout.useCallback[handleNavClick]"], [
        showLoader
    ]);
    const handleNavHover = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CustomerLayout.useCallback[handleNavHover]": (href)=>{
            // Prefetch on hover for instant navigation
            router.prefetch(href);
        }
    }["CustomerLayout.useCallback[handleNavHover]"], [
        router
    ]);
    const toggleUserDropdown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CustomerLayout.useCallback[toggleUserDropdown]": ()=>{
            setIsUserDropdownOpen(!isUserDropdownOpen);
        }
    }["CustomerLayout.useCallback[toggleUserDropdown]"], [
        isUserDropdownOpen
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex h-screen overflow-hidden",
        children: [
            isMobileSidebarOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden",
                onClick: ()=>setIsMobileSidebarOpen(false)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                lineNumber: 153,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("aside", {
                className: "\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\n        ".concat(isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0', "\n      "),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col h-full",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    src: "/images/macra-logo.png",
                                    alt: "MACRA Logo",
                                    className: "max-h-12 w-auto",
                                    width: 120,
                                    height: 48,
                                    priority: true
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 168,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                lineNumber: 167,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                            lineNumber: 166,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                            className: "mt-6 px-4 side-nav",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-1",
                                    children: navigationItems.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: item.href,
                                            onClick: ()=>handleNavClick(item.href, item.name),
                                            onMouseEnter: ()=>handleNavHover(item.href),
                                            className: "\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\n                    ".concat(item.current ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100', "\n                  "),
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-5 h-5 flex items-center justify-center mr-3 ".concat(item.current ? 'text-red-600 dark:text-red-400' : ''),
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                        className: item.icon
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                        lineNumber: 198,
                                                        columnNumber: 21
                                                    }, ("TURBOPACK compile-time value", void 0))
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 197,
                                                    columnNumber: 19
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                item.name
                                            ]
                                        }, item.name, true, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 184,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 182,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mt-8",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider",
                                            children: "Support"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 207,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "mt-2 space-y-1",
                                            children: supportItems.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    href: item.href,
                                                    onClick: ()=>handleNavClick(item.href, item.name),
                                                    onMouseEnter: ()=>handleNavHover(item.href),
                                                    className: "flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "w-5 h-5 flex items-center justify-center mr-3",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                className: item.icon
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                                lineNumber: 220,
                                                                columnNumber: 23
                                                            }, ("TURBOPACK compile-time value", void 0))
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                            lineNumber: 219,
                                                            columnNumber: 21
                                                        }, ("TURBOPACK compile-time value", void 0)),
                                                        item.name
                                                    ]
                                                }, item.name, true, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 212,
                                                    columnNumber: 19
                                                }, ("TURBOPACK compile-time value", void 0)))
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 210,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 206,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                            lineNumber: 180,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                    lineNumber: 164,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                lineNumber: 160,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 flex flex-col overflow-hidden",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                        className: "bg-white dark:bg-gray-800 shadow-sm z-10",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between h-16 px-4 sm:px-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    type: "button",
                                    onClick: toggleMobileSidebar,
                                    className: "md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none",
                                    "aria-label": "Open mobile menu",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-6 h-6 flex items-center justify-center",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: "ri-menu-line ri-lg"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 245,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                        lineNumber: 244,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 238,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-1",
                                    children: breadcrumbs && breadcrumbs.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                                        className: "flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400",
                                        children: breadcrumbs.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Fragment, {
                                                children: [
                                                    index > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                        className: "ri-arrow-right-s-line"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                        lineNumber: 254,
                                                        columnNumber: 37
                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                    item.href ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        href: item.href,
                                                        className: "hover:text-primary",
                                                        children: item.label
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                        lineNumber: 256,
                                                        columnNumber: 25
                                                    }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-gray-900 dark:text-gray-100",
                                                        children: item.label
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                        lineNumber: 260,
                                                        columnNumber: 25
                                                    }, ("TURBOPACK compile-time value", void 0))
                                                ]
                                            }, index, true, {
                                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                lineNumber: 253,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0)))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                        lineNumber: 251,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 249,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$NotificationBell$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            className: "mr-4"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 269,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    type: "button",
                                                    onClick: toggleUserDropdown,
                                                    className: "flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "sr-only",
                                                            children: "Open user menu"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                            lineNumber: 277,
                                                            columnNumber: 19
                                                        }, ("TURBOPACK compile-time value", void 0)),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            className: "h-8 w-8 rounded-full",
                                                            src: (user === null || user === void 0 ? void 0 : user.profile_image) || "https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp",
                                                            alt: "Profile",
                                                            width: 32,
                                                            height: 32
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                            lineNumber: 278,
                                                            columnNumber: 19
                                                        }, ("TURBOPACK compile-time value", void 0))
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 272,
                                                    columnNumber: 17
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                isUserDropdownOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "py-1",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                href: "/customer/profile",
                                                                className: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",
                                                                onClick: ()=>{
                                                                    handleNavClick('/customer/profile', 'Profile');
                                                                    setIsUserDropdownOpen(false);
                                                                },
                                                                children: "Your Profile"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                                lineNumber: 290,
                                                                columnNumber: 23
                                                            }, ("TURBOPACK compile-time value", void 0)),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$LogoutButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                variant: "text",
                                                                size: "sm",
                                                                className: "w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",
                                                                showConfirmation: true,
                                                                children: "Sign out"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                                lineNumber: 301,
                                                                columnNumber: 23
                                                            }, ("TURBOPACK compile-time value", void 0))
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                        lineNumber: 289,
                                                        columnNumber: 21
                                                    }, ("TURBOPACK compile-time value", void 0))
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 288,
                                                    columnNumber: 19
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 271,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 268,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                            lineNumber: 237,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                        lineNumber: 236,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                        className: "flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "py-6",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
                                children: children
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                lineNumber: 320,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                            lineNumber: 319,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                        lineNumber: 318,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                lineNumber: 234,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
        lineNumber: 150,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(CustomerLayout, "Bvue6xx7qybDlJR/560gagzUyBI=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$LoadingContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLoading"]
    ];
});
_c = CustomerLayout;
const __TURBOPACK__default__export__ = CustomerLayout;
var _c;
__turbopack_context__.k.register(_c, "CustomerLayout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/config/licenseTypeStepConfig.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * License Type Step Configuration System
 *
 * SINGLE SOURCE OF TRUTH for all license type step configurations
 *
 * This consolidated configuration system defines:
 * - Form steps required for each license type
 * - Step order and navigation flow
 * - Validation requirements and estimated times
 * - Fallback configurations for unknown license types
 *
 * Supported license type codes:
 * - telecommunications
 * - postal_services
 * - standards_compliance
 * - broadcasting
 * - spectrum_management
 * - clf (Consumer Lending and Finance)
 * - shortcode
 *
 * Features:
 * - Optimized step loading based on license type codes
 * - Automatic fallback for unsupported types
 * - Smart license type resolution (UUID, code, name mapping)
 * - Comprehensive helper functions for navigation and progress tracking
 */ __turbopack_context__.s({
    "LICENSE_TYPE_STEP_CONFIGS": ()=>LICENSE_TYPE_STEP_CONFIGS,
    "calculateProgress": ()=>calculateProgress,
    "getLicenseTypeStepConfig": ()=>getLicenseTypeStepConfig,
    "getNextStep": ()=>getNextStep,
    "getOptimizedStepConfig": ()=>getOptimizedStepConfig,
    "getOptionalSteps": ()=>getOptionalSteps,
    "getPreviousStep": ()=>getPreviousStep,
    "getRequiredSteps": ()=>getRequiredSteps,
    "getStepByIndex": ()=>getStepByIndex,
    "getStepByRoute": ()=>getStepByRoute,
    "getStepIndex": ()=>getStepIndex,
    "getStepsByLicenseTypeCode": ()=>getStepsByLicenseTypeCode,
    "getTotalSteps": ()=>getTotalSteps,
    "isLicenseTypeCodeSupported": ()=>isLicenseTypeCodeSupported
});
// ============================================================================
// BASE STEP DEFINITIONS
// ============================================================================
/**
 * Reusable step configurations that can be composed into different license types
 */ const BASE_STEPS = {
    // Core Information Steps
    applicantInfo: {
        id: 'applicant-info',
        name: 'Applicant Information',
        component: 'ApplicantInfo',
        route: 'applicant-info',
        required: true,
        description: 'Personal or company information of the applicant',
        estimatedTime: '5'
    },
    // Equipment-related Steps
    equipmentDetails: {
        id: 'equipment-details',
        name: 'Details for Equipment',
        component: 'EquipmentDetails',
        route: 'equipment-details',
        required: true,
        description: 'Equipment details including IMEI, brand, model, and manufacturer information',
        estimatedTime: '10'
    },
    imeiValidation: {
        id: 'imei-validation',
        name: 'Equipment Details',
        component: 'IMEIValidation',
        route: 'imei-validation',
        required: true,
        description: 'Device IMEI validation for standards compliance',
        estimatedTime: '5'
    },
    addressInfo: {
        id: 'address-info',
        name: 'Address Information',
        component: 'AddressInfo',
        route: 'address-info',
        required: true,
        description: 'Physical and postal address details',
        estimatedTime: '3'
    },
    contactInfo: {
        id: 'contact-info',
        name: 'Contact Information',
        component: 'ContactInfo',
        route: 'contact-info',
        required: true,
        description: 'Contact details and communication preferences',
        estimatedTime: '5'
    },
    // Organizational Steps
    management: {
        id: 'management',
        name: 'Management Structure',
        component: 'Management',
        route: 'management',
        required: false,
        description: 'Management team and organizational structure',
        estimatedTime: '8'
    },
    professionalServices: {
        id: 'professional-services',
        name: 'Professional Services',
        component: 'ProfessionalServices',
        route: 'professional-services',
        required: false,
        description: 'External consultants and service providers',
        estimatedTime: '6'
    },
    // Service and Compliance Steps
    serviceScope: {
        id: 'service-scope',
        name: 'Service Scope',
        component: 'ServiceScope',
        route: 'service-scope',
        required: true,
        description: 'Services offered and geographic coverage',
        estimatedTime: '8'
    },
    legalHistory: {
        id: 'legal-history',
        name: 'Legal History',
        component: 'LegalHistory',
        route: 'legal-history',
        required: true,
        description: 'Legal compliance and regulatory history',
        estimatedTime: '5'
    },
    // Final Steps
    documents: {
        id: 'documents',
        name: 'Required Documents',
        component: 'Documents',
        route: 'documents',
        required: true,
        description: 'Upload required documents for license application',
        estimatedTime: '10'
    },
    submit: {
        id: 'submit',
        name: 'Finalise Application',
        component: 'Submit',
        route: 'submit',
        required: true,
        description: 'Final review and submission of application',
        estimatedTime: '5'
    }
};
const LICENSE_TYPE_STEP_CONFIGS = {
    telecommunications: {
        licenseTypeId: 'telecommunications',
        name: 'Telecommunications License',
        description: 'License for telecommunications service providers including ISPs, mobile operators, and fixed-line services',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.addressInfo,
            BASE_STEPS.contactInfo,
            BASE_STEPS.management,
            BASE_STEPS.serviceScope,
            BASE_STEPS.legalHistory,
            BASE_STEPS.documents,
            BASE_STEPS.submit
        ],
        estimatedTotalTime: '97 minutes',
        requirements: [
            'Business registration certificate',
            'Tax compliance certificate',
            'Technical specifications',
            'Financial statements',
            'Management CVs',
            'Network coverage plans'
        ]
    },
    postal_services: {
        licenseTypeId: 'postal_services',
        name: 'Postal Services License',
        description: 'License for postal and courier service providers',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.addressInfo,
            BASE_STEPS.contactInfo,
            BASE_STEPS.legalHistory,
            BASE_STEPS.documents,
            BASE_STEPS.submit
        ],
        estimatedTotalTime: '65 minutes',
        requirements: [
            'Business registration certificate',
            'Fleet inventory',
            'Service coverage map',
            'Insurance certificates',
            'Premises documentation'
        ]
    },
    standards_compliance: {
        licenseTypeId: 'standards_compliance',
        name: 'Standards Compliance License',
        description: 'License for standards compliance and certification services',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.imeiValidation,
            BASE_STEPS.addressInfo,
            BASE_STEPS.contactInfo,
            BASE_STEPS.management,
            BASE_STEPS.professionalServices,
            BASE_STEPS.serviceScope,
            BASE_STEPS.legalHistory,
            BASE_STEPS.documents,
            BASE_STEPS.submit
        ],
        estimatedTotalTime: '87 minutes',
        requirements: [
            'Device IMEI number',
            'Accreditation certificates',
            'Technical competency proof',
            'Quality management system',
            'Laboratory facilities documentation',
            'Staff qualifications'
        ]
    },
    type_approval_certificate: {
        licenseTypeId: 'type_approval_certificate',
        name: 'Type Approval Certificate',
        description: 'Certificate for equipment type approval and standards compliance',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.equipmentDetails,
            BASE_STEPS.documents,
            BASE_STEPS.submit
        ],
        estimatedTotalTime: '20 minutes',
        requirements: [
            'Device IMEI number',
            'Equipment brand and model details',
            'Manufacturer information',
            'ETSI Documents',
            'Test Reports from accredited Labs',
            'Technical Specifications',
            'Authorization Letter (Power of Attorney)',
            'Declaration of Conformity',
            'Any Copies of Approval from ITU Region 1',
            'Any other necessary extra Documents which will help in analyzing the Model'
        ]
    },
    broadcasting: {
        licenseTypeId: 'broadcasting',
        name: 'Broadcasting License',
        description: 'License for radio and television broadcasting services',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.addressInfo,
            BASE_STEPS.contactInfo,
            BASE_STEPS.management,
            BASE_STEPS.serviceScope,
            BASE_STEPS.professionalServices,
            BASE_STEPS.legalHistory,
            BASE_STEPS.documents,
            BASE_STEPS.submit
        ],
        estimatedTotalTime: '86 minutes',
        requirements: [
            'Broadcasting equipment specifications',
            'Content programming plan',
            'Studio facility documentation',
            'Transmission coverage maps',
            'Local content compliance plan'
        ]
    },
    spectrum_management: {
        licenseTypeId: 'spectrum_management',
        name: 'Spectrum Management License',
        description: 'License for radio frequency spectrum management and allocation',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.management,
            BASE_STEPS.serviceScope,
            BASE_STEPS.professionalServices,
            BASE_STEPS.legalHistory,
            BASE_STEPS.documents,
            BASE_STEPS.submit
        ],
        estimatedTotalTime: '89 minutes',
        requirements: [
            'Spectrum usage plan',
            'Technical interference analysis',
            'Equipment type approval',
            'Frequency coordination agreements',
            'Monitoring capabilities documentation'
        ]
    },
    shortcode: {
        licenseTypeId: 'shortcode',
        name: 'Short Code License',
        description: 'Short code allocation and management license',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.addressInfo,
            BASE_STEPS.documents,
            BASE_STEPS.submit
        ],
        estimatedTotalTime: '23 minutes',
        requirements: [
            'Business registration certificate',
            'Service description',
            'Technical specifications',
            'Content compliance plan'
        ]
    },
    clf: {
        licenseTypeId: 'clf',
        name: 'Consumer Lending and Finance License',
        description: 'License for consumer lending and finance services',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.addressInfo,
            BASE_STEPS.contactInfo,
            BASE_STEPS.management,
            BASE_STEPS.professionalServices,
            BASE_STEPS.legalHistory,
            BASE_STEPS.documents,
            BASE_STEPS.submit
        ],
        estimatedTotalTime: '50 minutes',
        requirements: [
            'Financial institution license',
            'Capital adequacy documentation',
            'Risk management framework',
            'Consumer protection policies',
            'Anti-money laundering procedures'
        ]
    }
};
// ============================================================================
// MAPPING AND FALLBACK CONFIGURATIONS
// ============================================================================
/**
 * License type name to configuration key mapping
 * Supports various naming conventions and aliases
 */ const LICENSE_TYPE_NAME_MAPPING = {
    'telecommunications': 'telecommunications',
    'postal_services': 'postal_services',
    'standards_compliance': 'standards_compliance',
    'broadcasting': 'broadcasting',
    'spectrum_management': 'spectrum_management',
    'clf': 'clf',
    'consumer lending and finance': 'clf',
    'shortcode': 'shortcode',
    'Type Approval Certificate': 'type_approval_certificate'
};
/**
 * Default fallback configuration for unknown license types
 * Includes all possible steps to ensure comprehensive coverage
 */ const DEFAULT_FALLBACK_CONFIG = {
    licenseTypeId: 'default',
    name: 'License Application',
    description: 'Comprehensive application process with all required steps',
    steps: [
        BASE_STEPS.applicantInfo,
        BASE_STEPS.addressInfo,
        BASE_STEPS.contactInfo,
        BASE_STEPS.management,
        BASE_STEPS.professionalServices,
        BASE_STEPS.serviceScope,
        BASE_STEPS.legalHistory,
        BASE_STEPS.documents,
        BASE_STEPS.submit
    ],
    estimatedTotalTime: '55 minutes',
    requirements: [
        'Business registration certificate',
        'Tax compliance certificate',
        'Financial statements',
        'Management CVs',
        'Professional qualifications',
        'Service documentation'
    ]
};
var licenseCategory = null;
const getLicenseTypeStepConfig = function(licenseTypeCode) {
    let category = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;
    if (category) {
        licenseCategory = category;
    }
    // Handle license category override logic
    if (licenseCategory) {
        if (isLicenseTypeCodeSupported(licenseCategory.name)) {
            licenseTypeCode = licenseCategory.name;
        } else {
        // Default to shortcode for unrecognized categories
        }
    }
    // Validate input
    if (!licenseTypeCode || typeof licenseTypeCode !== 'string') {
        return DEFAULT_FALLBACK_CONFIG;
    }
    // Strategy 1: Direct exact match
    let config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode];
    if (config) {
        return config;
    }
    // Strategy 2: Normalized lookup (lowercase with underscores)
    const normalizedId = licenseTypeCode.toLowerCase().replace(/[^a-z0-9]/g, '_');
    config = LICENSE_TYPE_STEP_CONFIGS[normalizedId];
    if (config) {
        return config;
    }
    // Strategy 3: Name mapping for common variations
    const mappedKey = LICENSE_TYPE_NAME_MAPPING[normalizedId];
    if (mappedKey) {
        return LICENSE_TYPE_STEP_CONFIGS[mappedKey];
    }
    // Strategy 4: Partial matching for known license type codes
    const knownCodes = Object.keys(LICENSE_TYPE_STEP_CONFIGS);
    const partialMatch = knownCodes.find((code)=>licenseTypeCode.toLowerCase().includes(code) || code.includes(licenseTypeCode.toLowerCase()));
    if (partialMatch) {
        return LICENSE_TYPE_STEP_CONFIGS[partialMatch];
    }
    // Fallback to default configuration
    return DEFAULT_FALLBACK_CONFIG;
};
const isLicenseTypeCodeSupported = (licenseTypeCode)=>{
    return typeof LICENSE_TYPE_NAME_MAPPING[licenseTypeCode] !== 'undefined';
};
const getStepsByLicenseTypeCode = (licenseTypeCode)=>{
    return getLicenseTypeStepConfig(licenseTypeCode).steps;
};
const getStepByRoute = (licenseTypeCode, stepRoute)=>{
    const config = getLicenseTypeStepConfig(licenseTypeCode);
    return config.steps.find((step)=>step.route === stepRoute) || null;
};
const getStepByIndex = (licenseTypeCode, stepIndex)=>{
    const config = getLicenseTypeStepConfig(licenseTypeCode);
    if (stepIndex < 0 || stepIndex >= config.steps.length) return null;
    return config.steps[stepIndex];
};
const getStepIndex = (licenseTypeCode, stepRoute)=>{
    const config = getLicenseTypeStepConfig(licenseTypeCode);
    return config.steps.findIndex((step)=>step.route === stepRoute);
};
const getNextStep = (licenseTypeCode, currentStepRoute)=>{
    const config = getLicenseTypeStepConfig(licenseTypeCode);
    const currentIndex = getStepIndex(licenseTypeCode, currentStepRoute);
    if (currentIndex === -1 || currentIndex >= config.steps.length - 1) return null;
    return config.steps[currentIndex + 1];
};
const getPreviousStep = (licenseTypeCode, currentStepRoute)=>{
    const config = getLicenseTypeStepConfig(licenseTypeCode);
    const currentIndex = getStepIndex(licenseTypeCode, currentStepRoute);
    if (currentIndex <= 0) return null;
    return config.steps[currentIndex - 1];
};
const getTotalSteps = (licenseTypeCode)=>{
    const config = getLicenseTypeStepConfig(licenseTypeCode);
    return config.steps.length;
};
const getRequiredSteps = (licenseTypeCode)=>{
    const config = getLicenseTypeStepConfig(licenseTypeCode);
    return config.steps.filter((step)=>step.required);
};
const getOptionalSteps = (licenseTypeCode)=>{
    const config = getLicenseTypeStepConfig(licenseTypeCode);
    return config.steps.filter((step)=>!step.required);
};
const calculateProgress = (licenseTypeCode, completedSteps)=>{
    const config = getLicenseTypeStepConfig(licenseTypeCode);
    const totalSteps = config.steps.length;
    const completed = completedSteps.length;
    return Math.round(completed / totalSteps * 100);
};
const getOptimizedStepConfig = (licenseTypeCode)=>{
    if (isLicenseTypeCodeSupported(licenseTypeCode)) {
        return LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode];
    }
    return DEFAULT_FALLBACK_CONFIG;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/customer-api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "CustomerApiService": ()=>CustomerApiService,
    "customerApi": ()=>customerApi,
    "customerApiClient": ()=>customerApiClient
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-client] (ecmascript)");
;
;
;
;
// API Configuration
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:3001") || 'http://localhost:3001';
// Create axios instance for customer portal (same as staff portal)
const customerApiClient = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: API_BASE_URL,
    timeout: 120000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
});
// Create auth-specific client (same as staff portal)
const customerAuthApiClient = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: "".concat(API_BASE_URL, "/auth"),
    timeout: 120000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
});
customerAuthApiClient.interceptors.response.use((response)=>{
    if ("TURBOPACK compile-time truthy", 1) {
        console.log('Customer Auth API Response Success:', {
            status: response.status,
            statusText: response.statusText,
            url: response.config.url
        });
    }
    return response;
}, (error)=>{
    if ("TURBOPACK compile-time truthy", 1) {
        var _error_response, _error_response1, _error_config, _error_config1, _error_config2, _error_response2, _error_config3, _error_config4;
        console.error('Customer Auth API Interceptor Error:', {
            message: (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error',
            code: (error === null || error === void 0 ? void 0 : error.code) || 'NO_CODE',
            status: (error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) || 'NO_STATUS',
            statusText: (error === null || error === void 0 ? void 0 : (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText) || 'NO_STATUS_TEXT',
            url: (error === null || error === void 0 ? void 0 : (_error_config = error.config) === null || _error_config === void 0 ? void 0 : _error_config.url) || 'NO_URL',
            method: (error === null || error === void 0 ? void 0 : (_error_config1 = error.config) === null || _error_config1 === void 0 ? void 0 : _error_config1.method) || 'NO_METHOD',
            baseURL: (error === null || error === void 0 ? void 0 : (_error_config2 = error.config) === null || _error_config2 === void 0 ? void 0 : _error_config2.baseURL) || 'NO_BASE_URL',
            isAxiosError: (error === null || error === void 0 ? void 0 : error.isAxiosError) || false,
            responseData: (error === null || error === void 0 ? void 0 : (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data) || 'NO_RESPONSE_DATA',
            requestData: (error === null || error === void 0 ? void 0 : (_error_config3 = error.config) === null || _error_config3 === void 0 ? void 0 : _error_config3.data) || 'NO_REQUEST_DATA',
            headers: (error === null || error === void 0 ? void 0 : (_error_config4 = error.config) === null || _error_config4 === void 0 ? void 0 : _error_config4.headers) || 'NO_HEADERS'
        });
    }
    // Don't handle 401 here, let the login method handle it
    return Promise.reject(error);
});
// Request interceptor to add auth token
customerApiClient.interceptors.request.use((config)=>{
    const token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('auth_token');
    if (token) {
        config.headers.Authorization = "Bearer ".concat(token);
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Response interceptor for error handling with retry logic
customerApiClient.interceptors.response.use((response)=>{
    return response;
}, async (error)=>{
    var _error_response, _error_response1;
    const originalRequest = error.config;
    // Handle 429 Rate Limiting
    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 429) {
        if (!originalRequest._retry) {
            originalRequest._retry = true;
            // Get retry delay from headers or use exponential backoff
            const retryAfter = error.response.headers['retry-after'];
            const delay = retryAfter ? parseInt(retryAfter) * 1000 : Math.min(1000 * Math.pow(2, originalRequest._retryCount || 0), 10000);
            originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;
            // Don't retry more than 3 times
            if (originalRequest._retryCount <= 3) {
                console.warn("Rate limited. Retrying after ".concat(delay, "ms (attempt ").concat(originalRequest._retryCount, ")"));
                await new Promise((resolve)=>setTimeout(resolve, delay));
                return customerApiClient(originalRequest);
            }
        }
    }
    // Handle 401 Unauthorized
    if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 401) {
        // Clear auth token and redirect to login
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove('auth_token');
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove('auth_user');
        window.location.href = '/auth/login';
    }
    return Promise.reject(error);
});
class CustomerApiService {
    // Request deduplication helper
    async deduplicateRequest(key, requestFn) {
        if (this.pendingRequests.has(key)) {
            return this.pendingRequests.get(key);
        }
        const promise = requestFn().finally(()=>{
            this.pendingRequests.delete(key);
        });
        this.pendingRequests.set(key, promise);
        return promise;
    }
    // Set auth token
    setAuthToken(token) {
        this.api.defaults.headers.common['Authorization'] = "Bearer ".concat(token);
    }
    // Remove auth token
    removeAuthToken() {
        delete this.api.defaults.headers.common['Authorization'];
    }
    async logout() {
        const response = await customerAuthApiClient.post('/logout');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async refreshToken() {
        const response = await customerAuthApiClient.post('/refresh');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // 2FA endpoints
    async generateTwoFactorCode(userId, action) {
        const response = await customerAuthApiClient.post('/generate-2fa', {
            user_id: userId,
            action
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async verify2FA(data) {
        var _processApiResponse;
        const response = await customerAuthApiClient.post('/verify-2fa', data);
        // Handle response structure consistently with login
        if ((_processApiResponse = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response)) === null || _processApiResponse === void 0 ? void 0 : _processApiResponse.data) {
            const authData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response).data;
            // Map backend field names to frontend expected format
            const mappedAuthData = {
                access_token: authData.access_token,
                user: {
                    id: authData.user.user_id,
                    firstName: authData.user.first_name,
                    lastName: authData.user.last_name,
                    email: authData.user.email,
                    roles: authData.user.roles || [],
                    isAdmin: (authData.user.roles || []).includes('administrator'),
                    profileImage: authData.user.profile_image,
                    createdAt: authData.user.created_at || new Date().toISOString(),
                    lastLogin: authData.user.last_login,
                    organizationName: authData.user.organization_name,
                    two_factor_enabled: authData.user.two_factor_enabled
                }
            };
            return mappedAuthData;
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async setupTwoFactorAuth(data) {
        const response = await customerAuthApiClient.post('/setup-2fa', data);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // User profile endpoints
    async getProfile() {
        return this.deduplicateRequest('getProfile', async ()=>{
            const response = await this.api.get('/users/profile');
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        });
    }
    async updateProfile(profileData) {
        const response = await this.api.put('/users/profile', profileData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async deactivateAccount(deactivationData) {
        const response = await this.api.post('/users/deactivate', deactivationData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // Addressing endpoints
    async getAddresses() {
        const response = await this.api.get('/address/all');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async createAddress(addressData) {
        const response = await this.api.post('/address/create', addressData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getAddress(id) {
        const response = await this.api.get("/address/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async editAddress(addressData) {
        const { address_id, ...updateData } = addressData;
        if (!address_id) {
            throw new Error('Address ID is required for updating');
        }
        const response = await this.api.put("/address/".concat(address_id), updateData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getAddressesByEntity(entityType, entityId) {
        const response = await this.api.get("/address/all?entity_type=".concat(encodeURIComponent(entityType), "&entity_id=").concat(encodeURIComponent(entityId)));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async deleteAddress(id) {
        const response = await this.api.delete("/address/soft/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async searchPostcodes(searchParams) {
        const response = await this.api.post('/postal-codes/search', searchParams);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // License endpoints
    async getLicenses(params) {
        const response = await this.api.get('/licenses', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getLicense(id) {
        const response = await this.api.get("/licenses/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async createLicenseApplication(applicationData) {
        const response = await this.api.post('/license-applications', applicationData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // License Types endpoints
    async getLicenseTypes(params) {
        const response = await this.api.get('/license-types', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getLicenseType(id) {
        const response = await this.api.get("/license-types/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // License Categories endpoints
    async getLicenseCategories(params) {
        const response = await this.api.get('/license-categories', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getLicenseCategoriesByType(licenseTypeId) {
        const response = await this.api.get("/license-categories/by-license-type/".concat(licenseTypeId));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getLicenseCategoryTree(licenseTypeId) {
        const response = await this.api.get("/license-categories/license-type/".concat(licenseTypeId, "/tree"));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getLicenseCategory(id) {
        const response = await this.api.get("/license-categories/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // Application endpoints
    async getApplications(params) {
        const response = await this.api.get('/applications', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getApplication(id) {
        const response = await this.api.get("/applications/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async createApplication(applicationData) {
        const response = await this.api.post('/applications', applicationData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async updateApplication(id, applicationData) {
        const response = await this.api.put("/applications/".concat(id), applicationData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // Payment endpoints (customer-specific)
    async getPayments(params) {
        const response = await this.api.get('/payments/customer/my-payments', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getPayment(id) {
        const response = await this.api.get("/payments/customer/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async createPayment(paymentData) {
        const response = await this.api.post('/payments', paymentData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async uploadProofOfPayment(invoiceId, formData) {
        const response = await this.api.post("/payments/customer/invoice/".concat(invoiceId, "/proof-of-payment"), formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getPaymentStatistics() {
        const response = await this.api.get('/payments/customer/statistics');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getApplicationPayments(applicationId) {
        const response = await this.api.get("/payments/customer/application/".concat(applicationId));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getInvoicePayments(invoiceId) {
        const response = await this.api.get("/payments/customer/invoice/".concat(invoiceId));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // Invoice endpoints (customer-specific)
    async getInvoices(params) {
        const response = await this.api.get('/customer-invoices', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getInvoice(id) {
        const response = await this.api.get("/customer-invoices/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getInvoicesByEntity(entityType, entityId) {
        // For customer API, we use application-specific endpoint
        if (entityType === 'application') {
            const response = await this.api.get("/customer-invoices/application/".concat(entityId));
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        }
        // Fallback to general endpoint for other entity types
        const response = await this.api.get("/invoices/entity/".concat(entityType, "/").concat(entityId));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getApplicationInvoiceStatus(applicationId) {
        const response = await this.api.get("/customer-invoices/application/".concat(applicationId, "/status"));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getInvoiceStatistics() {
        const response = await this.api.get('/customer-invoices/statistics');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async downloadInvoice(invoiceId) {
        const response = await this.api.get("/customer-invoices/".concat(invoiceId, "/download"), {
            responseType: 'blob'
        });
        return response.data;
    }
    // Document endpoints
    async getDocuments(params) {
        const response = await this.api.get('/documents', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async uploadDocument(formData) {
        const response = await this.api.post('/documents/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async downloadDocument(id) {
        const response = await this.api.get("/documents/".concat(id, "/download"), {
            responseType: 'blob'
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // Dashboard statistics
    async getDashboardStats() {
        const response = await this.api.get('/dashboard/stats');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // Notifications
    async getNotifications(params) {
        const response = await this.api.get('/notifications', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async markNotificationAsRead(id) {
        const response = await this.api.patch("/notifications/".concat(id, "/read"));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // Procurement endpoints
    async getTenders(params) {
        const response = await this.api.get('/procurement/tenders', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getTender(id) {
        const response = await this.api.get("/procurement/tenders/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async payForTenderAccess(tenderId, paymentData) {
        const response = await this.api.post("/procurement/tenders/".concat(tenderId, "/pay-access"), paymentData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async downloadTenderDocument(documentId) {
        const response = await this.api.get("/procurement/documents/".concat(documentId, "/download"), {
            responseType: 'blob'
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getMyBids(params) {
        const response = await this.api.get('/procurement/my-bids', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getBid(id) {
        const response = await this.api.get("/procurement/bids/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async submitBid(formData) {
        const response = await this.api.post('/procurement/bids', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async updateBid(id, formData) {
        const response = await this.api.put("/procurement/bids/".concat(id), formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getProcurementPayments(params) {
        const response = await this.api.get('/procurement/payments', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getProcurementPayment(id) {
        const response = await this.api.get("/procurement/payments/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // Consumer Affairs endpoints
    async getComplaints(params) {
        const response = await this.api.get('/consumer-affairs/complaints', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getComplaint(id) {
        const response = await this.api.get("/consumer-affairs/complaints/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async submitComplaint(complaintData) {
        const formData = new FormData();
        formData.append('title', complaintData.title);
        formData.append('description', complaintData.description);
        formData.append('category', complaintData.category);
        if (complaintData.attachments) {
            complaintData.attachments.forEach((file, index)=>{
                formData.append("attachments[".concat(index, "]"), file);
            });
        }
        const response = await this.api.post('/consumer-affairs/complaints', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async updateComplaint(id, updates) {
        const response = await this.api.put("/consumer-affairs/complaints/".concat(id), updates);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async downloadComplaintAttachment(complaintId, attachmentId) {
        const response = await this.api.get("/consumer-affairs/complaints/".concat(complaintId, "/attachments/").concat(attachmentId, "/download"), {
            responseType: 'blob'
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "api", void 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "pendingRequests", new Map());
        this.api = customerApiClient;
    }
}
const customerApi = new CustomerApiService();
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/applications/ApplicationProgress.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/config/licenseTypeStepConfig.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$customer$2d$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/customer-api.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
// Cache for license data to avoid repeated API calls
const licenseDataCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const ApplicationProgress = (param)=>{
    let { className = '' } = param;
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    // Create customer API service instance
    const customerApi = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "ApplicationProgress.useMemo[customerApi]": ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$customer$2d$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CustomerApiService"]()
    }["ApplicationProgress.useMemo[customerApi]"], []);
    // Get query parameters
    const licenseCategoryId = searchParams.get('license_category_id');
    const applicationId = searchParams.get('application_id');
    // State
    const [applicationSteps, setApplicationSteps] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Get current step from pathname (memoized)
    const currentStepIndex = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "ApplicationProgress.useMemo[currentStepIndex]": ()=>{
            if (!applicationSteps.length) return -1;
            const pathSegments = pathname.split('/');
            const currentStepId = pathSegments[pathSegments.length - 1];
            return applicationSteps.findIndex({
                "ApplicationProgress.useMemo[currentStepIndex]": (step)=>step.id === currentStepId
            }["ApplicationProgress.useMemo[currentStepIndex]"]);
        }
    }["ApplicationProgress.useMemo[currentStepIndex]"], [
        pathname,
        applicationSteps
    ]);
    // Check cache for license data
    const getCachedLicenseData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ApplicationProgress.useCallback[getCachedLicenseData]": (licenseCategoryId)=>{
            const cached = licenseDataCache.get(licenseCategoryId);
            if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
                return cached;
            }
            return null;
        }
    }["ApplicationProgress.useCallback[getCachedLicenseData]"], []);
    // Cache license data
    const cacheLicenseData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ApplicationProgress.useCallback[cacheLicenseData]": (licenseCategoryId, data)=>{
            licenseDataCache.set(licenseCategoryId, {
                ...data,
                timestamp: Date.now()
            });
        }
    }["ApplicationProgress.useCallback[cacheLicenseData]"], []);
    // Load data with caching and optimization
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ApplicationProgress.useEffect": ()=>{
            const loadData = {
                "ApplicationProgress.useEffect.loadData": async ()=>{
                    try {
                        if (!licenseCategoryId) {
                            setLoading(false);
                            return;
                        }
                        setError(null);
                        // Check cache first
                        const cachedData = getCachedLicenseData(licenseCategoryId);
                        if (cachedData) {
                            console.log('Using cached license data for:', licenseCategoryId);
                            setApplicationSteps(cachedData.steps);
                            setLoading(false);
                            return;
                        }
                        // Load license category
                        const category = await customerApi.getLicenseCategory(licenseCategoryId);
                        if (!(category === null || category === void 0 ? void 0 : category.license_type_id)) {
                            throw new Error('License category does not have a license type ID');
                        }
                        const licenseType = await customerApi.getLicenseType(category.license_type_id);
                        if (!licenseType) {
                            throw new Error('License type not found');
                        }
                        // Use optimized step configuration
                        let steps = [];
                        steps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLicenseTypeStepConfig"])(licenseType.code, category).steps;
                        // Cache the data
                        cacheLicenseData(licenseCategoryId, {
                            category,
                            licenseType,
                            steps
                        });
                        setApplicationSteps(steps);
                        setLoading(false);
                    } catch (err) {
                        console.error('Error loading application steps:', err);
                        setError(err.message || 'Failed to load license information');
                        setApplicationSteps([]);
                        setLoading(false);
                    }
                }
            }["ApplicationProgress.useEffect.loadData"];
            loadData();
        }
    }["ApplicationProgress.useEffect"], [
        licenseCategoryId,
        applicationId,
        customerApi,
        getCachedLicenseData,
        cacheLicenseData
    ]);
    // Navigation handlers
    const handleStepClick = (stepIndex)=>{
        // Prevent navigation to future steps if not editing an existing application
        if (!applicationId && stepIndex > currentStepIndex) {
            return;
        }
        const step = applicationSteps[stepIndex];
        const params = new URLSearchParams();
        params.set('license_category_id', licenseCategoryId);
        if (applicationId) {
            params.set('application_id', applicationId);
        }
        router.push("/customer/applications/apply/".concat(step.id, "?").concat(params.toString()));
    };
    // Loading state
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "mb-8 ".concat(className),
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-pulse",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"
                        }, void 0, false, {
                            fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                            lineNumber: 146,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-2",
                            children: [
                                ...Array(4)
                            ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center p-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded-full mr-3"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                                            lineNumber: 150,
                                            columnNumber: 19
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-1"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                                                    lineNumber: 152,
                                                    columnNumber: 21
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "h-2 bg-gray-200 dark:bg-gray-700 rounded w-1/2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                                                    lineNumber: 153,
                                                    columnNumber: 21
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                                            lineNumber: 151,
                                            columnNumber: 19
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, i, true, {
                                    fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                                    lineNumber: 149,
                                    columnNumber: 17
                                }, ("TURBOPACK compile-time value", void 0)))
                        }, void 0, false, {
                            fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                            lineNumber: 147,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                    lineNumber: 145,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                lineNumber: 144,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
            lineNumber: 143,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0));
    }
    // Error state
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "mb-8 ".concat(className),
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-red-200 dark:border-red-800 p-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center text-red-600 dark:text-red-400",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-error-warning-line mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                                lineNumber: 170,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-sm font-medium",
                                children: "Failed to load progress"
                            }, void 0, false, {
                                fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                                lineNumber: 171,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                        lineNumber: 169,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-xs text-red-500 dark:text-red-400 mt-1",
                        children: error
                    }, void 0, false, {
                        fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                        lineNumber: 173,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                lineNumber: 168,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
            lineNumber: 167,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0));
    }
    // No steps available
    if (!applicationSteps.length) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "mb-8 ".concat(className),
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center text-gray-500 dark:text-gray-400",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-file-list-line text-2xl mb-2"
                        }, void 0, false, {
                            fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                            lineNumber: 185,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm",
                            children: "No application steps available"
                        }, void 0, false, {
                            fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                            lineNumber: 186,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                    lineNumber: 184,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                lineNumber: 183,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
            lineNumber: 182,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0));
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "mb-8 ".concat(className),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                    className: "text-sm font-medium text-gray-900 dark:text-gray-100 mb-4",
                    children: [
                        "Application Progress (",
                        currentStepIndex + 1,
                        " of ",
                        applicationSteps.length,
                        ")"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                    lineNumber: 196,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-2",
                    children: applicationSteps.map((step, index)=>{
                        const isAccessible = applicationId || index <= currentStepIndex;
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center p-2 rounded-md transition-colors ".concat(isAccessible ? 'cursor-pointer' : 'cursor-not-allowed opacity-60', " ").concat(index === currentStepIndex ? 'bg-primary/10 border border-primary/20' : index < currentStepIndex ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' : 'bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600'),
                            onClick: ()=>isAccessible && handleStepClick(index),
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium mr-3 ".concat(index === currentStepIndex ? 'bg-primary text-white' : index < currentStepIndex ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-600 dark:bg-gray-600 dark:text-gray-300'),
                                    children: index < currentStepIndex ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                        className: "ri-check-line"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                                        lineNumber: 226,
                                        columnNumber: 21
                                    }, ("TURBOPACK compile-time value", void 0)) : index + 1
                                }, void 0, false, {
                                    fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                                    lineNumber: 216,
                                    columnNumber: 17
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-sm font-medium ".concat(index === currentStepIndex ? 'text-primary' : index < currentStepIndex ? 'text-green-700 dark:text-green-300' : 'text-gray-600 dark:text-gray-400'),
                                            children: step.name
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                                            lineNumber: 232,
                                            columnNumber: 19
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        step.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-xs text-gray-500 dark:text-gray-400 mt-1",
                                            children: step.description
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                                            lineNumber: 242,
                                            columnNumber: 21
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                                    lineNumber: 231,
                                    columnNumber: 17
                                }, ("TURBOPACK compile-time value", void 0)),
                                step.required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-xs text-red-500 ml-2",
                                    children: "*"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                                    lineNumber: 248,
                                    columnNumber: 19
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, step.id, true, {
                            fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                            lineNumber: 203,
                            columnNumber: 15
                        }, ("TURBOPACK compile-time value", void 0));
                    })
                }, void 0, false, {
                    fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
                    lineNumber: 199,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
            lineNumber: 195,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/applications/ApplicationProgress.tsx",
        lineNumber: 194,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(ApplicationProgress, "R/JhlUaNTQ+Arq04TUXg+VQJqbc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
_c = ApplicationProgress;
const __TURBOPACK__default__export__ = ApplicationProgress;
var _c;
__turbopack_context__.k.register(_c, "ApplicationProgress");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/applications/ApplicationLayout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$applications$2f$ApplicationProgress$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/applications/ApplicationProgress.tsx [app-client] (ecmascript)");
'use client';
;
;
;
// Progress Loading Fallback Component
const ProgressLoadingFallback = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "mb-8",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-pulse",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                        lineNumber: 42,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: [
                            ...Array(4)
                        ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center p-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded-full mr-3"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                        lineNumber: 46,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-1"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                lineNumber: 48,
                                                columnNumber: 17
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "h-2 bg-gray-200 dark:bg-gray-700 rounded w-1/2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                lineNumber: 49,
                                                columnNumber: 17
                                            }, ("TURBOPACK compile-time value", void 0))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                        lineNumber: 47,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, i, true, {
                                fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                lineNumber: 45,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)))
                    }, void 0, false, {
                        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                        lineNumber: 43,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                lineNumber: 41,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
            lineNumber: 40,
            columnNumber: 5
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
        lineNumber: 39,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
_c = ProgressLoadingFallback;
const ApplicationLayout = (param)=>{
    let { children, onSubmit, onSave, onNext, onPrevious, isSubmitting = false, isSaving = false, showNextButton = true, showPreviousButton = true, showSaveButton = false, showSubmitButton = false, nextButtonText = 'Next', previousButtonText = 'Previous', saveButtonText = 'Save', submitButtonText = 'Submit', nextButtonDisabled = false, previousButtonDisabled = false, saveButtonDisabled = false, submitButtonDisabled = false, className = '', showProgress = true, progressFallback, licenseTypeCode, currentStepRoute, stepValidationErrors = [], showStepInfo = false } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50 dark:bg-gray-900 ".concat(className),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-7xl mx-auto",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 lg:grid-cols-4 gap-8",
                children: [
                    showProgress && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "lg:col-span-1",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "sticky top-8",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Suspense"], {
                                fallback: progressFallback || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ProgressLoadingFallback, {}, void 0, false, {
                                    fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                    lineNumber: 95,
                                    columnNumber: 57
                                }, void 0),
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$applications$2f$ApplicationProgress$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                    lineNumber: 96,
                                    columnNumber: 19
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                lineNumber: 95,
                                columnNumber: 17
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                            lineNumber: 94,
                            columnNumber: 15
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                        lineNumber: 93,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: showProgress ? "lg:col-span-3" : "lg:col-span-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",
                            children: [
                                showStepInfo && licenseTypeCode && currentStepRoute && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "border-b border-gray-200 dark:border-gray-700 p-4 bg-blue-50 dark:bg-blue-900/20",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-between",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "text-sm font-medium text-blue-900 dark:text-blue-100",
                                                        children: [
                                                            "License Type: ",
                                                            licenseTypeCode.replace(/_/g, ' ').toUpperCase()
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                        lineNumber: 110,
                                                        columnNumber: 23
                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-xs text-blue-700 dark:text-blue-300 mt-1",
                                                        children: [
                                                            "Current Step: ",
                                                            currentStepRoute.replace(/-/g, ' ').replace(/\b\w/g, (l)=>l.toUpperCase())
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                        lineNumber: 113,
                                                        columnNumber: 23
                                                    }, ("TURBOPACK compile-time value", void 0))
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                lineNumber: 109,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-xs text-blue-600 dark:text-blue-400",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                        className: "ri-information-line mr-1"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                        lineNumber: 118,
                                                        columnNumber: 23
                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                    "Optimized Configuration Active"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                lineNumber: 117,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                        lineNumber: 108,
                                        columnNumber: 19
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                    lineNumber: 107,
                                    columnNumber: 17
                                }, ("TURBOPACK compile-time value", void 0)),
                                stepValidationErrors.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "border-b border-gray-200 dark:border-gray-700 p-4 bg-red-50 dark:bg-red-900/20",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-start",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                className: "ri-error-warning-line text-red-500 mr-2 mt-0.5"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                lineNumber: 129,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: "text-sm font-medium text-red-900 dark:text-red-100 mb-2",
                                                        children: "Please fix the following issues:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                        lineNumber: 131,
                                                        columnNumber: 23
                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                        className: "text-xs text-red-700 dark:text-red-300 space-y-1",
                                                        children: stepValidationErrors.map((error, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                children: [
                                                                    "• ",
                                                                    error
                                                                ]
                                                            }, index, true, {
                                                                fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                                lineNumber: 136,
                                                                columnNumber: 27
                                                            }, ("TURBOPACK compile-time value", void 0)))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                        lineNumber: 134,
                                                        columnNumber: 23
                                                    }, ("TURBOPACK compile-time value", void 0))
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                lineNumber: 130,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                        lineNumber: 128,
                                        columnNumber: 19
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                    lineNumber: 127,
                                    columnNumber: 17
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-6",
                                    children: children
                                }, void 0, false, {
                                    fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                    lineNumber: 145,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "px-6 py-4 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-600 rounded-b-lg",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-between",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: showPreviousButton && onPrevious && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    type: "button",
                                                    onClick: onPrevious,
                                                    disabled: previousButtonDisabled,
                                                    className: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                            className: "ri-arrow-left-line mr-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                            lineNumber: 161,
                                                            columnNumber: 25
                                                        }, ("TURBOPACK compile-time value", void 0)),
                                                        previousButtonText
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                    lineNumber: 155,
                                                    columnNumber: 23
                                                }, ("TURBOPACK compile-time value", void 0))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                lineNumber: 153,
                                                columnNumber: 19
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center space-x-3",
                                                children: [
                                                    showSaveButton && onSave && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        type: "button",
                                                        onClick: ()=>{
                                                            console.log('🔘 Save button clicked in ApplicationLayout');
                                                            onSave();
                                                        },
                                                        disabled: saveButtonDisabled || isSaving,
                                                        className: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",
                                                        children: isSaving ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                    className: "ri-loader-4-line animate-spin mr-2"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                                    lineNumber: 182,
                                                                    columnNumber: 29
                                                                }, ("TURBOPACK compile-time value", void 0)),
                                                                "Saving..."
                                                            ]
                                                        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                    className: "ri-save-line mr-2"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                                    lineNumber: 187,
                                                                    columnNumber: 29
                                                                }, ("TURBOPACK compile-time value", void 0)),
                                                                saveButtonText
                                                            ]
                                                        }, void 0, true)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                        lineNumber: 171,
                                                        columnNumber: 23
                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                    showSubmitButton && onSubmit && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        type: "button",
                                                        onClick: onSubmit,
                                                        disabled: submitButtonDisabled || isSubmitting,
                                                        className: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed",
                                                        children: isSubmitting ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                    className: "ri-loader-4-line animate-spin mr-2"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                                    lineNumber: 204,
                                                                    columnNumber: 29
                                                                }, ("TURBOPACK compile-time value", void 0)),
                                                                "Submitting..."
                                                            ]
                                                        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                    className: "ri-send-plane-line mr-2"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                                    lineNumber: 209,
                                                                    columnNumber: 29
                                                                }, ("TURBOPACK compile-time value", void 0)),
                                                                submitButtonText
                                                            ]
                                                        }, void 0, true)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                        lineNumber: 196,
                                                        columnNumber: 23
                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                    showNextButton && onNext && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        type: "button",
                                                        onClick: ()=>{
                                                            console.log('🔘 Next button clicked in ApplicationLayout');
                                                            onNext();
                                                        },
                                                        disabled: nextButtonDisabled,
                                                        className: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",
                                                        children: [
                                                            nextButtonText,
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                className: "ri-arrow-right-line ml-2"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                                lineNumber: 228,
                                                                columnNumber: 25
                                                            }, ("TURBOPACK compile-time value", void 0))
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                        lineNumber: 218,
                                                        columnNumber: 23
                                                    }, ("TURBOPACK compile-time value", void 0))
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                                lineNumber: 168,
                                                columnNumber: 19
                                            }, ("TURBOPACK compile-time value", void 0))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                        lineNumber: 151,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                                    lineNumber: 150,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                            lineNumber: 104,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                        lineNumber: 103,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
                lineNumber: 90,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
            lineNumber: 89,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/applications/ApplicationLayout.tsx",
        lineNumber: 88,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_c1 = ApplicationLayout;
const __TURBOPACK__default__export__ = ApplicationLayout;
var _c, _c1;
__turbopack_context__.k.register(_c, "ProgressLoadingFallback");
__turbopack_context__.k.register(_c1, "ApplicationLayout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/forms/FileUpload.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
const FileUpload = (param)=>{
    let { id, label, accept = '.pdf', maxSize = 10, required = false, value, onChange, description, className = '' } = param;
    _s();
    const fileInputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [error, setError] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState(null);
    const handleFileChange = (event)=>{
        var _event_target_files;
        const file = ((_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0]) || null;
        setError(null);
        if (file) {
            // Check file size
            if (file.size > maxSize * 1024 * 1024) {
                setError("File size exceeds the maximum limit of ".concat(maxSize, "MB. Please select a smaller file."));
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
                return;
            }
            // Check file type
            if (accept && !accept.split(',').some((type)=>file.name.toLowerCase().endsWith(type.trim().replace('*', '')))) {
                setError("Invalid file type. Accepted formats: ".concat(accept.replace(/\./g, '').toUpperCase()));
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
                return;
            }
        }
        onChange(file);
    };
    const handleClick = ()=>{
        var _fileInputRef_current;
        (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();
    };
    const handleRemove = (e)=>{
        e.stopPropagation();
        onChange(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                htmlFor: id,
                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                children: [
                    label,
                    " ",
                    required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-red-500",
                        children: "*"
                    }, void 0, false, {
                        fileName: "[project]/src/components/forms/FileUpload.tsx",
                        lineNumber: 74,
                        columnNumber: 30
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/forms/FileUpload.tsx",
                lineNumber: 73,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                onClick: handleClick,
                className: "border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-3 text-center cursor-pointer hover:border-primary hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        ref: fileInputRef,
                        type: "file",
                        accept: accept,
                        onChange: handleFileChange,
                        className: "hidden",
                        id: id,
                        required: required
                    }, void 0, false, {
                        fileName: "[project]/src/components/forms/FileUpload.tsx",
                        lineNumber: 81,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    value ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-file-text-line text-2xl text-green-500"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/forms/FileUpload.tsx",
                                    lineNumber: 94,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/forms/FileUpload.tsx",
                                lineNumber: 93,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm font-medium text-green-600 dark:text-green-400",
                                children: value.name
                            }, void 0, false, {
                                fileName: "[project]/src/components/forms/FileUpload.tsx",
                                lineNumber: 96,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xs text-gray-500 dark:text-gray-400",
                                children: [
                                    (value.size / 1024 / 1024).toFixed(2),
                                    " MB"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/forms/FileUpload.tsx",
                                lineNumber: 99,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: handleRemove,
                                className: "inline-flex items-center px-3 py-1 bg-red-100 text-red-700 hover:bg-red-200 rounded-md text-xs font-medium transition-colors",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                        className: "ri-delete-bin-line mr-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/forms/FileUpload.tsx",
                                        lineNumber: 107,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    "Remove"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/forms/FileUpload.tsx",
                                lineNumber: 102,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/forms/FileUpload.tsx",
                        lineNumber: 92,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-upload-cloud-2-line text-2xl text-gray-400"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/forms/FileUpload.tsx",
                                    lineNumber: 114,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/forms/FileUpload.tsx",
                                lineNumber: 113,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-gray-600 dark:text-gray-400",
                                children: [
                                    "Click to upload ",
                                    label.toLowerCase()
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/forms/FileUpload.tsx",
                                lineNumber: 116,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xs text-gray-500 dark:text-gray-500",
                                children: description
                            }, void 0, false, {
                                fileName: "[project]/src/components/forms/FileUpload.tsx",
                                lineNumber: 120,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                        className: "ri-folder-upload-line mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/forms/FileUpload.tsx",
                                        lineNumber: 125,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    "Choose File"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/forms/FileUpload.tsx",
                                lineNumber: 124,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/forms/FileUpload.tsx",
                        lineNumber: 112,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/forms/FileUpload.tsx",
                lineNumber: 77,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            description && !value && !error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "mt-2 text-xs text-gray-500 dark:text-gray-400",
                children: description
            }, void 0, false, {
                fileName: "[project]/src/components/forms/FileUpload.tsx",
                lineNumber: 133,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-2 p-2 bg-red-50 dark:bg-red-900/30 rounded border border-red-200 dark:border-red-800",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs text-red-600 dark:text-red-400 flex items-start",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-error-warning-line mr-1 mt-0.5 flex-shrink-0"
                        }, void 0, false, {
                            fileName: "[project]/src/components/forms/FileUpload.tsx",
                            lineNumber: 141,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: error
                        }, void 0, false, {
                            fileName: "[project]/src/components/forms/FileUpload.tsx",
                            lineNumber: 142,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/forms/FileUpload.tsx",
                    lineNumber: 140,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/forms/FileUpload.tsx",
                lineNumber: 139,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                        className: "ri-information-line mr-1"
                    }, void 0, false, {
                        fileName: "[project]/src/components/forms/FileUpload.tsx",
                        lineNumber: 148,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    "Maximum file size: ",
                    maxSize,
                    "MB"
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/forms/FileUpload.tsx",
                lineNumber: 147,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/forms/FileUpload.tsx",
        lineNumber: 72,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(FileUpload, "LlCUfJXoJpaEtu63Oo3IMY15b4I=");
_c = FileUpload;
const __TURBOPACK__default__export__ = FileUpload;
var _c;
__turbopack_context__.k.register(_c, "FileUpload");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/applicationService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "applicationService": ()=>applicationService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
;
;
const applicationService = {
    // Get all applications with pagination and filters
    async getApplications (params) {
        var _params_filters, _params_filters1, _params_filters2;
        const queryParams = new URLSearchParams();
        if (params === null || params === void 0 ? void 0 : params.page) queryParams.append('page', params.page.toString());
        if (params === null || params === void 0 ? void 0 : params.limit) queryParams.append('limit', params.limit.toString());
        if (params === null || params === void 0 ? void 0 : params.search) queryParams.append('search', params.search);
        if (params === null || params === void 0 ? void 0 : params.sortBy) queryParams.append('sortBy', params.sortBy);
        if (params === null || params === void 0 ? void 0 : params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
        // Add filters
        if (params === null || params === void 0 ? void 0 : (_params_filters = params.filters) === null || _params_filters === void 0 ? void 0 : _params_filters.licenseTypeId) {
            queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);
        }
        if (params === null || params === void 0 ? void 0 : (_params_filters1 = params.filters) === null || _params_filters1 === void 0 ? void 0 : _params_filters1.licenseCategoryId) {
            queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);
        }
        if (params === null || params === void 0 ? void 0 : (_params_filters2 = params.filters) === null || _params_filters2 === void 0 ? void 0 : _params_filters2.status) {
            queryParams.append('filter.status', params.filters.status);
        }
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/applications?".concat(queryParams.toString()));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get applications by license type (through license category)
    async getApplicationsByLicenseType (licenseTypeId, params) {
        const queryParams = new URLSearchParams();
        if (params === null || params === void 0 ? void 0 : params.page) queryParams.append('page', params.page.toString());
        if (params === null || params === void 0 ? void 0 : params.limit) queryParams.append('limit', params.limit.toString());
        if (params === null || params === void 0 ? void 0 : params.search) queryParams.append('search', params.search);
        if (params === null || params === void 0 ? void 0 : params.status) queryParams.append('filter.status', params.status);
        // Filter by license type through license category
        queryParams.append('filter.license_category.license_type_id', licenseTypeId);
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/applications?".concat(queryParams.toString()));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get single application by ID
    async getApplication (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/applications/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get applications by applicant
    async getApplicationsByApplicant (applicantId) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/applications/by-applicant/".concat(applicantId));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get applications by status
    async getApplicationsByStatus (status) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/applications/by-status/".concat(status));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Update application status
    async updateApplicationStatus (id, status) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put("/applications/".concat(id, "/status?status=").concat(status));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Update application progress
    async updateApplicationProgress (id, currentStep, progressPercentage) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put("/applications/".concat(id, "/progress?currentStep=").concat(currentStep, "&progressPercentage=").concat(progressPercentage));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get application statistics
    async getApplicationStats () {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/applications/stats');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Create new application
    async createApplication (data) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/applications', data);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            throw error;
        }
    },
    // Update application with improved error handling
    async updateApplication (id, data) {
        try {
            console.log('Updating application:', id, 'with data:', data);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put("/applications/".concat(id), data, {
                timeout: 30000
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            var _error_response, _error_response1;
            console.error('Error updating application:', error);
            // Handle specific error cases
            if (error.code === 'ECONNABORTED') {
                throw new Error('Request timeout - please try again');
            }
            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 400) {
                var _error_response_data, _error_response2, _error_response3;
                const message = ((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Invalid application data';
                console.error('400 Bad Request details:', (_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.data);
                throw new Error("Bad Request: ".concat(message));
            }
            if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 429) {
                throw new Error('Too many requests - please wait a moment and try again');
            }
            throw error;
        }
    },
    // Delete application
    async deleteApplication (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete("/applications/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Create new application with applicant data
    async createApplicationWithApplicant (data) {
        try {
            // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)
            const now = new Date();
            const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
            const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
            const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();
            const applicationNumber = "APP-".concat(dateStr, "-").concat(timeStr, "-").concat(randomStr);
            // Validate user_id is a proper UUID
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
            if (!uuidRegex.test(data.user_id)) {
                throw new Error("Invalid user_id format: ".concat(data.user_id, ". Expected UUID format."));
            }
            // Create application using user_id as applicant_id
            // In most systems, the authenticated user is the applicant
            const application = await this.createApplication({
                application_number: applicationNumber,
                applicant_id: data.user_id,
                license_category_id: data.license_category_id,
                current_step: 1,
                progress_percentage: 0 // Start with 0% progress
            });
            return application;
        } catch (error) {
            console.error('Error creating application with applicant:', error);
            throw error;
        }
    },
    // Save application section data
    async saveApplicationSection (applicationId, sectionName, sectionData) {
        try {
            // Try to save using form data service, but continue if it fails
            let completedSections = 1; // At least one section is being saved
            // Estimate progress based on section name
            const sectionOrder = [
                'applicantInfo',
                'companyProfile',
                'businessInfo',
                'serviceScope',
                'businessPlan',
                'legalHistory',
                'reviewSubmit'
            ];
            const sectionIndex = sectionOrder.indexOf(sectionName);
            completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;
            // Calculate progress based on completed sections (excluding reviewSubmit from total)
            const totalSections = 6; // Total number of form sections (excluding reviewSubmit)
            const progressPercentage = Math.min(Math.round(completedSections / totalSections * 100), 100);
            // Update the application progress
            await this.updateApplication(applicationId, {
                progress_percentage: progressPercentage,
                current_step: completedSections
            });
        } catch (error) {
            throw error;
        }
    },
    // Get application section data
    async getApplicationSection (applicationId, sectionName) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/applications/".concat(applicationId, "/sections/").concat(sectionName));
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            throw error;
        }
    },
    // Submit application for review
    async submitApplication (applicationId) {
        try {
            // Update application status to submitted and set submission date
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put("/applications/".concat(applicationId), {
                status: 'submitted',
                submitted_at: new Date().toISOString(),
                progress_percentage: 100,
                current_step: 7
            });
            console.log('Application submitted successfully:', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error submitting application:', error);
            throw error;
        }
    },
    // Get user's applications (filtered by authenticated user)
    async getUserApplications () {
        try {
            // Use dedicated endpoint that explicitly filters by current user
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/applications/user-applications');
            const processedResponse = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
            // Handle paginated response structure
            let applications = [];
            if (processedResponse === null || processedResponse === void 0 ? void 0 : processedResponse.data) {
                applications = Array.isArray(processedResponse.data) ? processedResponse.data : [];
            } else if (Array.isArray(processedResponse)) {
                applications = processedResponse;
            } else if (processedResponse) {
                // Single application or other structure
                applications = [
                    processedResponse
                ];
            }
            return applications;
        } catch (error) {
            throw error;
        }
    },
    // Save application as draft
    async saveAsDraft (applicationId, formData) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put("/applications/".concat(applicationId), {
                form_data: formData,
                status: 'draft'
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error saving application as draft:', error);
            throw error;
        }
    },
    // Validate application before submission
    async validateApplication (applicationId) {
        try {
            // Get data from entity-specific APIs for validation
            let formData = {};
            const errors = [];
            const requiredSections = [
                'applicantInfo',
                'companyProfile',
                'businessInfo',
                'legalHistory'
            ];
            // Check if all required sections are completed
            for (const section of requiredSections){
                if (!formData[section] || Object.keys(formData[section]).length === 0) {
                    errors.push("".concat(section, " section is incomplete"));
                }
            }
            return {
                isValid: errors.length === 0,
                errors
            };
        } catch (error) {
            console.error('Error validating application:', error);
            throw error;
        }
    },
    // Update application status
    async updateStatus (applicationId, status) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].patch("/applications/".concat(applicationId, "/status"), {
                status
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error updating application status:', error);
            throw error;
        }
    },
    // Assign application to an officer
    async assignApplication (applicationId, assignedTo) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].patch("/applications/".concat(applicationId, "/assign"), {
                assignedTo
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error assigning application:', error);
            throw error;
        }
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/documentService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "documentService": ()=>documentService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
;
;
// Cache for preventing duplicate requests
const requestCache = new Map();
const documentService = {
    // Get all documents with pagination and filtering
    async getDocuments (params) {
        try {
            const queryParams = new URLSearchParams();
            if (params === null || params === void 0 ? void 0 : params.page) queryParams.append('page', params.page.toString());
            if (params === null || params === void 0 ? void 0 : params.limit) queryParams.append('limit', params.limit.toString());
            if (params === null || params === void 0 ? void 0 : params.search) queryParams.append('search', params.search);
            if (params === null || params === void 0 ? void 0 : params.sortBy) queryParams.append('sortBy', params.sortBy);
            const cacheKey = "/documents?".concat(queryParams.toString());
            // Check if we already have a pending request for this exact query
            if (requestCache.has(cacheKey)) {
                console.log('Returning cached request for:', cacheKey);
                return await requestCache.get(cacheKey);
            }
            // Create new request and cache it
            const requestPromise = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(cacheKey).then((response)=>{
                // Remove from cache after completion
                requestCache.delete(cacheKey);
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
            }).catch((error)=>{
                // Remove from cache on error too
                requestCache.delete(cacheKey);
                throw error;
            });
            requestCache.set(cacheKey, requestPromise);
            return await requestPromise;
        } catch (error) {
            console.error('DocumentService.getDocuments error:', error);
            throw error;
        }
    },
    // Get documents by entity (polymorphic relationship)
    async getDocumentsByEntity (entityType, entityId) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/documents/entity/".concat(entityType, "/").concat(entityId));
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('DocumentService.getDocumentsByEntity error:', error);
            throw error;
        }
    },
    // Get documents by application
    async getDocumentsByApplication (applicationId) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/documents/by-application/".concat(applicationId));
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            throw error;
        }
    },
    // Get required documents for license category
    async getRequiredDocumentsForLicenseCategory (licenseCategoryId) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/license-category-documents/category/".concat(licenseCategoryId));
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('DocumentService.getRequiredDocumentsForLicenseCategory error:', error);
            throw error;
        }
    },
    // Upload document (using the dedicated upload endpoint)
    async uploadDocument (file, documentData) {
        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('document_type', documentData.document_type);
            formData.append('entity_type', documentData.entity_type);
            formData.append('entity_id', documentData.entity_id);
            formData.append('is_required', (documentData.is_required || false).toString());
            formData.append('file_name', file.name);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/documents/upload', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
            return {
                document: result.data,
                message: result.message || 'Document uploaded successfully'
            };
        } catch (error) {
            console.error('DocumentService.uploadDocument error:', error);
            throw error;
        }
    },
    // Create document record (without file upload)
    async createDocument (documentData) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/documents', documentData);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('DocumentService.createDocument error:', error);
            throw error;
        }
    },
    // Update document
    async updateDocument (documentId, updateData) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put("/documents/".concat(documentId), updateData);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('DocumentService.updateDocument error:', error);
            throw error;
        }
    },
    // Delete document
    async deleteDocument (documentId) {
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete("/documents/".concat(documentId));
        } catch (error) {
            console.error('DocumentService.deleteDocument error:', error);
            throw error;
        }
    },
    // Get document by ID
    async getDocument (documentId) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/documents/".concat(documentId));
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('DocumentService.getDocument error:', error);
            throw error;
        }
    },
    // Download document
    async downloadDocument (documentId) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/documents/".concat(documentId, "/download"), {
                responseType: 'blob'
            });
            return response.data; // Return blob directly, not processed
        } catch (error) {
            console.error('DocumentService.downloadDocument error:', error);
            throw error;
        }
    },
    // Preview document
    async previewDocument (documentId) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/documents/".concat(documentId, "/preview"), {
                responseType: 'blob'
            });
            return response.data; // Return blob directly for preview
        } catch (error) {
            console.error('DocumentService.previewDocument error:', error);
            throw error;
        }
    },
    // Check if document type is previewable
    isPreviewable () {
        let mimeType = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : "";
        if (!mimeType) return false;
        const previewableMimeTypes = [
            'application/pdf',
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp',
            'text/plain',
            'text/html',
            'text/css',
            'text/javascript',
            'application/json'
        ];
        return previewableMimeTypes.includes(mimeType.toLowerCase());
    },
    // Check if all required documents are uploaded for an application
    async checkRequiredDocuments (applicationId, licenseCategoryId) {
        try {
            // Get required documents for license category
            const requiredDocs = await this.getRequiredDocumentsForLicenseCategory(licenseCategoryId);
            // Get uploaded documents for application
            const data = await this.getDocumentsByApplication(applicationId);
            const uploadedDocs = data.data;
            // Check which required documents are missing
            const uploadedTypes = uploadedDocs.map((doc)=>doc.document_type);
            const missing = requiredDocs.filter((reqDoc)=>reqDoc.is_required && !uploadedTypes.includes(reqDoc.name.toLowerCase().replace(/\s+/g, '_')));
            return {
                allUploaded: missing.length === 0,
                missing,
                uploaded: uploadedDocs
            };
        } catch (error) {
            console.error('DocumentService.checkRequiredDocuments error:', error);
            throw error;
        }
    },
    // Get document types enum
    getDocumentTypes () {
        return [
            'certificate_incorporation',
            'memorandum_association',
            'shareholding_structure',
            'business_plan',
            'financial_statements',
            'technical_proposal',
            'coverage_plan',
            'network_diagram',
            'equipment_specifications',
            'insurance_certificate',
            'tax_clearance',
            'audited_accounts',
            'bank_statement',
            'cv_document',
            'other'
        ];
    },
    // Format document type for display
    formatDocumentType (type) {
        return type.split('_').map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    },
    // Map document name to DocumentType enum value
    mapDocumentNameToType (documentName) {
        const nameToTypeMap = {
            'Certificate of Incorporation': 'certificate_incorporation',
            'Memorandum of Association': 'memorandum_association',
            'Shareholding Structure': 'shareholding_structure',
            'Business Plan': 'business_plan',
            'Financial Statements': 'financial_statements',
            'Technical Proposal': 'technical_proposal',
            'Coverage Plan': 'coverage_plan',
            'Network Diagram': 'network_diagram',
            'Equipment Specifications': 'equipment_specifications',
            'Insurance Certificate': 'insurance_certificate',
            'Tax Clearance Certificate': 'tax_clearance',
            'Tax Clearance': 'tax_clearance',
            'Audited Accounts': 'audited_accounts',
            'Bank Statement': 'bank_statement',
            'CV Document': 'cv_document',
            'Other': 'other'
        };
        // Try exact match first
        if (nameToTypeMap[documentName]) {
            return nameToTypeMap[documentName];
        }
        // Try case-insensitive match
        const lowerName = documentName.toLowerCase();
        for (const [name, type] of Object.entries(nameToTypeMap)){
            if (name.toLowerCase() === lowerName) {
                return type;
            }
        }
        // Fallback: convert name to snake_case
        return documentName.toLowerCase().replace(/\s+/g, '_');
    },
    // Validate file type and size
    validateFile (file) {
        let maxSizeMB = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, allowedTypes = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];
        // Check file size (convert MB to bytes)
        const maxSizeBytes = maxSizeMB * 1024 * 1024;
        if (file.size > maxSizeBytes) {
            return {
                isValid: false,
                error: "File size must be less than ".concat(maxSizeMB, "MB")
            };
        }
        // Check file type if specified
        if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
            return {
                isValid: false,
                error: "File type not allowed. Allowed types: ".concat(allowedTypes.join(', '))
            };
        }
        return {
            isValid: true
        };
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/licenseCategoryDocumentService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "licenseCategoryDocumentService": ()=>licenseCategoryDocumentService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
;
;
const licenseCategoryDocumentService = {
    // Get all license category documents with pagination
    async getLicenseCategoryDocuments () {
        let query = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
        const params = new URLSearchParams();
        if (query.page) params.set('page', query.page.toString());
        if (query.limit) params.set('limit', query.limit.toString());
        if (query.search) params.set('search', query.search);
        if (query.sortBy) {
            query.sortBy.forEach((sort)=>params.append('sortBy', sort));
        }
        if (query.searchBy) {
            query.searchBy.forEach((search)=>params.append('searchBy', search));
        }
        if (query.filter) {
            Object.entries(query.filter).forEach((param)=>{
                let [key, value] = param;
                if (Array.isArray(value)) {
                    value.forEach((v)=>params.append("filter.".concat(key), v));
                } else {
                    params.set("filter.".concat(key), value);
                }
            });
        }
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/license-category-documents?".concat(params.toString()));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get license category document by ID
    async getLicenseCategoryDocument (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/license-category-documents/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get license category documents by license category
    async getLicenseCategoryDocumentsByCategory (licenseCategoryId) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/license-category-documents/by-license-category/".concat(licenseCategoryId));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response).data;
    },
    // Create new license category document
    async createLicenseCategoryDocument (documentData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/license-category-documents', documentData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Update license category document
    async updateLicenseCategoryDocument (id, documentData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].patch("/license-category-documents/".concat(id), documentData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Delete license category document
    async deleteLicenseCategoryDocument (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete("/license-category-documents/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get all license category documents (simple list for dropdowns)
    async getAllLicenseCategoryDocuments () {
        const response = await this.getLicenseCategoryDocuments({
            limit: 1000
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useDynamicNavigation.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useDynamicNavigation": ()=>useDynamicNavigation
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/config/licenseTypeStepConfig.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$customer$2d$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/customer-api.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
const useDynamicNavigation = (param)=>{
    let { currentStepRoute, licenseCategoryId, applicationId } = param;
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [licenseTypeCode, setLicenseTypeCode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [steps, setSteps] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    // Create customer API service instance
    const customerApi = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useDynamicNavigation.useMemo[customerApi]": ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$customer$2d$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CustomerApiService"]()
    }["useDynamicNavigation.useMemo[customerApi]"], []);
    // Load license type and steps
    const loadLicenseTypeSteps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useDynamicNavigation.useCallback[loadLicenseTypeSteps]": async ()=>{
            if (!licenseCategoryId) {
                setError('License category ID is required');
                setLoading(false);
                return;
            }
            try {
                setLoading(true);
                setError(null);
                // Get license category and type
                const category = await customerApi.getLicenseCategory(licenseCategoryId);
                if (!(category === null || category === void 0 ? void 0 : category.license_type_id)) {
                    throw new Error('License category does not have a license type ID');
                }
                // Add small delay to prevent rate limiting
                await new Promise({
                    "useDynamicNavigation.useCallback[loadLicenseTypeSteps]": (resolve)=>setTimeout(resolve, 500)
                }["useDynamicNavigation.useCallback[loadLicenseTypeSteps]"]);
                const licenseType = await customerApi.getLicenseType(category.license_type_id);
                if (!licenseType) {
                    throw new Error('License type not found');
                }
                const typeCode = licenseType.code || licenseType.license_type_id;
                setLicenseTypeCode(typeCode);
                // Get steps based on license type code
                let licenseSteps = [];
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isLicenseTypeCodeSupported"])(typeCode)) {
                    licenseSteps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLicenseTypeStepConfig"])(typeCode, category).steps;
                } else {
                    const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLicenseTypeStepConfig"])(typeCode, category);
                    licenseSteps = config.steps;
                }
                setSteps(licenseSteps);
            } catch (err) {
                console.error('Error loading license type steps:', err);
                setError(err.message || 'Failed to load navigation configuration');
                // Use default fallback steps
                const fallbackConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLicenseTypeStepConfig"])('default');
                setSteps(fallbackConfig.steps);
                setLicenseTypeCode('default');
            } finally{
                setLoading(false);
            }
        }
    }["useDynamicNavigation.useCallback[loadLicenseTypeSteps]"], [
        licenseCategoryId,
        customerApi
    ]);
    // Load steps when dependencies change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useDynamicNavigation.useEffect": ()=>{
            loadLicenseTypeSteps();
        }
    }["useDynamicNavigation.useEffect"], [
        loadLicenseTypeSteps
    ]);
    // Computed values
    const currentStepIndex = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useDynamicNavigation.useMemo[currentStepIndex]": ()=>{
            return steps.findIndex({
                "useDynamicNavigation.useMemo[currentStepIndex]": (step)=>step.route === currentStepRoute
            }["useDynamicNavigation.useMemo[currentStepIndex]"]);
        }
    }["useDynamicNavigation.useMemo[currentStepIndex]"], [
        steps,
        currentStepRoute
    ]);
    const currentStep = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useDynamicNavigation.useMemo[currentStep]": ()=>{
            return steps[currentStepIndex] || null;
        }
    }["useDynamicNavigation.useMemo[currentStep]"], [
        steps,
        currentStepIndex
    ]);
    const nextStep = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useDynamicNavigation.useMemo[nextStep]": ()=>{
            return currentStepIndex >= 0 && currentStepIndex < steps.length - 1 ? steps[currentStepIndex + 1] : null;
        }
    }["useDynamicNavigation.useMemo[nextStep]"], [
        steps,
        currentStepIndex
    ]);
    const previousStep = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useDynamicNavigation.useMemo[previousStep]": ()=>{
            return currentStepIndex > 0 ? steps[currentStepIndex - 1] : null;
        }
    }["useDynamicNavigation.useMemo[previousStep]"], [
        steps,
        currentStepIndex
    ]);
    const totalSteps = steps.length;
    const isFirstStep = currentStepIndex === 0;
    const isLastStep = currentStepIndex === steps.length - 1;
    const canNavigateNext = !isLastStep && nextStep !== null;
    const canNavigatePrevious = !isFirstStep && previousStep !== null;
    // Navigation functions
    const createNavigationUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useDynamicNavigation.useCallback[createNavigationUrl]": (stepRoute)=>{
            const params = new URLSearchParams();
            params.set('license_category_id', licenseCategoryId || '');
            if (applicationId) {
                params.set('application_id', applicationId);
            }
            return "/customer/applications/apply/".concat(stepRoute, "?").concat(params.toString());
        }
    }["useDynamicNavigation.useCallback[createNavigationUrl]"], [
        licenseCategoryId,
        applicationId
    ]);
    const navigateToStep = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useDynamicNavigation.useCallback[navigateToStep]": (stepRoute)=>{
            const url = createNavigationUrl(stepRoute);
            console.log('🧭 Navigating to step:', stepRoute, 'URL:', url);
            router.push(url);
        }
    }["useDynamicNavigation.useCallback[navigateToStep]"], [
        createNavigationUrl,
        router
    ]);
    const handleNext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useDynamicNavigation.useCallback[handleNext]": async (saveFunction)=>{
            if (!canNavigateNext || !nextStep) {
                return;
            }
            // If save function is provided, save first
            if (saveFunction) {
                console.log('💾 Saving current step before navigation...');
                try {
                    const saved = await saveFunction();
                    if (!saved) {
                        console.warn('⚠️ Save failed, not navigating');
                        return;
                    }
                } catch (error) {
                    var _error_message, _error_message1, _error_message2;
                    console.error('❌ Error during save operation:', error);
                    // Handle specific error types
                    if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout')) {
                        console.error('Save operation timed out');
                    } else if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('Bad Request')) {
                        console.error('Invalid data provided for save operation');
                    } else if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('Too many requests')) {
                        console.error('Rate limit exceeded, please wait and try again');
                    }
                    // Don't navigate if save failed
                    return;
                }
            }
            navigateToStep(nextStep.route);
        }
    }["useDynamicNavigation.useCallback[handleNext]"], [
        canNavigateNext,
        nextStep,
        navigateToStep
    ]);
    const handlePrevious = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useDynamicNavigation.useCallback[handlePrevious]": ()=>{
            if (!canNavigatePrevious || !previousStep) {
                console.warn('⚠️ Cannot navigate to previous step');
                return;
            }
            navigateToStep(previousStep.route);
        }
    }["useDynamicNavigation.useCallback[handlePrevious]"], [
        canNavigatePrevious,
        previousStep,
        navigateToStep
    ]);
    return {
        // Navigation functions
        handleNext,
        handlePrevious,
        navigateToStep,
        // Step information
        currentStep,
        nextStep,
        previousStep,
        currentStepIndex,
        totalSteps,
        // State
        loading,
        error,
        licenseTypeCode,
        // Utility functions
        isFirstStep,
        isLastStep,
        canNavigateNext,
        canNavigatePrevious
    };
};
_s(useDynamicNavigation, "FnGDWMOZ9Eajyn5FFACWP2wMN3A=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/customer/applications/apply/documents/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$CustomerLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/customer/CustomerLayout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$applications$2f$ApplicationLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/applications/ApplicationLayout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$forms$2f$FileUpload$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/forms/FileUpload.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/applicationService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$documentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/documentService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseCategoryDocumentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/licenseCategoryDocumentService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useDynamicNavigation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useDynamicNavigation.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
const DocumentsPage = ()=>{
    _s();
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const { isAuthenticated, loading: authLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    // URL parameters
    const licenseCategoryId = searchParams.get('license_category_id');
    const applicationId = searchParams.get('application_id');
    // State management
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [isSaving, setIsSaving] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loadingWarning, setLoadingWarning] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [validationErrors, setValidationErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    // Document data
    const [requiredDocuments, setRequiredDocuments] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [uploadedDocuments, setUploadedDocuments] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [documentFiles, setDocumentFiles] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [uploadProgress, setUploadProgress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [application, setApplication] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Dynamic navigation hook
    const { handleNext: dynamicHandleNext, handlePrevious: dynamicHandlePrevious, nextStep } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useDynamicNavigation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDynamicNavigation"])({
        currentStepRoute: 'documents',
        licenseCategoryId,
        applicationId
    });
    // Load required documents and existing uploads
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DocumentsPage.useEffect": ()=>{
            const loadData = {
                "DocumentsPage.useEffect.loadData": async ()=>{
                    if (!applicationId || !licenseCategoryId || !isAuthenticated || authLoading) return;
                    const application = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicationService"].getApplication(applicationId);
                    setApplication(application);
                    try {
                        setIsLoading(true);
                        setError(null);
                        setLoadingWarning(null);
                        let requiredDocsArray = [];
                        try {
                            const requiredDocs = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseCategoryDocumentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["licenseCategoryDocumentService"].getLicenseCategoryDocumentsByCategory(licenseCategoryId);
                            // Ensure requiredDocs is always an array
                            requiredDocsArray = Array.isArray(requiredDocs) ? requiredDocs : [];
                            setRequiredDocuments(requiredDocsArray);
                        } catch (docError) {
                            // Check if it's a 404 (no documents found) vs other errors
                            requiredDocsArray = [];
                            setRequiredDocuments([]);
                        }
                        try {
                            // Use the existing /documents/by-application/{applicationId} endpoint
                            const data = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$documentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["documentService"].getDocumentsByApplication(applicationId);
                            const uploadedDocs = data.data;
                            const uploadedDocsArray = Array.isArray(uploadedDocs) ? uploadedDocs : [];
                            setUploadedDocuments(uploadedDocsArray);
                            // Cross-reference uploaded documents with required documents
                            if (requiredDocsArray.length > 0) {
                                const requiredDocTypes = requiredDocsArray.map({
                                    "DocumentsPage.useEffect.loadData.requiredDocTypes": (doc)=>doc.name.toLowerCase().replace(/\s+/g, '_')
                                }["DocumentsPage.useEffect.loadData.requiredDocTypes"]);
                                const uploadedDocTypes = uploadedDocsArray.map({
                                    "DocumentsPage.useEffect.loadData.uploadedDocTypes": (doc)=>doc.document_type
                                }["DocumentsPage.useEffect.loadData.uploadedDocTypes"]);
                                const missingDocs = requiredDocTypes.filter({
                                    "DocumentsPage.useEffect.loadData.missingDocs": (type)=>!uploadedDocTypes.includes(type)
                                }["DocumentsPage.useEffect.loadData.missingDocs"]);
                                const extraDocs = uploadedDocTypes.filter({
                                    "DocumentsPage.useEffect.loadData.extraDocs": (type)=>!requiredDocTypes.includes(type)
                                }["DocumentsPage.useEffect.loadData.extraDocs"]);
                            }
                            // If no documents found, that's expected for new applications
                            if (uploadedDocsArray.length === 0) {
                                console.log('📝 No existing documents found - this is normal for new applications');
                            }
                        } catch (uploadError) {
                            var _uploadError_response, _uploadError_response1, _uploadError_response2, _uploadError_response3, _uploadError_response4;
                            console.error('❌ Error loading uploaded documents:', uploadError);
                            console.error('Error details:', {
                                status: (_uploadError_response = uploadError.response) === null || _uploadError_response === void 0 ? void 0 : _uploadError_response.status,
                                statusText: (_uploadError_response1 = uploadError.response) === null || _uploadError_response1 === void 0 ? void 0 : _uploadError_response1.statusText,
                                data: (_uploadError_response2 = uploadError.response) === null || _uploadError_response2 === void 0 ? void 0 : _uploadError_response2.data,
                                message: uploadError.message
                            });
                            // Set empty array for uploaded documents
                            setUploadedDocuments([]);
                            // Set appropriate warning based on error type
                            if (((_uploadError_response3 = uploadError.response) === null || _uploadError_response3 === void 0 ? void 0 : _uploadError_response3.status) === 401) {
                                setLoadingWarning('Authentication required. Please log in to view documents.');
                            } else if (((_uploadError_response4 = uploadError.response) === null || _uploadError_response4 === void 0 ? void 0 : _uploadError_response4.status) === 404) {
                                console.log('📝 No documents found for this application (404) - this is normal for new applications');
                            } else {
                                setLoadingWarning('Could not load existing documents from server. You can still upload new documents.');
                            }
                        }
                    } catch (err) {
                        setError('Failed to load documents data');
                    } finally{
                        setIsLoading(false);
                    }
                }
            }["DocumentsPage.useEffect.loadData"];
            loadData();
        }
    }["DocumentsPage.useEffect"], [
        applicationId,
        licenseCategoryId,
        isAuthenticated,
        authLoading
    ]);
    // Handle file selection
    const handleFileChange = (documentType, file)=>{
        setDocumentFiles((prev)=>{
            const newFiles = {
                ...prev
            };
            if (file) {
                newFiles[documentType] = file;
            } else {
                delete newFiles[documentType];
            }
            return newFiles;
        });
        // Clear validation error for this document
        if (validationErrors[documentType]) {
            setValidationErrors((prev)=>{
                const newErrors = {
                    ...prev
                };
                delete newErrors[documentType];
                return newErrors;
            });
        }
    };
    // Upload a single document
    const uploadDocument = async (documentType, file)=>{
        try {
            setUploadProgress((prev)=>({
                    ...prev,
                    [documentType]: 0
                }));
            // Map the document name to the correct DocumentType enum value
            const requiredDoc = Array.isArray(requiredDocuments) ? requiredDocuments.find((doc)=>doc.name.toLowerCase().replace(/\s+/g, '_') === documentType) : undefined;
            const mappedDocumentType = requiredDoc ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$documentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["documentService"].mapDocumentNameToType(requiredDoc.name) : documentType;
            const uploadData = {
                document_type: mappedDocumentType,
                entity_type: 'application',
                entity_id: applicationId,
                is_required: (requiredDoc === null || requiredDoc === void 0 ? void 0 : requiredDoc.is_required) || false
            };
            try {
                // Try to upload to backend service
                const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$documentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["documentService"].uploadDocument(file, uploadData);
                setUploadProgress((prev)=>({
                        ...prev,
                        [documentType]: 100
                    }));
                // Update uploaded documents list
                setUploadedDocuments((prev)=>[
                        ...prev,
                        result.document
                    ]);
            } catch (uploadError) {
                // Fallback: simulate upload and store in local state
                setUploadProgress((prev)=>({
                        ...prev,
                        [documentType]: 100
                    }));
                const mockDocument = {
                    document_id: "mock_".concat(Date.now(), "_").concat(documentType),
                    document_type: documentType,
                    file_name: file.name,
                    entity_type: 'application',
                    entity_id: applicationId,
                    file_path: "mock_path/".concat(file.name),
                    file_size: file.size,
                    mime_type: file.type,
                    is_required: uploadData.is_required,
                    created_at: new Date().toISOString()
                };
                // Update uploaded documents list with mock document
                setUploadedDocuments((prev)=>[
                        ...prev,
                        mockDocument
                    ]);
                console.log("✅ Document ".concat(documentType, " stored locally"));
            }
            // Remove from pending files
            setDocumentFiles((prev)=>{
                const newFiles = {
                    ...prev
                };
                delete newFiles[documentType];
                return newFiles;
            });
            return true;
        } catch (error) {
            setValidationErrors((prev)=>({
                    ...prev,
                    [documentType]: 'Failed to process document. Please try again.'
                }));
            setUploadProgress((prev)=>{
                const newProgress = {
                    ...prev
                };
                delete newProgress[documentType];
                return newProgress;
            });
            return false;
        }
    };
    // Save all documents
    const handleSave = async ()=>{
        if (!applicationId) {
            setValidationErrors({
                save: 'Application ID is required'
            });
            return false;
        }
        setIsSaving(true);
        try {
            // Skip validation - allow users to upload documents gradually
            console.log('📋 Skipping document validation - users can upload documents gradually');
            // Clear any existing validation errors
            setValidationErrors({});
            console.log('💾 Processing documents for application:', applicationId);
            // Upload all pending documents
            if (Object.keys(documentFiles).length > 0) {
                const uploadPromises = Object.entries(documentFiles).map((param)=>{
                    let [docType, file] = param;
                    return uploadDocument(docType, file);
                });
                const uploadResults = await Promise.all(uploadPromises);
                const allUploaded = uploadResults.every((result)=>result);
                if (!allUploaded) {
                    throw new Error('Some documents failed to upload');
                }
            } else {
                console.log('📋 No pending documents to upload');
            }
            // Document status is tracked through the documents API
            // Update application progress
            try {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicationService"].updateApplication(applicationId, {
                    current_step: 7,
                    progress_percentage: 86 // Documents step completed (~7/8 steps)
                });
                console.log('📈 Application progress updated');
            } catch (progressError) {
                console.warn('Failed to update application progress:', progressError);
            }
            setValidationErrors({});
            console.log('✅ All documents processed successfully');
            return true;
        } catch (error) {
            console.error('❌ Error saving documents:', error);
            setValidationErrors({
                save: 'Failed to process documents. Please try again.'
            });
            return false;
        } finally{
            setIsSaving(false);
        }
    };
    // Navigation functions using dynamic navigation
    const handleNext = async ()=>{
        await dynamicHandleNext(handleSave);
    };
    const handlePrevious = ()=>{
        dynamicHandlePrevious();
    };
    // Remove uploaded document
    const handleRemoveDocument = async (documentId)=>{
        try {
            // Check if it's a mock document (starts with 'mock_')
            if (documentId.startsWith('mock_')) {
                // Just remove from local state
                setUploadedDocuments((prev)=>prev.filter((doc)=>doc.document_id !== documentId));
                console.log('🗑️ Mock document removed from local storage');
            } else {
                // Try to remove from backend
                try {
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$documentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["documentService"].deleteDocument(documentId);
                    setUploadedDocuments((prev)=>prev.filter((doc)=>doc.document_id !== documentId));
                    console.log('🗑️ Document removed from backend successfully');
                } catch (backendError) {
                    console.warn('⚠️ Backend removal failed, removing from local state:', backendError);
                    setUploadedDocuments((prev)=>prev.filter((doc)=>doc.document_id !== documentId));
                    console.log('🗑️ Document removed from local state');
                }
            }
        } catch (error) {
            console.error('❌ Error removing document:', error);
            setValidationErrors((prev)=>({
                    ...prev,
                    remove: 'Failed to remove document. Please try again.'
                }));
        }
    };
    // Check if document is uploaded
    const isDocumentUploaded = (docType)=>{
        return uploadedDocuments.some((doc)=>doc.document_type === docType);
    };
    // Get uploaded document
    const getUploadedDocument = (docType)=>{
        return uploadedDocuments.find((doc)=>doc.document_type === docType);
    };
    // Create comprehensive document list combining required and uploaded documents
    const getComprehensiveDocumentList = ()=>{
        const documentMap = new Map();
        // Add all required documents first
        if (Array.isArray(requiredDocuments)) {
            requiredDocuments.forEach((reqDoc)=>{
                const docType = reqDoc.name.toLowerCase().replace(/\s+/g, '_');
                documentMap.set(docType, {
                    type: 'required',
                    requiredDoc: reqDoc,
                    uploadedDoc: null,
                    docType,
                    isRequired: reqDoc.is_required,
                    isUploaded: false
                });
            });
        }
        // Add/update with uploaded documents
        uploadedDocuments.forEach((uploadedDoc)=>{
            const docType = uploadedDoc.document_type;
            const existing = documentMap.get(docType);
            if (existing) {
                // Update existing required document with uploaded info
                documentMap.set(docType, {
                    ...existing,
                    uploadedDoc,
                    isUploaded: true
                });
            } else {
                // Add uploaded document that's not in required list
                documentMap.set(docType, {
                    type: 'uploaded',
                    requiredDoc: null,
                    uploadedDoc,
                    docType,
                    isRequired: false,
                    isUploaded: true
                });
            }
        });
        return Array.from(documentMap.values());
    };
    const comprehensiveDocumentList = getComprehensiveDocumentList();
    // Handle document preview
    const handlePreviewDocument = async (doc)=>{
        try {
            const blob = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$documentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["documentService"].previewDocument(doc.document_id);
            const url = URL.createObjectURL(blob);
            window.open(url, '_blank');
        } catch (error) {
            console.error('Error previewing document:', error);
            alert('Failed to preview document');
        }
    };
    // Handle document download
    const handleDownloadDocument = async (doc)=>{
        try {
            const blob = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$documentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["documentService"].downloadDocument(doc.document_id);
            const url = URL.createObjectURL(blob);
            const link = window.document.createElement('a');
            link.href = url;
            link.download = doc.file_name;
            window.document.body.appendChild(link);
            link.click();
            link.remove();
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error downloading document:', error);
            alert('Failed to download document');
        }
    };
    if (authLoading || isLoading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$CustomerLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-center min-h-96",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                            lineNumber: 414,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 dark:text-gray-400",
                            children: "Loading required documents..."
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                            lineNumber: 415,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                    lineNumber: 413,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                lineNumber: 412,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
            lineNumber: 411,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0));
    }
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$CustomerLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-4xl mx-auto p-6",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"
                            }, void 0, false, {
                                fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                lineNumber: 428,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-lg font-medium text-red-800 dark:text-red-200",
                                        children: "Error Loading Documents"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                        lineNumber: 430,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-red-700 dark:text-red-300 mt-1",
                                        children: error
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                        lineNumber: 431,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>dynamicHandlePrevious(),
                                        className: "mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                className: "ri-arrow-left-line mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                lineNumber: 436,
                                                columnNumber: 19
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            "Go Back"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                        lineNumber: 432,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                lineNumber: 429,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                        lineNumber: 427,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                    lineNumber: 426,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                lineNumber: 425,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
            lineNumber: 424,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0));
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$CustomerLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$applications$2f$ApplicationLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            onNext: handleNext,
            onPrevious: handlePrevious,
            onSave: handleSave,
            showNextButton: true,
            showPreviousButton: true,
            showSaveButton: true,
            nextButtonText: nextStep ? "Continue to ".concat(nextStep.name) : "Continue",
            previousButtonText: "Back to Previous Step",
            saveButtonText: "Save Changes",
            nextButtonDisabled: false,
            isSaving: isSaving,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-lg font-medium text-gray-900 dark:text-gray-100",
                            children: "Required Documents"
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                            lineNumber: 464,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-gray-600 dark:text-gray-400 mt-1",
                            children: "Upload documents for your license application. You can upload documents gradually and return to this page later."
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                            lineNumber: 467,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        application && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-blue-700 dark:text-blue-300",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                        className: "ri-file-upload-line mr-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                        lineNumber: 473,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    "Application Number: ",
                                    application === null || application === void 0 ? void 0 : application.application_number
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                lineNumber: 472,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                            lineNumber: 471,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        loadingWarning && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-yellow-700 dark:text-yellow-300",
                                children: [
                                    "⚠️ ",
                                    loadingWarning
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                lineNumber: 480,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                            lineNumber: 479,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                    lineNumber: 463,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                Object.keys(validationErrors).length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-sm font-medium text-red-800 dark:text-red-200 mb-2",
                            children: "Please fix the following errors:"
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                            lineNumber: 490,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                            className: "text-sm text-red-700 dark:text-red-300 list-disc list-inside",
                            children: Object.entries(validationErrors).map((param)=>{
                                let [field, error] = param;
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: error
                                }, field, false, {
                                    fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                    lineNumber: 493,
                                    columnNumber: 17
                                }, ("TURBOPACK compile-time value", void 0));
                            })
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                            lineNumber: 491,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                    lineNumber: 489,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "border-b border-gray-200 dark:border-gray-700 pb-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-lg font-medium text-gray-900 dark:text-gray-100",
                                        children: "Document Upload"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                        lineNumber: 503,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm text-gray-600 dark:text-gray-400 mt-1",
                                        children: "Upload your documents as they become available. Accepted formats: PDF, DOC, DOCX, JPG, PNG"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                        lineNumber: 506,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm font-medium flex items-center text-yellow-700 dark:text-yellow-300",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                    className: "ri-information-line text-lg mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                    lineNumber: 511,
                                                    columnNumber: 19
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: [
                                                        "Maximum file size: ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "font-bold",
                                                            children: "10MB"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                            lineNumber: 512,
                                                            columnNumber: 44
                                                        }, ("TURBOPACK compile-time value", void 0)),
                                                        " per document. Files exceeding this limit will be rejected."
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                    lineNumber: 512,
                                                    columnNumber: 19
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                            lineNumber: 510,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                        lineNumber: 509,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                lineNumber: 502,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-4",
                                        children: comprehensiveDocumentList.map((docItem)=>{
                                            const { docType, requiredDoc, uploadedDoc, isRequired, isUploaded } = docItem;
                                            const isUploading = uploadProgress[docType] !== undefined;
                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center justify-between",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                        className: "text-sm font-medium text-gray-900 dark:text-gray-100",
                                                                        children: [
                                                                            requiredDoc.name,
                                                                            requiredDoc.is_required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "text-red-500 ml-1",
                                                                                children: "*"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                                                lineNumber: 531,
                                                                                columnNumber: 57
                                                                            }, ("TURBOPACK compile-time value", void 0))
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                                        lineNumber: 529,
                                                                        columnNumber: 27
                                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                                    isUploaded && uploadedDoc && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        className: "text-xs text-green-600 dark:text-green-400 mt-1",
                                                                        children: [
                                                                            "✅ Uploaded: ",
                                                                            uploadedDoc.file_name
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                                        lineNumber: 534,
                                                                        columnNumber: 29
                                                                    }, ("TURBOPACK compile-time value", void 0))
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                                lineNumber: 528,
                                                                columnNumber: 25
                                                            }, ("TURBOPACK compile-time value", void 0)),
                                                            isUploaded && uploadedDoc && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center space-x-2",
                                                                children: [
                                                                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$documentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["documentService"].isPreviewable(uploadedDoc.file_name) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                        onClick: ()=>handlePreviewDocument(uploadedDoc),
                                                                        className: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                                className: "ri-eye-line mr-1"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                                                lineNumber: 546,
                                                                                columnNumber: 31
                                                                            }, ("TURBOPACK compile-time value", void 0)),
                                                                            "Preview"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                                        lineNumber: 542,
                                                                        columnNumber: 29
                                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                        onClick: ()=>handleDownloadDocument(uploadedDoc),
                                                                        className: "text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 text-sm",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                                className: "ri-download-line mr-1"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                                                lineNumber: 554,
                                                                                columnNumber: 29
                                                                            }, ("TURBOPACK compile-time value", void 0)),
                                                                            "Download"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                                        lineNumber: 550,
                                                                        columnNumber: 27
                                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                        onClick: ()=>handleRemoveDocument(uploadedDoc.document_id),
                                                                        className: "text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 text-sm",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                                className: "ri-delete-bin-line mr-1"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                                                lineNumber: 561,
                                                                                columnNumber: 29
                                                                            }, ("TURBOPACK compile-time value", void 0)),
                                                                            "Remove"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                                        lineNumber: 557,
                                                                        columnNumber: 27
                                                                    }, ("TURBOPACK compile-time value", void 0))
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                                lineNumber: 540,
                                                                columnNumber: 27
                                                            }, ("TURBOPACK compile-time value", void 0))
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                        lineNumber: 527,
                                                        columnNumber: 23
                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                    !isUploaded && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$forms$2f$FileUpload$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        id: "document-".concat(docType),
                                                        label: "",
                                                        accept: ".pdf,.doc,.docx,.jpg,.jpeg,.png",
                                                        required: false,
                                                        maxSize: 10,
                                                        value: documentFiles[docType] || null,
                                                        onChange: (file)=>handleFileChange(docType, file),
                                                        description: "Upload ".concat(requiredDoc.name.toLowerCase())
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                        lineNumber: 569,
                                                        columnNumber: 25
                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                    isUploading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "bg-primary h-2 rounded-full transition-all duration-300",
                                                            style: {
                                                                width: "".concat(uploadProgress[docType], "%")
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                            lineNumber: 585,
                                                            columnNumber: 27
                                                        }, ("TURBOPACK compile-time value", void 0))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                        lineNumber: 584,
                                                        columnNumber: 25
                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                    validationErrors[docType] && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm text-red-600 dark:text-red-400",
                                                        children: validationErrors[docType]
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                        lineNumber: 593,
                                                        columnNumber: 25
                                                    }, ("TURBOPACK compile-time value", void 0))
                                                ]
                                            }, requiredDoc.license_category_document_id, true, {
                                                fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                lineNumber: 526,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0));
                                        })
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                        lineNumber: 520,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-center py-8",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center justify-between",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                            className: "text-sm font-medium text-gray-900 dark:text-gray-100",
                                                            children: [
                                                                "Other Supporting Documents ",
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "text-red-500 ml-1",
                                                                    children: "*"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                                    lineNumber: 606,
                                                                    columnNumber: 50
                                                                }, ("TURBOPACK compile-time value", void 0))
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                            lineNumber: 605,
                                                            columnNumber: 21
                                                        }, ("TURBOPACK compile-time value", void 0))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                        lineNumber: 604,
                                                        columnNumber: 19
                                                    }, ("TURBOPACK compile-time value", void 0))
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                    lineNumber: 603,
                                                    columnNumber: 17
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$forms$2f$FileUpload$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    id: "general-attachment",
                                                    label: "Upload Supporting Documents",
                                                    accept: ".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt",
                                                    maxSize: 10,
                                                    onChange: (file)=>{
                                                        if (file) {
                                                            setDocumentFiles((prev)=>({
                                                                    ...prev,
                                                                    'general_attachment': file
                                                                }));
                                                        }
                                                    },
                                                    className: "mb-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                                    lineNumber: 612,
                                                    columnNumber: 21
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, "General-Documents", true, {
                                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                            lineNumber: 602,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0))
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                        lineNumber: 601,
                                        columnNumber: 13
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                lineNumber: 518,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                        lineNumber: 501,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                    lineNumber: 500,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                Array.isArray(requiredDocuments) && requiredDocuments.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                            className: "text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",
                            children: "Upload Summary"
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                            lineNumber: 636,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-gray-600 dark:text-gray-400",
                                            children: "Total Required:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                            lineNumber: 641,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "ml-2 font-medium text-gray-900 dark:text-gray-100",
                                            children: requiredDocuments.filter((doc)=>doc.is_required).length
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                            lineNumber: 642,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                    lineNumber: 640,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-gray-600 dark:text-gray-400",
                                            children: "Uploaded:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                            lineNumber: 647,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "ml-2 font-medium text-green-600 dark:text-green-400",
                                            children: uploadedDocuments.length
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                            lineNumber: 648,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                    lineNumber: 646,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-gray-600 dark:text-gray-400",
                                            children: "Pending:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                            lineNumber: 653,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "ml-2 font-medium text-orange-600 dark:text-orange-400",
                                            children: Object.keys(documentFiles).length
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                            lineNumber: 654,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                                    lineNumber: 652,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                            lineNumber: 639,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
                    lineNumber: 635,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
            lineNumber: 449,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/app/customer/applications/apply/documents/page.tsx",
        lineNumber: 448,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(DocumentsPage, "+17QWZySMC/x7lPv3Xw6aR7Y6qg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useDynamicNavigation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDynamicNavigation"]
    ];
});
_c = DocumentsPage;
const __TURBOPACK__default__export__ = DocumentsPage;
var _c;
__turbopack_context__.k.register(_c, "DocumentsPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_43bceee4._.js.map