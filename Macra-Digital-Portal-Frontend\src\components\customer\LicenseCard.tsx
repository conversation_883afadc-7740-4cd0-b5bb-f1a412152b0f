import React from 'react';
import Link from 'next/link';

interface LicenseCardProps {
  id: string;
  title: string;
  licenseNumber: string;
  status: 'Active' | 'Expiring Soon' | 'Expired' | 'Pending';
  issueDate: string;
  expirationDate: string;
}

const LicenseCard: React.FC<LicenseCardProps> = ({
  id,
  title,
  licenseNumber,
  status,
  issueDate,
  expirationDate
}) => {
  const getStatusStyles = (status: string) => {
    switch (status) {
      case 'Active':
        return {
          badge: 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200',
          iconBg: 'bg-green-100 dark:bg-green-900/30',
          iconColor: 'text-green-600 dark:text-green-400'
        };
      case 'Expiring Soon':
        return {
          badge: 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200',
          iconBg: 'bg-orange-100 dark:bg-orange-900/30',
          iconColor: 'text-orange-600 dark:text-orange-400'
        };
      case 'Expired':
        return {
          badge: 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200',
          iconBg: 'bg-red-100 dark:bg-red-900/30',
          iconColor: 'text-red-600 dark:text-red-400'
        };
      case 'Pending':
        return {
          badge: 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200',
          iconBg: 'bg-yellow-100 dark:bg-yellow-900/30',
          iconColor: 'text-yellow-600 dark:text-yellow-400'
        };
      default:
        return {
          badge: 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200',
          iconBg: 'bg-gray-100 dark:bg-gray-700',
          iconColor: 'text-gray-600 dark:text-gray-400'
        };
    }
  };

  const statusStyles = getStatusStyles(status);

  return (
    <div className="license-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden hover:transform hover:-translate-y-1 transition-all duration-300">
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className={`w-10 h-10 flex items-center justify-center rounded-full ${statusStyles.iconBg} dark:bg-opacity-20`}>
              <div className={`w-5 h-5 flex items-center justify-center ${statusStyles.iconColor}`}>
                <i className="ri-verified-badge-line"></i>
              </div>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">{title}</h3>
              <p className="text-xs text-gray-500 dark:text-gray-400">{licenseNumber}</p>
            </div>
          </div>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusStyles.badge}`}>
            {status}
          </span>
        </div>
        <div className="mt-3 grid grid-cols-2 gap-2 text-xs">
          <div>
            <span className="text-gray-500 dark:text-gray-400">Issue Date</span>
            <p className="font-medium text-gray-900 dark:text-gray-100 mt-1">{issueDate}</p>
          </div>
          <div>
            <span className="text-gray-500 dark:text-gray-400">Expiration</span>
            <p className="font-medium text-gray-900 dark:text-gray-100 mt-1">{expirationDate}</p>
          </div>
        </div>
      </div>
      <div className="bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 px-4 py-2 flex justify-between">
        <Link
          href={`/customer/licenses/${id}`}
          className="text-xs font-medium text-primary hover:text-primary-dark dark:text-primary-light dark:hover:text-primary transition-colors duration-200"
        >
          View details
        </Link>
        <Link
          href={`/customer/licenses/${id}/renew`}
          className="text-xs font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors duration-200"
        >
          Renew
        </Link>
      </div>
    </div>
  );
};

export default LicenseCard;
