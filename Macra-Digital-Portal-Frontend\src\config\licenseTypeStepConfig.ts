/**
 * License Type Step Configuration System
 *
 * SINGLE SOURCE OF TRUTH for all license type step configurations
 *
 * This consolidated configuration system defines:
 * - Form steps required for each license type
 * - Step order and navigation flow
 * - Validation requirements and estimated times
 * - Fallback configurations for unknown license types
 *
 * Supported license type codes:
 * - telecommunications
 * - postal_services
 * - standards_compliance
 * - broadcasting
 * - spectrum_management
 * - clf (Consumer Lending and Finance)
 * - shortcode
 *
 * Features:
 * - Optimized step loading based on license type codes
 * - Automatic fallback for unsupported types
 * - Smart license type resolution (UUID, code, name mapping)
 * - Comprehensive helper functions for navigation and progress tracking
 */

import { LicenseCategory } from "@/types";

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface StepConfig {
  id: string;
  name: string;
  component: string;
  route: string;
  required: boolean;
  description: string;
  estimatedTime: string; // in minutes
}

export interface LicenseTypeStepConfig {
  licenseTypeId: string;
  name: string;
  description: string;
  steps: StepConfig[];
  estimatedTotalTime: string;
  requirements: string[];
}

// ============================================================================
// BASE STEP DEFINITIONS
// ============================================================================

/**
 * Reusable step configurations that can be composed into different license types
 */
const BASE_STEPS: Record<string, StepConfig> = {

    // Core Information Steps
  applicantInfo: {
    id: 'applicant-info',
    name: 'Applicant Information',
    component: 'ApplicantInfo',
    route: 'applicant-info',
    required: true,
    description: 'Personal or company information of the applicant',
    estimatedTime: '5'
  },

  // Equipment-related Steps
  equipmentDetails: {
    id: 'equipment-details',
    name: 'Details for Equipment',
    component: 'EquipmentDetails',
    route: 'equipment-details',
    required: true,
    description: 'Equipment details including IMEI, brand, model, and manufacturer information',
    estimatedTime: '10'
  },



  addressInfo: {
    id: 'address-info',
    name: 'Address Information',
    component: 'AddressInfo',
    route: 'address-info',
    required: true,
    description: 'Physical and postal address details',
    estimatedTime: '3'
  },

  contactInfo: {
    id: 'contact-info',
    name: 'Contact Information',
    component: 'ContactInfo',
    route: 'contact-info',
    required: true,
    description: 'Contact details and communication preferences',
    estimatedTime: '5'
  },

  // Organizational Steps
  management: {
    id: 'management',
    name: 'Management Structure',
    component: 'Management',
    route: 'management',
    required: false,
    description: 'Management team and organizational structure',
    estimatedTime: '8'
  },

  professionalServices: {
    id: 'professional-services',
    name: 'Professional Services',
    component: 'ProfessionalServices',
    route: 'professional-services',
    required: false,
    description: 'External consultants and service providers',
    estimatedTime: '6'
  },

  // Service and Compliance Steps
  serviceScope: {
    id: 'service-scope',
    name: 'Service Scope',
    component: 'ServiceScope',
    route: 'service-scope',
    required: true,
    description: 'Services offered and geographic coverage',
    estimatedTime: '8'
  },

  legalHistory: {
    id: 'legal-history',
    name: 'Legal History',
    component: 'LegalHistory',
    route: 'legal-history',
    required: true,
    description: 'Legal compliance and regulatory history',
    estimatedTime: '5'
  },

  // Shortcode-specific Steps
  shortCodeUsage: {
    id: 'short-code-usage',
    name: 'Short Code Usage',
    component: 'ShortCodeUsage',
    route: 'short-code-usage',
    required: true,
    description: 'Short code usage and service description',
    estimatedTime: '5'
  },

  // Final Steps
  documents: {
    id: 'documents',
    name: 'Required Documents',
    component: 'Documents',
    route: 'documents',
    required: true,
    description: 'Upload required documents for license application',
    estimatedTime: '10'
  },

  submit: {
    id: 'submit',
    name: 'Finalise Application',
    component: 'Submit',
    route: 'submit',
    required: true,
    description: 'Final review and submission of application',
    estimatedTime: '5'
  }
};

// ============================================================================
// LICENSE TYPE CONFIGURATIONS
// ============================================================================

/**
 * License type specific step configurations
 * Each configuration defines the complete application flow for a specific license type
 */
export const LICENSE_TYPE_STEP_CONFIGS: Record<string, LicenseTypeStepConfig> = {
  telecommunications: {
    licenseTypeId: 'telecommunications',
    name: 'Telecommunications License',
    description: 'License for telecommunications service providers including ISPs, mobile operators, and fixed-line services',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.addressInfo,
      BASE_STEPS.contactInfo,
      BASE_STEPS.management,
      BASE_STEPS.serviceScope,
      BASE_STEPS.legalHistory,
      BASE_STEPS.documents,
      BASE_STEPS.submit
    ],
    estimatedTotalTime: '97 minutes',
    requirements: [
      'Business registration certificate',
      'Tax compliance certificate',
      'Technical specifications',
      'Financial statements',
      'Management CVs',
      'Network coverage plans'
    ]
  },

  postal_services: {
    licenseTypeId: 'postal_services',
    name: 'Postal Services License',
    description: 'License for postal and courier service providers',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.addressInfo,
      BASE_STEPS.contactInfo,
      BASE_STEPS.legalHistory,
      BASE_STEPS.documents,
      BASE_STEPS.submit
    ],
    estimatedTotalTime: '65 minutes',
    requirements: [
      'Business registration certificate',
      'Fleet inventory',
      'Service coverage map',
      'Insurance certificates',
      'Premises documentation'
    ]
  },

  standards_compliance: {
    licenseTypeId: 'standards_compliance',
    name: 'Standards Compliance License',
    description: 'License for standards compliance and certification services',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.equipmentDetails,
      BASE_STEPS.addressInfo,
      BASE_STEPS.contactInfo,
      BASE_STEPS.management,
      BASE_STEPS.professionalServices,
      BASE_STEPS.serviceScope,
      BASE_STEPS.legalHistory,
      BASE_STEPS.documents,
      BASE_STEPS.submit
    ],
    estimatedTotalTime: '87 minutes',
    requirements: [
      'Device IMEI number',
      'Accreditation certificates',
      'Technical competency proof',
      'Quality management system',
      'Laboratory facilities documentation',
      'Staff qualifications'
    ]
  },

  type_approval_certificate: {
    licenseTypeId: 'type_approval_certificate',
    name: 'Type Approval Certificate',
    description: 'Certificate for equipment type approval and standards compliance',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.equipmentDetails,
      BASE_STEPS.documents,
      BASE_STEPS.submit
    ],
    estimatedTotalTime: '25 minutes',
    requirements: [
      'Proof of Payment',
      'ETSI Documents',
      'Test Reports from accredited Labs',
      'Technical Specifications',
      'Authorization Letter (Power of Attorney)',
      'Declaration of Conformity',
      'Any Copies of Approval from ITU Region 1'
    ]
  },

  broadcasting: {
    licenseTypeId: 'broadcasting',
    name: 'Broadcasting License',
    description: 'License for radio and television broadcasting services',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.addressInfo,
      BASE_STEPS.contactInfo,
      BASE_STEPS.management,
      BASE_STEPS.serviceScope,
      BASE_STEPS.professionalServices,
      BASE_STEPS.legalHistory,
      BASE_STEPS.documents,
      BASE_STEPS.submit
    ],
    estimatedTotalTime: '86 minutes',
    requirements: [
      'Broadcasting equipment specifications',
      'Content programming plan',
      'Studio facility documentation',
      'Transmission coverage maps',
      'Local content compliance plan'
    ]
  },

  spectrum_management: {
    licenseTypeId: 'spectrum_management',
    name: 'Spectrum Management License',
    description: 'License for radio frequency spectrum management and allocation',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.management,
      BASE_STEPS.serviceScope,
      BASE_STEPS.professionalServices,
      BASE_STEPS.legalHistory,
      BASE_STEPS.documents,
      BASE_STEPS.submit
    ],
    estimatedTotalTime: '89 minutes',
    requirements: [
      'Spectrum usage plan',
      'Technical interference analysis',
      'Equipment type approval',
      'Frequency coordination agreements',
      'Monitoring capabilities documentation'
    ]
  },

  shortcode: {
    licenseTypeId: 'shortcode',
    name: 'Short Code License',
    description: 'Apply for an SMS short code allocation',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.shortCodeUsage,
      BASE_STEPS.documents,
      BASE_STEPS.submit
    ],
    estimatedTotalTime: '15 minutes',
    requirements: [
      'Any other required documents'
    ]
  }, 

  clf: {
    licenseTypeId: 'clf',
    name: 'Consumer Lending and Finance License',
    description: 'License for consumer lending and finance services',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.addressInfo,
      BASE_STEPS.contactInfo,
      BASE_STEPS.management,
      BASE_STEPS.professionalServices,
      BASE_STEPS.legalHistory,
      BASE_STEPS.documents,
      BASE_STEPS.submit
    ],
    estimatedTotalTime: '50 minutes',
    requirements: [
      'Financial institution license',
      'Capital adequacy documentation',
      'Risk management framework',
      'Consumer protection policies',
      'Anti-money laundering procedures'
    ]
  } 
};

// ============================================================================
// MAPPING AND FALLBACK CONFIGURATIONS
// ============================================================================

/**
 * License type name to configuration key mapping
 * Supports various naming conventions and aliases
 */
const LICENSE_TYPE_NAME_MAPPING: Record<string, string> = {
  'telecommunications': 'telecommunications',
  'postal_services': 'postal_services',
  'standards_compliance': 'standards_compliance',
  'broadcasting': 'broadcasting',
  'spectrum_management': 'spectrum_management',
  'clf': 'clf',
  'consumer lending and finance': 'clf',
  'short_code_allocation': 'shortcode',
  'shortcode': 'shortcode',
  'short_code': 'shortcode',
  'short code': 'shortcode',
  'Short Code License': 'shortcode',
  'type_approval_certificate': 'type_approval_certificate',
  'Type Approval Certificate': 'type_approval_certificate'
};

/**
 * Default fallback configuration for unknown license types
 * Includes all possible steps to ensure comprehensive coverage
 */
const DEFAULT_FALLBACK_CONFIG: LicenseTypeStepConfig = {
  licenseTypeId: 'default',
  name: 'License Application',
  description: 'Comprehensive application process with all required steps',
  steps: [
    BASE_STEPS.applicantInfo,
    BASE_STEPS.addressInfo,
    BASE_STEPS.contactInfo,
    BASE_STEPS.management,
    BASE_STEPS.professionalServices,
    BASE_STEPS.serviceScope,
    BASE_STEPS.legalHistory,
    BASE_STEPS.documents,
    BASE_STEPS.submit
  ],
  estimatedTotalTime: '55 minutes',
  requirements: [
    'Business registration certificate',
    'Tax compliance certificate',
    'Financial statements',
    'Management CVs',
    'Professional qualifications',
    'Service documentation'
  ]
};

var  licenseCategory:LicenseCategory| null = null


// ============================================================================
// CORE HELPER FUNCTIONS
// ============================================================================

/**
 * Get license type step configuration with intelligent fallback
 * Supports multiple lookup strategies for maximum compatibility
 */
export const getLicenseTypeStepConfig = (
  licenseTypeCode: string,
  category: LicenseCategory | null = null
): LicenseTypeStepConfig => {


  if (category) {
  licenseCategory =category;
  console.log('In License steps config, license category exists', licenseCategory);
  }

  // Handle license category override logic
  if (licenseCategory && licenseCategory.name) {
    console.log('Checking license category for specific overrides...');

    // Special case: Check if this is a shortcode category
    const categoryLowerName = licenseCategory.name;
    const categoryCode = categoryLowerName.replace(/\s+/g, '_');
    console.log('Generated category code:', categoryCode);

    if (isLicenseTypeCodeSupported(categoryCode)) {
      console.log(`Using category-based code: ${categoryCode}`);
      licenseTypeCode = categoryCode;
    }
    // Fallback: check if the category name directly matches
    else if (isLicenseTypeCodeSupported(licenseCategory.name)) {
      console.log(`Using category name directly: ${licenseCategory.name}`);
      licenseTypeCode = licenseCategory.name;
    }
    // Otherwise keep the original licenseTypeCode
    else {
      console.log(`No category override found, keeping original: ${licenseTypeCode}`);
    }
  }

  // Validate input
  if (!licenseTypeCode || typeof licenseTypeCode !== 'string') {
    return DEFAULT_FALLBACK_CONFIG;
  }

  // Strategy 1: Direct exact match
  let config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode];
  if (config) {
    console.log(`✅ Found direct match for '${licenseTypeCode}':`, config.name, `(${config.steps.length} steps)`);
    return config;
  }

  // Strategy 2: Normalized lookup (lowercase with underscores)
  const normalizedId = licenseTypeCode.toLowerCase().replace(/[^a-z0-9]/g, '_');
  config = LICENSE_TYPE_STEP_CONFIGS[normalizedId];
  if (config) {
    return config;
  }

  // Strategy 3: Name mapping for common variations
  const mappedKey = LICENSE_TYPE_NAME_MAPPING[normalizedId];
  if (mappedKey) {
    return LICENSE_TYPE_STEP_CONFIGS[mappedKey];
  }

  // Strategy 4: Partial matching for known license type codes
  const knownCodes = Object.keys(LICENSE_TYPE_STEP_CONFIGS);
  const partialMatch = knownCodes.find(code =>
    licenseTypeCode.toLowerCase().includes(code) ||
    code.includes(licenseTypeCode.toLowerCase())
  );

  if (partialMatch) {
    return LICENSE_TYPE_STEP_CONFIGS[partialMatch];
  }

  // Fallback to default configuration
  return DEFAULT_FALLBACK_CONFIG;
};

/**
 * Check if a license type code is supported
 */
export const isLicenseTypeCodeSupported = (licenseTypeCode: string): boolean => {
  // Check direct configuration match
  if (LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode]) {
    return true;
  }

  // Check name mapping
  if (LICENSE_TYPE_NAME_MAPPING[licenseTypeCode]) {
    return true;
  }

  // Check normalized version
  const normalizedId = licenseTypeCode.toLowerCase().replace(/[^a-z0-9]/g, '_');
  return LICENSE_TYPE_STEP_CONFIGS[normalizedId] !== undefined ||
         LICENSE_TYPE_NAME_MAPPING[normalizedId] !== undefined;
};

/**
 * Get steps array for a specific license type
 */
export const getStepsByLicenseTypeCode = (
  licenseTypeCode: string
): StepConfig[] => {
  return getLicenseTypeStepConfig(licenseTypeCode).steps;
};

// ============================================================================
// STEP NAVIGATION FUNCTIONS
// ============================================================================

/**
 * Find a step by its route within a license type configuration
 */
export const getStepByRoute = (
  licenseTypeCode: string,
  stepRoute: string
): StepConfig | null => {
  const config = getLicenseTypeStepConfig(licenseTypeCode);
  return config.steps.find(step => step.route === stepRoute) || null;
};

/**
 * Get a step by its index position
 */
export const getStepByIndex = (
  licenseTypeCode: string,
  stepIndex: number
): StepConfig | null => {
  const config = getLicenseTypeStepConfig(licenseTypeCode);
  if (stepIndex < 0 || stepIndex >= config.steps.length) return null;
  return config.steps[stepIndex];
};

/**
 * Get the index of a step by its route
 */
export const getStepIndex = (
  licenseTypeCode: string,
  stepRoute: string
): number => {
  const config = getLicenseTypeStepConfig(licenseTypeCode);
  return config.steps.findIndex(step => step.route === stepRoute);
};

/**
 * Get the next step in the sequence
 */
export const getNextStep = (
  licenseTypeCode: string,
  currentStepRoute: string
): StepConfig | null => {
  const config = getLicenseTypeStepConfig(licenseTypeCode);
  const currentIndex = getStepIndex(licenseTypeCode, currentStepRoute);
  if (currentIndex === -1 || currentIndex >= config.steps.length - 1) return null;
  return config.steps[currentIndex + 1];
};

/**
 * Get the previous step in the sequence
 */
export const getPreviousStep = (
  licenseTypeCode: string,
  currentStepRoute: string
): StepConfig | null => {
  const config = getLicenseTypeStepConfig(licenseTypeCode);
  const currentIndex = getStepIndex(licenseTypeCode, currentStepRoute);
  if (currentIndex <= 0) return null;
  return config.steps[currentIndex - 1];
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get total number of steps for a license type
 */
export const getTotalSteps = (
  licenseTypeCode: string
): number => {
  const config = getLicenseTypeStepConfig(licenseTypeCode);
  return config.steps.length;
};

/**
 * Get only required steps
 */
export const getRequiredSteps = (
  licenseTypeCode: string
): StepConfig[] => {
  const config = getLicenseTypeStepConfig(licenseTypeCode);
  return config.steps.filter(step => step.required);
};

/**
 * Get only optional steps
 */
export const getOptionalSteps = (
  licenseTypeCode: string
): StepConfig[] => {
  const config = getLicenseTypeStepConfig(licenseTypeCode);
  return config.steps.filter(step => !step.required);
};

/**
 * Calculate completion progress as percentage
 */
export const calculateProgress = (
  licenseTypeCode: string,
  completedSteps: string[]
): number => {
  const config = getLicenseTypeStepConfig(licenseTypeCode);
  const totalSteps = config.steps.length;
  const completed = completedSteps.length;
  return Math.round((completed / totalSteps) * 100);
};

/**
 * Get optimized step configuration with validation
 * @deprecated Use getLicenseTypeStepConfig instead
 */
export const getOptimizedStepConfig = (licenseTypeCode: string): LicenseTypeStepConfig => {
  if (isLicenseTypeCodeSupported(licenseTypeCode)) {
    return LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode];
  }
  return DEFAULT_FALLBACK_CONFIG;
};