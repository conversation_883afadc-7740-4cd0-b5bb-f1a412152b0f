import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NotificationsController } from './notifications.controller';
import { NotificationsService } from './notifications.service';
import { NotificationHelperService } from './notification-helper.service';
import { EmailTemplateService } from './email-template.service';
import { NotificationProcessorService } from './notification-processor.service';
import { Notifications } from '../entities/notifications.entity';
import { UsersModule } from '../users/users.module';
import { ActivityNotesModule } from '../activity-notes/activity-notes.module';
import { ApplicationsModule } from '../applications/applications.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Notifications]),
    forwardRef(() => UsersModule),
    forwardRef(() => ActivityNotesModule),
    forwardRef(() => ApplicationsModule),
  ],
  controllers: [NotificationsController],
  providers: [NotificationsService, NotificationHelperService, EmailTemplateService, NotificationProcessorService],
  exports: [NotificationsService, NotificationHelperService, EmailTemplateService, NotificationProcessorService],
})
export class NotificationsModule {}
