import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MulterModule } from '@nestjs/platform-express';
import { PaymentsService } from './payments.service';
import { PaymentsController } from './payments.controller';
import { Payment } from './entities/payment.entity';
import { User } from '../entities/user.entity';
import { Applications } from '../entities/applications.entity';
import { Invoices } from '../entities/invoices.entity';
import { Documents } from '../entities/documents.entity';
import { Task } from '../entities/tasks.entity';
import { TasksModule } from '../tasks/tasks.module';
import { UsersModule } from '../users/users.module';
import { InvoicesModule } from '../invoices/invoices.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { DocumentsModule } from '../documents/documents.module';
import * as multer from 'multer';

@Module({
  imports: [
    TypeOrmModule.forFeature([Payment, User, Applications, Invoices, Documents, Task]),
    MulterModule.register({
      storage: multer.memoryStorage(),
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
    }),
    forwardRef(() => TasksModule),
    forwardRef(() => UsersModule),
    forwardRef(() => InvoicesModule),
    forwardRef(() => NotificationsModule),
    forwardRef(() => DocumentsModule),
  ],
  controllers: [PaymentsController],
  providers: [PaymentsService],
  exports: [PaymentsService],
})
export class PaymentsModule {}
