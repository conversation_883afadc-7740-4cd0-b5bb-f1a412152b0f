import React, { useState } from 'react';
import ActivityNotesModal from './ActivityNotesModal';
import { useAuth } from '@/contexts/AuthContext';

interface DataField {
  label: string;
  value: any;
  type?: 'text' | 'email' | 'phone' | 'url' | 'date' | 'currency' | 'number' | 'boolean' | 'array' | 'object' | 'status';
  icon?: string;
  fullWidth?: boolean;
}

interface DataDisplayCardProps {
  title: string;
  icon?: string;
  fields: DataField[];
  className?: string;
  showEmptyFields?: boolean;
  defaultCollapsed?: boolean;
  // Email functionality props
  creatorEmail?: string;
  creatorName?: string;
  showEmailButton?: boolean;
}

const DataDisplayCard: React.FC<DataDisplayCardProps> = ({
  title,
  icon,
  fields,
  className = '',
  showEmptyFields = true,
  defaultCollapsed = false,
  creatorEmail,
  creatorName,
  showEmailButton = true
}) => {
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  const {user} = useAuth();

  const isValidEmail = (email: string) => {
    return email != user?.email;
  };
  
  const formatValue = (value: any, type: string = 'text'): string => {
    if (value === null || value === undefined || value === '') {
      return 'Not provided';
    }

    switch (type) {
      case 'date':
        return new Date(value).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(value);
      case 'number':
        return new Intl.NumberFormat('en-US').format(value);
      case 'boolean':
        return value ? 'Yes' : 'No';
      case 'array':
        if (Array.isArray(value)) {
          return value.length > 0 ? value.join(', ') : 'None';
        }
        return 'None';
      case 'object':
        if (typeof value === 'object') {
          return JSON.stringify(value, null, 2);
        }
        return String(value);
      case 'email':
      case 'phone':
      case 'url':
      case 'text':
      default:
        return String(value);
    }
  };

  const getValueColor = (value: any): string => {
    if (value === null || value === undefined || value === '' || value === 'Not provided') {
      return 'text-gray-400 dark:text-gray-500 italic';
    }
    return 'text-gray-900 dark:text-gray-100';
  };

  const renderValue = (field: DataField) => {
    const formattedValue = formatValue(field.value, field.type);
    const colorClass = getValueColor(field.value);

    if (field.type === 'email' && field.value && isValidEmail(field.value)) {
      return (
        <a
          href={`mailto:${field.value}`}
          className="text-blue-600 dark:text-blue-400 hover:underline"
        >
          {formattedValue}
        </a>
      );
    }

    if (field.type === 'phone' && field.value) {
      return (
        <a
          href={`tel:${field.value}`}
          className="text-blue-600 dark:text-blue-400 hover:underline"
        >
          {formattedValue}
        </a>
      );
    }

    if (field.type === 'url' && field.value) {
      return (
        <a
          href={field.value}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 dark:text-blue-400 hover:underline"
        >
          {formattedValue}
        </a>
      );
    }

    if (field.type === 'object' && field.value) {
      return (
        <pre className="text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded overflow-auto max-h-32">
          {formattedValue}
        </pre>
      );
    }

    if (field.type === 'status' && field.value) {
      // Get status badge styles
      const getStatusBadgeStyles = (status: string) => {
        const normalizedStatus = status.toLowerCase().replace(/[_\s]/g, '');

        switch (normalizedStatus) {
          case 'submitted':
          case 'pending':
          case 'underreview':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200';
          case 'approved':
          case 'active':
          case 'paid':
            return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200';
          case 'rejected':
          case 'denied':
          case 'expired':
          case 'cancelled':
            return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200';
          case 'draft':
          case 'incomplete':
            return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
          case 'pendingpayment':
          case 'awaitingpayment':
            return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-200';
          case 'processing':
          case 'inprogress':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200';
          default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
        }
      };

      return (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeStyles(field.value)}`}>
          {formattedValue}
        </span>
      );
    }

    return <span className={colorClass}>{formattedValue}</span>;
  };

  const visibleFields = showEmptyFields 
    ? fields 
    : fields.filter(field => field.value !== null && field.value !== undefined && field.value !== '');

  if (visibleFields.length === 0 && !showEmptyFields) {
    return null;
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {icon && (
              <i className={`${icon} text-xl text-red-600 dark:text-red-400 mr-3`}></i>
            )}
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {title}
            </h3>
          </div>
          <button
            type="button"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
            title={isCollapsed ? 'Expand' : 'Collapse'}
          >
            <i className={`${isCollapsed ? 'ri-add-line' : 'ri-subtract-line'} text-lg text-gray-500 dark:text-gray-400`}></i>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className={`transition-all duration-300 ease-in-out overflow-hidden ${isCollapsed ? 'max-h-0' : 'max-h-none'}`}>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {visibleFields.map((field, index) => (
              <div
                key={index}
                className={field.fullWidth ? 'md:col-span-2' : ''}
              >
                <div className="flex items-start space-x-3">
                  {field.icon && (
                    <i className={`${field.icon} text-gray-400 dark:text-gray-500 mt-1 flex-shrink-0`}></i>
                  )}
                  <div className="flex-1 min-w-0">
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      {field.label}
                    </label>
                    <div className="text-sm">
                      {renderValue(field)}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {visibleFields.length === 0 && showEmptyFields && (
            <div className="text-center py-8">
              <i className="ri-information-line text-3xl text-gray-300 dark:text-gray-600 mb-2"></i>
              <p className="text-gray-500 dark:text-gray-400">No data available for this section</p>
            </div>
          )}
        </div>
      </div>

      {/* Activity Notes Modal for Email */}
      {creatorEmail && (
        <ActivityNotesModal
          isOpen={isEmailModalOpen}
          onClose={() => setIsEmailModalOpen(false)}
          entityId="general"
          entityType="general"
          initialEmails={creatorEmail || ''}
          title={`Email ${creatorName || 'Creator'} - ${title}`}
        />
      )}
    </div>
  );
};

export default DataDisplayCard;
