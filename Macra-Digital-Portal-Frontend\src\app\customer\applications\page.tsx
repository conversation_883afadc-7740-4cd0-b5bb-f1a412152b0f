'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import { applicationService } from '@/services/applicationService';
import { licenseService } from '@/services/licenseService';
import { useApplicationNotifications } from '@/hooks/useApplicationNotifications';
import { Application, License, ApplicationStatus } from '@/types/license';
import ApplicationViewModal from '@/components/license/ApplicationViewModal';
import DataTable from '@/components/common/DataTable';
import Select from '@/components/common/Select';
import toast from 'react-hot-toast';

// Define PaginateQuery interface locally since it's not exported
interface PaginateQuery {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string[];
  filter?: Record<string, string>;
}

interface ApplicationFilters {
  status?: string;
  license_category_id?: string;
}

const MyApplicationPage: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Enable application notifications
  useApplicationNotifications();

  const [applicationsData, setApplicationsData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState<boolean>(false);
  const [filters, setFilters] = useState<ApplicationFilters>({});
  const modalOpeningRef = React.useRef<boolean>(false);
  const [currentQuery, setCurrentQuery] = useState<PaginateQuery>({ page: 1, limit: 10 });

  // Breadcrumbs
  const breadcrumbs = [
    { name: 'Dashboard', href: '/customer', label: 'Dashboard' },
    { name: 'My Applications', href: '/customer/applications', label: 'My Applications' }
  ];

  // Check for success message from URL params
  useEffect(() => {
    const success = searchParams.get('success');
    if (success === 'application_submitted') {
      setShowSuccessMessage(true);
      // Clear the URL parameter
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('success');
      window.history.replaceState({}, '', newUrl.toString());
      
      // Hide success message after 5 seconds
      setTimeout(() => setShowSuccessMessage(false), 5000);
    }
  }, [searchParams]);

  // Modal functions
  const openStatusModal = (application: Application) => {
    if (modalOpeningRef.current) {
      console.log('🚫 Modal already opening, ignoring duplicate click');
      return;
    }
    
    modalOpeningRef.current = true;
    console.log('🔄 Opening status modal for application:', application.application_id);
    setSelectedApplication(application);
    // Small delay to ensure state is stable
    setTimeout(() => {
      setIsModalOpen(true);
      modalOpeningRef.current = false;
    }, 50);
  };

  const closeStatusModal = () => {
    console.log('🔄 Closing status modal');
    modalOpeningRef.current = false;
    setIsModalOpen(false);
    // Delay clearing the selected application to prevent flashing
    setTimeout(() => {
      setSelectedApplication(null);
    }, 300);
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof ApplicationFilters, value: string) => {
    const newFilters = { ...filters };

    if (value && value.trim() !== '') {
      newFilters[key] = value;
    } else {
      delete newFilters[key];
    }

    setFilters(newFilters);
  };

  // Fetch user applications with pagination
  const loadApplications = useCallback(async (query: PaginateQuery) => {
    if (!isAuthenticated) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setCurrentQuery(query);

      const response = await applicationService.getUserApplications({
        page: query.page,
        limit: query.limit,
        search: query.search,
        sortBy: query.sortBy?.[0],
        sortOrder: query.sortBy?.[1] as 'ASC' | 'DESC',
        filter: { ...query.filter, ...filters },
      });

      console.log('📋 Fetched applications:', response);

      // Process applications data if it exists
      if (response?.data) {
        const processedApplications = response.data.map((app: Application) => ({
          ...app,
          status: app.status || 'draft',
          progress_percentage: app.progress_percentage || 0,
          current_step: app.current_step || 1,
          application_number: app.application_number || `APP-${app.application_id?.slice(0, 8)}`,
          license_category: app.license_category ? {
            ...app.license_category,
            name: app.license_category.name || 'License Category'
          } : undefined
        }));

        console.log('📋 Processed applications:', processedApplications);

        const finalData = {
          ...response,
          data: processedApplications
        };

        console.log('📋 Final data structure:', finalData);
        setApplicationsData(finalData);
      } else {
        console.log('📋 Setting response directly:', response);
        setApplicationsData(response);
      }
    } catch (err: any) {
      console.error('❌ Error fetching applications:', err);
      setError(err.response?.data?.message || 'Failed to load applications');

      // Set empty data structure to prevent undefined errors
      setApplicationsData({
        data: [],
        meta: {
          itemsPerPage: query.limit || 10,
          totalItems: 0,
          currentPage: query.page || 1,
          totalPages: 0,
          sortBy: [],
          searchBy: [],
          search: '',
          select: [],
        },
        links: { current: '' },
      });
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  useEffect(() => {
    if (isAuthenticated) {
      loadApplications({ page: 1, limit: 10 });
    }
  }, [isAuthenticated, loadApplications]);

  // Reload data when filters change
  useEffect(() => {
    // Filter out undefined values and ensure proper typing
    const cleanFilters: Record<string, string> = {};
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value.trim() !== '') {
        cleanFilters[key] = value;
      }
    });

    // Always reload with current query and filters
    loadApplications({
      page: 1,
      limit: currentQuery.limit || 10,
      filter: Object.keys(cleanFilters).length > 0 ? cleanFilters : undefined
    });
  }, [filters, loadApplications, currentQuery.limit]);

  // Show loading state
  if (loading) {
    return (
      <CustomerLayout breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading your licenses...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <CustomerLayout breadcrumbs={breadcrumbs}>
        <div className="text-center py-12">
          <div className="mx-auto h-12 w-12 text-red-400">
            <i className="ri-error-warning-line text-4xl"></i>
          </div>
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Error loading applications</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{error}</p>
          <div className="mt-6">
            <button
              type="button"
              onClick={() => loadApplications({ page: 1, limit: 10 })}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Try again
            </button>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Function to continue working on an application
  const handleContinueApplication = async (application: Application) => {
    // Clear any existing errors
    setError(null)
    if (!application || !application.license_category_id) {
      setError('Invalid application data');
      return;
    }

    try {
      // Navigate to the application form with the application ID
      const url = `/customer/applications/apply/applicant-info?license_category_id=${application.license_category_id}&application_id=${application.application_id}`;
      console.log('🔄 Navigating to:', url);
      router.push(url);
    } catch (err: any) {
      console.error('❌ Error continuing application:', err);
      setError('Failed to continue application');
    }
  };

  // Function to check if application can be continued
  const canContinueApplication = (application: Application): boolean => {
    return application.status === ApplicationStatus.DRAFT;
  };

  // Define columns for applications table
  const applicationColumns = [
    {
      key: 'application',
      label: 'Application',
      render: (value: unknown, application: Application) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-primary flex items-center justify-center">
              <i className="ri-file-text-line text-white"></i>
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {application.application_number}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              ID: {application.application_id?.slice(0, 8)}...
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'license_category',
      label: 'License Type',
      render: (value: unknown, application: Application) => (
        <div>
          <div className="text-sm text-gray-900 dark:text-gray-100">
            {application.license_category?.name || 'Unknown'}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {application.license_category?.description || 'No description'}
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: unknown, application: Application) => {
        const getStatusBadge = (status: string): React.ReactElement => {
          const statusConfig = {
            'draft': { color: 'bg-blue-100 text-blue-800', label: 'Draft' },
            'submitted': { color: 'bg-blue-100 text-blue-800', label: 'Submitted' },
            'under_review': { color: 'bg-yellow-100 text-yellow-800', label: 'Under Review' },
            'pass_evaluation': { color: 'bg-yellow-100 text-yellow-800', label: 'Pass Evaluation' },
            'pending_payment': { color: 'bg-yellow-100 text-yellow-800', label: 'Pending Payment' },
            'evaluation': { color: 'bg-purple-100 text-purple-800', label: 'Evaluation' },
            'approved': { color: 'bg-green-100 text-green-800', label: 'Approved' },
            'rejected': { color: 'bg-red-100 text-red-800', label: 'Rejected' }
          };
          const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.submitted;
          return (
            <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${config.color}`}>
              {config.label}
            </span>
          );
        };

        return (
          <div className="flex flex-col space-y-1">
            {getStatusBadge(application.status)}
            <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1.5">
              <div
                className="bg-primary h-1.5 rounded-full transition-all duration-300"
                style={{ width: `${application.progress_percentage || 0}%` }}
              ></div>
            </div>
          </div>
        );
      },
    },
    {
      key: 'submitted_at',
      label: 'Submitted',
      render: (value: unknown, application: Application) => (
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {application.submitted_at
            ? new Date(application.submitted_at).toLocaleDateString()
            : application.created_at
            ? new Date(application.created_at).toLocaleDateString()
            : 'Not submitted'
          }
        </div>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: unknown, application: Application) => (
        <div className="flex items-center justify-end space-x-2">
          {/* View Button */}
          <button
            type="button"
            title="View application details"
            onClick={() => openStatusModal(application)}
            className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full hover:bg-blue-200 transition-colors"
          >
            <i className="ri-eye-line"></i>
            View
          </button>

          {/* Continue Application Button - for draft applications */}
          {canContinueApplication(application) && (
            <button
              type="button"
              title="Continue working on this application"
              onClick={() => handleContinueApplication(application)}
              className="inline-flex items-center px-3 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full hover:bg-yellow-200 transition-colors"
            >
              <i className="ri-edit-line mr-1"></i>
              Continue
            </button>
          )}

          {/* Download License Button - for approved applications */}
          {application.status === ApplicationStatus.APPROVED && (
            <button
              type="button"
              title="Download license document"
              onClick={async () => {
                try {
                  const license = await licenseService.getLicenseByApplication(application.application_id);
                  if (license?.license_id) {
                    const pdfBlob = await licenseService.downloadLicensePDF(license.license_id);
                    const url = window.URL.createObjectURL(pdfBlob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `${license.license_number}_license.pdf`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                  }
                } catch (err) {
                  toast.error('Failed to download license');
                }
              }}
              className="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full hover:bg-green-200 transition-colors"
            >
              <i className="ri-download-line mr-1"></i>
              License
            </button>
          )}
        </div>
      ),
    },
  ];

  return (
    <CustomerLayout breadcrumbs={breadcrumbs}>
      <div className="max-w-7xl mx-auto">
        {/* Page header */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">My Applications</h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Track your license applications and manage your approved licenses.
              </p>
            </div>
            <div className="flex space-x-3">
              <Link
                href="/customer/applications/apply"
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-button text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
              >
                <div className="w-4 h-4 flex items-center justify-center mr-2">
                  <i className="ri-add-line"></i>
                </div>
                New Application
              </Link>
            </div>
          </div>
        </div>

        {/* Success Message */}
        {showSuccessMessage && (
          <div className="mb-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <i className="ri-checkbox-circle-line text-green-400 text-xl"></i>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800 dark:text-green-200">Application Submitted Successfully!</h3>
                <p className="mt-2 text-sm text-green-700 dark:text-green-300">
                  Your application has been submitted and is now under review. You will receive email notifications as your application progresses.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Filters Section */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            {Object.keys(filters).length > 0 && (
              <button
                onClick={() => setFilters({})}
                className="text-sm text-primary hover:text-red-700 font-medium"
              >
                Clear Filters
              </button>
            )}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Status Filter */}
            <Select
              label="Status"
              value={filters.status || ''}
              onChange={(value) => handleFilterChange('status', value)}
              options={[
                { value: '', label: 'All Statuses' },
                { value: 'draft', label: 'Draft' },
                { value: 'submitted', label: 'Submitted' },
                { value: 'pass_evaluation', label: 'Pass Evaluation' },
                { value: 'under_review', label: 'Under Review' },
                { value: 'evaluation', label: 'Evaluation' },
                { value: 'approved', label: 'Approved' },
                { value: 'rejected', label: 'Rejected' }
              ]}
            />

            {/* License Category Filter - We'll add this later when we have license categories data */}
            {/*
            <Select
              label="License Category"
              value={filters.license_category_id || ''}
              onChange={(value) => handleFilterChange('license_category_id', value)}
              options={[
                { value: '', label: 'All Categories' },
                // Add license categories here
              ]}
            />
            */}
          </div>
        </div>

        {/* Applications DataTable */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Applications Overview</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              View and track all your license applications
            </p>
          </div>

          <DataTable
            columns={applicationColumns}
            data={applicationsData}
            loading={loading}
            onQueryChange={(query) => {
              console.log('📋 DataTable query change:', query);
              loadApplications({
                page: query.page,
                limit: query.limit,
                search: query.search,
                sortBy: query.sortBy,
                filter: { ...query.filter, ...filters }
              });
            }}
            searchPlaceholder="Search applications by number or status..."
            emptyStateIcon="ri-file-list-line"
            emptyStateMessage="No applications found. Start your first license application!"
          />

        </div>
      </div>

      {/* Application View Modal */}
      <ApplicationViewModal
        isOpen={isModalOpen}
        onClose={closeStatusModal}
        applicationId={selectedApplication?.application_id || null}
        onUpdate={() => loadApplications({ page: 1, limit: 10 })}
      />
    </CustomerLayout>
  );
};

export default MyApplicationPage;
