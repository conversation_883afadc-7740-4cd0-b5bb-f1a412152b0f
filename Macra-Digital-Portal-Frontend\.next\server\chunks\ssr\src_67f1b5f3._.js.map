{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Pagination.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\n// Import the pagination response type from the existing structure\r\ninterface PaginationMeta {\r\n  itemsPerPage: number;\r\n  totalItems: number;\r\n  currentPage: number;\r\n  totalPages: number;\r\n  sortBy: [string, string][];\r\n  searchBy: string[];\r\n  search: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\ninterface PaginationProps {\r\n  meta: PaginationMeta;\r\n  onPageChange: (page: number) => void;\r\n  onPageSizeChange?: (pageSize: number) => void;\r\n  showFirstLast?: boolean;\r\n  showPageSizeSelector?: boolean;\r\n  showInfo?: boolean;\r\n  maxVisiblePages?: number;\r\n  pageSizeOptions?: number[];\r\n  className?: string;\r\n}\r\n\r\nconst Pagination: React.FC<PaginationProps> = ({\r\n  meta,\r\n  onPageChange,\r\n  onPageSizeChange,\r\n  showFirstLast = true,\r\n  showPageSizeSelector = true,\r\n  showInfo = true,\r\n  maxVisiblePages = 7,\r\n  pageSizeOptions = [10, 25, 50, 100],\r\n  className = ''\r\n}) => {\r\n  const { currentPage, totalPages, totalItems, itemsPerPage } = meta;\r\n\r\n  // Don't render if there's only one page or no pages and no additional features\r\n  if (totalPages <= 1 && !showPageSizeSelector && !showInfo) {\r\n    return null;\r\n  }\r\n\r\n  // Calculate current items range\r\n  const startItem = totalItems > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;\r\n  const endItem = Math.min(currentPage * itemsPerPage, totalItems);\r\n\r\n  // Calculate which pages to show\r\n  const getVisiblePages = (): (number | string)[] => {\r\n    const pages: (number | string)[] = [];\r\n    \r\n    // If total pages is less than or equal to maxVisiblePages, show all\r\n    if (totalPages <= maxVisiblePages) {\r\n      for (let i = 1; i <= totalPages; i++) {\r\n        pages.push(i);\r\n      }\r\n      return pages;\r\n    }\r\n\r\n    // Always show first page\r\n    pages.push(1);\r\n\r\n    // Calculate start and end of the visible range around current page\r\n    const sidePages = Math.floor((maxVisiblePages - 3) / 2); // -3 for first, last, and current\r\n    let startPage = Math.max(2, currentPage - sidePages);\r\n    let endPage = Math.min(totalPages - 1, currentPage + sidePages);\r\n\r\n    // Adjust if we're near the beginning\r\n    if (currentPage <= sidePages + 2) {\r\n      startPage = 2;\r\n      endPage = Math.min(totalPages - 1, maxVisiblePages - 1);\r\n    }\r\n\r\n    // Adjust if we're near the end\r\n    if (currentPage >= totalPages - sidePages - 1) {\r\n      startPage = Math.max(2, totalPages - maxVisiblePages + 2);\r\n      endPage = totalPages - 1;\r\n    }\r\n\r\n    // Add ellipsis after first page if needed\r\n    if (startPage > 2) {\r\n      pages.push('...');\r\n    }\r\n\r\n    // Add pages in the visible range\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    // Add ellipsis before last page if needed\r\n    if (endPage < totalPages - 1) {\r\n      pages.push('...');\r\n    }\r\n\r\n    // Always show last page (if it's not already included)\r\n    if (totalPages > 1) {\r\n      pages.push(totalPages);\r\n    }\r\n\r\n    return pages;\r\n  };\r\n\r\n  const visiblePages = getVisiblePages();\r\n\r\n  const handlePageClick = (page: number | string) => {\r\n    if (typeof page === 'number' && page !== currentPage) {\r\n      onPageChange(page);\r\n    }\r\n  };\r\n\r\n  const handlePageSizeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {\r\n    const newPageSize = parseInt(event.target.value);\r\n    if (onPageSizeChange) {\r\n      onPageSizeChange(newPageSize);\r\n    }\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    if (currentPage > 1) {\r\n      onPageChange(currentPage - 1);\r\n    }\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (currentPage < totalPages) {\r\n      onPageChange(currentPage + 1);\r\n    }\r\n  };\r\n\r\n  const handleFirst = () => {\r\n    if (currentPage !== 1) {\r\n      onPageChange(1);\r\n    }\r\n  };\r\n\r\n  const handleLast = () => {\r\n    if (currentPage !== totalPages) {\r\n      onPageChange(totalPages);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 ${className}`}>\r\n      {/* Left side - Info and page size selector */}\r\n      <div className=\"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4\">\r\n        {/* Items info */}\r\n        {showInfo && totalItems > 0 && (\r\n          <div className=\"text-sm text-gray-700 dark:text-gray-300\">\r\n            Showing <span className=\"font-medium\">{startItem}</span> to{' '}\r\n            <span className=\"font-medium\">{endItem}</span> of{' '}\r\n            <span className=\"font-medium\">{totalItems}</span> results\r\n          </div>\r\n        )}\r\n\r\n        {/* Page size selector */}\r\n        {showPageSizeSelector && onPageSizeChange && (\r\n          <div className=\"flex items-center space-x-2\">\r\n            <select\r\n              value={itemsPerPage}\r\n              onChange={handlePageSizeChange}\r\n              className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\r\n            >\r\n              {pageSizeOptions.map((size) => (\r\n                <option key={size} value={size}>\r\n                  {size} per page\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Right side - Pagination controls */}\r\n      {totalPages > 1 && (\r\n        <nav className=\"flex items-center space-x-1\" aria-label=\"Pagination\">\r\n          {/* First page button */}\r\n          {showFirstLast && currentPage > 1 && (\r\n            <button\r\n              onClick={handleFirst}\r\n              className=\"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300\"\r\n              aria-label=\"Go to first page\"\r\n            >\r\n              <i className=\"ri-skip-back-line\"></i>\r\n            </button>\r\n          )}\r\n\r\n          {/* Previous button */}\r\n          <button\r\n            onClick={handlePrevious}\r\n            disabled={currentPage === 1}\r\n            className={`inline-flex items-center px-2 py-2 text-sm font-medium border ${\r\n              currentPage === 1\r\n                ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500'\r\n                : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n            } ${showFirstLast && currentPage > 1 ? '' : 'rounded-l-md'}`}\r\n            aria-label=\"Go to previous page\"\r\n          >\r\n            <i className=\"ri-arrow-left-s-line\"></i>\r\n          </button>\r\n\r\n          {/* Page numbers */}\r\n          {visiblePages.map((page, index) => (\r\n            <React.Fragment key={index}>\r\n              {page === '...' ? (\r\n                <span className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400\">\r\n                  ...\r\n                </span>\r\n              ) : (\r\n                <button\r\n                  onClick={() => handlePageClick(page)}\r\n                  className={`inline-flex items-center px-4 py-2 text-sm font-medium border ${\r\n                    page === currentPage\r\n                      ? 'text-white bg-red-600 border-red-600 hover:bg-red-700'\r\n                      : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n                  }`}\r\n                  aria-label={`Go to page ${page}`}\r\n                  aria-current={page === currentPage ? 'page' : undefined}\r\n                >\r\n                  {page}\r\n                </button>\r\n              )}\r\n            </React.Fragment>\r\n          ))}\r\n\r\n          {/* Next button */}\r\n          <button\r\n            onClick={handleNext}\r\n            disabled={currentPage === totalPages}\r\n            className={`inline-flex items-center px-2 py-2 text-sm font-medium border ${\r\n              currentPage === totalPages\r\n                ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500'\r\n                : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n            } ${showFirstLast && currentPage < totalPages ? '' : 'rounded-r-md'}`}\r\n            aria-label=\"Go to next page\"\r\n          >\r\n            <i className=\"ri-arrow-right-s-line\"></i>\r\n          </button>\r\n\r\n          {/* Last page button */}\r\n          {showFirstLast && currentPage < totalPages && (\r\n            <button\r\n              onClick={handleLast}\r\n              className=\"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300\"\r\n              aria-label=\"Go to last page\"\r\n            >\r\n              <i className=\"ri-skip-forward-line\"></i>\r\n            </button>\r\n          )}\r\n        </nav>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Pagination;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AA4BA,MAAM,aAAwC,CAAC,EAC7C,IAAI,EACJ,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,IAAI,EACpB,uBAAuB,IAAI,EAC3B,WAAW,IAAI,EACf,kBAAkB,CAAC,EACnB,kBAAkB;IAAC;IAAI;IAAI;IAAI;CAAI,EACnC,YAAY,EAAE,EACf;IACC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG;IAE9D,+EAA+E;IAC/E,IAAI,cAAc,KAAK,CAAC,wBAAwB,CAAC,UAAU;QACzD,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,YAAY,aAAa,IAAI,CAAC,cAAc,CAAC,IAAI,eAAe,IAAI;IAC1E,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,cAAc;IAErD,gCAAgC;IAChC,MAAM,kBAAkB;QACtB,MAAM,QAA6B,EAAE;QAErC,oEAAoE;QACpE,IAAI,cAAc,iBAAiB;YACjC,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,MAAM,IAAI,CAAC;YACb;YACA,OAAO;QACT;QAEA,yBAAyB;QACzB,MAAM,IAAI,CAAC;QAEX,mEAAmE;QACnE,MAAM,YAAY,KAAK,KAAK,CAAC,CAAC,kBAAkB,CAAC,IAAI,IAAI,kCAAkC;QAC3F,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc;QAC1C,IAAI,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc;QAErD,qCAAqC;QACrC,IAAI,eAAe,YAAY,GAAG;YAChC,YAAY;YACZ,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG,kBAAkB;QACvD;QAEA,+BAA+B;QAC/B,IAAI,eAAe,aAAa,YAAY,GAAG;YAC7C,YAAY,KAAK,GAAG,CAAC,GAAG,aAAa,kBAAkB;YACvD,UAAU,aAAa;QACzB;QAEA,0CAA0C;QAC1C,IAAI,YAAY,GAAG;YACjB,MAAM,IAAI,CAAC;QACb;QAEA,iCAAiC;QACjC,IAAK,IAAI,IAAI,WAAW,KAAK,SAAS,IAAK;YACzC,MAAM,IAAI,CAAC;QACb;QAEA,0CAA0C;QAC1C,IAAI,UAAU,aAAa,GAAG;YAC5B,MAAM,IAAI,CAAC;QACb;QAEA,uDAAuD;QACvD,IAAI,aAAa,GAAG;YAClB,MAAM,IAAI,CAAC;QACb;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,MAAM,kBAAkB,CAAC;QACvB,IAAI,OAAO,SAAS,YAAY,SAAS,aAAa;YACpD,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAc,SAAS,MAAM,MAAM,CAAC,KAAK;QAC/C,IAAI,kBAAkB;YACpB,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,YAAY;YAC5B,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,gBAAgB,GAAG;YACrB,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,gBAAgB,YAAY;YAC9B,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,gKAAgK,EAAE,WAAW;;0BAE5L,8OAAC;gBAAI,WAAU;;oBAEZ,YAAY,aAAa,mBACxB,8OAAC;wBAAI,WAAU;;4BAA2C;0CAChD,8OAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAiB;4BAAI;0CAC5D,8OAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAe;4BAAI;0CAClD,8OAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAkB;;;;;;;oBAKpD,wBAAwB,kCACvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,OAAO;4BACP,UAAU;4BACV,WAAU;sCAET,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC;oCAAkB,OAAO;;wCACvB;wCAAK;;mCADK;;;;;;;;;;;;;;;;;;;;;YAUtB,aAAa,mBACZ,8OAAC;gBAAI,WAAU;gBAA8B,cAAW;;oBAErD,iBAAiB,cAAc,mBAC9B,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;kCAKjB,8OAAC;wBACC,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,WAAW,CAAC,8DAA8D,EACxE,gBAAgB,IACZ,0HACA,uLACL,CAAC,EAAE,iBAAiB,cAAc,IAAI,KAAK,gBAAgB;wBAC5D,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;oBAId,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;sCACZ,SAAS,sBACR,8OAAC;gCAAK,WAAU;0CAAgK;;;;;yFAIhL,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,CAAC,8DAA8D,EACxE,SAAS,cACL,0DACA,wLACJ;gCACF,cAAY,CAAC,WAAW,EAAE,MAAM;gCAChC,gBAAc,SAAS,cAAc,SAAS;0CAE7C;;;;;;2BAhBc;;;;;kCAuBvB,8OAAC;wBACC,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,WAAW,CAAC,8DAA8D,EACxE,gBAAgB,aACZ,0HACA,uLACL,CAAC,EAAE,iBAAiB,cAAc,aAAa,KAAK,gBAAgB;wBACrE,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;oBAId,iBAAiB,cAAc,4BAC9B,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAO3B;uCAEe", "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/DataTable.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { PaginateQuery } from '../../types';\r\nimport Pagination from './Pagination';\r\nimport '../../styles/DataTable.css';\r\n\r\n// Generic paginated response interface to handle different response types\r\ninterface GenericPaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems?: number;\r\n    currentPage?: number;\r\n    totalPages?: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    select?: string[];\r\n    filter?: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first?: string;\r\n    previous?: string;\r\n    current: string;\r\n    next?: string;\r\n    last?: string;\r\n  };\r\n}\r\n\r\ninterface Column<T> {\r\n  key: keyof T | string;\r\n  label: string;\r\n  sortable?: boolean;\r\n  searchable?: boolean;\r\n  render?: (value: unknown, item: T) => React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\ninterface DataTableProps<T> {\r\n  columns: Column<T>[];\r\n  data: GenericPaginatedResponse<T> | null;\r\n  loading?: boolean;\r\n  onQueryChange: (query: PaginateQuery) => void;\r\n  searchPlaceholder?: string;\r\n  className?: string;\r\n  emptyStateIcon?: string;\r\n  emptyStateMessage?: string;\r\n}\r\n\r\nexport default function DataTable<T extends Record<string, unknown>>({\r\n  columns,\r\n  data,\r\n  loading = false,\r\n  onQueryChange,\r\n  searchPlaceholder = \"Search...\",\r\n  className = \"\",\r\n  emptyStateIcon = \"ri-inbox-line\",\r\n  emptyStateMessage = \"No data found\",\r\n}: DataTableProps<T>) {\r\n  const [query, setQuery] = useState<PaginateQuery>({\r\n    page: 1,\r\n    limit: 10,\r\n    search: '',\r\n    sortBy: [],\r\n  });\r\n  const [searchInput, setSearchInput] = useState('');\r\n\r\n  const handleSearch = useCallback((search: string) => {\r\n    try {\r\n      const newQuery = { ...query, search, page: 1 };\r\n      setQuery(newQuery);\r\n      onQueryChange(newQuery);\r\n    } catch (error) {\r\n      console.error('Error handling search:', error);\r\n    }\r\n  }, [query, onQueryChange]);\r\n\r\n  // Debounced search effect\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (searchInput !== query.search) {\r\n        handleSearch(searchInput);\r\n      }\r\n    }, 300);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [searchInput, query.search, handleSearch]);\r\n\r\n  const handleSort = (columnKey: string) => {\r\n    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));\r\n    let newSortBy: string[] = [];\r\n\r\n    if (!currentSort) {\r\n      newSortBy = [`${columnKey}:ASC`];\r\n    } else if (currentSort.endsWith(':ASC')) {\r\n      newSortBy = [`${columnKey}:DESC`];\r\n    } else {\r\n      newSortBy = [];\r\n    }\r\n\r\n    const newQuery = { ...query, sortBy: newSortBy, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const handlePageChange = (page: number) => {\r\n    const newQuery = { ...query, page };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const getSortDirection = (columnKey: string): 'asc' | 'desc' | null => {\r\n    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));\r\n    if (!currentSort) return null;\r\n    return currentSort.endsWith(':ASC') ? 'asc' : 'desc';\r\n  };\r\n\r\n  // Handle null data case early\r\n  if (!data) {\r\n    return (\r\n      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>\r\n        <div className=\"p-6 text-center text-gray-500 dark:text-gray-400\">\r\n          <div className=\"flex items-center justify-center\">\r\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600\"></div>\r\n            <span className=\"ml-2\">Loading...</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const handlePageSizeChange = (newPageSize: number) => {\r\n    const newQuery = { ...query, limit: newPageSize, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  return (\r\n    <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>\r\n      {/* Search Bar */}\r\n      <div className=\"p-6 border-b border-gray-200 dark:border-gray-700\">\r\n        <div className=\"relative\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n            <i className=\"ri-search-line text-gray-400 dark:text-gray-500\"></i>\r\n          </div>\r\n          <input\r\n            type=\"text\"\r\n            placeholder={searchPlaceholder}\r\n            value={searchInput}\r\n            onChange={(e) => setSearchInput(e.target.value)}\r\n            className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 dark:text-gray-100\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <div className=\"overflow-x-auto data-table-container\">\r\n        <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n          <thead className=\"bg-gray-50 dark:bg-gray-900\">\r\n            <tr>\r\n              {columns.map((column) => (\r\n                <th\r\n                  key={String(column.key)}\r\n                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${\r\n                    column.sortable ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800' : ''\r\n                  } ${column.className || ''}`}\r\n                  onClick={() => column.sortable && handleSort(String(column.key))}\r\n                >\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <span>{column.label}</span>\r\n                    {column.sortable && (\r\n                      <div className=\"flex flex-col\">\r\n                        <i className={`ri-arrow-up-s-line text-xs ${\r\n                          getSortDirection(String(column.key)) === 'asc' ? 'text-red-600' : 'text-gray-400'\r\n                        }`}></i>\r\n                        <i className={`ri-arrow-down-s-line text-xs -mt-1 ${\r\n                          getSortDirection(String(column.key)) === 'desc' ? 'text-red-600' : 'text-gray-400'\r\n                        }`}></i>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </th>\r\n              ))}\r\n            </tr>\r\n          </thead>\r\n          <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n            {loading ? (\r\n              <tr>\r\n                <td colSpan={columns.length} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\r\n                  <div className=\"flex items-center justify-center\">\r\n                    <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600\"></div>\r\n                    <span className=\"ml-2\">Loading...</span>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            ) : !data?.data || data.data.length === 0 ? (\r\n              <tr>\r\n                <td colSpan={columns.length} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\r\n                  <div className=\"flex flex-col items-center justify-center py-8\">\r\n                    <i className={`${emptyStateIcon} text-4xl mb-2`}></i>\r\n                    <p>{emptyStateMessage}</p>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            ) : (\r\n              data.data.map((item, index) => (\r\n                <tr key={index} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                  {columns.map((column) => (\r\n                    <td key={String(column.key)} className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100\">\r\n                      {column.render\r\n                        ? column.render(item[column.key as keyof T], item)\r\n                        : String(item[column.key as keyof T] || '')\r\n                      }\r\n                    </td>\r\n                  ))}\r\n                </tr>\r\n              ))\r\n            )}\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      {/* Pagination */}\r\n      {data?.meta && data.meta.totalItems !== undefined && data.meta.currentPage !== undefined && data.meta.totalPages !== undefined && (\r\n        <Pagination\r\n          meta={{\r\n            ...data.meta,\r\n            totalItems: data.meta.totalItems,\r\n            currentPage: data.meta.currentPage,\r\n            totalPages: data.meta.totalPages,\r\n          }}\r\n          onPageChange={handlePageChange}\r\n          onPageSizeChange={handlePageSizeChange}\r\n          showFirstLast={true}\r\n          showPageSizeSelector={true}\r\n          showInfo={true}\r\n          maxVisiblePages={7}\r\n          pageSizeOptions={[10, 25, 50, 100]}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;;AAkDe,SAAS,UAA6C,EACnE,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,aAAa,EACb,oBAAoB,WAAW,EAC/B,YAAY,EAAE,EACd,iBAAiB,eAAe,EAChC,oBAAoB,eAAe,EACjB;IAClB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAChD,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ,EAAE;IACZ;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,IAAI;YACF,MAAM,WAAW;gBAAE,GAAG,KAAK;gBAAE;gBAAQ,MAAM;YAAE;YAC7C,SAAS;YACT,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF,GAAG;QAAC;QAAO;KAAc;IAEzB,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B,IAAI,gBAAgB,MAAM,MAAM,EAAE;gBAChC,aAAa;YACf;QACF,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAa,MAAM,MAAM;QAAE;KAAa;IAE5C,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc,MAAM,MAAM,EAAE,KAAK,CAAA,OAAQ,KAAK,UAAU,CAAC;QAC/D,IAAI,YAAsB,EAAE;QAE5B,IAAI,CAAC,aAAa;YAChB,YAAY;gBAAC,GAAG,UAAU,IAAI,CAAC;aAAC;QAClC,OAAO,IAAI,YAAY,QAAQ,CAAC,SAAS;YACvC,YAAY;gBAAC,GAAG,UAAU,KAAK,CAAC;aAAC;QACnC,OAAO;YACL,YAAY,EAAE;QAChB;QAEA,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE,QAAQ;YAAW,MAAM;QAAE;QACxD,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE;QAAK;QAClC,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,cAAc,MAAM,MAAM,EAAE,KAAK,CAAA,OAAQ,KAAK,UAAU,CAAC;QAC/D,IAAI,CAAC,aAAa,OAAO;QACzB,OAAO,YAAY,QAAQ,CAAC,UAAU,QAAQ;IAChD;IAEA,8BAA8B;IAC9B,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAW,CAAC,4DAA4D,EAAE,WAAW;sBACxF,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;IAKjC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE,OAAO;YAAa,MAAM;QAAE;QACzD,SAAS;QACT,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,4DAA4D,EAAE,WAAW;;0BAExF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BACC,MAAK;4BACL,aAAa;4BACb,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BAAM,WAAU;sCACf,cAAA,8OAAC;0CACE,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;wCAEC,WAAW,CAAC,kGAAkG,EAC5G,OAAO,QAAQ,GAAG,4DAA4D,GAC/E,CAAC,EAAE,OAAO,SAAS,IAAI,IAAI;wCAC5B,SAAS,IAAM,OAAO,QAAQ,IAAI,WAAW,OAAO,OAAO,GAAG;kDAE9D,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAM,OAAO,KAAK;;;;;;gDAClB,OAAO,QAAQ,kBACd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAW,CAAC,2BAA2B,EACxC,iBAAiB,OAAO,OAAO,GAAG,OAAO,QAAQ,iBAAiB,iBAClE;;;;;;sEACF,8OAAC;4DAAE,WAAW,CAAC,mCAAmC,EAChD,iBAAiB,OAAO,OAAO,GAAG,OAAO,SAAS,iBAAiB,iBACnE;;;;;;;;;;;;;;;;;;uCAfH,OAAO,OAAO,GAAG;;;;;;;;;;;;;;;sCAuB9B,8OAAC;4BAAM,WAAU;sCACd,wBACC,8OAAC;0CACC,cAAA,8OAAC;oCAAG,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CACrC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAO;;;;;;;;;;;;;;;;;;;;;uCAI3B,CAAC,MAAM,QAAQ,KAAK,IAAI,CAAC,MAAM,KAAK,kBACtC,8OAAC;0CACC,cAAA,8OAAC;oCAAG,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CACrC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAW,GAAG,eAAe,cAAc,CAAC;;;;;;0DAC/C,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;uCAKV,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC;oCAAe,WAAU;8CACvB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;4CAA4B,WAAU;sDACpC,OAAO,MAAM,GACV,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,CAAY,EAAE,QAC3C,OAAO,IAAI,CAAC,OAAO,GAAG,CAAY,IAAI;2CAHnC,OAAO,OAAO,GAAG;;;;;mCAFrB;;;;;;;;;;;;;;;;;;;;;YAiBlB,MAAM,QAAQ,KAAK,IAAI,CAAC,UAAU,KAAK,aAAa,KAAK,IAAI,CAAC,WAAW,KAAK,aAAa,KAAK,IAAI,CAAC,UAAU,KAAK,2BACnH,8OAAC,0IAAA,CAAA,UAAU;gBACT,MAAM;oBACJ,GAAG,KAAK,IAAI;oBACZ,YAAY,KAAK,IAAI,CAAC,UAAU;oBAChC,aAAa,KAAK,IAAI,CAAC,WAAW;oBAClC,YAAY,KAAK,IAAI,CAAC,UAAU;gBAClC;gBACA,cAAc;gBACd,kBAAkB;gBAClB,eAAe;gBACf,sBAAsB;gBACtB,UAAU;gBACV,iBAAiB;gBACjB,iBAAiB;oBAAC;oBAAI;oBAAI;oBAAI;iBAAI;;;;;;;;;;;;AAK5C", "debugId": null}}, {"offset": {"line": 676, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface SelectOption {\r\n  value: string;\r\n  label: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {\r\n  label?: string;\r\n  error?: string;\r\n  helperText?: string;\r\n  required?: boolean;\r\n  options: SelectOption[];\r\n  placeholder?: string;\r\n  className?: string;\r\n  containerClassName?: string;\r\n  onChange?: (value: string) => void;\r\n}\r\n\r\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  required = false,\r\n  options = [],\r\n  placeholder = 'Select an option...',\r\n  className = '',\r\n  containerClassName = '',\r\n  onChange,\r\n  id,\r\n  value,\r\n  ...props\r\n}, ref) => {\r\n  const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;\r\n  \r\n  const baseSelectClasses = `\r\n    w-full px-3 py-2 border rounded-md shadow-sm \r\n    focus:outline-none focus:ring-2 focus:ring-offset-2 \r\n    disabled:opacity-50 disabled:cursor-not-allowed\r\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\r\n    transition-colors duration-200\r\n    appearance-none bg-white\r\n    bg-no-repeat bg-right bg-[length:16px_16px]\r\n    pr-10\r\n  `;\r\n  \r\n  const selectClasses = error\r\n    ? `${baseSelectClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`\r\n    : `${baseSelectClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\r\n    if (onChange) {\r\n      onChange(e.target.value);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`space-y-1 ${containerClassName}`}>\r\n      {label && (\r\n        <label \r\n          htmlFor={selectId}\r\n          className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\r\n        >\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <div className=\"relative\">\r\n        <select\r\n          ref={ref}\r\n          id={selectId}\r\n          value={value || ''}\r\n          onChange={handleChange}\r\n          className={`${selectClasses} ${className}`}\r\n          {...props}\r\n        >\r\n          {placeholder && (\r\n            <option value=\"\" disabled>\r\n              {placeholder}\r\n            </option>\r\n          )}\r\n          \r\n          {options.map((option) => (\r\n            <option \r\n              key={option.value} \r\n              value={option.value}\r\n              disabled={option.disabled}\r\n            >\r\n              {option.label}\r\n            </option>\r\n          ))}\r\n        </select>\r\n        \r\n        {/* Custom dropdown arrow */}\r\n        <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\r\n          <i className=\"ri-arrow-down-s-line text-gray-400 dark:text-gray-500\"></i>\r\n        </div>\r\n      </div>\r\n      \r\n      {error && (\r\n        <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\r\n          <i className=\"ri-error-warning-line mr-1\"></i>\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nSelect.displayName = 'Select';\r\n\r\nexport default Select;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAsBA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAkC,CAAC,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,UAAU,EAAE,EACZ,cAAc,qBAAqB,EACnC,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,QAAQ,EACR,EAAE,EACF,KAAK,EACL,GAAG,OACJ,EAAE;IACD,MAAM,WAAW,MAAM,CAAC,OAAO,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAE1E,MAAM,oBAAoB,CAAC;;;;;;;;;EAS3B,CAAC;IAED,MAAM,gBAAgB,QAClB,GAAG,kBAAkB,2EAA2E,CAAC,GACjG,GAAG,kBAAkB,0GAA0G,CAAC;IAEpI,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU;YACZ,SAAS,EAAE,MAAM,CAAC,KAAK;QACzB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,oBAAoB;;YAC9C,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAK;wBACL,IAAI;wBACJ,OAAO,SAAS;wBAChB,UAAU;wBACV,WAAW,GAAG,cAAc,CAAC,EAAE,WAAW;wBACzC,GAAG,KAAK;;4BAER,6BACC,8OAAC;gCAAO,OAAM;gCAAG,QAAQ;0CACtB;;;;;;4BAIJ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;oCAEC,OAAO,OAAO,KAAK;oCACnB,UAAU,OAAO,QAAQ;8CAExB,OAAO,KAAK;mCAJR,OAAO,KAAK;;;;;;;;;;;kCAUvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;YAIhB,uBACC,8OAAC;gBAAE,WAAU;;kCACX,8OAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 817, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useTaskNavigation.ts"], "sourcesContent": ["import { useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport toast from 'react-hot-toast';\r\nimport { taskAssignmentService } from '@/services/task-assignment';\r\nimport { Task } from '@/types';\r\n\r\ninterface TaskNavigationInfo {\r\n  task: Task;\r\n  canNavigateToEntity: boolean;\r\n}\r\n\r\nexport const useTaskNavigation = () => {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const router = useRouter();\r\n\r\n  /**\r\n   * Get task navigation information\r\n   */\r\n  const getTaskNavigationInfo = async (taskId: string): Promise<Task | null> => {\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const task = await taskAssignmentService.getTaskById(taskId);\r\n      if (task) {\r\n        return task;\r\n      }\r\n      return null;\r\n    } catch (error: any) {\r\n      toast.error('Failed to load task information');\r\n      return null;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Get the appropriate view URL for a task without navigating\r\n   */\r\n  const getTaskViewUrl = async (taskId: string): Promise<string | null> => {\r\n    const task = await getTaskNavigationInfo(taskId);\r\n\r\n    if (!task) {\r\n      return null;\r\n    }\r\n\r\n    // const task  = taskInfo;\r\n\r\n    // // If task is related to an application, try to build evaluation URL\r\n    // if (task.entity_type === 'application' && task.entity_id) {\r\n \r\n    //   try {\r\n    //     const application = await applicationService.getApplication(task.entity_id);\r\n    //     const licenseTypeCode = application.license_category?.license_type?.code;\r\n    //     if (licenseTypeCode) {\r\n    //       return `/applications/${licenseTypeCode}/evaluate/applicant-info?application_id=${application.application_id}&license_category_id=${application.license_category_id}`;\r\n    //     }\r\n    //   } catch (error) {\r\n    //     console.error('Error fetching application details for URL:', error);\r\n    //   }\r\n    //   // Fallback to task details if application fetch fails\r\n    // }\r\n\r\n    return `/tasks/${taskId}`;\r\n\r\n  };\r\n\r\n  /**\r\n   * Open task view in a new tab\r\n   */\r\n  const openTaskViewInNewTab = async (taskId: string) => {\r\n    const url = await getTaskViewUrl(taskId);\r\n    \r\n    if (url) {\r\n      window.open(url, '_blank');\r\n    } else {\r\n      toast.error('Unable to determine task view URL');\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Open task view in a new tab\r\n   */\r\n  const openTaskView = async (taskId: string) => {\r\n    const url = await getTaskViewUrl(taskId);\r\n    if (url) {\r\n      router.push(url);\r\n    } else {\r\n      toast.error('Unable to determine task view URL');\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Get display information for task navigation\r\n   */\r\n  const getTaskDisplayInfo = (taskInfo: TaskNavigationInfo) => {\r\n    const { task } = taskInfo;\r\n\r\n    let title = task.title;\r\n    let subtitle = '';\r\n    let icon = '📋';\r\n\r\n    // For application tasks, the title usually contains the application number\r\n    if (task.entity_type === 'application') {\r\n      icon = '📄';\r\n      subtitle = 'Application Evaluation';\r\n    }\r\n\r\n    switch (task.task_type) {\r\n      case 'application':\r\n      case 'evaluation':\r\n        icon = '📄';\r\n        break;\r\n      case 'complaint':\r\n        icon = '⚠️';\r\n        break;\r\n      case 'data_breach':\r\n        icon = '🔒';\r\n        break;\r\n      case 'inspection':\r\n        icon = '🔍';\r\n        break;\r\n      case 'document_review':\r\n        icon = '📑';\r\n        break;\r\n      default:\r\n        icon = '📋';\r\n        break;\r\n    }\r\n\r\n    return {\r\n      title,\r\n      subtitle,\r\n      icon,\r\n      taskType: task.task_type,\r\n      status: task.status,\r\n    };\r\n  };\r\n\r\n  return {\r\n    // navigateToTaskView,\r\n    getTaskViewUrl,\r\n    openTaskViewInNewTab,\r\n    getTaskNavigationInfo,\r\n    getTaskDisplayInfo,\r\n    openTaskView,\r\n    isLoading,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAQO,MAAM,oBAAoB;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB;;GAEC,GACD,MAAM,wBAAwB,OAAO;QACnC,aAAa;QAEb,IAAI;YACF,MAAM,OAAO,MAAM,qIAAA,CAAA,wBAAqB,CAAC,WAAW,CAAC;YACrD,IAAI,MAAM;gBACR,OAAO;YACT;YACA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB,OAAO;QAC5B,MAAM,OAAO,MAAM,sBAAsB;QAEzC,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QAEA,0BAA0B;QAE1B,uEAAuE;QACvE,8DAA8D;QAE9D,UAAU;QACV,mFAAmF;QACnF,gFAAgF;QAChF,6BAA6B;QAC7B,+KAA+K;QAC/K,QAAQ;QACR,sBAAsB;QACtB,2EAA2E;QAC3E,MAAM;QACN,2DAA2D;QAC3D,IAAI;QAEJ,OAAO,CAAC,OAAO,EAAE,QAAQ;IAE3B;IAEA;;GAEC,GACD,MAAM,uBAAuB,OAAO;QAClC,MAAM,MAAM,MAAM,eAAe;QAEjC,IAAI,KAAK;YACP,OAAO,IAAI,CAAC,KAAK;QACnB,OAAO;YACL,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,OAAO;QAC1B,MAAM,MAAM,MAAM,eAAe;QACjC,IAAI,KAAK;YACP,OAAO,IAAI,CAAC;QACd,OAAO;YACL,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA;;GAEC,GACD,MAAM,qBAAqB,CAAC;QAC1B,MAAM,EAAE,IAAI,EAAE,GAAG;QAEjB,IAAI,QAAQ,KAAK,KAAK;QACtB,IAAI,WAAW;QACf,IAAI,OAAO;QAEX,2EAA2E;QAC3E,IAAI,KAAK,WAAW,KAAK,eAAe;YACtC,OAAO;YACP,WAAW;QACb;QAEA,OAAQ,KAAK,SAAS;YACpB,KAAK;YACL,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF;gBACE,OAAO;gBACP;QACJ;QAEA,OAAO;YACL;YACA;YACA;YACA,UAAU,KAAK,SAAS;YACxB,QAAQ,KAAK,MAAM;QACrB;IACF;IAEA,OAAO;QACL,sBAAsB;QACtB;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 946, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/tasks/TaskModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport Select from '../common/Select';\r\nimport { taskService } from '@/services/task-assignment';\r\nimport { Task, CreateTaskDto, TaskType, TaskPriority, TaskStatus, User, UpdateTaskDto } from '@/types';\r\n\r\ninterface TaskModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onSave: (task: Task) => void;\r\n  task?: Task | null;\r\n}\r\n\r\nconst TaskModal = ({ isOpen, onClose, onSave, task }: TaskModalProps) => {\r\n  const [formData, setFormData] = useState<CreateTaskDto>({\r\n    task_type: TaskType.APPLICATION,\r\n    title: '',\r\n    description: '',\r\n    priority: TaskPriority.MEDIUM,\r\n    status: TaskStatus.PENDING,\r\n    entity_type: '',\r\n    entity_id: '',\r\n    due_date: '',\r\n    assigned_to: '',\r\n  });\r\n  const [users, setUsers] = useState<User[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      loadUsers();\r\n      if (task) {\r\n        // Edit mode - populate form with task data\r\n        setFormData({\r\n          task_type: task.task_type,\r\n          title: task.title,\r\n          description: task.description,\r\n          priority: task.priority,\r\n          status: task.status,\r\n          entity_type: task.entity_type || '',\r\n          entity_id: task.entity_id || '',\r\n          due_date: task.due_date ? task.due_date.split('T')[0] : '', // Format for date input\r\n          assigned_to: task.assigned_to || '',\r\n        });\r\n      } else {\r\n        // Create mode - reset form\r\n        setFormData({\r\n          task_type: TaskType.APPLICATION,\r\n          title: '',\r\n          description: '',\r\n          priority: TaskPriority.MEDIUM,\r\n          status: TaskStatus.PENDING,\r\n          entity_type: '',\r\n          entity_id: '',\r\n          due_date: '',\r\n          assigned_to: '',\r\n        });\r\n      }\r\n      setErrors({});\r\n    }\r\n  }, [isOpen, task]);\r\n\r\n  const loadUsers = async () => {\r\n    try {\r\n      const response = await taskService.getOfficers();\r\n      setUsers(response.data);\r\n    } catch (error) {\r\n      console.error('Error loading users:', error);\r\n      setUsers([]);\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (field: keyof CreateTaskDto, value: string) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [field]: value\r\n    }));\r\n    // Clear error when user starts typing\r\n    if (errors[field]) {\r\n      setErrors(prev => ({\r\n        ...prev,\r\n        [field]: ''\r\n      }));\r\n    }\r\n  };\r\n\r\n  const validateForm = (): boolean => {\r\n    const newErrors: Record<string, string> = {};\r\n\r\n    if (!formData.title.trim()) {\r\n      newErrors.title = 'Title is required';\r\n    }\r\n\r\n    if (formData.description && !formData.description.trim()) {\r\n      newErrors.description = 'Description is required';\r\n    }\r\n\r\n    if (!formData.task_type) {\r\n      newErrors.task_type = 'Task type is required';\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (!validateForm()) {\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      let savedTask: Task;\r\n      \r\n      if (task) {\r\n        // Update existing task\r\n        const updateData: UpdateTaskDto = {\r\n          title: formData.title,\r\n          description: formData.description,\r\n          priority: formData.priority,\r\n          status: formData.status,\r\n          entity_type: formData.entity_type || undefined,\r\n          entity_id: formData.entity_id || undefined,\r\n          due_date: formData.due_date || undefined,\r\n        };\r\n        savedTask = await taskService.updateTask(task.task_id, updateData);\r\n      } else {\r\n        // Create new task\r\n        const createData: CreateTaskDto = {\r\n          ...formData,\r\n          entity_type: formData.entity_type || undefined,\r\n          entity_id: formData.entity_id || undefined,\r\n          due_date: formData.due_date || undefined,\r\n          assigned_to: formData.assigned_to || undefined,\r\n        };\r\n        savedTask = await taskService.createTask(createData);\r\n      }\r\n\r\n      onSave(savedTask);\r\n      onClose();\r\n    } catch (error: any) {\r\n      console.error('Error saving task:', error);\r\n      if (error.response?.data?.message) {\r\n        setErrors({ submit: error.response.data.message });\r\n      } else {\r\n        setErrors({ submit: 'Failed to save task. Please try again.' });\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleClose = () => {\r\n    if (!loading) {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl mx-4 transform transition-all max-h-[90vh] overflow-hidden\">\r\n        {/* Header */}\r\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n            {task ? 'Edit Task' : 'Create New Task'}\r\n          </h3>\r\n          <button\r\n            type=\"button\"\r\n            onClick={handleClose}\r\n            disabled={loading}\r\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 rounded-md p-1\"\r\n            aria-label=\"Close modal\"\r\n          >\r\n            <i className=\"ri-close-line text-xl\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        {/* Content */}\r\n        <div className=\"overflow-y-auto max-h-[calc(90vh-120px)]\">\r\n          <div className=\"p-6\">\r\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n        {errors.submit && (\r\n          <div className=\"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded\">\r\n            {errors.submit}\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          {/* Title */}\r\n          <div className=\"md:col-span-2\">\r\n            <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Title *\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"title\"\r\n              value={formData.title}\r\n              onChange={(e) => handleInputChange('title', e.target.value)}\r\n              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\r\n                errors.title ? 'border-red-300 dark:border-red-600' : 'border-gray-300 dark:border-gray-600'\r\n              }`}\r\n              placeholder=\"Enter task title\"\r\n            />\r\n            {errors.title && (\r\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.title}</p>\r\n            )}\r\n          </div>\r\n\r\n          {/* Task Type */}\r\n          <div>\r\n            <Select\r\n              label=\"Task Type *\"\r\n              value={formData.task_type}\r\n              onChange={(value) => handleInputChange('task_type', value)}\r\n              options={[\r\n                { value: TaskType.APPLICATION, label: 'Application' },\r\n                { value: TaskType.COMPLAINT, label: 'Complaint' },\r\n                { value: TaskType.DATA_BREACH, label: 'Data Breach' },\r\n                { value: TaskType.EVALUATION, label: 'Evaluation' },\r\n                { value: TaskType.INSPECTION, label: 'Inspection' },\r\n                { value: TaskType.DOCUMENT_REVIEW, label: 'Document Review' },\r\n                { value: TaskType.COMPLIANCE_CHECK, label: 'Compliance Check' },\r\n                { value: TaskType.FOLLOW_UP, label: 'Follow Up' },\r\n              ]}\r\n              error={errors.task_type}\r\n            />\r\n          </div>\r\n\r\n          {/* Priority */}\r\n          <div>\r\n            <Select\r\n              label=\"Priority\"\r\n              value={formData.priority}\r\n              onChange={(value) => handleInputChange('priority', value)}\r\n              options={[\r\n                { value: TaskPriority.LOW, label: 'Low' },\r\n                { value: TaskPriority.MEDIUM, label: 'Medium' },\r\n                { value: TaskPriority.HIGH, label: 'High' },\r\n                { value: TaskPriority.URGENT, label: 'Urgent' },\r\n              ]}\r\n            />\r\n          </div>\r\n\r\n          {/* Status */}\r\n          <div>\r\n            <Select\r\n              label=\"Status\"\r\n              value={formData.status}\r\n              onChange={(value) => handleInputChange('status', value)}\r\n              options={[\r\n                { value: TaskStatus.PENDING, label: 'Pending' },\r\n                { value: TaskStatus.IN_PROGRESS, label: 'In Progress' },\r\n                { value: TaskStatus.COMPLETED, label: 'Completed' },\r\n                { value: TaskStatus.CANCELLED, label: 'Cancelled' },\r\n                { value: TaskStatus.ON_HOLD, label: 'On Hold' },\r\n              ]}\r\n            />\r\n          </div>\r\n\r\n          {/* Assigned To */}\r\n          {users.length > 0 && (\r\n            <div>\r\n              <Select\r\n                label=\"Assigned To\"\r\n                value={formData.assigned_to}\r\n                onChange={(value) => handleInputChange('assigned_to', value)}\r\n                options={[\r\n                  { value: '', label: 'Unassigned' },\r\n                  ...users.map((user) => ({\r\n                    value: user.user_id,\r\n                    label: `${user.first_name} ${user.last_name}`\r\n                  }))\r\n                ]}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          {/* Due Date */}\r\n          <div>\r\n            <label htmlFor=\"due_date\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Due Date\r\n            </label>\r\n            <input\r\n              type=\"date\"\r\n              id=\"due_date\"\r\n              value={formData.due_date}\r\n              onChange={(e) => handleInputChange('due_date', e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white\"\r\n            />\r\n          </div>\r\n\r\n\r\n        </div>\r\n\r\n        {/* Description */}\r\n        <div>\r\n          <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Description *\r\n          </label>\r\n          <textarea\r\n            id=\"description\"\r\n            rows={4}\r\n            value={formData.description}\r\n            onChange={(e) => handleInputChange('description', e.target.value)}\r\n            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\r\n              errors.description ? 'border-red-300 dark:border-red-600' : 'border-gray-300 dark:border-gray-600'\r\n            }`}\r\n            placeholder=\"Enter task description\"\r\n          />\r\n          {errors.description && (\r\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.description}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Form Actions */}\r\n        <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={handleClose}\r\n            disabled={loading}\r\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n          >\r\n            Cancel\r\n          </button>\r\n          <button\r\n            type=\"submit\"\r\n            disabled={loading}\r\n            className=\"px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed dark:focus:ring-offset-gray-900\"\r\n          >\r\n            {loading ? (\r\n              <div className=\"flex items-center\">\r\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                {task ? 'Updating...' : 'Creating...'}\r\n              </div>\r\n            ) : (\r\n              task ? 'Update Task' : 'Create Task'\r\n            )}\r\n          </button>\r\n            </div>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TaskModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAcA,MAAM,YAAY,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAkB;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACtD,WAAW,oHAAA,CAAA,WAAQ,CAAC,WAAW;QAC/B,OAAO;QACP,aAAa;QACb,UAAU,oHAAA,CAAA,eAAY,CAAC,MAAM;QAC7B,QAAQ,oHAAA,CAAA,aAAU,CAAC,OAAO;QAC1B,aAAa;QACb,WAAW;QACX,UAAU;QACV,aAAa;IACf;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;YACA,IAAI,MAAM;gBACR,2CAA2C;gBAC3C,YAAY;oBACV,WAAW,KAAK,SAAS;oBACzB,OAAO,KAAK,KAAK;oBACjB,aAAa,KAAK,WAAW;oBAC7B,UAAU,KAAK,QAAQ;oBACvB,QAAQ,KAAK,MAAM;oBACnB,aAAa,KAAK,WAAW,IAAI;oBACjC,WAAW,KAAK,SAAS,IAAI;oBAC7B,UAAU,KAAK,QAAQ,GAAG,KAAK,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;oBACxD,aAAa,KAAK,WAAW,IAAI;gBACnC;YACF,OAAO;gBACL,2BAA2B;gBAC3B,YAAY;oBACV,WAAW,oHAAA,CAAA,WAAQ,CAAC,WAAW;oBAC/B,OAAO;oBACP,aAAa;oBACb,UAAU,oHAAA,CAAA,eAAY,CAAC,MAAM;oBAC7B,QAAQ,oHAAA,CAAA,aAAU,CAAC,OAAO;oBAC1B,aAAa;oBACb,WAAW;oBACX,UAAU;oBACV,aAAa;gBACf;YACF;YACA,UAAU,CAAC;QACb;IACF,GAAG;QAAC;QAAQ;KAAK;IAEjB,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,WAAW;YAC9C,SAAS,SAAS,IAAI;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS,EAAE;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC,OAA4B;QACrD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;QACD,sCAAsC;QACtC,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE;gBACX,CAAC;QACH;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,SAAS,WAAW,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YACxD,UAAU,WAAW,GAAG;QAC1B;QAEA,IAAI,CAAC,SAAS,SAAS,EAAE;YACvB,UAAU,SAAS,GAAG;QACxB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,WAAW;QACX,IAAI;YACF,IAAI;YAEJ,IAAI,MAAM;gBACR,uBAAuB;gBACvB,MAAM,aAA4B;oBAChC,OAAO,SAAS,KAAK;oBACrB,aAAa,SAAS,WAAW;oBACjC,UAAU,SAAS,QAAQ;oBAC3B,QAAQ,SAAS,MAAM;oBACvB,aAAa,SAAS,WAAW,IAAI;oBACrC,WAAW,SAAS,SAAS,IAAI;oBACjC,UAAU,SAAS,QAAQ,IAAI;gBACjC;gBACA,YAAY,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC,KAAK,OAAO,EAAE;YACzD,OAAO;gBACL,kBAAkB;gBAClB,MAAM,aAA4B;oBAChC,GAAG,QAAQ;oBACX,aAAa,SAAS,WAAW,IAAI;oBACrC,WAAW,SAAS,SAAS,IAAI;oBACjC,UAAU,SAAS,QAAQ,IAAI;oBAC/B,aAAa,SAAS,WAAW,IAAI;gBACvC;gBACA,YAAY,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;YAC3C;YAEA,OAAO;YACP;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sBAAsB;YACpC,IAAI,MAAM,QAAQ,EAAE,MAAM,SAAS;gBACjC,UAAU;oBAAE,QAAQ,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;gBAAC;YAClD,OAAO;gBACL,UAAU;oBAAE,QAAQ;gBAAyC;YAC/D;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ;QACF;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,OAAO,cAAc;;;;;;sCAExB,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;8BAKjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;gCAC3C,OAAO,MAAM,kBACZ,8OAAC;oCAAI,WAAU;8CACZ,OAAO,MAAM;;;;;;8CAIlB,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAAkE;;;;;;8DAGnG,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1D,WAAW,CAAC,2KAA2K,EACrL,OAAO,KAAK,GAAG,uCAAuC,wCACtD;oDACF,aAAY;;;;;;gDAEb,OAAO,KAAK,kBACX,8OAAC;oDAAE,WAAU;8DAA+C,OAAO,KAAK;;;;;;;;;;;;sDAK5E,8OAAC;sDACC,cAAA,8OAAC,sIAAA,CAAA,UAAM;gDACL,OAAM;gDACN,OAAO,SAAS,SAAS;gDACzB,UAAU,CAAC,QAAU,kBAAkB,aAAa;gDACpD,SAAS;oDACP;wDAAE,OAAO,oHAAA,CAAA,WAAQ,CAAC,WAAW;wDAAE,OAAO;oDAAc;oDACpD;wDAAE,OAAO,oHAAA,CAAA,WAAQ,CAAC,SAAS;wDAAE,OAAO;oDAAY;oDAChD;wDAAE,OAAO,oHAAA,CAAA,WAAQ,CAAC,WAAW;wDAAE,OAAO;oDAAc;oDACpD;wDAAE,OAAO,oHAAA,CAAA,WAAQ,CAAC,UAAU;wDAAE,OAAO;oDAAa;oDAClD;wDAAE,OAAO,oHAAA,CAAA,WAAQ,CAAC,UAAU;wDAAE,OAAO;oDAAa;oDAClD;wDAAE,OAAO,oHAAA,CAAA,WAAQ,CAAC,eAAe;wDAAE,OAAO;oDAAkB;oDAC5D;wDAAE,OAAO,oHAAA,CAAA,WAAQ,CAAC,gBAAgB;wDAAE,OAAO;oDAAmB;oDAC9D;wDAAE,OAAO,oHAAA,CAAA,WAAQ,CAAC,SAAS;wDAAE,OAAO;oDAAY;iDACjD;gDACD,OAAO,OAAO,SAAS;;;;;;;;;;;sDAK3B,8OAAC;sDACC,cAAA,8OAAC,sIAAA,CAAA,UAAM;gDACL,OAAM;gDACN,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,QAAU,kBAAkB,YAAY;gDACnD,SAAS;oDACP;wDAAE,OAAO,oHAAA,CAAA,eAAY,CAAC,GAAG;wDAAE,OAAO;oDAAM;oDACxC;wDAAE,OAAO,oHAAA,CAAA,eAAY,CAAC,MAAM;wDAAE,OAAO;oDAAS;oDAC9C;wDAAE,OAAO,oHAAA,CAAA,eAAY,CAAC,IAAI;wDAAE,OAAO;oDAAO;oDAC1C;wDAAE,OAAO,oHAAA,CAAA,eAAY,CAAC,MAAM;wDAAE,OAAO;oDAAS;iDAC/C;;;;;;;;;;;sDAKL,8OAAC;sDACC,cAAA,8OAAC,sIAAA,CAAA,UAAM;gDACL,OAAM;gDACN,OAAO,SAAS,MAAM;gDACtB,UAAU,CAAC,QAAU,kBAAkB,UAAU;gDACjD,SAAS;oDACP;wDAAE,OAAO,oHAAA,CAAA,aAAU,CAAC,OAAO;wDAAE,OAAO;oDAAU;oDAC9C;wDAAE,OAAO,oHAAA,CAAA,aAAU,CAAC,WAAW;wDAAE,OAAO;oDAAc;oDACtD;wDAAE,OAAO,oHAAA,CAAA,aAAU,CAAC,SAAS;wDAAE,OAAO;oDAAY;oDAClD;wDAAE,OAAO,oHAAA,CAAA,aAAU,CAAC,SAAS;wDAAE,OAAO;oDAAY;oDAClD;wDAAE,OAAO,oHAAA,CAAA,aAAU,CAAC,OAAO;wDAAE,OAAO;oDAAU;iDAC/C;;;;;;;;;;;wCAKJ,MAAM,MAAM,GAAG,mBACd,8OAAC;sDACC,cAAA,8OAAC,sIAAA,CAAA,UAAM;gDACL,OAAM;gDACN,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,QAAU,kBAAkB,eAAe;gDACtD,SAAS;oDACP;wDAAE,OAAO;wDAAI,OAAO;oDAAa;uDAC9B,MAAM,GAAG,CAAC,CAAC,OAAS,CAAC;4DACtB,OAAO,KAAK,OAAO;4DACnB,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE;wDAC/C,CAAC;iDACF;;;;;;;;;;;sDAMP,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAAkE;;;;;;8DAGtG,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,OAAO,SAAS,QAAQ;oDACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC7D,WAAU;;;;;;;;;;;;;;;;;;8CAQhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAc,WAAU;sDAAkE;;;;;;sDAGzG,8OAAC;4CACC,IAAG;4CACH,MAAM;4CACN,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4CAChE,WAAW,CAAC,2KAA2K,EACrL,OAAO,WAAW,GAAG,uCAAuC,wCAC5D;4CACF,aAAY;;;;;;wCAEb,OAAO,WAAW,kBACjB,8OAAC;4CAAE,WAAU;sDAA+C,OAAO,WAAW;;;;;;;;;;;;8CAKlF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU;4CACV,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,wBACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;oDACd,OAAO,gBAAgB;;;;;;2FAG1B,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrC;uCAEe", "debugId": null}}, {"offset": {"line": 1494, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/ConfirmationModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ReactNode } from 'react';\r\n\r\ninterface ConfirmationModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onConfirm: () => void;\r\n  title: string;\r\n  message: string | ReactNode;\r\n  confirmText?: string;\r\n  cancelText?: string;\r\n  confirmVariant?: 'danger' | 'primary' | 'warning';\r\n  loading?: boolean;\r\n  icon?: ReactNode;\r\n}\r\n\r\nexport default function ConfirmationModal({\r\n  isOpen,\r\n  onClose,\r\n  onConfirm,\r\n  title,\r\n  message,\r\n  confirmText = 'Confirm',\r\n  cancelText = 'Cancel',\r\n  confirmVariant = 'danger',\r\n  loading = false,\r\n  icon,\r\n}: ConfirmationModalProps) {\r\n  if (!isOpen) return null;\r\n\r\n  const getConfirmButtonStyles = () => {\r\n    switch (confirmVariant) {\r\n      case 'danger':\r\n        return 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500';\r\n      case 'primary':\r\n        return 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500';\r\n      case 'warning':\r\n        return 'bg-yellow-600 hover:bg-yellow-700 text-white focus:ring-yellow-500';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500';\r\n    }\r\n  };\r\n\r\n  const getDefaultIcon = () => {\r\n    switch (confirmVariant) {\r\n      case 'danger':\r\n        return (\r\n          <div className=\"flex-shrink-0\">\r\n            <div className=\"h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center\">\r\n              <i className=\"ri-delete-bin-line text-red-600 dark:text-red-400 text-xl\"></i>\r\n            </div>\r\n          </div>\r\n        );\r\n      case 'warning':\r\n        return (\r\n          <div className=\"flex-shrink-0\">\r\n            <div className=\"h-10 w-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center\">\r\n              <i className=\"ri-alert-line text-yellow-600 dark:text-yellow-400 text-xl\"></i>\r\n            </div>\r\n          </div>\r\n        );\r\n      default:\r\n        return (\r\n          <div className=\"flex-shrink-0\">\r\n            <div className=\"h-10 w-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center\">\r\n              <i className=\"ri-information-line text-blue-600 dark:text-blue-400 text-xl\"></i>\r\n            </div>\r\n          </div>\r\n        );\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all\">\r\n        <div className=\"p-6\">\r\n          {/* Header */}\r\n          <div className=\"flex items-start mb-4\">\r\n            {icon || getDefaultIcon()}\r\n            <div className=\"ml-4 flex-1\">\r\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\">\r\n                {title}\r\n              </h3>\r\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                {typeof message === 'string' ? (\r\n                  <p>{message}</p>\r\n                ) : (\r\n                  message\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Actions */}\r\n          <div className=\"flex space-x-3 mt-6\">\r\n            <button\r\n              onClick={onConfirm}\r\n              disabled={loading}\r\n              className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed ${getConfirmButtonStyles()}`}\r\n            >\r\n              {loading ? (\r\n                <div className=\"flex items-center justify-center\">\r\n                  <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                  </svg>\r\n                  Processing...\r\n                </div>\r\n              ) : (\r\n                confirmText\r\n              )}\r\n            </button>\r\n            <button\r\n              onClick={onClose}\r\n              disabled={loading}\r\n              className=\"flex-1 px-4 py-2 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              {cancelText}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAiBe,SAAS,kBAAkB,EACxC,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,OAAO,EACP,cAAc,SAAS,EACvB,aAAa,QAAQ,EACrB,iBAAiB,QAAQ,EACzB,UAAU,KAAK,EACf,IAAI,EACmB;IACvB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;YAIrB,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;YAIrB;gBACE,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;QAIvB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;4BACZ,QAAQ;0CACT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX;;;;;;kDAEH,8OAAC;wCAAI,WAAU;kDACZ,OAAO,YAAY,yBAClB,8OAAC;sDAAG;;;;;mDAEJ;;;;;;;;;;;;;;;;;;kCAOR,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAW,CAAC,mNAAmN,EAAE,0BAA0B;0CAE1P,wBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAA6C,OAAM;4CAA6B,MAAK;4CAAO,SAAQ;;8DACjH,8OAAC;oDAAO,WAAU;oDAAa,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAe,aAAY;;;;;;8DACxF,8OAAC;oDAAK,WAAU;oDAAa,MAAK;oDAAe,GAAE;;;;;;;;;;;;wCAC/C;;;;;;2CAIR;;;;;;0CAGJ,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 1724, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/AssignModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { taskService } from '@/services/task-assignment';\r\nimport { Task, TaskType, TaskPriority, TaskStatus, CreateTaskDto } from '@/types';\r\n\r\ninterface Officer {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n  department?: {\r\n    department_id: string;\r\n    name: string;\r\n    code: string;\r\n  } | string; // Can be either a department object or string for backward compatibility\r\n}\r\n\r\ninterface AssignModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  itemId: string | null;\r\n  itemType: string;\r\n  itemTitle?: string;\r\n  onAssignSuccess?: () => void;\r\n  // Reassignment mode props\r\n  task?: Task | null; // For reassignment mode\r\n  onReassignSuccess?: () => void;\r\n}\r\n\r\nconst AssignModal: React.FC<AssignModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  itemId,\r\n  itemType,\r\n  itemTitle,\r\n  onAssignSuccess,\r\n  task,\r\n  onReassignSuccess\r\n}) => {\r\n  const { showSuccess, showError } = useToast();\r\n  const [officers, setOfficers] = useState<Officer[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [assigning, setAssigning] = useState(false);\r\n  const [selectedOfficer, setSelectedOfficer] = useState<string>('');\r\n  const [comment, setComment] = useState('');\r\n  const [dueDate, setDueDate] = useState<string>('');\r\n  const [loadTask, setTask] = useState<Task | null>(null);\r\n  const [isReassignMode, setIsReassignMode] = useState<boolean>(false);\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      fetchOfficers();\r\n      fetchExistingTasks();\r\n      // For reassignment, pre-select the current assignee\r\n      setSelectedOfficer(isReassignMode ? (loadTask?.assigned_to || '') : '');\r\n      setComment('');\r\n      // For reassignment, pre-fill the current due date if available\r\n      setDueDate(isReassignMode && loadTask?.due_date ? loadTask.due_date.split('T')[0] : '');\r\n    }\r\n  }, [isOpen, isReassignMode, task]);\r\n\r\n\r\n  const fetchExistingTasks = async () => {\r\n    if(task) {\r\n      setTask(task);\r\n      setIsReassignMode(true);\r\n      return\r\n    }\r\n\r\n    if (itemId && itemType === 'application') {\r\n      const loadTask = await taskService.getTaskForApplication(itemId)\r\n      if(loadTask && loadTask.task_id) {\r\n        setTask(loadTask)\r\n        setIsReassignMode(true);\r\n      } else {\r\n        setIsReassignMode(false);\r\n        setTask(null);\r\n      }\r\n    }\r\n\r\n  }\r\n\r\n  const fetchOfficers = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await taskService.getOfficers();\r\n      const officersData = response.data || [];\r\n      setOfficers(officersData);\r\n\r\n      if (officersData.length === 0) {\r\n        console.warn('No officers found for task assignment');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching officers:', error);\r\n      setOfficers([]);\r\n      showError('Failed to load officers. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const getTaskTypeFromItemType = (itemType: string): TaskType => {\r\n    switch (itemType) {\r\n      case 'application':\r\n        return TaskType.EVALUATION;\r\n      case 'data_breach':\r\n        return TaskType.DATA_BREACH;\r\n      case 'complaint':\r\n        return TaskType.COMPLAINT;\r\n      case 'inspection':\r\n        return TaskType.INSPECTION;\r\n      case 'document_review':\r\n        return TaskType.DOCUMENT_REVIEW;\r\n      case 'task':\r\n        return TaskType.APPLICATION; // Default for existing tasks\r\n      default:\r\n        return TaskType.APPLICATION;\r\n    }\r\n  };\r\n\r\n  const getTaskTitle = (itemType: string, itemTitle?: string): string => {\r\n    const baseTitle = itemTitle || 'Untitled';\r\n    switch (itemType) {\r\n      case 'application':\r\n        return `Application Evaluation: ${baseTitle}`;\r\n      case 'data_breach':\r\n        return `Data Breach Investigation: ${baseTitle}`;\r\n      case 'complaint':\r\n        return `Complaint Review: ${baseTitle}`;\r\n      case 'inspection':\r\n        return `Inspection Task: ${baseTitle}`;\r\n      case 'document_review':\r\n        return `Document Review: ${baseTitle}`;\r\n      default:\r\n        return `Task: ${baseTitle}`;\r\n    }\r\n  };\r\n\r\n  const getTaskDescription = (itemType: string, itemTitle?: string): string => {\r\n    const baseTitle = itemTitle || 'item';\r\n    switch (itemType) {\r\n      case 'application':\r\n        return `Evaluate and review application ${baseTitle} for compliance and approval.`;\r\n      case 'data_breach':\r\n        return `Investigate and assess data breach report ${baseTitle} for regulatory compliance.`;\r\n      case 'complaint':\r\n        return `Review and resolve complaint ${baseTitle} according to regulatory procedures.`;\r\n      case 'inspection':\r\n        return `Conduct inspection for ${baseTitle} to ensure regulatory compliance.`;\r\n      case 'document_review':\r\n        return `Review and validate document ${baseTitle} for accuracy and compliance.`;\r\n      default:\r\n        return `Process and review ${baseTitle}.`;\r\n    }\r\n  };\r\n\r\n  const handleAssign = async () => {\r\n    if (!selectedOfficer) {\r\n      showError('Please select an officer');\r\n      return;\r\n    }\r\n\r\n    if (!dueDate) {\r\n      showError('Please select a due date');\r\n      return;\r\n    }\r\n\r\n    if (!isReassignMode && !itemId) {\r\n      showError('Item ID is missing');\r\n      return;\r\n    }\r\n\r\n    setAssigning(true);\r\n\r\n  \r\n    try {\r\n      if (isReassignMode && loadTask) {\r\n        // Reassign existing task\r\n        await taskService.reassignTask(loadTask.task_id, {\r\n          assignedTo: selectedOfficer,\r\n          comment: comment.trim() || undefined,\r\n          due_date: dueDate,\r\n          priority: TaskPriority.MEDIUM\r\n        });\r\n        showSuccess('Task reassigned successfully');\r\n        onReassignSuccess?.();\r\n\r\n      } else {\r\n        const taskData: CreateTaskDto  = {\r\n          task_type: getTaskTypeFromItemType(itemType),\r\n          title: getTaskTitle(itemType, itemTitle),\r\n          description: getTaskDescription(itemType, itemTitle),\r\n          priority: TaskPriority.MEDIUM,\r\n          status: TaskStatus.PENDING,\r\n          entity_type: itemType,\r\n          entity_id: itemId!,\r\n          assigned_to: selectedOfficer,\r\n          due_date: dueDate,\r\n          metadata: {\r\n            comment: comment.trim() || undefined,\r\n            original_item_title: itemTitle,\r\n            assignment_context: 'manual_assignment'\r\n          }\r\n        };\r\n        // For other entity types, create a new task\r\n        await createNewTask(taskData);\r\n        onReassignSuccess?.();\r\n      }\r\n\r\n      onClose();\r\n    } catch (error) {\r\n      console.error(`Error ${isReassignMode ? 'reassigning' : 'creating assignment'} task:`, error);\r\n      showError(`Failed to ${isReassignMode ? 'reassign' : 'assign'} task`);\r\n    } finally {\r\n      setAssigning(false);\r\n    }\r\n  };\r\n\r\n    // Helper function to create a new task\r\n  async function createNewTask(taskData: CreateTaskDto) {\r\n    const createdTask = await taskService.createTask(taskData);\r\n    showSuccess(`Successfully assigned ${itemType.replace('_', ' ')} to officer`);\r\n    onAssignSuccess?.();\r\n  }\r\n  \r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col shadow-2xl\">\r\n        {/* Header - Fixed */}\r\n        <div className=\"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <div className=\"flex-shrink-0\">\r\n              <div className=\"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center\">\r\n                <i className={isReassignMode ? \"ri-user-shared-line text-white text-lg\" : \"ri-task-line text-white text-lg\"}></i>\r\n              </div>\r\n            </div>\r\n            <div>\r\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n                {isReassignMode ? 'Reassign Task' : `Create Task for ${itemType.replace('_', ' ').toUpperCase()}`}\r\n              </h3>\r\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                {isReassignMode ? 'Transfer task to another officer' : 'Assign this item to an officer for processing'}\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <button\r\n            type=\"button\"\r\n            onClick={onClose}\r\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n          >\r\n            <i className=\"ri-close-line text-xl\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        {/* Content - Scrollable */}\r\n        <div className=\"flex-1 overflow-y-auto p-6 bg-gray-50 dark:bg-gray-900\">\r\n          {/* Task Details */}\r\n          <div className=\"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800\">\r\n            <h4 className=\"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center\">\r\n              <i className=\"ri-task-line mr-2\"></i>\r\n              {isReassignMode ? 'Task Details:' : 'Task to be Created:'}\r\n            </h4>\r\n            <p className=\"text-sm text-blue-700 dark:text-blue-300 font-medium\">\r\n              {isReassignMode ? loadTask?.title : getTaskTitle(itemType, itemTitle)}\r\n            </p>\r\n            <p className=\"text-xs text-blue-600 dark:text-blue-400 mt-1 break-words whitespace-pre-wrap overflow-wrap-anywhere hyphens-auto\">\r\n              {isReassignMode ? loadTask?.description : getTaskDescription(itemType, itemTitle)}\r\n            </p>\r\n            {isReassignMode && loadTask?.task_number && (\r\n              <p className=\"text-xs text-blue-600 dark:text-blue-400 mt-1\">\r\n                Task #{loadTask?.task_number}\r\n              </p>\r\n            )}\r\n            {isReassignMode && loadTask?.assignee && (\r\n              <p className=\"text-xs text-blue-600 dark:text-blue-400 mt-1\">\r\n                Currently assigned to: {loadTask.assignee.first_name} {loadTask.assignee.last_name}\r\n              </p>\r\n            )}\r\n          </div>\r\n\r\n          {/* Officer Selection */}\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              {isReassignMode ? 'Reassign to Officer *' : 'Assign to Officer *'}\r\n            </label>\r\n            {loading ? (\r\n              <div className=\"text-center py-4 text-gray-500 dark:text-gray-400\">\r\n                Loading officers...\r\n              </div>\r\n            ) : (\r\n              <select\r\n                value={selectedOfficer}\r\n                onChange={(e) => setSelectedOfficer(e.target.value)}\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n              >\r\n                <option value=\"\">Select an officer...</option>\r\n                {officers.map((officer) => (\r\n                  <option key={officer.user_id} value={officer.user_id}>\r\n                    {officer.first_name} {officer.last_name} - {officer.email}\r\n                    {officer.department ? ` (${typeof officer.department === 'string' ? officer.department : officer.department.name})` : ''}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            )}\r\n          </div>\r\n\r\n          {/* Due Date */}\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Due Date *\r\n            </label>\r\n            <input\r\n              type=\"date\"\r\n              value={dueDate}\r\n              onChange={(e) => setDueDate(e.target.value)}\r\n              min={new Date().toISOString().split('T')[0]} // Prevent past dates\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n            />\r\n          </div>\r\n\r\n          {/* Comment */}\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              {isReassignMode ? 'Reassignment Notes (Optional)' : 'Task Notes (Optional)'}\r\n            </label>\r\n            <textarea\r\n              value={comment}\r\n              onChange={(e) => setComment(e.target.value)}\r\n              placeholder={isReassignMode ? 'Add any notes about this reassignment...' : 'Add any specific instructions or context for this loadTask...'}\r\n              rows={3}\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n            />\r\n          </div>\r\n\r\n          {/* Selected Officer Summary */}\r\n          {selectedOfficer && (\r\n            <div className=\"mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\">\r\n              <h4 className=\"text-sm font-medium text-green-900 dark:text-green-100 mb-2\">\r\n                Selected Officer:\r\n              </h4>\r\n              <div className=\"text-sm text-green-700 dark:text-green-200\">\r\n                {(() => {\r\n                  const officer = officers.find(o => o.user_id === selectedOfficer);\r\n                  return officer ? (\r\n                    <>\r\n                      {officer.first_name} {officer.last_name}\r\n                      <br />\r\n                      {officer.email}\r\n                      {officer.department && (\r\n                        <>\r\n                          <br />\r\n                          Department: {typeof officer.department === 'string' ? officer.department : officer.department.name}\r\n                        </>\r\n                      )}\r\n                    </>\r\n                  ) : 'Officer not found';\r\n                })()}\r\n              </div>\r\n              {dueDate && (\r\n                <div className=\"text-sm text-green-700 dark:text-green-200 mt-2\">\r\n                  <strong>Due Date:</strong> {new Date(dueDate).toLocaleDateString()}\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Footer - Fixed */}\r\n        <div className=\"flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={onClose}\r\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n          >\r\n            Cancel\r\n          </button>\r\n          <button\r\n            type=\"button\"\r\n            onClick={handleAssign}\r\n            disabled={!selectedOfficer || !dueDate || assigning}\r\n            className=\"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"\r\n          >\r\n            {assigning ? (\r\n              <>\r\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                {isReassignMode ? 'Reassigning...' : 'Creating Task...'}\r\n              </>\r\n            ) : (\r\n              <>\r\n                <i className={isReassignMode ? \"ri-user-shared-line mr-2\" : \"ri-task-line mr-2\"}></i>\r\n                {isReassignMode ? 'Reassign Task' : 'Create Task'}\r\n              </>\r\n            )}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AssignModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AA+BA,MAAM,cAA0C,CAAC,EAC/C,MAAM,EACN,OAAO,EACP,MAAM,EACN,QAAQ,EACR,SAAS,EACT,eAAe,EACf,IAAI,EACJ,iBAAiB,EAClB;IACC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,CAAC,UAAU,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;YACA;YACA,oDAAoD;YACpD,mBAAmB,iBAAkB,UAAU,eAAe,KAAM;YACpE,WAAW;YACX,+DAA+D;YAC/D,WAAW,kBAAkB,UAAU,WAAW,SAAS,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;QACtF;IACF,GAAG;QAAC;QAAQ;QAAgB;KAAK;IAGjC,MAAM,qBAAqB;QACzB,IAAG,MAAM;YACP,QAAQ;YACR,kBAAkB;YAClB;QACF;QAEA,IAAI,UAAU,aAAa,eAAe;YACxC,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,qBAAqB,CAAC;YACzD,IAAG,YAAY,SAAS,OAAO,EAAE;gBAC/B,QAAQ;gBACR,kBAAkB;YACpB,OAAO;gBACL,kBAAkB;gBAClB,QAAQ;YACV;QACF;IAEF;IAEA,MAAM,gBAAgB;QACpB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,WAAW;YAC9C,MAAM,eAAe,SAAS,IAAI,IAAI,EAAE;YACxC,YAAY;YAEZ,IAAI,aAAa,MAAM,KAAK,GAAG;gBAC7B,QAAQ,IAAI,CAAC;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,YAAY,EAAE;YACd,UAAU;QACZ,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO,oHAAA,CAAA,WAAQ,CAAC,UAAU;YAC5B,KAAK;gBACH,OAAO,oHAAA,CAAA,WAAQ,CAAC,WAAW;YAC7B,KAAK;gBACH,OAAO,oHAAA,CAAA,WAAQ,CAAC,SAAS;YAC3B,KAAK;gBACH,OAAO,oHAAA,CAAA,WAAQ,CAAC,UAAU;YAC5B,KAAK;gBACH,OAAO,oHAAA,CAAA,WAAQ,CAAC,eAAe;YACjC,KAAK;gBACH,OAAO,oHAAA,CAAA,WAAQ,CAAC,WAAW,EAAE,6BAA6B;YAC5D;gBACE,OAAO,oHAAA,CAAA,WAAQ,CAAC,WAAW;QAC/B;IACF;IAEA,MAAM,eAAe,CAAC,UAAkB;QACtC,MAAM,YAAY,aAAa;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,wBAAwB,EAAE,WAAW;YAC/C,KAAK;gBACH,OAAO,CAAC,2BAA2B,EAAE,WAAW;YAClD,KAAK;gBACH,OAAO,CAAC,kBAAkB,EAAE,WAAW;YACzC,KAAK;gBACH,OAAO,CAAC,iBAAiB,EAAE,WAAW;YACxC,KAAK;gBACH,OAAO,CAAC,iBAAiB,EAAE,WAAW;YACxC;gBACE,OAAO,CAAC,MAAM,EAAE,WAAW;QAC/B;IACF;IAEA,MAAM,qBAAqB,CAAC,UAAkB;QAC5C,MAAM,YAAY,aAAa;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,gCAAgC,EAAE,UAAU,6BAA6B,CAAC;YACpF,KAAK;gBACH,OAAO,CAAC,0CAA0C,EAAE,UAAU,2BAA2B,CAAC;YAC5F,KAAK;gBACH,OAAO,CAAC,6BAA6B,EAAE,UAAU,oCAAoC,CAAC;YACxF,KAAK;gBACH,OAAO,CAAC,uBAAuB,EAAE,UAAU,iCAAiC,CAAC;YAC/E,KAAK;gBACH,OAAO,CAAC,6BAA6B,EAAE,UAAU,6BAA6B,CAAC;YACjF;gBACE,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC;QAC7C;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,iBAAiB;YACpB,UAAU;YACV;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,UAAU;YACV;QACF;QAEA,IAAI,CAAC,kBAAkB,CAAC,QAAQ;YAC9B,UAAU;YACV;QACF;QAEA,aAAa;QAGb,IAAI;YACF,IAAI,kBAAkB,UAAU;gBAC9B,yBAAyB;gBACzB,MAAM,qIAAA,CAAA,cAAW,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;oBAC/C,YAAY;oBACZ,SAAS,QAAQ,IAAI,MAAM;oBAC3B,UAAU;oBACV,UAAU,oHAAA,CAAA,eAAY,CAAC,MAAM;gBAC/B;gBACA,YAAY;gBACZ;YAEF,OAAO;gBACL,MAAM,WAA2B;oBAC/B,WAAW,wBAAwB;oBACnC,OAAO,aAAa,UAAU;oBAC9B,aAAa,mBAAmB,UAAU;oBAC1C,UAAU,oHAAA,CAAA,eAAY,CAAC,MAAM;oBAC7B,QAAQ,oHAAA,CAAA,aAAU,CAAC,OAAO;oBAC1B,aAAa;oBACb,WAAW;oBACX,aAAa;oBACb,UAAU;oBACV,UAAU;wBACR,SAAS,QAAQ,IAAI,MAAM;wBAC3B,qBAAqB;wBACrB,oBAAoB;oBACtB;gBACF;gBACA,4CAA4C;gBAC5C,MAAM,cAAc;gBACpB;YACF;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,MAAM,EAAE,iBAAiB,gBAAgB,sBAAsB,MAAM,CAAC,EAAE;YACvF,UAAU,CAAC,UAAU,EAAE,iBAAiB,aAAa,SAAS,KAAK,CAAC;QACtE,SAAU;YACR,aAAa;QACf;IACF;IAEE,uCAAuC;IACzC,eAAe,cAAc,QAAuB;QAClD,MAAM,cAAc,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;QACjD,YAAY,CAAC,sBAAsB,EAAE,SAAS,OAAO,CAAC,KAAK,KAAK,WAAW,CAAC;QAC5E;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAW,iBAAiB,2CAA2C;;;;;;;;;;;;;;;;8CAG9E,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,iBAAiB,kBAAkB,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC,KAAK,KAAK,WAAW,IAAI;;;;;;sDAEnG,8OAAC;4CAAE,WAAU;sDACV,iBAAiB,qCAAqC;;;;;;;;;;;;;;;;;;sCAI7D,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;8BAKjB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAE,WAAU;;;;;;wCACZ,iBAAiB,kBAAkB;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CACV,iBAAiB,UAAU,QAAQ,aAAa,UAAU;;;;;;8CAE7D,8OAAC;oCAAE,WAAU;8CACV,iBAAiB,UAAU,cAAc,mBAAmB,UAAU;;;;;;gCAExE,kBAAkB,UAAU,6BAC3B,8OAAC;oCAAE,WAAU;;wCAAgD;wCACpD,UAAU;;;;;;;gCAGpB,kBAAkB,UAAU,0BAC3B,8OAAC;oCAAE,WAAU;;wCAAgD;wCACnC,SAAS,QAAQ,CAAC,UAAU;wCAAC;wCAAE,SAAS,QAAQ,CAAC,SAAS;;;;;;;;;;;;;sCAMxF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CACd,iBAAiB,0BAA0B;;;;;;gCAE7C,wBACC,8OAAC;oCAAI,WAAU;8CAAoD;;;;;6FAInE,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gDAA6B,OAAO,QAAQ,OAAO;;oDACjD,QAAQ,UAAU;oDAAC;oDAAE,QAAQ,SAAS;oDAAC;oDAAI,QAAQ,KAAK;oDACxD,QAAQ,UAAU,GAAG,CAAC,EAAE,EAAE,OAAO,QAAQ,UAAU,KAAK,WAAW,QAAQ,UAAU,GAAG,QAAQ,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG;;+CAF3G,QAAQ,OAAO;;;;;;;;;;;;;;;;;sCAUpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC1C,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oCAC3C,WAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CACd,iBAAiB,kCAAkC;;;;;;8CAEtD,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC1C,aAAa,iBAAiB,6CAA6C;oCAC3E,MAAM;oCACN,WAAU;;;;;;;;;;;;wBAKb,iCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,8OAAC;oCAAI,WAAU;8CACZ,CAAC;wCACA,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;wCACjD,OAAO,wBACL;;gDACG,QAAQ,UAAU;gDAAC;gDAAE,QAAQ,SAAS;8DACvC,8OAAC;;;;;gDACA,QAAQ,KAAK;gDACb,QAAQ,UAAU,kBACjB;;sEACE,8OAAC;;;;;wDAAK;wDACO,OAAO,QAAQ,UAAU,KAAK,WAAW,QAAQ,UAAU,GAAG,QAAQ,UAAU,CAAC,IAAI;;;;2DAItG;oCACN,CAAC;;;;;;gCAEF,yBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAO;;;;;;wCAAkB;wCAAE,IAAI,KAAK,SAAS,kBAAkB;;;;;;;;;;;;;;;;;;;8BAQ1E,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,UAAU,CAAC,mBAAmB,CAAC,WAAW;4BAC1C,WAAU;sCAET,0BACC;;kDACE,8OAAC;wCAAI,WAAU;;;;;;oCACd,iBAAiB,mBAAmB;;6DAGvC;;kDACE,8OAAC;wCAAE,WAAW,iBAAiB,6BAA6B;;;;;;oCAC3D,iBAAiB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;AAQpD;uCAEe", "debugId": null}}, {"offset": {"line": 2322, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/tasks/ReassignTaskModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { Task } from '../../services/task-assignment';\r\nimport AssignModal from '../common/AssignModal';\r\n\r\ninterface ReassignTaskModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  task: Task | null;\r\n  onReassignSuccess: () => void;\r\n}\r\n\r\nconst ReassignTaskModal: React.FC<ReassignTaskModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  task,\r\n  onReassignSuccess\r\n}) => {\r\n  return (\r\n    <AssignModal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      itemId={task?.task_id || null}\r\n      itemType=\"task\"\r\n      itemTitle={task?.title}\r\n      mode=\"reassign\"\r\n      task={task}\r\n      onReassignSuccess={onReassignSuccess}\r\n    />\r\n  );\r\n};\r\n\r\nexport default ReassignTaskModal;\r\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAaA,MAAM,oBAAsD,CAAC,EAC3D,MAAM,EACN,OAAO,EACP,IAAI,EACJ,iBAAiB,EAClB;IACC,qBACE,8OAAC,2IAAA,CAAA,UAAW;QACV,QAAQ;QACR,SAAS;QACT,QAAQ,MAAM,WAAW;QACzB,UAAS;QACT,WAAW,MAAM;QACjB,MAAK;QACL,MAAM;QACN,mBAAmB;;;;;;AAGzB;uCAEe", "debugId": null}}, {"offset": {"line": 2351, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/utils/formatters.ts"], "sourcesContent": ["/**\r\n * Formats a number as currency with commas for better readability\r\n * For numbers over 5 digits, adds a comma after the first 2 figures\r\n * \r\n * @param amount - The amount to format\r\n * @param currency - The currency code (e.g., 'MWK', 'USD')\r\n * @param minimumFractionDigits - Minimum number of decimal places (default: 0)\r\n * @returns Formatted currency string\r\n */\r\nexport const formatCurrency = (\r\n  amount: number | string,\r\n  currency: string = 'USD',\r\n  minimumFractionDigits: number = 0\r\n): string => {\r\n  // Convert string to number if needed\r\n  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\r\n\r\n  if(currency == '$') currency = 'USD';\r\n  \r\n  // Get the currency symbol\r\n  const formatter = new Intl.NumberFormat('en-MW', {\r\n    style: 'currency',\r\n    currency: currency,\r\n    minimumFractionDigits: minimumFractionDigits,\r\n    useGrouping: false, // We'll handle grouping manually\r\n  });\r\n  \r\n  // Format without grouping to get the base string\r\n  const formatted = formatter.format(numericAmount);\r\n  \r\n  // Extract the numeric part (remove currency symbol and any spaces)\r\n  const parts = formatted.match(/([^\\d]*)(\\d+(?:\\.\\d+)?)(.*)/);\r\n  if (!parts) return formatted;\r\n  \r\n  const [, prefix, numericPart, suffix] = parts;\r\n  \r\n  // Format the number with custom grouping\r\n  let formattedNumber = numericPart;\r\n  \r\n  // For numbers with 5 or more digits, we want to ensure there's a comma after the first 2 figures\r\n  if (numericPart.replace(/\\D/g, '').length >= 5) {\r\n    // Split the integer and decimal parts\r\n    const [integerPart, decimalPart] = numericPart.split('.');\r\n    \r\n    // Format the integer part with commas\r\n    // First, add a comma after the first 2 digits\r\n    let formattedInteger = integerPart.slice(0, 2) + ',' + integerPart.slice(2);\r\n    \r\n    // Then add commas for the rest of the number every 3 digits\r\n    formattedInteger = formattedInteger.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n    \r\n    // Combine the parts back\r\n    formattedNumber = formattedInteger + (decimalPart ? '.' + decimalPart : '');\r\n  } else {\r\n    // For smaller numbers, use standard grouping (every 3 digits)\r\n    formattedNumber = numericPart.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n  }\r\n  \r\n  // Combine everything back\r\n  return prefix + formattedNumber + suffix;\r\n};\r\n\r\n// ============================================================================\r\n// ENHANCED AMOUNT FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format amount with currency (alternative to formatCurrency for consistency)\r\n * @param amount - The amount to format\r\n * @param currency - Currency code (default: '$')\r\n * @param locale - Locale for formatting (default: 'en-US')\r\n */\r\nexport const formatAmount = (\r\n  amount: number | string,\r\n  currency: string = 'USD',\r\n  locale: string = 'en-US'\r\n): string => {\r\n  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\r\n  if (isNaN(numAmount)) return `$ 0.00`;\r\n\r\n  return `${currency} ${numAmount.toLocaleString(locale, {\r\n    minimumFractionDigits: 2,\r\n    maximumFractionDigits: 2\r\n  })}`;\r\n};\r\n\r\n/**\r\n * Format amount without currency symbol\r\n * @param amount - The amount to format\r\n * @param decimals - Number of decimal places (default: 2)\r\n */\r\nexport const formatNumber = (amount: number | string, decimals: number = 2): string => {\r\n  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\r\n  if (isNaN(numAmount)) return '0.00';\r\n\r\n  return numAmount.toLocaleString('en-US', {\r\n    minimumFractionDigits: decimals,\r\n    maximumFractionDigits: decimals\r\n  });\r\n};\r\n\r\n/**\r\n * Format license category fee with special handling for \"Short Code Allocation\"\r\n * @param fee - The fee amount (string or number)\r\n * @param categoryName - The name of the license category\r\n * @param currency - Currency code (default: 'MWK')\r\n */\r\nexport const formatLicenseCategoryFee = (\r\n  fee: string | number,\r\n  categoryName: string,\r\n): string => {\r\n  // Check if fee is 0 or empty\r\n  if (!fee || fee === '0' || parseFloat(fee.toString()) === 0) {\r\n    // Show \"Free\" for categories with 0 fee\r\n    return \"Free\";\r\n  }\r\n  // Format as currency for non-zero fees\r\n  return formatCurrency(fee);\r\n};\r\n\r\n/**\r\n * Format percentage\r\n * @param value - The value to format as percentage\r\n * @param decimals - Number of decimal places (default: 1)\r\n */\r\nexport const formatPercentage = (value: number | string, decimals: number = 1): string => {\r\n  const numValue = typeof value === 'string' ? parseFloat(value) : value;\r\n  if (isNaN(numValue)) return '0%';\r\n\r\n  return `${numValue.toFixed(decimals)}%`;\r\n};\r\n\r\n// ============================================================================\r\n// ENHANCED DATE & TIME FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Formats a date string to a readable format\r\n *\r\n * @param dateString - The date string to format\r\n * @param options - Intl.DateTimeFormatOptions\r\n * @returns Formatted date string\r\n */\r\nexport const formatDate = (\r\n  dateString: string | Date,\r\n  options: Intl.DateTimeFormatOptions = {\r\n    year: 'numeric',\r\n    month: 'short',\r\n    day: 'numeric'\r\n  }\r\n): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid date';\r\n\r\n  return new Intl.DateTimeFormat('en-MW', options).format(date);\r\n};\r\n\r\n/**\r\n * Format date in long format (e.g., \"January 15, 2024\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatDateLong = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid date';\r\n\r\n  return date.toLocaleDateString('en-US', {\r\n    year: 'numeric',\r\n    month: 'long',\r\n    day: 'numeric'\r\n  });\r\n};\r\n\r\n/**\r\n * Format time (e.g., \"2:30 PM\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatTime = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid time';\r\n\r\n  return date.toLocaleTimeString('en-US', {\r\n    hour: 'numeric',\r\n    minute: '2-digit',\r\n    hour12: true\r\n  });\r\n};\r\n\r\n/**\r\n * Format datetime (e.g., \"Jan 15, 2024 at 2:30 PM\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatDateTime = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid datetime';\r\n\r\n  return `${formatDate(date)} at ${formatTime(date)}`;\r\n};\r\n\r\n/**\r\n * Format relative time (e.g., \"2 hours ago\", \"in 3 days\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatRelativeTime = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid date';\r\n\r\n  const now = new Date();\r\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n  if (diffInSeconds < 60) return 'Just now';\r\n  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\r\n  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\r\n  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;\r\n  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;\r\n\r\n  return `${Math.floor(diffInSeconds / 31536000)} years ago`;\r\n};\r\n\r\n/**\r\n * Format date for input fields (YYYY-MM-DD)\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatDateForInput = (dateString: string | Date): string => {\r\n  if (!dateString) return '';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return '';\r\n\r\n  return date.toISOString().split('T')[0];\r\n};\r\n\r\n// ============================================================================\r\n// STRING CASE FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Convert string to camelCase\r\n * @param str - String to convert\r\n */\r\nexport const toCamelCase = (str: string): string => {\r\n  return str\r\n    .replace(/(?:^\\w|[A-Z]|\\b\\w)/g, (word, index) => {\r\n      return index === 0 ? word.toLowerCase() : word.toUpperCase();\r\n    })\r\n    .replace(/\\s+/g, '');\r\n};\r\n\r\n/**\r\n * Convert string to PascalCase\r\n * @param str - String to convert\r\n */\r\nexport const toPascalCase = (str: string): string => {\r\n  return str\r\n    .replace(/(?:^\\w|[A-Z]|\\b\\w)/g, (word) => {\r\n      return word.toUpperCase();\r\n    })\r\n    .replace(/\\s+/g, '');\r\n};\r\n\r\n/**\r\n * Convert string to kebab-case\r\n * @param str - String to convert\r\n */\r\nexport const toKebabCase = (str: string): string => {\r\n  return str\r\n    .replace(/([a-z])([A-Z])/g, '$1-$2')\r\n    .replace(/[\\s_]+/g, '-')\r\n    .toLowerCase();\r\n};\r\n\r\n/**\r\n * Convert string to snake_case\r\n * @param str - String to convert\r\n */\r\nexport const toSnakeCase = (str: string): string => {\r\n  return str\r\n    .replace(/([a-z])([A-Z])/g, '$1_$2')\r\n    .replace(/[\\s-]+/g, '_')\r\n    .toLowerCase();\r\n};\r\n\r\n/**\r\n * Convert string to Title Case\r\n * @param str - String to convert\r\n */\r\nexport const toTitleCase = (str: string): string => {\r\n  return str.replace(/\\w\\S*/g, (txt) => {\r\n    return txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase();\r\n  });\r\n};\r\n\r\n/**\r\n * Convert string to Sentence case\r\n * @param str - String to convert\r\n */\r\nexport const toSentenceCase = (str: string): string => {\r\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\r\n};\r\n\r\n// ============================================================================\r\n// TEXT FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Truncate text with ellipsis\r\n * @param text - Text to truncate\r\n * @param maxLength - Maximum length before truncation\r\n * @param suffix - Suffix to add (default: '...')\r\n */\r\nexport const truncateText = (text: string, maxLength: number, suffix: string = '...'): string => {\r\n  if (!text || text.length <= maxLength) return text || '';\r\n  return text.substring(0, maxLength - suffix.length) + suffix;\r\n};\r\n\r\n/**\r\n * Capitalize first letter of each word\r\n * @param str - String to capitalize\r\n */\r\nexport const capitalizeWords = (str: string): string => {\r\n  return str.replace(/\\b\\w/g, (char) => char.toUpperCase());\r\n};\r\n\r\n/**\r\n * Remove extra whitespace and normalize spacing\r\n * @param str - String to normalize\r\n */\r\nexport const normalizeWhitespace = (str: string): string => {\r\n  return str.replace(/\\s+/g, ' ').trim();\r\n};\r\n\r\n/**\r\n * Extract initials from a name\r\n * @param name - Full name\r\n * @param maxInitials - Maximum number of initials (default: 2)\r\n */\r\nexport const getInitials = (name: string, maxInitials: number = 2): string => {\r\n  if (!name) return '';\r\n\r\n  return name\r\n    .split(' ')\r\n    .filter(word => word.length > 0)\r\n    .slice(0, maxInitials)\r\n    .map(word => word.charAt(0).toUpperCase())\r\n    .join('');\r\n};\r\n\r\n/**\r\n * Convert text to slug format (URL-friendly)\r\n * @param text - Text to convert\r\n */\r\nexport const toSlug = (text: string): string => {\r\n  return text\r\n    .toLowerCase()\r\n    .replace(/[^\\w\\s-]/g, '') // Remove special characters\r\n    .replace(/[\\s_-]+/g, '-') // Replace spaces and underscores with hyphens\r\n    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens\r\n};\r\n\r\n/**\r\n * Highlight search terms in text\r\n * @param text - Text to highlight\r\n * @param searchTerm - Term to highlight\r\n * @param className - CSS class for highlighting (default: 'highlight')\r\n */\r\nexport const highlightText = (text: string, searchTerm: string, className: string = 'highlight'): string => {\r\n  if (!searchTerm) return text;\r\n\r\n  const regex = new RegExp(`(${searchTerm})`, 'gi');\r\n  return text.replace(regex, `<span class=\"${className}\">$1</span>`);\r\n};\r\n\r\n// ============================================================================\r\n// PHONE & EMAIL FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format phone number\r\n * @param phone - Phone number to format\r\n * @param format - Format type ('international' | 'national' | 'minimal')\r\n */\r\nexport const formatPhone = (phone: string, format: 'international' | 'national' | 'minimal' = 'national'): string => {\r\n  if (!phone) return '';\r\n\r\n  // Remove all non-digit characters\r\n  const digits = phone.replace(/\\D/g, '');\r\n\r\n  if (digits.length < 10) return phone; // Return original if too short\r\n\r\n  switch (format) {\r\n    case 'international':\r\n      return `+${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 9)} ${digits.slice(9)}`;\r\n    case 'national':\r\n      return `(${digits.slice(-10, -7)}) ${digits.slice(-7, -4)}-${digits.slice(-4)}`;\r\n    case 'minimal':\r\n      return `${digits.slice(-10, -7)}.${digits.slice(-7, -4)}.${digits.slice(-4)}`;\r\n    default:\r\n      return phone;\r\n  }\r\n};\r\n\r\n/**\r\n * Mask email for privacy (e.g., \"j***@example.com\")\r\n * @param email - Email to mask\r\n */\r\nexport const maskEmail = (email: string): string => {\r\n  if (!email || !email.includes('@')) return email;\r\n\r\n  const [username, domain] = email.split('@');\r\n  if (username.length <= 2) return email;\r\n\r\n  const maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);\r\n  return `${maskedUsername}@${domain}`;\r\n};\r\n\r\n/**\r\n * Validate email format\r\n * @param email - Email to validate\r\n */\r\nexport const isValidEmail = (email: string): boolean => {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(email);\r\n};\r\n\r\n// ============================================================================\r\n// ID & REFERENCE FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format application number with prefix\r\n * @param number - Application number\r\n * @param prefix - Prefix to add (default: 'APP')\r\n */\r\nexport const formatApplicationNumber = (number: string | number, prefix: string = 'APP'): string => {\r\n  if (!number) return '';\r\n  return `${prefix}-${String(number).padStart(6, '0')}`;\r\n};\r\n\r\n/**\r\n * Format invoice number with prefix\r\n * @param number - Invoice number\r\n * @param prefix - Prefix to add (default: 'INV')\r\n */\r\nexport const formatInvoiceNumber = (number: string | number, prefix: string = 'INV'): string => {\r\n  if (!number) return '';\r\n  return `${prefix}-${String(number).padStart(6, '0')}`;\r\n};\r\n\r\n/**\r\n * Format task number with prefix\r\n * @param number - Task number\r\n * @param prefix - Prefix to add (default: 'TASK')\r\n */\r\nexport const formatTaskNumber = (number: string | number, prefix: string = 'TASK'): string => {\r\n  if (!number) return '';\r\n  return `${prefix}-${String(number).padStart(6, '0')}`;\r\n};\r\n\r\n/**\r\n * Generate a random reference ID\r\n * @param length - Length of the ID (default: 8)\r\n * @param prefix - Optional prefix\r\n */\r\nexport const generateReferenceId = (length: number = 8, prefix?: string): string => {\r\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r\n  let result = '';\r\n\r\n  for (let i = 0; i < length; i++) {\r\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\r\n  }\r\n\r\n  return prefix ? `${prefix}-${result}` : result;\r\n};\r\n\r\n/**\r\n * Convert UUID to user-friendly reference ID for customers\r\n * @param uuid - The UUID to convert\r\n * @param prefix - Optional prefix (default: 'REF')\r\n */\r\nexport const formatCustomerReferenceId = (uuid: string, prefix: string = 'REF'): string => {\r\n  if (!uuid) return '';\r\n\r\n  // Take first 8 characters of UUID (without hyphens) and convert to uppercase\r\n  const cleanUuid = uuid.replace(/-/g, '').toUpperCase();\r\n  const shortId = cleanUuid.substring(0, 8);\r\n\r\n  return `${prefix}-${shortId}`;\r\n};\r\n\r\n/**\r\n * Mask sensitive ID (show only first and last 2 characters)\r\n * @param id - ID to mask\r\n */\r\nexport const maskId = (id: string): string => {\r\n  if (!id || id.length <= 4) return id;\r\n\r\n  const start = id.slice(0, 2);\r\n  const end = id.slice(-2);\r\n  const middle = '*'.repeat(id.length - 4);\r\n\r\n  return `${start}${middle}${end}`;\r\n};\r\n\r\n// ============================================================================\r\n// STATUS & BADGE FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format status text for display\r\n * @param status - Status to format\r\n */\r\nexport const formatStatus = (status: string): string => {\r\n  if (!status) return '';\r\n\r\n  return status\r\n    .replace(/_/g, ' ')\r\n    .replace(/\\b\\w/g, char => char.toUpperCase());\r\n};\r\n\r\n/**\r\n * Get status color class\r\n * @param status - Status to get color for\r\n */\r\n\r\n\r\nexport const getStatusColor = (status: any): string => {\r\n  const statusLower = status.toLowerCase();\r\n\r\n  switch (statusLower) {\r\n    case 'active':\r\n    case 'approved':\r\n    case 'completed':\r\n    case 'paid':\r\n    case 'success':\r\n      return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';\r\n\r\n    case 'pending':\r\n    case 'in_progress':\r\n    case 'processing':\r\n    case 'review':\r\n      return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20';\r\n\r\n    case 'rejected':\r\n    case 'failed':\r\n    case 'error':\r\n    case 'overdue':\r\n    case 'cancelled':\r\n      return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';\r\n\r\n    case 'draft':\r\n    case 'inactive':\r\n    case 'disabled':\r\n      return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';\r\n\r\n    case 'warning':\r\n    case 'attention':\r\n      return 'text-orange-600 bg-orange-100 dark:text-orange-400 dark:bg-orange-900/20';\r\n\r\n    default:\r\n      return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';\r\n  }\r\n};\r\n\r\n// ============================================================================\r\n// FILE SIZE & VALIDATION FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format file size in human readable format\r\n * @param bytes - File size in bytes\r\n * @param decimals - Number of decimal places (default: 2)\r\n */\r\nexport const formatFileSize = (bytes: number, decimals: number = 2): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n\r\n  const k = 1024;\r\n  const dm = decimals < 0 ? 0 : decimals;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\r\n};\r\n\r\n/**\r\n * Get file extension from filename\r\n * @param filename - Filename to extract extension from\r\n */\r\nexport const getFileExtension = (filename: string): string => {\r\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);\r\n};\r\n\r\n/**\r\n * Get file type icon class based on extension\r\n * @param filename - Filename to get icon for\r\n */\r\nexport const getFileTypeIcon = (filename: string): string => {\r\n  const extension = getFileExtension(filename).toLowerCase();\r\n\r\n  switch (extension) {\r\n    case 'pdf':\r\n      return 'ri-file-pdf-line text-red-500';\r\n    case 'doc':\r\n    case 'docx':\r\n      return 'ri-file-word-line text-blue-500';\r\n    case 'xls':\r\n    case 'xlsx':\r\n      return 'ri-file-excel-line text-green-500';\r\n    case 'ppt':\r\n    case 'pptx':\r\n      return 'ri-file-ppt-line text-orange-500';\r\n    case 'jpg':\r\n    case 'jpeg':\r\n    case 'png':\r\n    case 'gif':\r\n    case 'bmp':\r\n      return 'ri-image-line text-purple-500';\r\n    case 'zip':\r\n    case 'rar':\r\n    case '7z':\r\n      return 'ri-file-zip-line text-yellow-500';\r\n    case 'txt':\r\n      return 'ri-file-text-line text-gray-500';\r\n    default:\r\n      return 'ri-file-line text-gray-500';\r\n  }\r\n};\r\n\r\nexport const formatHumanReadable = (text : string, caseType = 'first') => {\r\n  if (!text || typeof text !== 'string') return '';\r\n  \r\n  // Clean and normalize the text\r\n  let formatted = text\r\n    .trim()\r\n    .replace(/[-_]+/g, ' ') // Replace hyphens and underscores with spaces\r\n    .replace(/\\s+/g, ' ')  // Collapse multiple spaces\r\n    .toLowerCase();\r\n  \r\n  // Split into words\r\n  const words = formatted.split(' ');\r\n  \r\n  // Format based on caseType\r\n  switch (caseType.toLowerCase()) {\r\n    case 'lower':\r\n      return formatted;\r\n      \r\n    case 'upper':\r\n      return words\r\n        .map(word => word.charAt(0).toUpperCase() + word.slice(1))\r\n        .join(' ');\r\n        \r\n    case 'first':\r\n    default:\r\n      return words\r\n        .map((word, index) => \r\n          index === 0 \r\n            ? word.charAt(0).toUpperCase() + word.slice(1)\r\n            : word\r\n        )\r\n        .join(' ');\r\n  }\r\n}"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACM,MAAM,iBAAiB,CAC5B,QACA,WAAmB,KAAK,EACxB,wBAAgC,CAAC;IAEjC,qCAAqC;IACrC,MAAM,gBAAgB,OAAO,WAAW,WAAW,WAAW,UAAU;IAExE,IAAG,YAAY,KAAK,WAAW;IAE/B,0BAA0B;IAC1B,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,SAAS;QAC/C,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,aAAa;IACf;IAEA,iDAAiD;IACjD,MAAM,YAAY,UAAU,MAAM,CAAC;IAEnC,mEAAmE;IACnE,MAAM,QAAQ,UAAU,KAAK,CAAC;IAC9B,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,GAAG,QAAQ,aAAa,OAAO,GAAG;IAExC,yCAAyC;IACzC,IAAI,kBAAkB;IAEtB,iGAAiG;IACjG,IAAI,YAAY,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI,GAAG;QAC9C,sCAAsC;QACtC,MAAM,CAAC,aAAa,YAAY,GAAG,YAAY,KAAK,CAAC;QAErD,sCAAsC;QACtC,8CAA8C;QAC9C,IAAI,mBAAmB,YAAY,KAAK,CAAC,GAAG,KAAK,MAAM,YAAY,KAAK,CAAC;QAEzE,4DAA4D;QAC5D,mBAAmB,iBAAiB,OAAO,CAAC,yBAAyB;QAErE,yBAAyB;QACzB,kBAAkB,mBAAmB,CAAC,cAAc,MAAM,cAAc,EAAE;IAC5E,OAAO;QACL,8DAA8D;QAC9D,kBAAkB,YAAY,OAAO,CAAC,yBAAyB;IACjE;IAEA,0BAA0B;IAC1B,OAAO,SAAS,kBAAkB;AACpC;AAYO,MAAM,eAAe,CAC1B,QACA,WAAmB,KAAK,EACxB,SAAiB,OAAO;IAExB,MAAM,YAAY,OAAO,WAAW,WAAW,WAAW,UAAU;IACpE,IAAI,MAAM,YAAY,OAAO,CAAC,MAAM,CAAC;IAErC,OAAO,GAAG,SAAS,CAAC,EAAE,UAAU,cAAc,CAAC,QAAQ;QACrD,uBAAuB;QACvB,uBAAuB;IACzB,IAAI;AACN;AAOO,MAAM,eAAe,CAAC,QAAyB,WAAmB,CAAC;IACxE,MAAM,YAAY,OAAO,WAAW,WAAW,WAAW,UAAU;IACpE,IAAI,MAAM,YAAY,OAAO;IAE7B,OAAO,UAAU,cAAc,CAAC,SAAS;QACvC,uBAAuB;QACvB,uBAAuB;IACzB;AACF;AAQO,MAAM,2BAA2B,CACtC,KACA;IAEA,6BAA6B;IAC7B,IAAI,CAAC,OAAO,QAAQ,OAAO,WAAW,IAAI,QAAQ,QAAQ,GAAG;QAC3D,wCAAwC;QACxC,OAAO;IACT;IACA,uCAAuC;IACvC,OAAO,eAAe;AACxB;AAOO,MAAM,mBAAmB,CAAC,OAAwB,WAAmB,CAAC;IAC3E,MAAM,WAAW,OAAO,UAAU,WAAW,WAAW,SAAS;IACjE,IAAI,MAAM,WAAW,OAAO;IAE5B,OAAO,GAAG,SAAS,OAAO,CAAC,UAAU,CAAC,CAAC;AACzC;AAaO,MAAM,aAAa,CACxB,YACA,UAAsC;IACpC,MAAM;IACN,OAAO;IACP,KAAK;AACP,CAAC;IAED,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS,SAAS,MAAM,CAAC;AAC1D;AAMO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAMO,MAAM,aAAa,CAAC;IACzB,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF;AAMO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,GAAG,WAAW,MAAM,IAAI,EAAE,WAAW,OAAO;AACrD;AAMO,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,YAAY,CAAC;IAChF,IAAI,gBAAgB,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,UAAU,CAAC;IACjF,IAAI,gBAAgB,SAAS,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,SAAS,CAAC;IACnF,IAAI,gBAAgB,UAAU,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,SAAS,WAAW,CAAC;IAExF,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,UAAU,UAAU,CAAC;AAC5D;AAMO,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;AACzC;AAUO,MAAM,cAAc,CAAC;IAC1B,OAAO,IACJ,OAAO,CAAC,uBAAuB,CAAC,MAAM;QACrC,OAAO,UAAU,IAAI,KAAK,WAAW,KAAK,KAAK,WAAW;IAC5D,GACC,OAAO,CAAC,QAAQ;AACrB;AAMO,MAAM,eAAe,CAAC;IAC3B,OAAO,IACJ,OAAO,CAAC,uBAAuB,CAAC;QAC/B,OAAO,KAAK,WAAW;IACzB,GACC,OAAO,CAAC,QAAQ;AACrB;AAMO,MAAM,cAAc,CAAC;IAC1B,OAAO,IACJ,OAAO,CAAC,mBAAmB,SAC3B,OAAO,CAAC,WAAW,KACnB,WAAW;AAChB;AAMO,MAAM,cAAc,CAAC;IAC1B,OAAO,IACJ,OAAO,CAAC,mBAAmB,SAC3B,OAAO,CAAC,WAAW,KACnB,WAAW;AAChB;AAMO,MAAM,cAAc,CAAC;IAC1B,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC;QAC5B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;IAC/D;AACF;AAMO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAYO,MAAM,eAAe,CAAC,MAAc,WAAmB,SAAiB,KAAK;IAClF,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,WAAW,OAAO,QAAQ;IACtD,OAAO,KAAK,SAAS,CAAC,GAAG,YAAY,OAAO,MAAM,IAAI;AACxD;AAMO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,OAAS,KAAK,WAAW;AACxD;AAMO,MAAM,sBAAsB,CAAC;IAClC,OAAO,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI;AACtC;AAOO,MAAM,cAAc,CAAC,MAAc,cAAsB,CAAC;IAC/D,IAAI,CAAC,MAAM,OAAO;IAElB,OAAO,KACJ,KAAK,CAAC,KACN,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAC7B,KAAK,CAAC,GAAG,aACT,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC;AACV;AAMO,MAAM,SAAS,CAAC;IACrB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,4BAA4B;KACrD,OAAO,CAAC,YAAY,KAAK,8CAA8C;KACvE,OAAO,CAAC,YAAY,KAAK,kCAAkC;AAChE;AAQO,MAAM,gBAAgB,CAAC,MAAc,YAAoB,YAAoB,WAAW;IAC7F,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE;IAC5C,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,WAAW,CAAC;AACnE;AAWO,MAAM,cAAc,CAAC,OAAe,SAAmD,UAAU;IACtG,IAAI,CAAC,OAAO,OAAO;IAEnB,kCAAkC;IAClC,MAAM,SAAS,MAAM,OAAO,CAAC,OAAO;IAEpC,IAAI,OAAO,MAAM,GAAG,IAAI,OAAO,OAAO,+BAA+B;IAErE,OAAQ;QACN,KAAK;YACH,OAAO,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI;QAChG,KAAK;YACH,OAAO,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,IAAI;QACjF,KAAK;YACH,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,IAAI;QAC/E;YACE,OAAO;IACX;AACF;AAMO,MAAM,YAAY,CAAC;IACxB,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM,OAAO;IAE3C,MAAM,CAAC,UAAU,OAAO,GAAG,MAAM,KAAK,CAAC;IACvC,IAAI,SAAS,MAAM,IAAI,GAAG,OAAO;IAEjC,MAAM,iBAAiB,SAAS,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,CAAC,SAAS,MAAM,GAAG;IAChH,OAAO,GAAG,eAAe,CAAC,EAAE,QAAQ;AACtC;AAMO,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAWO,MAAM,0BAA0B,CAAC,QAAyB,SAAiB,KAAK;IACrF,IAAI,CAAC,QAAQ,OAAO;IACpB,OAAO,GAAG,OAAO,CAAC,EAAE,OAAO,QAAQ,QAAQ,CAAC,GAAG,MAAM;AACvD;AAOO,MAAM,sBAAsB,CAAC,QAAyB,SAAiB,KAAK;IACjF,IAAI,CAAC,QAAQ,OAAO;IACpB,OAAO,GAAG,OAAO,CAAC,EAAE,OAAO,QAAQ,QAAQ,CAAC,GAAG,MAAM;AACvD;AAOO,MAAM,mBAAmB,CAAC,QAAyB,SAAiB,MAAM;IAC/E,IAAI,CAAC,QAAQ,OAAO;IACpB,OAAO,GAAG,OAAO,CAAC,EAAE,OAAO,QAAQ,QAAQ,CAAC,GAAG,MAAM;AACvD;AAOO,MAAM,sBAAsB,CAAC,SAAiB,CAAC,EAAE;IACtD,MAAM,QAAQ;IACd,IAAI,SAAS;IAEb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IAEA,OAAO,SAAS,GAAG,OAAO,CAAC,EAAE,QAAQ,GAAG;AAC1C;AAOO,MAAM,4BAA4B,CAAC,MAAc,SAAiB,KAAK;IAC5E,IAAI,CAAC,MAAM,OAAO;IAElB,6EAA6E;IAC7E,MAAM,YAAY,KAAK,OAAO,CAAC,MAAM,IAAI,WAAW;IACpD,MAAM,UAAU,UAAU,SAAS,CAAC,GAAG;IAEvC,OAAO,GAAG,OAAO,CAAC,EAAE,SAAS;AAC/B;AAMO,MAAM,SAAS,CAAC;IACrB,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,GAAG,OAAO;IAElC,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG;IAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC;IACtB,MAAM,SAAS,IAAI,MAAM,CAAC,GAAG,MAAM,GAAG;IAEtC,OAAO,GAAG,QAAQ,SAAS,KAAK;AAClC;AAUO,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,QAAQ,OAAO;IAEpB,OAAO,OACJ,OAAO,CAAC,MAAM,KACd,OAAO,CAAC,SAAS,CAAA,OAAQ,KAAK,WAAW;AAC9C;AAQO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,cAAc,OAAO,WAAW;IAEtC,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;YACH,OAAO;QAET;YACE,OAAO;IACX;AACF;AAWO,MAAM,iBAAiB,CAAC,OAAe,WAAmB,CAAC;IAChE,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,KAAK,WAAW,IAAI,IAAI;IAC9B,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAE/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,MAAM,KAAK,CAAC,EAAE;AAC1E;AAMO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAMO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,YAAY,iBAAiB,UAAU,WAAW;IAExD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,MAAM,sBAAsB,CAAC,MAAe,WAAW,OAAO;IACnE,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU,OAAO;IAE9C,+BAA+B;IAC/B,IAAI,YAAY,KACb,IAAI,GACJ,OAAO,CAAC,UAAU,KAAK,8CAA8C;KACrE,OAAO,CAAC,QAAQ,KAAM,2BAA2B;KACjD,WAAW;IAEd,mBAAmB;IACnB,MAAM,QAAQ,UAAU,KAAK,CAAC;IAE9B,2BAA2B;IAC3B,OAAQ,SAAS,WAAW;QAC1B,KAAK;YACH,OAAO;QAET,KAAK;YACH,OAAO,MACJ,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;QAEV,KAAK;QACL;YACE,OAAO,MACJ,GAAG,CAAC,CAAC,MAAM,QACV,UAAU,IACN,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,KAC1C,MAEL,IAAI,CAAC;IACZ;AACF", "debugId": null}}, {"offset": {"line": 2735, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/tasks/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { useToast } from '../../contexts/ToastContext';\r\nimport DataTable from '../../components/common/DataTable';\r\nimport Select from '../../components/common/Select';\r\nimport { useTaskNavigation } from '../../hooks/useTaskNavigation';\r\nimport { PaginateQuery, Task, TaskFilters, TaskPriority, TaskStatus, User } from '@/types';\r\nimport TaskModal from '../../components/tasks/TaskModal';\r\nimport { taskService } from '@/services/task-assignment';\r\nimport ConfirmationModal from '@/components/common/ConfirmationModal';\r\nimport ReassignTaskModal from '@/components/tasks/ReassignTaskModal';\r\nimport { formatStatus, getStatusColor } from '@/utils/formatters';\r\n\r\nexport default function TasksPage() {\r\n  const { showSuccess } = useToast();\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n  const [editingTask, setEditingTask] = useState<Task | null>(null);\r\n  const [tasksData, setTasksData] = useState<any>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\r\n  const [taskToDelete, setTaskToDelete] = useState<Task | null>(null);\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n  const [showReassignModal, setShowReassignModal] = useState(false);\r\n  const [taskToReassign, setTaskToReassign] = useState<Task | null>(null);\r\n  const [filters, setFilters] = useState<TaskFilters>({\r\n    status: TaskStatus.PENDING // Default to showing pending tasks\r\n  });\r\n  const [users, setUsers] = useState<User[]>([]);\r\n  const [currentQuery, setCurrentQuery] = useState<PaginateQuery>({ page: 1, limit: 10 });\r\n\r\n  // Add task navigation hook\r\n  const { openTaskView, isLoading: isNavigating } = useTaskNavigation();\r\n\r\n  const handleEditTask = (task: Task) => {\r\n    setEditingTask(task);\r\n    setIsModalOpen(true);\r\n  };\r\n\r\n  const handleCreateTask = () => {\r\n    setEditingTask(null);\r\n    setIsModalOpen(true);\r\n  };\r\n\r\n  const handleModalClose = () => {\r\n    setIsModalOpen(false);\r\n    setEditingTask(null);\r\n  };\r\n\r\n  const handleTaskSaved = () => {\r\n    if (editingTask) {\r\n      showSuccess('Task updated successfully!');\r\n    } else {\r\n      showSuccess('Task created successfully!');\r\n    }\r\n    // Reload tasks to show updated data\r\n    loadTasks(currentQuery);\r\n  };\r\n\r\n  const handleFilterChange = (key: keyof TaskFilters, value: string) => {\r\n    setFilters((prev: any) => ({\r\n      ...prev,\r\n      [key]: value === '' ? undefined : value\r\n    }));\r\n  };\r\n\r\n  const handleDeleteTask = (task: Task) => {\r\n    setTaskToDelete(task);\r\n    setShowDeleteModal(true);\r\n  };\r\n\r\n  const handleCancelDelete = () => {\r\n    setShowDeleteModal(false);\r\n    setTaskToDelete(null);\r\n  };\r\n\r\n  const handleReassignTask = (task: Task) => {\r\n    setTaskToReassign(task);\r\n    setShowReassignModal(true);\r\n  };\r\n\r\n  const handleCancelReassign = () => {\r\n    setShowReassignModal(false);\r\n    setTaskToReassign(null);\r\n  };\r\n\r\n  const handleReassignSuccess = () => {\r\n    setShowReassignModal(false);\r\n    setTaskToReassign(null);\r\n    // Reload tasks to show updated assignment\r\n    loadTasks(currentQuery);\r\n  };\r\n\r\n  const handleConfirmDelete = async () => {\r\n    if (!taskToDelete) return;\r\n\r\n    setIsDeleting(true);\r\n    try {\r\n      await taskService.deleteTask(taskToDelete.task_id);\r\n      setShowDeleteModal(false);\r\n      setTaskToDelete(null);\r\n      // Reload tasks\r\n      loadTasks(currentQuery);\r\n    } catch (err) {\r\n      console.error('Error deleting task:', err);\r\n      setError('Failed to delete task');\r\n    } finally {\r\n      setIsDeleting(false);\r\n    }\r\n  };\r\n\r\n  const loadTasks = useCallback(async (query: PaginateQuery) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      setCurrentQuery(query);\r\n\r\n      // Combine query with current filters\r\n      const params = {\r\n        ...query,\r\n        ...filters\r\n      };\r\n\r\n      console.log('Loading tasks with params:', params);\r\n      const response = await taskService.getTasks(params);\r\n      console.log('📋 Tasks response received:', response);\r\n      setTasksData(response);\r\n    } catch (err: any) {\r\n      console.error('Error loading tasks:', err);\r\n      let errorMessage = 'Failed to load tasks. Please try again.';\r\n\r\n      if (err && typeof err === 'object') {\r\n        if ('response' in err && err.response && typeof err.response === 'object') {\r\n          if ('status' in err.response) {\r\n            const status = err.response.status;\r\n            if (status === 401) {\r\n              errorMessage = 'Authentication required. Please log in again.';\r\n            } else if (status === 403) {\r\n              errorMessage = 'You do not have permission to view tasks.';\r\n            } else if (status === 500) {\r\n              errorMessage = 'Server error. Please try again later.';\r\n            } else if ('data' in err.response &&\r\n                      err.response.data &&\r\n                      typeof err.response.data === 'object' &&\r\n                      'message' in err.response.data &&\r\n                      typeof err.response.data.message === 'string') {\r\n              errorMessage = err.response.data.message;\r\n            }\r\n          }\r\n        } else if ('message' in err && typeof err.message === 'string') {\r\n          errorMessage = err.message;\r\n        }\r\n      }\r\n\r\n      setError(errorMessage);\r\n      setTasksData({\r\n        data: [],\r\n        meta: {\r\n          itemsPerPage: query.limit || 10,\r\n          totalItems: 0,\r\n          currentPage: query.page || 1,\r\n          totalPages: 0,\r\n          sortBy: [],\r\n          searchBy: [],\r\n          search: '',\r\n          select: [],\r\n        },\r\n        links: {\r\n          current: '',\r\n        },\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [filters]);\r\n\r\n  // Load tasks when component mounts or filters change\r\n  useEffect(() => {\r\n    console.log('🔄 Filters changed, loading tasks:', filters);\r\n    loadTasks({ page: 1, limit: 10 });\r\n  }, [filters, loadTasks]);\r\n\r\n  // Load users on component mount\r\n  useEffect(() => {\r\n    loadUsers();\r\n  }, []);\r\n\r\n  // Handler for DataTable query changes (pagination, search, sorting)\r\n  const handleQueryChange = useCallback((query: PaginateQuery) => {\r\n    loadTasks(query);\r\n  }, [loadTasks]);\r\n\r\n  const loadUsers = async () => {\r\n    try {\r\n      const usersResponse = await taskService.getOfficers();\r\n      setUsers(usersResponse.data);\r\n    } catch (err) {\r\n      console.error('Error loading users:', err);\r\n      setUsers([]);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const taskColumns = [\r\n    {\r\n      key: 'task_number',\r\n      label: 'Task Number',\r\n      sortable: true,\r\n      render: (value: unknown, task: Task & Record<string, unknown>) => (\r\n        <div\r\n          className=\"text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 hover:underline\"\r\n          onClick={() => openTaskView(task.task_id)}\r\n          title=\"Click to view task\"\r\n        >\r\n          {String(value)}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'title',\r\n      label: 'Title',\r\n      sortable: true,\r\n      render: (value: unknown, task: Task & Record<string, unknown>) => (\r\n        <div>\r\n          <div\r\n            className=\"text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 hover:underline\"\r\n            onClick={() => openTaskView(task.task_id)}\r\n            title=\"Click to view task\"\r\n          >\r\n            {String(value)}\r\n          </div>\r\n          <div className=\"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs\">\r\n            {task.description}\r\n          </div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'task_type',\r\n      label: 'Type',\r\n      render: (value: unknown) => (\r\n        <span className=\"text-sm text-gray-900 dark:text-gray-100 capitalize\">\r\n          {String(value).replace('_', ' ')}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      key: 'status',\r\n      label: 'Status',\r\n      sortable: true,\r\n      render: (value: unknown) => (\r\n        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(value)}`}>\r\n          {formatStatus(value as string)}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      key: 'assignee',\r\n      label: 'Assigned To',\r\n      render: (_: unknown, task: Task & Record<string, unknown>) => (\r\n        <span className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n          {task.assignee ? `${task.assignee.first_name} ${task.assignee.last_name}` : 'Unassigned'}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      key: 'due_date',\r\n      label: 'Due Date',\r\n      sortable: true,\r\n      render: (value: unknown) => (\r\n        <span className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n          {value ? new Date(String(value)).toLocaleDateString() : 'No due date'}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      render: (_: unknown, task: Task) => (\r\n        <div className=\"flex items-center space-x-2\">\r\n          {task.assigned_at? (\r\n          <button\r\n            onClick={() => openTaskView(task.task_id)}\r\n            disabled={isNavigating}\r\n            className=\"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 p-1 rounded hover:bg-green-50 dark:hover:bg-green-900 disabled:opacity-50\"\r\n            title=\"Open in new tab\"\r\n          >\r\n            <i className=\"ri-external-link-line\"></i> View\r\n          </button>\r\n          ): null}\r\n          {/* Hide reassign/assign button for completed tasks */}\r\n          {task.status !== TaskStatus.COMPLETED ? (\r\n            <button\r\n              onClick={() => handleReassignTask(task)}\r\n              className=\"text-purple-600 dark:text-purple-400 hover:text-purple-900 dark:hover:text-purple-300 p-1 rounded hover:bg-purple-50 dark:hover:bg-purple-900\"\r\n              title=\"Reassign task\"\r\n            >\r\n              <i className=\"ri-user-shared-line\"></i>\r\n              {task.assignee ? 'Reassign' : 'Assign'}\r\n            </button>\r\n          ) : (\r\n            <span className=\"text-sm text-gray-500 dark:text-gray-400 italic flex items-center\">\r\n              <i className=\"ri-check-circle-line mr-1 text-green-600 dark:text-green-400\"></i>\r\n              Completed\r\n            </span>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-6\">\r\n      {/* Header */}\r\n      <div className=\"mb-6\">\r\n        <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\">\r\n          Task Management\r\n        </h1>\r\n        <p className=\"text-gray-600 dark:text-gray-400\">\r\n          Manage and track tasks across your organization\r\n        </p>\r\n      </div>\r\n\r\n      {/* Action header */}\r\n      <div className=\"mb-6 flex justify-end\">\r\n        <button type=\"button\"\r\n          onClick={handleCreateTask}\r\n          className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900\"\r\n        >\r\n          <i className=\"ri-add-line w-5 h-5 mr-2\"></i>\r\n          Add Task\r\n        </button>\r\n      </div>\r\n\r\n      {error && (\r\n        <div className=\"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded\">\r\n          {error}\r\n        </div>\r\n      )}\r\n\r\n      {/* Filters Section */}\r\n      <div className=\"mb-6\">\r\n        <h2 className=\"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4\">Filters</h2>\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\">\r\n          {/* Status Filter */}\r\n          <Select\r\n            label=\"Status\"\r\n            value={filters.status || ''}\r\n            onChange={(value) => handleFilterChange('status', value)}\r\n            options={[\r\n              { value: '', label: 'All Statuses' },\r\n              { value: TaskStatus.PENDING, label: 'Pending' },\r\n              { value: TaskStatus.COMPLETED, label: 'Completed' },\r\n              { value: TaskStatus.CANCELLED, label: 'Cancelled' },\r\n              { value: TaskStatus.ON_HOLD, label: 'On Hold' },\r\n            ]}\r\n          />\r\n\r\n          {/* Priority Filter */}\r\n          <Select\r\n            label=\"Priority\"\r\n            value={filters.priority || ''}\r\n            onChange={(value) => handleFilterChange('priority', value)}\r\n            options={[\r\n              { value: '', label: 'All Priorities' },\r\n              { value: TaskPriority.LOW, label: 'Low' },\r\n              { value: TaskPriority.MEDIUM, label: 'Medium' },\r\n              { value: TaskPriority.HIGH, label: 'High' },\r\n              { value: TaskPriority.URGENT, label: 'Urgent' },\r\n            ]}\r\n          />\r\n\r\n          {/* Assignment Status Filter */}\r\n          <Select\r\n            label=\"Assignment Status\"\r\n            value={filters.assigned_to || ''}\r\n            onChange={(value) => handleFilterChange('assignment_status', value)}\r\n            options={[\r\n              { value: '', label: 'All Tasks' },\r\n              { value: 'assigned', label: 'Assigned' },\r\n              { value: 'unassigned', label: 'Unassigned' },\r\n            ]}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <DataTable<Task & Record<string, unknown>>\r\n        columns={taskColumns}\r\n        data={tasksData}\r\n        loading={loading}\r\n        onQueryChange={handleQueryChange}\r\n        searchPlaceholder=\"Search tasks by title, description, or task number...\"\r\n      />\r\n\r\n      {/* Delete Confirmation Modal */}\r\n      <ConfirmationModal\r\n        isOpen={showDeleteModal}\r\n        onClose={handleCancelDelete}\r\n        onConfirm={handleConfirmDelete}\r\n        title=\"Delete Task\"\r\n        message={\r\n          taskToDelete ? (\r\n            <div>\r\n              <p className=\"mb-2\">\r\n                Are you sure you want to delete task <strong>{taskToDelete.task_number}</strong>?\r\n              </p>\r\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                This action cannot be undone. All data associated with this task will be permanently removed.\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            'Are you sure you want to delete this task?'\r\n          )\r\n        }\r\n        confirmText=\"Yes, Delete Task\"\r\n        cancelText=\"Cancel\"\r\n        confirmVariant=\"danger\"\r\n        loading={isDeleting}\r\n      />\r\n\r\n      {/* Reassign Task Modal */}\r\n      <ReassignTaskModal\r\n        isOpen={showReassignModal}\r\n        onClose={handleCancelReassign}\r\n        task={taskToReassign}\r\n        onReassignSuccess={handleReassignSuccess}\r\n      />\r\n\r\n      {/* Task Modal */}\r\n      <TaskModal\r\n        isOpen={isModalOpen}\r\n        onClose={handleModalClose}\r\n        onSave={handleTaskSaved}\r\n        task={editingTask}\r\n      />\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAce,SAAS;IACtB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC/B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAClD,QAAQ,oHAAA,CAAA,aAAU,CAAC,OAAO,CAAC,mCAAmC;IAChE;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAAE,MAAM;QAAG,OAAO;IAAG;IAErF,2BAA2B;IAC3B,MAAM,EAAE,YAAY,EAAE,WAAW,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD;IAElE,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,kBAAkB;QACtB,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,YAAY;QACd;QACA,oCAAoC;QACpC,UAAU;IACZ;IAEA,MAAM,qBAAqB,CAAC,KAAwB;QAClD,WAAW,CAAC,OAAc,CAAC;gBACzB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE,UAAU,KAAK,YAAY;YACpC,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,mBAAmB;IACrB;IAEA,MAAM,qBAAqB;QACzB,mBAAmB;QACnB,gBAAgB;IAClB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,qBAAqB;IACvB;IAEA,MAAM,uBAAuB;QAC3B,qBAAqB;QACrB,kBAAkB;IACpB;IAEA,MAAM,wBAAwB;QAC5B,qBAAqB;QACrB,kBAAkB;QAClB,0CAA0C;QAC1C,UAAU;IACZ;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,cAAc;QAEnB,cAAc;QACd,IAAI;YACF,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC,aAAa,OAAO;YACjD,mBAAmB;YACnB,gBAAgB;YAChB,eAAe;YACf,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;QACX,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACnC,IAAI;YACF,WAAW;YACX,SAAS;YACT,gBAAgB;YAEhB,qCAAqC;YACrC,MAAM,SAAS;gBACb,GAAG,KAAK;gBACR,GAAG,OAAO;YACZ;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;YAC5C,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,aAAa;QACf,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,IAAI,eAAe;YAEnB,IAAI,OAAO,OAAO,QAAQ,UAAU;gBAClC,IAAI,cAAc,OAAO,IAAI,QAAQ,IAAI,OAAO,IAAI,QAAQ,KAAK,UAAU;oBACzE,IAAI,YAAY,IAAI,QAAQ,EAAE;wBAC5B,MAAM,SAAS,IAAI,QAAQ,CAAC,MAAM;wBAClC,IAAI,WAAW,KAAK;4BAClB,eAAe;wBACjB,OAAO,IAAI,WAAW,KAAK;4BACzB,eAAe;wBACjB,OAAO,IAAI,WAAW,KAAK;4BACzB,eAAe;wBACjB,OAAO,IAAI,UAAU,IAAI,QAAQ,IACvB,IAAI,QAAQ,CAAC,IAAI,IACjB,OAAO,IAAI,QAAQ,CAAC,IAAI,KAAK,YAC7B,aAAa,IAAI,QAAQ,CAAC,IAAI,IAC9B,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK,UAAU;4BACvD,eAAe,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO;wBAC1C;oBACF;gBACF,OAAO,IAAI,aAAa,OAAO,OAAO,IAAI,OAAO,KAAK,UAAU;oBAC9D,eAAe,IAAI,OAAO;gBAC5B;YACF;YAEA,SAAS;YACT,aAAa;gBACX,MAAM,EAAE;gBACR,MAAM;oBACJ,cAAc,MAAM,KAAK,IAAI;oBAC7B,YAAY;oBACZ,aAAa,MAAM,IAAI,IAAI;oBAC3B,YAAY;oBACZ,QAAQ,EAAE;oBACV,UAAU,EAAE;oBACZ,QAAQ;oBACR,QAAQ,EAAE;gBACZ;gBACA,OAAO;oBACL,SAAS;gBACX;YACF;QACF,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAQ;IAEZ,qDAAqD;IACrD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,sCAAsC;QAClD,UAAU;YAAE,MAAM;YAAG,OAAO;QAAG;IACjC,GAAG;QAAC;QAAS;KAAU;IAEvB,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,oEAAoE;IACpE,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,UAAU;IACZ,GAAG;QAAC;KAAU;IAEd,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,gBAAgB,MAAM,qIAAA,CAAA,cAAW,CAAC,WAAW;YACnD,SAAS,cAAc,IAAI;QAC7B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS,EAAE;QACb;IACF;IAIA,MAAM,cAAc;QAClB;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBACC,WAAU;oBACV,SAAS,IAAM,aAAa,KAAK,OAAO;oBACxC,OAAM;8BAEL,OAAO;;;;;;QAGd;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;;sCACC,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,aAAa,KAAK,OAAO;4BACxC,OAAM;sCAEL,OAAO;;;;;;sCAEV,8OAAC;4BAAI,WAAU;sCACZ,KAAK,WAAW;;;;;;;;;;;;QAIzB;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,sBACP,8OAAC;oBAAK,WAAU;8BACb,OAAO,OAAO,OAAO,CAAC,KAAK;;;;;;QAGlC;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,sBACP,8OAAC;oBAAK,WAAW,CAAC,8DAA8D,EAAE,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;8BACtG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE;;;;;;QAGpB;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAY,qBACnB,8OAAC;oBAAK,WAAU;8BACb,KAAK,QAAQ,GAAG,GAAG,KAAK,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,SAAS,EAAE,GAAG;;;;;;QAGlF;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,sBACP,8OAAC;oBAAK,WAAU;8BACb,QAAQ,IAAI,KAAK,OAAO,QAAQ,kBAAkB,KAAK;;;;;;QAG9D;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAY,qBACnB,8OAAC;oBAAI,WAAU;;wBACZ,KAAK,WAAW,iBACjB,8OAAC;4BACC,SAAS,IAAM,aAAa,KAAK,OAAO;4BACxC,UAAU;4BACV,WAAU;4BACV,OAAM;;8CAEN,8OAAC;oCAAE,WAAU;;;;;;gCAA4B;;;;;;mCAExC;wBAEF,KAAK,MAAM,KAAK,oHAAA,CAAA,aAAU,CAAC,SAAS,iBACnC,8OAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;4BACV,OAAM;;8CAEN,8OAAC;oCAAE,WAAU;;;;;;gCACZ,KAAK,QAAQ,GAAG,aAAa;;;;;;iDAGhC,8OAAC;4BAAK,WAAU;;8CACd,8OAAC;oCAAE,WAAU;;;;;;gCAAmE;;;;;;;;;;;;;QAM1F;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;0BAMlD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAO,MAAK;oBACX,SAAS;oBACT,WAAU;;sCAEV,8OAAC;4BAAE,WAAU;;;;;;wBAA+B;;;;;;;;;;;;YAK/C,uBACC,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAKL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA8D;;;;;;kCAC5E,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,sIAAA,CAAA,UAAM;gCACL,OAAM;gCACN,OAAO,QAAQ,MAAM,IAAI;gCACzB,UAAU,CAAC,QAAU,mBAAmB,UAAU;gCAClD,SAAS;oCACP;wCAAE,OAAO;wCAAI,OAAO;oCAAe;oCACnC;wCAAE,OAAO,oHAAA,CAAA,aAAU,CAAC,OAAO;wCAAE,OAAO;oCAAU;oCAC9C;wCAAE,OAAO,oHAAA,CAAA,aAAU,CAAC,SAAS;wCAAE,OAAO;oCAAY;oCAClD;wCAAE,OAAO,oHAAA,CAAA,aAAU,CAAC,SAAS;wCAAE,OAAO;oCAAY;oCAClD;wCAAE,OAAO,oHAAA,CAAA,aAAU,CAAC,OAAO;wCAAE,OAAO;oCAAU;iCAC/C;;;;;;0CAIH,8OAAC,sIAAA,CAAA,UAAM;gCACL,OAAM;gCACN,OAAO,QAAQ,QAAQ,IAAI;gCAC3B,UAAU,CAAC,QAAU,mBAAmB,YAAY;gCACpD,SAAS;oCACP;wCAAE,OAAO;wCAAI,OAAO;oCAAiB;oCACrC;wCAAE,OAAO,oHAAA,CAAA,eAAY,CAAC,GAAG;wCAAE,OAAO;oCAAM;oCACxC;wCAAE,OAAO,oHAAA,CAAA,eAAY,CAAC,MAAM;wCAAE,OAAO;oCAAS;oCAC9C;wCAAE,OAAO,oHAAA,CAAA,eAAY,CAAC,IAAI;wCAAE,OAAO;oCAAO;oCAC1C;wCAAE,OAAO,oHAAA,CAAA,eAAY,CAAC,MAAM;wCAAE,OAAO;oCAAS;iCAC/C;;;;;;0CAIH,8OAAC,sIAAA,CAAA,UAAM;gCACL,OAAM;gCACN,OAAO,QAAQ,WAAW,IAAI;gCAC9B,UAAU,CAAC,QAAU,mBAAmB,qBAAqB;gCAC7D,SAAS;oCACP;wCAAE,OAAO;wCAAI,OAAO;oCAAY;oCAChC;wCAAE,OAAO;wCAAY,OAAO;oCAAW;oCACvC;wCAAE,OAAO;wCAAc,OAAO;oCAAa;iCAC5C;;;;;;;;;;;;;;;;;;0BAKP,8OAAC,yIAAA,CAAA,UAAS;gBACR,SAAS;gBACT,MAAM;gBACN,SAAS;gBACT,eAAe;gBACf,mBAAkB;;;;;;0BAIpB,8OAAC,iJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS;gBACT,WAAW;gBACX,OAAM;gBACN,SACE,6BACE,8OAAC;;sCACC,8OAAC;4BAAE,WAAU;;gCAAO;8CACmB,8OAAC;8CAAQ,aAAa,WAAW;;;;;;gCAAU;;;;;;;sCAElF,8OAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;6BAK1D;gBAGJ,aAAY;gBACZ,YAAW;gBACX,gBAAe;gBACf,SAAS;;;;;;0BAIX,8OAAC,gJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS;gBACT,MAAM;gBACN,mBAAmB;;;;;;0BAIrB,8OAAC,wIAAA,CAAA,UAAS;gBACR,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,MAAM;;;;;;;;;;;;AAId", "debugId": null}}]}