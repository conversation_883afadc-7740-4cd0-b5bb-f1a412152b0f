'use client';

import { useState, useEffect, useCallback } from 'react';
import DataTable from '../../common/DataTable';
import Select from '../../common/Select';
import { Payment, PaymentFilters } from '@/types/invoice';
import { PaginatedResponse, PaginateQuery } from '@/types';
import { paymentService } from '@/services/paymentService';
import { formatAmount, formatNumber, formatDate, getStatusColor } from '@/utils/formatters';
import { useAuth } from '@/contexts/AuthContext';

interface InvoicesTabProps {
  onViewInvoice?: (invoice: any) => void;
  onPayInvoice?: (invoice: any) => void;
  onViewPaymentsDone?: (invoice: any) => void;
  onUploadPayment?: (invoice: any) => void;
}

const InvoicesTab = ({ onViewInvoice, onUploadPayment }: InvoicesTabProps) => {
  const [invoicesData, setInvoicesData] = useState<PaginatedResponse<Payment> | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<PaymentFilters>({});
  const [currentQuery, setCurrentQuery] = useState<PaginateQuery>({ page: 1, limit: 10 });
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);
  const [showProofModal, setShowProofModal] = useState(false);
  const {user} = useAuth();

  const loadInvoices = useCallback(async (query: PaginateQuery & PaymentFilters) => {
    try {
      setLoading(true);
      setError(null);
      const response = await paymentService.getInvoices(query, user?.isCustomer);
      setInvoicesData(response);
    } catch (err) {
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadInvoices({ ...currentQuery, ...filters });
  }, [loadInvoices, currentQuery, filters]);

  const handleViewProofOfPayment = (invoice: any) => {
    setSelectedInvoice(invoice);
    setShowProofModal(true);
  };

  const handleQueryChange = (query: PaginateQuery) => {
    setCurrentQuery(query);
  };

  const handleFilterChange = (key: keyof PaymentFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined,
    }));
  };

  const clearFilters = () => {
    setFilters({});
  };


  const getTypeColor = (type: string) => {
    switch (type) {
      case 'LICENSE_FEE': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'PROCUREMENT_FEE': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'APPLICATION_FEE': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400';
      case 'RENEWAL_FEE': return 'bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400';
      case 'PENALTY_FEE': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };


  const invoiceColumns = [
    {
      key: 'invoice_number',
      label: 'Invoice #',
      sortable: true,
      searchable: true,
      render: (value: unknown, item: any) => (
        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
          <button  onClick={() => onViewInvoice?.(item)}>
          {String(value)}
          </button>
        </div>
      ),
    },
    {
      key: 'entity_type',
      label: 'Type',
      render: (value: unknown, item: any) => (
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getTypeColor('LICENSE_FEE')}`}>
          {item.entity_type === 'application' ? 'Application Invoice' : String(value)}
        </span>
      ),
    },
    {
      key: 'description',
      label: 'Description',
      render: (value: unknown, item: any) => (
        <div className="text-sm text-gray-900 dark:text-gray-100">
          <div className="max-w-xs truncate font-medium" title={String(value)}>
            {String(value)}
          </div>
          {item.entity_type && item.entity_id && (
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {item.entity_type === 'application' ? '📋 Application' : '📄 ' + item.entity_type}
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'issue_date',
      label: 'Issue Date',
      sortable: true,
      render: (value: unknown) => (
        <div className="text-sm text-gray-900 dark:text-gray-100">
          {formatDate(String(value))}
        </div>
      ),
    },
    {
      key: 'due_date',
      label: 'Due Date',
      sortable: true,
      render: (value: unknown) => (
        <div className="text-sm text-gray-900 dark:text-gray-100">
          {formatDate(String(value))}
        </div>
      ),
    },
    {
      key: 'amount',
      label: 'Amount',
      sortable: true,
      render: (value: unknown, item: any) => (
        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
          ${formatNumber(Number(value))}
        </div>
      ),
    },
    {
      key: 'balance',
      label: 'Balance',
      render: (value: unknown, item: any) => {
        // Use balance from backend or calculate if not available
        const balance = item.balance !== undefined ? Number(item.balance) : Math.max(0, Number(item.amount || 0) - Number(item.paid_amount || 0));
        const isFullyPaid = balance === 0 || item.payment_status === 'FULLY_PAID';

        return (
          <div className={`text-sm font-medium ${
            isFullyPaid
              ? 'text-green-600 dark:text-green-400'
              : 'text-red-600 dark:text-red-400'
          }`}>
            {isFullyPaid ? (
              <div className="flex items-center">
                <i className="ri-check-circle-fill mr-1 text-xs"></i>
                <span className="text-xs">Paid</span>
              </div>
            ) : (
              <div className="flex flex-col">
                <span>{formatAmount(balance)}</span>
                {item.paid_amount > 0 && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    ({formatAmount(item.paid_amount)} paid)
                  </span>
                )}
              </div>
            )}
          </div>
        );
      },
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: unknown) => (
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(String(value))}`}>
          {String(value)}
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: unknown, item: any) => (
        <div className="flex flex-col space-y-1">
          {/* First row of actions */}
          <div className="flex space-x-2">
            <button
              onClick={() => onViewInvoice?.(item)}
              className="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 border border-blue-200 dark:border-blue-600 rounded hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
              title="View invoice details"
            >
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              View
            </button>
          </div>

          {/* Second row of actions */}
          <div className="flex space-x-2">
            {(item.status !== 'paid') && user?.isCustomer && (
              <button
                onClick={() => onUploadPayment?.(item)}
                className="inline-flex items-center px-2 py-1 text-xs font-medium text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 border border-purple-200 dark:border-purple-600 rounded hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-colors"
                title="Upload proof of payment"
              >
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                Upload
              </button>
            )}

            {/* View Payments button - show if there are documents */}
            {item.documents && item.documents.length > 0 && (
              <button
                onClick={() => handleViewProofOfPayment?.(item)}
                className="inline-flex items-center px-2 py-1 text-xs font-medium text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 border border-green-200 dark:border-green-600 rounded hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors"
                title="View proof of payment documents"
              >
                <i className="ri-file-text-line mr-1"></i>
                Payments ({item.documents.filter((doc: any) =>
                  doc.document_type === 'proof_of_payment' ||
                  doc.document_type === 'PROOF_OF_PAYMENT'
                ).length})
              </button>
            )}
          </div>
        </div>
      ),
    },
  ];



  const statusOptions = [
    { value: 'pending', label: 'Pending' },
    { value: 'paid', label: 'Paid' },
    { value: 'overdue', label: 'Overdue' },
  ];

  const dateRangeOptions = [
    { value: '', label: 'All Time' },
    { value: 'last-30', label: 'Last 30 Days' },
    { value: 'last-90', label: 'Last 90 Days' },
    { value: 'last-year', label: 'Last Year' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">Invoices</h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            View and manage your outstanding invoices
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {/* Total: {invoicesData?.meta.totalItems || 0} invoice{(invoicesData?.meta.totalItems || 0) !== 1 ? 's' : ''} */}
          </span>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
          <Select
            label="Status"
            value={filters.status || 'pending'}
            onChange={(value) => handleFilterChange('status', value)}
            options={statusOptions}
          />
          <Select
            label="Invoice Type"
            value={filters.entity_type || ''}
            onChange={(value) => handleFilterChange('entity_type', value)}
            options={[
              { value: '', label: 'All Types' },
              { value: 'application', label: 'Application Invoice' },
              { value: 'license', label: 'License Invoice' },
            ]}
          />
          <Select
            label="Date Range"
            value={filters.dateRange || ''}
            onChange={(value) => handleFilterChange('dateRange', value)}
            options={dateRangeOptions}
          />
          <div className="flex items-end">
            <button
              type="button"
              onClick={clearFilters}
              className="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-white bg-white dark:bg-gray-700 border border-red-500 rounded-full hover:bg-red-700 dark:hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-300"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded">
          <p>{error}</p>
        </div>
      )}

      {/* Data Table */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <DataTable
          columns={invoiceColumns}
          data={invoicesData}
          loading={loading}
          onQueryChange={handleQueryChange}
          searchPlaceholder="Search invoices by number, description..."
          emptyStateIcon="ri-file-list-line"
          emptyStateMessage="No invoices found"
        />
      </div>

      {/* Proof of Payment Modal */}
      {showProofModal && selectedInvoice && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            {/* Background overlay */}
            <div
              className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75"
              onClick={() => setShowProofModal(false)}
            ></div>

            {/* Modal panel */}
            <div className="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg">
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Proof of Payment Documents
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Invoice #{selectedInvoice.invoice_number} - {formatAmount(selectedInvoice.amount)}
                  </p>
                </div>
                <button
                  onClick={() => setShowProofModal(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Documents List */}
              <div className="space-y-4">
                {selectedInvoice.documents && selectedInvoice.documents.length > 0 ? (
                  selectedInvoice.documents
                    .filter((doc: any) =>
                      doc.document_type === 'proof_of_payment' ||
                      doc.document_type === 'PROOF_OF_PAYMENT'
                    )
                    .map((document: any, index: number) => (
                      <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <i className="ri-file-text-line text-2xl text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {document.file_name}
                              </h4>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                Uploaded on {formatDate(document.created_at)}
                              </p>
                              {document.file_size && (
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                  Size: {(document.file_size / 1024 / 1024).toFixed(2)} MB
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => window.open(`/api/documents/${document.document_id}/download`, '_blank')}
                              className="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                            >
                              <i className="ri-download-line mr-1"></i>
                              Download
                            </button>
                            <button
                              onClick={() => window.open(`/api/documents/${document.document_id}/view`, '_blank')}
                              className="inline-flex items-center px-3 py-1 text-xs font-medium text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
                            >
                              <i className="ri-eye-line mr-1"></i>
                              View
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                ) : (
                  <div className="text-center py-8">
                    <i className="ri-file-text-line text-4xl text-gray-400 dark:text-gray-600 mb-4"></i>
                    <p className="text-gray-500 dark:text-gray-400">
                      No proof of payment documents found for this invoice.
                    </p>
                  </div>
                )}
              </div>

              {/* Footer */}
              <div className="flex justify-end mt-6">
                <button
                  onClick={() => setShowProofModal(false)}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InvoicesTab;
