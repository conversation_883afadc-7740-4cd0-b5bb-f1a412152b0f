'use client';

import React, { useState, useEffect } from 'react';
import { applicationService } from '@/services/applicationService';
import { useToast } from '@/contexts/ToastContext';
import { Application, Task } from '@/types';
import ActivityHistory from '../common/ActivityHistory';
import StatusCard from '../customer/StatusCard';
import { ApplicantInfoCard, LegalHistoryCard, ManagementCard, EquipmentDetailsCard, ContactInfoCard, AddressInfoCard, DocumentCard } from '../evaluation';
import ApplicationStatusButtons from '../evaluation/ApplicationStatusButtons';
import InvoiceStatusCard from '../evaluation/InvoiceStatusCard';

interface ApplicationTaskComponentProps {
  task: Task;
  onTaskUpdate: () => void;
}

const ApplicationTaskComponent: React.FC<ApplicationTaskComponentProps> = ({ task, onTaskUpdate }) => {
  const { showToast } = useToast();
  const [application, setApplication] = useState<Application | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (task.entity_type === 'application' && task.entity_id) {
      loadApplication();
    }
  }, [task]);

  const loadApplication = async () => {
    try {
      setLoading(true);
      const response = await applicationService.getApplication(task.entity_id!);
      if (response) {
        setApplication(response);
      } else {
        throw new Error('Failed to load application');
      }
    } catch (error) {
      console.error('Error loading application:', error);
      showToast('Failed to load application details', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenEvaluation = () => {
    if (application) {
      // Open the evaluation interface in a new tab
      const evaluationUrl = `/evaluation/${application.id}`;
      window.open(evaluationUrl, '_blank');
    }
  };


  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mr-3"></div>
        <span className="text-gray-600 dark:text-gray-400">Loading application details...</span>
      </div>
    );
  }

  if (!application) {
    return (
      <div className="text-center py-8">
        <div className="bg-yellow-100 dark:bg-yellow-900/20 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
          <i className="ri-error-warning-line text-2xl text-yellow-600 dark:text-yellow-400"></i>
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Application Not Found</h3>
        <p className="text-gray-600 dark:text-gray-400">
          The application associated with this task could not be found.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <StatusCard title={'Application Status'} value={application.status} bgColor={'text-blue-200'} iconBgColor={'bg-blue-200'} iconTextColor={''} linkText={''} linkHref={''} icon={undefined} ></StatusCard>

      {/* Applicant Information Card */}
      <ApplicantInfoCard
        applicant={application.applicant ?? null}
        className="mb-6"
        showEmptyFields={false}
      />

      <ContactInfoCard application={application}></ContactInfoCard>
      <AddressInfoCard application={application}></AddressInfoCard>
      <ManagementCard application={application}></ManagementCard>
      
      <EquipmentDetailsCard application={application}></EquipmentDetailsCard>
      <DocumentCard application={application}></DocumentCard>

      {/* Invoice Status - Always visible for invoice generation */}
      <div className="mb-6">
        <InvoiceStatusCard
          application={application!}
          onInvoiceGenerated={(invoice) => {
            console.log('Invoice generated:', invoice);
          }}
          onStatusChange={(status) => {
            // Update invoice status when it changes
          }}
        />
      </div>

      {/* Activity History */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <ActivityHistory
          entityType="application"
          entityId={application.application_id ?? ""}
          title="Activity History"
          showSearch={true}
          showFilters={false}
          maxHeight="max-h-96"
          className="border-0 rounded-none"
        />
      </div>

      <ApplicationStatusButtons
        application={application}
        onStatusChange={(newStatus, updatedApplication) => {
          // Update the application state with new status
          setApplication(updatedApplication);
          loadApplication();
        }}
      />
              
    </div>
  );
};

export default ApplicationTaskComponent;
