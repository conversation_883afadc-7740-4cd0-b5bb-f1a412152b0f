'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { applicationService } from '@/services/applicationService';
import { Application, Applicant, ApplicationStatus } from '@/types/license';
import { AddressInfoCard, ApplicantInfoCard, ApplicationInfoCard, ContactInfoCard, DocumentCard, EquipmentDetailsCard, LegalHistoryCard, ManagementCard } from '../evaluation';
import InvoiceStatusCard from '../evaluation/InvoiceStatusCard';
import ActivityHistory from '../common/ActivityHistory';
import DataDisplayCard from '../evaluation/DataDisplayCard';
import { getApplicantFromApplication, getApplicantId } from '@/utils/applicantUtils';
import StatusCard from '../customer/StatusCard';
import ApplicationStatusButtons from '../evaluation/ApplicationStatusButtons';

interface ApplicationViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  applicationId: string | null;
  departmentType?: string;
  licenseTypeCode?: string;
  onUpdate?: () => void;
}

const ApplicationViewModal: React.FC<ApplicationViewModalProps> = ({
  isOpen,
  onClose,
  applicationId,
  departmentType,
  licenseTypeCode
}) => {
  const router = useRouter();
  
  const [application, setApplication] = useState<Application | null>(null);
  const [applicant, setApplicant] = useState<Applicant | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchApplicationDetails = useCallback(async () => {
    if (!applicationId) return;

    setLoading(true);
    setError(null);

    try {
      // Fetch application details
      const applicationResponse = await applicationService.getApplication(applicationId);
      setApplication(applicationResponse);

      // Get applicant data using utility function
      const applicantData = await getApplicantFromApplication(applicationResponse);
      setApplicant(applicantData);
    } catch (err: unknown) {
      console.error('Error fetching application details:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(`Failed to load application details: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }, [applicationId]);

  useEffect(() => {
    if (isOpen && applicationId) {
      fetchApplicationDetails();
    }
  }, [isOpen, applicationId, fetchApplicationDetails]);

  const handleEvaluate = () => {
    if (!application) return;

    // Get the license type code from the application data or use departmentType/licenseTypeCode
    const resolvedLicenseTypeCode = application?.license_category?.license_type?.code ||
                                   licenseTypeCode ||
                                   departmentType ||
                                   'telecommunications'; // fallback

    // Create URL with both application_id and license_category_id
    const params = new URLSearchParams();
    params.set('application_id', applicationId!);

    // Get license category ID from application
    const licenseCategoryId = application?.license_category_id;

    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }

    // Close modal and navigate to evaluation page
    handleClose();
    router.push(`/applications/${resolvedLicenseTypeCode}/evaluate?${params.toString()}`);
  };

  const handleClose = () => {
    setApplication(null);
    setApplicant(null);
    setError(null);
    setLoading(false);
    onClose();
  };

  // Function to continue working on an application
  const onContinueApplication = async (application: Application) => {
    // Clear any existing errors
    setError(null)
    if (!application || !application.license_category_id) {
      console.error('License category ID not found in application data:', application);
      setError('Unable to continue application: License category information is missing. Please contact support.');
      return;
    }
    // Build the continue URL using query parameters
    const continueUrl = `/customer/applications/apply/applicant-info?license_category_id=${application.license_category_id}&application_id=${application.application_id}`;
    router.push(continueUrl);
  };



  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col shadow-2xl">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center">
                <i className="ri-file-text-line text-white text-lg"></i>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Application Details
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {application?.application_number || 'Loading...'}
              </p>
            </div>
          </div>
          <button
            type="button"
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            aria-label="Close modal"
          >
            <i className="ri-close-line text-xl"></i>
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 bg-gray-50 dark:bg-gray-900">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">Loading application details...</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <i className="ri-error-warning-line text-4xl text-red-500 mb-4"></i>
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Error Loading Application</h3>
                <p className="text-gray-500 dark:text-gray-400 mb-4">{error}</p>
                <button 
                  onClick={fetchApplicationDetails}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          ) : application ? (
            <div className="space-y-6">

              <StatusCard title={'Application Status'} value={application.status} bgColor={'text-blue-200'} iconBgColor={'bg-blue-200'} iconTextColor={''} linkText={''} linkHref={''} icon={undefined} ></StatusCard>
              {/* Application Information Card */}
              <ApplicationInfoCard 
                application={application} 
                className="mb-6"
                showEmptyFields={false}
              />

              {/* Applicant Information Card */}
              <ApplicantInfoCard
                applicant={applicant}
                className="mb-6"
                showEmptyFields={false}
              />

              {/* Invoice Status - Always visible for invoice generation */}
              <div className="mb-6">
                <InvoiceStatusCard
                  application={application!}
                  onInvoiceGenerated={(invoice) => {
                    console.log('Invoice generated:', invoice);
                  }}
                  onStatusChange={(status) => {
                    // Update invoice status when it changes
                  }}
                />
              </div>

              {/* Activity History */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <ActivityHistory
                  entityType="application"
                  entityId={applicationId ?? ""}
                  title="Activity History"
                  showSearch={true}
                  showFilters={false}
                  maxHeight="max-h-96"
                  className="border-0 rounded-none"
                />
              </div>

              <ContactInfoCard application={application} defaultCollapsed={true}></ContactInfoCard>
              <AddressInfoCard application={application} defaultCollapsed={true}></AddressInfoCard>
              <ManagementCard application={application} defaultCollapsed={true}></ManagementCard>
              <LegalHistoryCard application={application} defaultCollapsed={true}></LegalHistoryCard>
              <EquipmentDetailsCard application={application} defaultCollapsed={true}></EquipmentDetailsCard>
              <DocumentCard application={application} defaultCollapsed={false}></DocumentCard>

              {application.status !== ApplicationStatus.PASS_EVALUATION && (
                <ApplicationStatusButtons
                  application={application}
                  onStatusChange={(newStatus, updatedApplication) => {
                    // Update the application state with new status
                    console.log('Application status changed to:', newStatus);
                    console.log('Application status changed to:', updatedApplication);
                    setApplication(updatedApplication);
                    fetchApplicationDetails();
                  }}
                />
              )}
              
            </div>
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <i className="ri-file-line text-4xl text-gray-400 mb-4"></i>
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No Application Data</h3>
                <p className="text-gray-500 dark:text-gray-400">Application details could not be found.</p>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="border-t border-gray-200 dark:border-gray-700 px-6 py-4 bg-white dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
              {application && (
                <>
                  <i className="ri-time-line"></i>
                  <span>Last updated: {new Date(application.updated_at).toLocaleString()}</span>
                </>
              )}
            </div>
            
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={handleClose}
                className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
              >
                <i className="ri-close-line mr-2"></i>
                Close
              </button>
              
              {/* {application && application.status !== ApplicationStatus.APPROVED && application.status !== ApplicationStatus.REJECTED && (
                <button
                  type="button"
                  onClick={handleEvaluate}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
                >
                  <i className="ri-clipboard-line mr-2"></i>
                  Evaluate Application
                </button>
              )} */}

              {/* Continue Application Button */}
              {application?.status == 'draft' && (
                <button
                  type="button"
                  onClick={() => {
                    onContinueApplication(application);
                    onClose();
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-blue-300 shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mr-3 sm:w-auto sm:text-sm"
                >
                  <i className="ri-edit-line mr-2"></i>
                  Continue Application
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApplicationViewModal;
