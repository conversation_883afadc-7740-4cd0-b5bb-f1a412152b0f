'use client';

import { invoiceService } from '@/services/invoiceService';
import { Invoice, InvoiceItem } from '@/types/invoice';
import { taskService } from '@/services/task-assignment';
import React, { useState, useEffect } from 'react';
import { Application, TaskStatus } from '@/types';
import { formatStatus } from '@/utils/formatters';

interface InvoiceStatusCardProps {
  application: Application;
  onInvoiceGenerated?: (invoice: Invoice) => void;
  onStatusChange?: (status: {
    hasInvoice: boolean;
    status?: 'paid' | 'pending' | 'overdue' | 'none';
  }) => void;
}

const InvoiceStatusCard: React.FC<InvoiceStatusCardProps> = ({
  application,
  onInvoiceGenerated,
  onStatusChange
}) => {
  const [loading, setLoading] = useState(true);
  const [invoiceStatus, setInvoiceStatus] = useState<{
    hasInvoice: boolean;
    invoice?: Invoice;
    status?: 'paid' | 'pending' | 'overdue' | 'none';
  }>({ hasInvoice: false, status: 'none' });
  const [allInvoices, setAllInvoices] = useState<Invoice[]>([]);
  const [loadingInvoices, setLoadingInvoices] = useState(false);
  const [showGenerateForm, setShowGenerateForm] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [formData, setFormData] = useState({
    amount: '',
    description: '',
    items: [] as InvoiceItem[]
  });
  const [loadingDefaults, setLoadingDefaults] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Load data when component mounts or application ID changes
  useEffect(() => {
    if (application?.application_id) {
      console.log('🔄 Loading invoice data for application:', application.application_id);
      loadInvoiceStatus();
      loadAllInvoices();
    }
  }, [application?.application_id]);

  // Also load data on component mount to ensure it runs when modal is shown
  useEffect(() => {
    if (application?.application_id) {
      console.log('🚀 Component mounted, loading invoice data for application:', application.application_id);
      loadInvoiceStatus();
      loadAllInvoices();
    }
  }, []); // Empty dependency array means this runs once on mount

  const loadInvoiceStatus = async () => {
    console.log('📋 loadInvoiceStatus called for application:', application.application_id);
    setLoading(true);
    try {
      const status = await invoiceService.getApplicationInvoiceStatus(application.application_id);
      console.log('✅ Invoice status loaded:', status);
      setInvoiceStatus(status);
      // Notify parent component of status change
      onStatusChange?.(status);
    } catch (error) {
      console.log('❌ Error loading invoice status:', error);
      const errorStatus = { hasInvoice: false, status: 'none' as const };
      setInvoiceStatus(errorStatus);
      // Notify parent component of status change
      onStatusChange?.(errorStatus);
    } finally {
      setLoading(false);
    }
  };

  const loadAllInvoices = async () => {
    console.log('📄 loadAllInvoices called for application:', application.application_id);
    setLoadingInvoices(true);
    try {
      const response = await invoiceService.getInvoicesByEntity('application', application.application_id);
      console.log('📄 Invoice response:', response);
      const invoices = response.data || response || [];
      console.log('✅ All invoices loaded:', invoices.length, 'invoices');
      setAllInvoices(invoices);
    } catch (error) {
      console.log('❌ Error loading all invoices:', error);
      setAllInvoices([]);
    } finally {
      setLoadingInvoices(false);
    }
  };

  const loadDefaultInvoiceData = async () => {
    setLoadingDefaults(true);
    try {
      const { defaultInvoiceData } = await invoiceService.getApplicationDetailsForInvoice(application.application_id);
      setFormData({
        amount: defaultInvoiceData.amount.toString(),
        description: defaultInvoiceData.description,
        items: defaultInvoiceData.items
      });
    } catch (error) {
      // Set basic defaults if API fails
      setFormData({
        amount: '0',
        description: 'License Application Fee',
        items: [{
          item_id: `license_fee_${Date.now()}`,
          description: 'License Application Fee',
          quantity: 1,
          unit_price: 0
        }]
      });
    } finally {
      setLoadingDefaults(false);
    }
  };

  const handleGenerateInvoice = async () => {
    if (!formData.amount || !formData.description) {
      setErrorMessage('Please fill in all required fields');
      setSuccessMessage(null);
      return;
    }

    // Calculate total amount from items
    const totalAmount = formData.items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0);

    setGenerating(true);
    try {
      const invoice = await invoiceService.generateApplicationInvoice(application.application_id, {
        amount: totalAmount,
        description: formData.description,
        items: formData.items.length > 0 ? formData.items : undefined
      });

      const newStatus = {
        hasInvoice: true,
        invoice,
        status: 'pending' as const
      };

      setInvoiceStatus(newStatus);
      // Notify parent component of status change
      onStatusChange?.(newStatus);

      setShowGenerateForm(false);
      setFormData({ amount: '', description: '', items: [] });
      setErrorMessage(null); // Clear any previous errors

      // Show success message
      const successMsg = invoice?.invoice_number
        ? `Invoice ${invoice.invoice_number} processed successfully!`
        : 'Invoice processed successfully!';

      setSuccessMessage(successMsg);

      // Auto-hide success message after 5 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);

      if (onInvoiceGenerated) {
        onInvoiceGenerated(invoice);
      }

      // Refresh the invoices list
      await loadAllInvoices();

      // Close the task for this application
      await closeApplicationTask();
    } catch (error: any) {
      console.error('Failed to generate invoice:', error);

      // Extract error message from API response
      let errorMessage = 'Failed to generate invoice. Please try again.';
      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      setErrorMessage(errorMessage);
      setSuccessMessage(null); // Clear any success message
    } finally {
      setGenerating(false);
    }
  };

  const closeApplicationTask = async () => {
    try {
      console.log('🔄 Attempting to close task for application:', application.applicant_id);

      // Get the task for this application
      const task = await taskService.getTaskForApplication(application.applicant_id);

      if (task && task.status !== TaskStatus.COMPLETED) {
        console.log('📋 Found task to close:', task.task_id);

        // Update the task status to completed
        await taskService.updateTask(task.task_id, {
          status: TaskStatus.COMPLETED,
          completion_notes: 'Task completed automatically when invoice was generated'
        });

        console.log('✅ Task closed successfully:', task.task_id);
      } else if (task && task.status === TaskStatus.COMPLETED) {
        console.log('ℹ️ Task is already completed:', task.task_id);
      } else {
        console.log('ℹ️ No task found for application:', application.applicant_id);
      }
    } catch (error) {
      console.error('⚠️ Failed to close application task:', error);
      // Don't throw error here - invoice generation should still succeed even if task closure fails
    }
  };

  const addInvoiceItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [
        ...prev.items,
        {
          item_id: `item_${Date.now()}`,
          description: '',
          quantity: 1,
          unit_price: 0
        }
      ]
    }));
  };

  const updateInvoiceItem = (index: number, field: keyof InvoiceItem, value: any) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  const removeInvoiceItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  const getStatusIcon = () => {
    switch (invoiceStatus.status) {
      case 'paid':
        return 'ri-check-circle-line text-green-600';
      case 'pending':
        return 'ri-time-line text-yellow-600';
      case 'overdue':
        return 'ri-error-warning-line text-red-600';
      default:
        return 'ri-file-list-line text-gray-600';
    }
  };

  const getStatusText = () => {
    switch (invoiceStatus.status) {
      case 'paid':
        return 'Invoice Paid';
      case 'pending':
        return 'Pending Payment from Applicant';
      case 'overdue':
        return 'Payment Overdue';
      default:
        return 'No Invoice Generated';
    }
  };

  const getStatusColor = () => {
    switch (invoiceStatus.status) {
      case 'paid':
        return 'text-green-700 bg-green-50 border-green-200';
      case 'pending':
        return 'text-yellow-700 bg-yellow-50 border-yellow-200';
      case 'overdue':
        return 'text-red-700 bg-red-50 border-red-200';
      default:
        return 'text-gray-700 bg-gray-50 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
          <span className="text-gray-600 dark:text-gray-400">Loading invoice status...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
          <i className="ri-bill-line mr-2 text-blue-600"></i>
          Invoice Status
        </h4>
      </div>

      {/* Success Message */}
      {successMessage && (
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center">
            <i className="ri-check-line text-green-600 dark:text-green-400 mr-2"></i>
            <span className="text-green-700 dark:text-green-300 text-sm">{successMessage}</span>
            <button
              onClick={() => setSuccessMessage(null)}
              className="ml-auto text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200"
            >
              <i className="ri-close-line"></i>
            </button>
          </div>
        </div>
      )}

      {/* Error Message */}
      {errorMessage && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center">
            <i className="ri-error-warning-line text-red-600 dark:text-red-400 mr-2"></i>
            <span className="text-red-700 dark:text-red-300 text-sm">{errorMessage}</span>
            <button
              onClick={() => setErrorMessage(null)}
              className="ml-auto text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200"
            >
              <i className="ri-close-line"></i>
            </button>
          </div>
        </div>
      )}

      <div className={`p-4 rounded-lg border ${getStatusColor()} dark:bg-gray-700 dark:border-gray-600`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <i className={`${getStatusIcon()} text-xl mr-3`}></i>
            <div>
              <p className="font-medium">{getStatusText()}</p>
              {invoiceStatus.invoice && (
                <div className="text-sm opacity-75 mt-1">
                  <p>Invoice #{invoiceStatus.invoice.invoice_number}</p>
                  <p>Amount: ${invoiceStatus.invoice.amount}</p>
                  {invoiceStatus.invoice.due_date && (
                    <p>Due: {new Date(invoiceStatus.invoice.due_date).toLocaleDateString()}</p>
                  )}
                </div>
              )}
            </div>
          </div>

          {!invoiceStatus.hasInvoice && application.status === 'pass_evaluation' && (
            <button
              onClick={() => {
                setShowGenerateForm(true);
                setErrorMessage(null); // Clear any previous errors
                setSuccessMessage(null); // Clear any success messages
                loadDefaultInvoiceData();
              }}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            >
              <i className="ri-add-line mr-2"></i>
              Generate Invoice
            </button>
          )}
        </div>
      </div>

      {/* Application Invoices List */}
      {allInvoices.length > 0 && (
        <div className="mt-6">
          <h5 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <i className="ri-file-list-3-line mr-2 text-blue-600"></i>
            Application Invoices ({allInvoices.length})
          </h5>

          {loadingInvoices ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
              <span className="text-gray-600 dark:text-gray-400">Loading invoices...</span>
            </div>
          ) : (
            <div className="space-y-3">
              {allInvoices.map((invoice, index) => (
                <div
                  key={invoice.invoice_id || index}
                  className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h6 className="font-medium text-gray-900 dark:text-gray-100">
                          Invoice #{invoice.invoice_number || 'N/A'}
                        </h6>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          invoice.status === 'paid'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                            : invoice.status === 'overdue'
                            ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                        }`}>
                          { formatStatus(invoice.status)}
                        </span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <div>
                          <span className="font-medium">Amount:</span> ${invoice.amount || '0.00'}
                        </div>
                        <div>
                          <span className="font-medium">Created:</span> {
                            invoice.created_at
                              ? new Date(invoice.created_at).toLocaleDateString()
                              : 'N/A'
                          }
                        </div>
                        <div>
                          <span className="font-medium">Due Date:</span> {
                            invoice.due_date
                              ? new Date(invoice.due_date).toLocaleDateString()
                              : 'N/A'
                          }
                        </div>
                      </div>

                      {invoice.description && (
                        <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                          <span className="font-medium">Description:</span> {invoice.description}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Generate Invoice Form */}
      {showGenerateForm && (
        <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border overflow-hidden">
          <h5 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Generate Invoice
          </h5>

          {loadingDefaults ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
              <span className="text-gray-600 dark:text-gray-400">Loading default values...</span>
            </div>
          ) : (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description <span className="text-red-500">*</span>
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100"
                  placeholder="License application processing fee..."
                />
              </div>

              {/* Invoice Items */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Invoice Items
                  </label>
                  <button
                    type="button"
                    onClick={addInvoiceItem}
                    className="px-3 py-1 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    <i className="ri-add-line mr-1"></i>
                    Add Item
                  </button>
                </div>

                <div className="space-y-3 overflow-x-auto">
                  {formData.items.map((item, index) => (
                    <div key={item.item_id} className="p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 min-w-0">
                      {/* Mobile Layout */}
                      <div className="block md:hidden space-y-3">
                        <div>
                          <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                            Description
                          </label>
                          <input
                            type="text"
                            value={item.description}
                            onChange={(e) => updateInvoiceItem(index, 'description', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                            placeholder="Item description"
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                              Quantity
                            </label>
                            <input
                              type="number"
                              min="1"
                              value={item.quantity}
                              onChange={(e) => updateInvoiceItem(index, 'quantity', parseInt(e.target.value) || 1)}
                              className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                            />
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                              Unit Price
                            </label>
                            <input
                              type="number"
                              step="0.01"
                              min="0"
                              value={item.unit_price}
                              onChange={(e) => updateInvoiceItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                              className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                            />
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            Subtotal: ${(item.quantity * item.unit_price).toFixed(2)}
                          </span>
                          <button
                            type="button"
                            onClick={() => removeInvoiceItem(index)}
                            className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-1 focus:ring-red-500"
                          >
                            <i className="ri-delete-bin-line text-xs mr-1"></i>
                            Remove
                          </button>
                        </div>
                      </div>

                      {/* Desktop Layout */}
                      <div className="hidden md:block">
                        <div className="grid grid-cols-12 gap-3 items-end">
                          <div className="col-span-5">
                            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                              Description
                            </label>
                            <input
                              type="text"
                              value={item.description}
                              onChange={(e) => updateInvoiceItem(index, 'description', e.target.value)}
                              className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                              placeholder="Item description"
                            />
                          </div>
                          <div className="col-span-2">
                            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                              Qty
                            </label>
                            <input
                              type="number"
                              min="1"
                              value={item.quantity}
                              onChange={(e) => updateInvoiceItem(index, 'quantity', parseInt(e.target.value) || 1)}
                              className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                            />
                          </div>
                          <div className="col-span-2">
                            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                              Price
                            </label>
                            <input
                              type="number"
                              step="0.01"
                              min="0"
                              value={item.unit_price}
                              onChange={(e) => updateInvoiceItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                              className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                            />
                          </div>
                          <div className="col-span-2">
                            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                              Subtotal
                            </label>
                            <div className="px-2 py-1 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 rounded border">
                              ${(item.quantity * item.unit_price).toFixed(2)}
                            </div>
                          </div>
                          <div className="col-span-1">
                            <button
                              type="button"
                              onClick={() => removeInvoiceItem(index)}
                              className="w-full px-2 py-1 bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-1 focus:ring-red-500"
                              title="Remove item"
                            >
                              <i className="ri-delete-bin-line text-xs"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Total Amount */}
                <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold text-blue-800 dark:text-blue-200">
                      Total Amount:
                    </span>
                    <span className="text-xl font-bold text-blue-900 dark:text-blue-100">
                      ${formData.items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0).toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowGenerateForm(false);
                    setFormData({ amount: '', description: '', items: [] });
                    setErrorMessage(null); // Clear any errors
                    setSuccessMessage(null); // Clear any success messages
                  }}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleGenerateInvoice}
                  disabled={generating || !formData.description || formData.items.length === 0}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {generating ? (
                    <>
                      <i className="ri-loader-4-line animate-spin mr-2"></i>
                      Generating...
                    </>
                  ) : (
                    <>
                      <i className="ri-save-line mr-2"></i>
                      Generate Invoice and Close evaluation task
                    </>
                  )}
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default InvoiceStatusCard;
