import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LicenseTypesController } from './license-types.controller';
import { LicenseTypeByCodeController } from './license-type-by-code.controller';
import { LicenseTypesService } from './license-types.service';
import { LicenseTypes } from '../entities/license-types.entity';

@Module({
  imports: [TypeOrmModule.forFeature([LicenseTypes])],
  controllers: [LicenseTypesController, LicenseTypeByCodeController],
  providers: [LicenseTypesService],
  exports: [LicenseTypesService],
})
export class LicenseTypesModule {}
