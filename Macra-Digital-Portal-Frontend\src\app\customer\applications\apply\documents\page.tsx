'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import ApplicationLayout from '@/components/applications/ApplicationLayout';
import DocumentPreviewModal from '@/components/documents/DocumentPreviewModal';
import { useAuth } from '@/contexts/AuthContext';
import FileUpload from '@/components/forms/FileUpload';
import { applicationService } from '@/services/applicationService';
import { documentService } from '@/services/documentService';
import { licenseCategoryDocumentService } from '@/services/licenseCategoryDocumentService';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { Application, LicenseCategoryDocument, Document } from '@/types';

const DocumentsPage: React.FC = () => {
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // URL parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Document data
  const [requiredDocuments, setRequiredDocuments] = useState<LicenseCategoryDocument[]>([]);
  const [uploadedDocuments, setUploadedDocuments] = useState<Document[]>([]);
  const [documentFiles, setDocumentFiles] = useState<Record<string, File>>({});
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [application, setApplication] = useState<Application | null>(null);

  // Preview modal state
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);

  // Dynamic navigation hook
  const {
    handleNext: dynamicHandleNext,
    handlePrevious: dynamicHandlePrevious,
    nextStep
  } = useDynamicNavigation({
    currentStepRoute: 'documents',
    licenseCategoryId,
    applicationId
  });

  // Load required documents and existing uploads
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !licenseCategoryId || !isAuthenticated || authLoading) return;

      const application = await applicationService.getApplication(applicationId);
      setApplication(application)
      
      try {
        setIsLoading(true);
        setError(null);
        let requiredDocsArray: LicenseCategoryDocument[] = [];

        try {
          const requiredDocs = await licenseCategoryDocumentService.getLicenseCategoryDocumentsByCategory(licenseCategoryId);
          requiredDocsArray = Array.isArray(requiredDocs) ? requiredDocs : [];
          setRequiredDocuments(requiredDocsArray);
        } catch (docError: any) {
          console.log('📝 No required documents found for this license category');
          requiredDocsArray = [];
          setRequiredDocuments([]);
        }

        try {
          const data = await documentService.getDocumentsByApplication(applicationId);
          const uploadedDocs = data.data;
          const uploadedDocsArray = Array.isArray(uploadedDocs) ? uploadedDocs : [];
          setUploadedDocuments(uploadedDocsArray);

          if (uploadedDocsArray.length === 0) {
            console.log('📝 No existing documents found - this is normal for new applications');
          }
        } catch (uploadError: any) {
          console.error('❌ Error loading uploaded documents:', uploadError);
          setUploadedDocuments([]);

          if (uploadError.response?.status === 404) {
            console.log('📝 No documents found for this application - this is normal for new applications');
          }
        }

      } catch (err: any) {
        setError('Failed to load documents data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [applicationId, licenseCategoryId, isAuthenticated, authLoading]);

  // Handle file selection
  const handleFileChange = (documentType: string, file: File | null) => {
    setDocumentFiles(prev => {
      const newFiles = { ...prev };
      if (file) {
        newFiles[documentType] = file;
      } else {
        delete newFiles[documentType];
      }
      return newFiles;
    });

    // Clear validation error for this document
    if (validationErrors[documentType]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[documentType];
        return newErrors;
      });
    }
  };

  // Upload a single document
  const uploadDocument = async (documentType: string, file: File): Promise<boolean> => {
    try {
      setUploadProgress(prev => ({ ...prev, [documentType]: 0 }));

      // Map the document name to the correct DocumentType enum value
      const requiredDoc = requiredDocuments.find(doc =>
        doc.name.toLowerCase().replace(/\s+/g, '_') === documentType
      );
      const mappedDocumentType = requiredDoc ?
        documentService.mapDocumentNameToType(requiredDoc.name) :
        documentType;

      const uploadData = {
        document_type: mappedDocumentType,
        entity_type: 'application',
        entity_id: applicationId!,
        is_required: requiredDoc?.is_required || false
      };

      const result = await documentService.uploadDocument(file, uploadData);
      setUploadProgress(prev => ({ ...prev, [documentType]: 100 }));
      setUploadedDocuments(prev => [...prev, result.document]);

      // Remove from pending files
      setDocumentFiles(prev => {
        const newFiles = { ...prev };
        delete newFiles[documentType];
        return newFiles;
      });

      return true;

    } catch (error: any) {
      console.error('❌ Error uploading document:', error);
      setValidationErrors(prev => ({
        ...prev,
        [documentType]: 'Failed to upload document. Please try again.'
      }));
      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[documentType];
        return newProgress;
      });
      return false;
    }
  };

  // Save all documents
  const handleSave = async (): Promise<boolean> => {
    if (!applicationId) {
      setValidationErrors({ save: 'Application ID is required' });
      return false;
    }

    setIsSaving(true);
    try {
      setValidationErrors({});

      // Upload all pending documents
      if (Object.keys(documentFiles).length > 0) {
        const uploadPromises = Object.entries(documentFiles).map(([docType, file]) =>
          uploadDocument(docType, file)
        );

        const uploadResults = await Promise.all(uploadPromises);
        const allUploaded = uploadResults.every(result => result);

        if (!allUploaded) {
          throw new Error('Some documents failed to upload');
        }
      }

      // Update application progress
      try {
        await applicationService.updateApplication(applicationId, {
          current_step: 7,
          progress_percentage: 86
        });
      } catch (progressError) {
        console.warn('Failed to update application progress:', progressError);
      }

      return true;

    } catch (error: any) {
      console.error('❌ Error saving documents:', error);
      setValidationErrors({ save: 'Failed to process documents. Please try again.' });
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  // Navigation functions using dynamic navigation
  const handleNext = async () => {
    await dynamicHandleNext(handleSave);
  };

  const handlePrevious = () => {
    dynamicHandlePrevious();
  };

  // Remove uploaded document
  const handleRemoveDocument = async (documentId: string) => {
    try {
      await documentService.deleteDocument(documentId);
      setUploadedDocuments(prev => prev.filter(doc => doc.document_id !== documentId));
    } catch (error) {
      console.error('❌ Error removing document:', error);
      setValidationErrors(prev => ({
        ...prev,
        remove: 'Failed to remove document. Please try again.'
      }));
    }
  };

  // Create comprehensive document list combining required and uploaded documents
  const getComprehensiveDocumentList = () => {
    const documentMap = new Map();

    // Add all required documents first
    if (Array.isArray(requiredDocuments)) {
      requiredDocuments.forEach(reqDoc => {
        const docType = reqDoc.name.toLowerCase().replace(/\s+/g, '_');
        documentMap.set(docType, {
          type: 'required',
          requiredDoc: reqDoc,
          uploadedDoc: null,
          docType,
          isRequired: reqDoc.is_required,
          isUploaded: false
        });
      });
    }

    // Add/update with uploaded documents
    uploadedDocuments.forEach(uploadedDoc => {
      const docType = uploadedDoc.document_type;
      const existing = documentMap.get(docType);

      if (existing) {
        // Update existing required document with uploaded info
        documentMap.set(docType, {
          ...existing,
          uploadedDoc,
          isUploaded: true
        });
      } else {
        // Add uploaded document that's not in required list
        documentMap.set(docType, {
          type: 'uploaded',
          requiredDoc: null,
          uploadedDoc,
          docType,
          isRequired: false,
          isUploaded: true
        });
      }
    });

    return Array.from(documentMap.values());
  };

  const comprehensiveDocumentList = getComprehensiveDocumentList();

  // Handle document preview with modal
  const handlePreviewDocument = (doc: Document) => {
    setSelectedDocument(doc);
    setIsPreviewModalOpen(true);
  };

  // Handle closing preview modal
  const handleClosePreview = () => {
    setIsPreviewModalOpen(false);
    setSelectedDocument(null);
  };

  if (authLoading || isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading required documents...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Error Loading Documents</h3>
                <p className="text-red-700 dark:text-red-300 mt-1">{error}</p>
                <button
                  onClick={() => dynamicHandlePrevious()}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40"
                >
                  <i className="ri-arrow-left-line mr-2"></i>
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <ApplicationLayout
        onNext={handleNext}
        onPrevious={handlePrevious}
        onSave={handleSave}
        showNextButton={true}
        showPreviousButton={true}
        showSaveButton={true}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText="Back to Previous Step"
        saveButtonText="Save Changes"
        nextButtonDisabled={false}
        isSaving={isSaving}
      >
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Document Upload
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Upload required documents for your license application.
          </p>
          {application && (
            <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                <i className="ri-file-upload-line mr-1"></i>
                Application: {application.application_number}
              </p>
            </div>
          )}
        </div>

        {/* Validation Errors */}
        {Object.keys(validationErrors).length > 0 && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">Please fix the following errors:</h3>
            <ul className="text-sm text-red-700 dark:text-red-300 list-disc list-inside">
              {Object.entries(validationErrors).map(([field, error]) => (
                <li key={field}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* File Upload Info */}
        <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
          <p className="text-sm font-medium flex items-center text-yellow-700 dark:text-yellow-300">
            <i className="ri-information-line text-lg mr-2"></i>
            <span>Accepted formats: PDF, DOC, DOCX, JPG, PNG. Maximum file size: <span className="font-bold">10MB</span> per document.</span>
          </p>
        </div>

        {/* Documents Upload Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          <div className="space-y-6">

            {/* Required Documents */}
            <div className="space-y-4">
              {comprehensiveDocumentList.map((docItem) => {
                const { docType, requiredDoc, uploadedDoc, isUploaded } = docItem;
                const isUploading = uploadProgress[docType] !== undefined;

                return (
                  <div key={requiredDoc?.license_category_document_id || docType} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {requiredDoc?.name || docType}
                          {requiredDoc?.is_required && <span className="text-red-500 ml-1">*</span>}
                        </h4>
                        {isUploaded && uploadedDoc && (
                          <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                            ✅ {uploadedDoc.file_name}
                          </p>
                        )}
                      </div>
                      {isUploaded && uploadedDoc && (
                        <div className="flex items-center space-x-2">
                          {documentService.isPreviewable(uploadedDoc.mime_type) && (
                            <button
                              onClick={() => handlePreviewDocument(uploadedDoc)}
                              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm"
                            >
                              <i className="ri-eye-line mr-1"></i>
                              Preview
                            </button>
                          )}
                          <button
                            onClick={() => handleRemoveDocument(uploadedDoc.document_id!)}
                            className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 text-sm"
                          >
                            <i className="ri-delete-bin-line mr-1"></i>
                            Remove
                          </button>
                        </div>
                      )}
                    </div>

                    {!isUploaded && (
                      <FileUpload
                        id={`document-${docType}`}
                        label=""
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                        required={false}
                        maxSize={10}
                        value={documentFiles[docType] || null}
                        onChange={(file) => handleFileChange(docType, file)}
                        description={`Upload ${requiredDoc?.name?.toLowerCase() || docType}`}
                      />
                    )}

                    {isUploading && (
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-primary h-2 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress[docType]}%` }}
                        ></div>
                      </div>
                    )}

                    {validationErrors[docType] && (
                      <p className="text-sm text-red-600 dark:text-red-400">
                        {validationErrors[docType]}
                      </p>
                    )}
                  </div>
                );
              })}

            {/* General Supporting Documents */}
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                Additional Supporting Documents
              </h4>
              <FileUpload
                id="general-attachment"
                label=""
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt"
                maxSize={10}
                value={documentFiles['general_attachment'] || null}
                onChange={(file) => handleFileChange('general_attachment', file)}
                description="Upload any additional supporting documents"
              />
            </div>
            </div>
          </div>
        </div>

        {/* Upload Summary */}
        {requiredDocuments.length > 0 && (
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">
                Required: <span className="font-medium text-gray-900 dark:text-gray-100">{requiredDocuments.filter(doc => doc.is_required).length}</span>
              </span>
              <span className="text-gray-600 dark:text-gray-400">
                Uploaded: <span className="font-medium text-green-600 dark:text-green-400">{uploadedDocuments.length}</span>
              </span>
              <span className="text-gray-600 dark:text-gray-400">
                Pending: <span className="font-medium text-orange-600 dark:text-orange-400">{Object.keys(documentFiles).length}</span>
              </span>
            </div>
          </div>
        )}

      </ApplicationLayout>

      {/* Document Preview Modal */}
      {selectedDocument && selectedDocument.document_id && (
        <DocumentPreviewModal
          document={selectedDocument as any}
          isOpen={isPreviewModalOpen}
          onClose={handleClosePreview}
        />
      )}
    </CustomerLayout>
  );
};

export default DocumentsPage;
