import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import DepartmentModal from './DepartmentModal';
import { departmentService } from '../../services/departmentService';
import { userService } from '../../services/userService';

// Mock the services
jest.mock('../../services/departmentService');
jest.mock('../../services/userService');

const mockDepartmentService = departmentService as jest.Mocked<typeof departmentService>;
const mockUserService = userService as jest.Mocked<typeof userService>;

const mockUsers = [
  {
    user_id: '1',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+265123456789',
    status: 'active' as const,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    user_id: '2',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+265987654321',
    status: 'active' as const,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
];

const mockDepartment = {
  department_id: 'dept-1',
  code: 'IT',
  name: 'Information Technology',
  description: 'IT Department',
  email: '<EMAIL>',
  manager_id: '1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

describe('DepartmentModal', () => {
  const mockOnClose = jest.fn();
  const mockOnSave = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUserService.getUsers.mockResolvedValue({
      data: mockUsers,
      meta: {
        itemCount: 2,
        totalItems: 2,
        itemsPerPage: 100,
        totalPages: 1,
        currentPage: 1,
      },
      links: {
        first: '',
        previous: '',
        next: '',
        last: '',
      },
    });
  });

  it('should load users when modal opens', async () => {
    render(
      <DepartmentModal
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    );

    await waitFor(() => {
      expect(mockUserService.getUsers).toHaveBeenCalledWith({
        limit: 100,
        filter: { exclude_customers: true },
      });
    });
  });

  it('should display manager selection dropdown', async () => {
    render(
      <DepartmentModal
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Department Manager')).toBeInTheDocument();
    });
  });

  it('should populate form with existing department data including manager', async () => {
    render(
      <DepartmentModal
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
        department={mockDepartment}
      />
    );

    await waitFor(() => {
      expect(screen.getByDisplayValue('IT')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Information Technology')).toBeInTheDocument();
      expect(screen.getByDisplayValue('IT Department')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    });
  });

  it('should include manager_id when creating new department', async () => {
    mockDepartmentService.createDepartment.mockResolvedValue(mockDepartment);

    render(
      <DepartmentModal
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    );

    // Fill form
    fireEvent.change(screen.getByLabelText('Department Code *'), {
      target: { value: 'HR' },
    });
    fireEvent.change(screen.getByLabelText('Department Name *'), {
      target: { value: 'Human Resources' },
    });
    fireEvent.change(screen.getByLabelText('Description *'), {
      target: { value: 'HR Department' },
    });
    fireEvent.change(screen.getByLabelText('Department Email'), {
      target: { value: '<EMAIL>' },
    });

    // Wait for users to load and select manager
    await waitFor(() => {
      const managerSelect = screen.getByLabelText('Department Manager');
      fireEvent.change(managerSelect, { target: { value: '1' } });
    });

    // Submit form
    fireEvent.click(screen.getByText('Create Department'));

    await waitFor(() => {
      expect(mockDepartmentService.createDepartment).toHaveBeenCalledWith({
        code: 'HR',
        name: 'Human Resources',
        description: 'HR Department',
        email: '<EMAIL>',
        manager_id: '1',
      });
    });
  });

  it('should include manager_id when updating department', async () => {
    mockDepartmentService.updateDepartment.mockResolvedValue(mockDepartment);

    render(
      <DepartmentModal
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
        department={mockDepartment}
      />
    );

    // Wait for users to load and change manager
    await waitFor(() => {
      const managerSelect = screen.getByLabelText('Department Manager');
      fireEvent.change(managerSelect, { target: { value: '2' } });
    });

    // Submit form
    fireEvent.click(screen.getByText('Update Department'));

    await waitFor(() => {
      expect(mockDepartmentService.updateDepartment).toHaveBeenCalledWith(
        'dept-1',
        {
          code: 'IT',
          name: 'Information Technology',
          description: 'IT Department',
          email: '<EMAIL>',
          manager_id: '2',
        }
      );
    });
  });
});
