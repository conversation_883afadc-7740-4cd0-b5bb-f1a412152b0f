import { useState } from 'react';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import { taskAssignmentService } from '@/services/task-assignment';
import { Task } from '@/types';

interface TaskNavigationInfo {
  task: Task;
  canNavigateToEntity: boolean;
}

export const useTaskNavigation = () => {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  /**
   * Get task navigation information
   */
  const getTaskNavigationInfo = async (taskId: string): Promise<Task | null> => {
    setIsLoading(true);

    try {
      const task = await taskAssignmentService.getTaskById(taskId);
      if (task) {
        return task;
      }
      return null;
    } catch (error: any) {
      toast.error('Failed to load task information');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Get the appropriate view URL for a task without navigating
   */
  const getTaskViewUrl = async (taskId: string): Promise<string | null> => {
    const task = await getTaskNavigationInfo(taskId);

    if (!task) {
      return null;
    }

    // const task  = taskInfo;

    // // If task is related to an application, try to build evaluation URL
    // if (task.entity_type === 'application' && task.entity_id) {
 
    //   try {
    //     const application = await applicationService.getApplication(task.entity_id);
    //     const licenseTypeCode = application.license_category?.license_type?.code;
    //     if (licenseTypeCode) {
    //       return `/applications/${licenseTypeCode}/evaluate/applicant-info?application_id=${application.application_id}&license_category_id=${application.license_category_id}`;
    //     }
    //   } catch (error) {
    //     console.error('Error fetching application details for URL:', error);
    //   }
    //   // Fallback to task details if application fetch fails
    // }

    return `/tasks/${taskId}`;

  };

  /**
   * Open task view in a new tab
   */
  const openTaskViewInNewTab = async (taskId: string) => {
    const url = await getTaskViewUrl(taskId);
    
    if (url) {
      window.open(url, '_blank');
    } else {
      toast.error('Unable to determine task view URL');
    }
  };

  /**
   * Open task view in a new tab
   */
  const openTaskView = async (taskId: string) => {
    const url = await getTaskViewUrl(taskId);
    if (url) {
      router.push(url);
    } else {
      toast.error('Unable to determine task view URL');
    }
  };

  /**
   * Get display information for task navigation
   */
  const getTaskDisplayInfo = (taskInfo: TaskNavigationInfo) => {
    const { task } = taskInfo;

    let title = task.title;
    let subtitle = '';
    let icon = '📋';

    // For application tasks, the title usually contains the application number
    if (task.entity_type === 'application') {
      icon = '📄';
      subtitle = 'Application Evaluation';
    }

    switch (task.task_type) {
      case 'application':
      case 'evaluation':
        icon = '📄';
        break;
      case 'complaint':
        icon = '⚠️';
        break;
      case 'data_breach':
        icon = '🔒';
        break;
      case 'inspection':
        icon = '🔍';
        break;
      case 'document_review':
        icon = '📑';
        break;
      default:
        icon = '📋';
        break;
    }

    return {
      title,
      subtitle,
      icon,
      taskType: task.task_type,
      status: task.status,
    };
  };

  return {
    // navigateToTaskView,
    getTaskViewUrl,
    openTaskViewInNewTab,
    getTaskNavigationInfo,
    getTaskDisplayInfo,
    openTaskView,
    isLoading,
  };
};
