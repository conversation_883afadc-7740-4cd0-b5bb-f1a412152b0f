'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useLicenseData } from '@/hooks/useLicenseData';

// Helper function to get icon and styling based on license type
const getLicenseTypeIcon = (name: string) => {
  const nameLower = name.toLowerCase();
  
  if (nameLower.includes('telecommunication')) {
    return {
      icon: 'ri-smartphone-line',
      bgColor: 'bg-blue-500',
      bgLight: 'bg-blue-50',
      textColor: 'text-blue-600',
      borderColor: 'border-blue-200'
    };
  } else if (nameLower.includes('postal')) {
    return {
      icon: 'ri-mail-line',
      bgColor: 'bg-green-500',
      bgLight: 'bg-green-50',
      textColor: 'text-green-600',
      borderColor: 'border-green-200'
    };
  } else if (nameLower.includes('broadcasting')) {
    return {
      icon: 'ri-broadcast-line',
      bgColor: 'bg-purple-500',
      bgLight: 'bg-purple-50',
      textColor: 'text-purple-600',
      borderColor: 'border-purple-200'
    };
  } else if (nameLower.includes('spectrum')) {
    return {
      icon: 'ri-radio-line',
      bgColor: 'bg-orange-500',
      bgLight: 'bg-orange-50',
      textColor: 'text-orange-600',
      borderColor: 'border-orange-200'
    };
  } else if (nameLower.includes('standards') || nameLower.includes('compliance')) {
    return {
      icon: 'ri-shield-check-line',
      bgColor: 'bg-teal-500',
      bgLight: 'bg-teal-50',
      textColor: 'text-teal-600',
      borderColor: 'border-teal-200'
    };
  } else {
    return {
      icon: 'ri-file-text-line',
      bgColor: 'bg-gray-500',
      bgLight: 'bg-gray-50',
      textColor: 'text-gray-600',
      borderColor: 'border-gray-200'
    };
  }
};

const LandingPage: React.FC = () => {
  const { licenseTypes, categories, loading, error, getCategoriesByType } = useLicenseData();

  // Add floating animation styles
  const floatingAnimation = `
    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }
    .floating { animation: float 3s ease-in-out infinite; }
    .floating-delayed { animation: float 3s ease-in-out infinite 1.5s; }
  `;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading license information...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <i className="ri-error-warning-line text-4xl"></i>
          </div>
          <p className="text-gray-600">Failed to load license information</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <style jsx>{floatingAnimation}</style>
      <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center shadow-md p-1">
                <Image 
                  src="/images/macra-logo.png" 
                  alt="MACRA Logo" 
                  width={40} 
                  height={40}
                  className="max-w-full max-h-full object-contain" 
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-red-600">MACRA</h1>
                <p className="text-xs text-red-600 font-medium">Digital Portal</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <Link
                href="/customer/auth/login"
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors"
              >
                Sign In
              </Link>
              <Link
                href="/customer/auth/signup"
                className="px-4 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-red-600 via-red-700 to-red-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Welcome to MACRA
            <span className="block text-red-200">Digital Portal</span>
          </h1>
          <p className="text-xl md:text-2xl text-red-100 max-w-3xl mx-auto">
            Your comprehensive gateway to telecommunications, broadcasting, and postal services licensing, consumer protection, data breach reporting, and procurement opportunities by MACRA.
          </p>
        </div>
      </section>

      {/* License Types Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Available License Types
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Choose from our comprehensive range of licenses to operate legally in Malawi's telecommunications, broadcasting, and postal sectors
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {licenseTypes.map((licenseType) => {
              const iconData = getLicenseTypeIcon(licenseType.name);
              const relatedCategories = getCategoriesByType(licenseType.license_type_id);

              return (
                <div
                  key={licenseType.license_type_id}
                  className={`bg-white rounded-lg shadow-md border ${iconData.borderColor} hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden`}
                >
                  <div className={`${iconData.bgLight} p-4 border-b ${iconData.borderColor}`}>
                    <div className={`w-12 h-12 ${iconData.bgColor} rounded-full flex items-center justify-center mb-3 mx-auto floating`}>
                      <i className={`${iconData.icon} text-lg text-white`}></i>
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 text-center mb-2">
                      {licenseType.name}
                    </h3>
                    <p className="text-gray-600 text-center text-xs">
                      {licenseType.description}
                    </p>
                  </div>

                  <div className="p-4">
                    <div className="mb-4">
                      <span className="text-sm text-gray-500 block mb-2">Available Categories</span>
                      <div className="text-sm text-gray-700">
                        {relatedCategories.length > 0 ? (
                          <span>{relatedCategories.length}</span>
                        ) : (
                          <span>0</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Consumer Services Section */}
      <section className="py-16 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Consumer & Business Services
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Access consumer protection services, data protection support, and procurement opportunities
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
            {/* Consumer Affairs Card */}
            <div className="bg-white rounded-lg shadow-md border border-purple-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden">
              <div className="bg-purple-50 p-4 border-b border-purple-200">
                <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mb-3 mx-auto floating">
                  <i className="ri-customer-service-2-line text-lg text-white"></i>
                </div>
                <h3 className="text-lg font-bold text-gray-900 text-center mb-2">
                  Consumer Affairs
                </h3>
                <p className="text-gray-600 text-center text-xs">
                  Report service quality issues, billing disputes, and other consumer concerns
                </p>
              </div>

              <div className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-xs text-gray-500">Service Type</span>
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-50 text-purple-600">
                    Complaint & Support
                  </span>
                </div>

                <div className="mb-4">
                  <span className="text-xs text-gray-500 block mb-2">Available Services</span>
                  <div className="text-xs text-gray-700">
                    <span>Billing disputes, service quality, consumer rights</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Data Breach Card */}
            <div className="bg-white rounded-lg shadow-md border border-red-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden">
              <div className="bg-red-50 p-4 border-b border-red-200">
                <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center mb-3 mx-auto floating-delayed">
                  <i className="ri-shield-user-line text-lg text-white"></i>
                </div>
                <h3 className="text-lg font-bold text-gray-900 text-center mb-2">
                  Data Breach Reporting
                </h3>
                <p className="text-gray-600 text-center text-xs">
                  Report unauthorized use of personal data and privacy violations
                </p>
              </div>

              <div className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-xs text-gray-500">Service Type</span>
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-50 text-red-600">
                    Data Protection
                  </span>
                </div>

                <div className="mb-4">
                  <span className="text-xs text-gray-500 block mb-2">Available Services</span>
                  <div className="text-xs text-gray-700">
                    <span>Data misuse, privacy violations, unauthorized access</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Procurement Card */}
            <div className="bg-white rounded-lg shadow-md border border-blue-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden">
              <div className="bg-blue-50 p-4 border-b border-blue-200">
                <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mb-3 mx-auto floating">
                  <i className="ri-auction-line text-lg text-white"></i>
                </div>
                <h3 className="text-lg font-bold text-gray-900 text-center mb-2">
                  Procurement & Tenders
                </h3>
                <p className="text-gray-600 text-center text-xs">
                  Access available tenders, submit bids, and participate in MACRA procurement processes
                </p>
              </div>

              <div className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-xs text-gray-500">Service Type</span>
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-600">
                    Business Opportunities
                  </span>
                </div>

                <div className="mb-4">
                  <span className="text-xs text-gray-500 block mb-2">Available Services</span>
                  <div className="text-xs text-gray-700">
                    <span>Tender announcements, bid submissions, procurement notices</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>



      {/* CTA Section */}
      <section className="py-20 bg-red-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-red-100 max-w-2xl mx-auto">
            Join thousands of businesses who trust MACRA for their licensing needs
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div>
              <div className="flex items-center space-x-2 mb-3">
                <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center p-1">
                  <Image 
                    src="/images/macra-logo.png" 
                    alt="MACRA Logo" 
                    width={24} 
                    height={24}
                    className="max-w-full max-h-full object-contain" 
                  />
                </div>
                <div>
                  <h3 className="text-base font-bold text-red-400">MACRA</h3>
                  <p className="text-xs text-gray-400">Digital Portal</p>
                </div>
              </div>
              <p className="text-gray-400 text-xs">
                Malawi Communications Regulatory Authority - Your trusted partner in telecommunications regulation.
              </p>
            </div>

            <div>
              <h4 className="text-base font-semibold mb-3">Services</h4>
              <ul className="space-y-1 text-xs text-gray-400">
                <li><Link href="#" className="hover:text-white transition-colors">Telecommunications</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Broadcasting</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Postal Services</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Standards Compliance</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-base font-semibold mb-3">Support</h4>
              <ul className="space-y-1 text-xs text-gray-400">
                <li><Link href="#" className="hover:text-white transition-colors">Help Center</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Contact Us</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Documentation</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">FAQs</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-base font-semibold mb-3">Contact</h4>
              <div className="space-y-1 text-xs text-gray-400">
                <p className="flex items-center">
                  <i className="ri-phone-line mr-2"></i>
                  +265 1 770 100
                </p>
                <p className="flex items-center">
                  <i className="ri-mail-line mr-2"></i>
                  <EMAIL>
                </p>
                <p className="flex items-center">
                  <i className="ri-map-pin-line mr-2"></i>
                  Lilongwe, Malawi
                </p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-6 pt-6 text-center">
            <p className="text-gray-400 text-xs">
              © {new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
    </>
  );
};

export default LandingPage;
