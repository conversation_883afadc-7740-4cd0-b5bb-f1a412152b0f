import { DocumentType } from '../entities/type-approval-document.entity';

/**
 * Required documents for MACRA type approval process
 */
export const REQUIRED_DOCUMENTS: DocumentType[] = [
  DocumentType.PROOF_OF_PAYMENT,
  DocumentType.ETSI_DOCUMENTS,
  DocumentType.TEST_REPORTS,
  DocumentType.TECHNICAL_SPECIFICATIONS,
  DocumentType.AUTHORIZATION_LETTER,
  DocumentType.DECLARATION_OF_CONFORMITY,
  DocumentType.ITU_REGION_1_APPROVAL,
];

/**
 * Optional documents that may help in analysis
 */
export const OPTIONAL_DOCUMENTS: DocumentType[] = [
  DocumentType.OTHER,
];

/**
 * Document requirements and descriptions
 */
export const DOCUMENT_REQUIREMENTS = {
  [DocumentType.PROOF_OF_PAYMENT]: {
    name: 'Proof of Payment',
    description: 'Receipt or proof of payment for the type approval application fee',
    required: true,
    acceptedFormats: ['pdf', 'jpg', 'jpeg', 'png'],
    maxSizeMB: 10,
  },
  [DocumentType.ETSI_DOCUMENTS]: {
    name: 'ETSI Documents',
    description: 'European Telecommunications Standards Institute documentation',
    required: true,
    acceptedFormats: ['pdf'],
    maxSizeMB: 50,
  },
  [DocumentType.TEST_REPORTS]: {
    name: 'Test Reports from Accredited Labs',
    description: 'Test reports from accredited laboratories showing compliance',
    required: true,
    acceptedFormats: ['pdf'],
    maxSizeMB: 100,
  },
  [DocumentType.TECHNICAL_SPECIFICATIONS]: {
    name: 'Technical Specifications',
    description: 'Detailed technical specifications of the equipment',
    required: true,
    acceptedFormats: ['pdf', 'doc', 'docx'],
    maxSizeMB: 50,
  },
  [DocumentType.AUTHORIZATION_LETTER]: {
    name: 'Authorization Letter (Power of Attorney)',
    description: 'Power of Attorney or authorization letter if applicable',
    required: true,
    acceptedFormats: ['pdf', 'doc', 'docx'],
    maxSizeMB: 10,
  },
  [DocumentType.DECLARATION_OF_CONFORMITY]: {
    name: 'Declaration of Conformity',
    description: 'Declaration of conformity to applicable standards',
    required: true,
    acceptedFormats: ['pdf', 'doc', 'docx'],
    maxSizeMB: 10,
  },
  [DocumentType.ITU_REGION_1_APPROVAL]: {
    name: 'Any Copies of Approval from ITU Region 1',
    description: 'Any copies of approval from ITU Region 1',
    required: true,
    acceptedFormats: ['pdf'],
    maxSizeMB: 20,
  },
  [DocumentType.OTHER]: {
    name: 'Other Supporting Documents',
    description: 'Any other necessary extra documents which will help in analyzing the Model',
    required: false,
    acceptedFormats: ['pdf', 'doc', 'docx', 'jpg', 'png'],
    maxSizeMB: 50,
  },
};

/**
 * MACRA type approval processing constants
 */
export const TYPE_APPROVAL_CONSTANTS = {
  PROCESSING_FEE_USD: 300.00,
  PROCESSING_DAYS: 45,
  CURRENCY: 'USD',
  CERTIFICATE_VALIDITY: 'No expiry period (once off)',
  RECERTIFICATION_TRIGGERS: [
    'Brand Name modification',
    'Model modification',
    'Technical Specifications modification',
  ],
};

/**
 * Validate if all required documents are present
 */
export function validateRequiredDocuments(submittedDocuments: DocumentType[]): {
  isValid: boolean;
  missingDocuments: DocumentType[];
  message: string;
} {
  const missingDocuments = REQUIRED_DOCUMENTS.filter(
    (required) => !submittedDocuments.includes(required)
  );

  const isValid = missingDocuments.length === 0;
  
  let message = '';
  if (!isValid) {
    const missingNames = missingDocuments.map(
      (doc) => DOCUMENT_REQUIREMENTS[doc].name
    );
    message = `Missing required documents: ${missingNames.join(', ')}`;
  } else {
    message = 'All required documents are present';
  }

  return {
    isValid,
    missingDocuments,
    message,
  };
}

/**
 * Validate file format for a document type
 */
export function validateFileFormat(
  documentType: DocumentType,
  fileExtension: string
): boolean {
  const requirements = DOCUMENT_REQUIREMENTS[documentType];
  return requirements.acceptedFormats.includes(fileExtension.toLowerCase());
}

/**
 * Validate file size for a document type
 */
export function validateFileSize(
  documentType: DocumentType,
  fileSizeBytes: number
): boolean {
  const requirements = DOCUMENT_REQUIREMENTS[documentType];
  const maxSizeBytes = requirements.maxSizeMB * 1024 * 1024;
  return fileSizeBytes <= maxSizeBytes;
}

/**
 * Get covering letter requirements for validation
 */
export function getCoveringLetterRequirements(): string[] {
  return [
    'BRAND/TRADE NAME must be clearly indicated',
    'MODEL NUMBER must be clearly indicated',
    'PRODUCT TYPE/NAME must be clearly indicated',
    'MANUFACTURER DETAILS must be provided',
  ];
}

/**
 * Calculate expected completion date based on submission date
 */
export function calculateExpectedCompletionDate(submissionDate: Date): Date {
  const completionDate = new Date(submissionDate);
  
  // Add 45 working days (excluding weekends)
  let workingDaysAdded = 0;
  while (workingDaysAdded < TYPE_APPROVAL_CONSTANTS.PROCESSING_DAYS) {
    completionDate.setDate(completionDate.getDate() + 1);
    
    // Skip weekends (Saturday = 6, Sunday = 0)
    if (completionDate.getDay() !== 0 && completionDate.getDay() !== 6) {
      workingDaysAdded++;
    }
  }
  
  return completionDate;
}

/**
 * Check if recertification is needed based on changes
 */
export function checkRecertificationNeeded(
  originalDevice: any,
  updatedDevice: any
): {
  needed: boolean;
  reasons: string[];
} {
  const reasons: string[] = [];
  
  if (originalDevice.brand_trade_name !== updatedDevice.brand_trade_name) {
    reasons.push('Brand Name modification');
  }
  
  if (originalDevice.model_number !== updatedDevice.model_number) {
    reasons.push('Model modification');
  }
  
  // Add more technical specification checks as needed
  
  return {
    needed: reasons.length > 0,
    reasons,
  };
}
