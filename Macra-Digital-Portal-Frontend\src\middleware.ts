import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

/**
 * Check if a JWT token is expired
 * @param token - JWT token to check
 * @returns true if token is expired, false otherwise
 */
const isTokenExpired = (token: string): boolean => {
  if (!token) return true;

  try {
    // Decode JWT payload (without verification - just for expiry check)
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);

    // Check if token has expired
    return payload.exp < currentTime;
  } catch (error) {
    console.error('Error decoding token:', error);
    return true; // Treat invalid tokens as expired
  }
};

/**
 * Check if user is authorized for admin/staff routes
 * @param user - User object with email and roles
 * @returns true if user is authorized for admin routes, false otherwise
 */
const isAuthorizedForAdminRoutes = (user: any): boolean => {
  if (!user) return false;

  // Check if user email ends with @macra.mw
  const hasValidEmail = user.email && user.email.endsWith('@macra.mw');

  // Check if user has customer role
  const hasStaffRole = user.roles && !user.roles.includes('customer');

  return hasValidEmail || hasStaffRole;
};

export function middleware(request: NextRequest) {
  const url = request.nextUrl.clone();

  // Get auth tokens from cookies
  const authToken = request.cookies.get('auth_token');
  const authUser = request.cookies.get('auth_user');

  // Parse user data if available
  let user = null;


  // Try to get user from customer auth first, then staff auth
  if (authUser) {
    try {
      user = JSON.parse(authUser.value);
    } catch (error) {
      console.error('Failed to parse user data:', error);
    }
  }


  // Always allow auth routes and public routes without redirection
  if (url.pathname.startsWith('/customer/auth/') ||
      url.pathname.startsWith('/auth/') ||
      url.pathname.startsWith('/public/')) {
    return NextResponse.next();
  }

  // Always allow auth routes and public routes without redirection
  if (url.pathname.endsWith('/customer') && !user) {
    return NextResponse.next();
  }

  // Handle root path redirections
  if (url.pathname === '/') {
    if (user && user.roles && user.roles.includes('customer')) {
      // Check if customer token is expired
      if (authToken && isTokenExpired(authToken.value)) {
        url.pathname = '/customer/auth/login';
        return NextResponse.redirect(url);
      } else {
        url.pathname = '/customer';
        return NextResponse.redirect(url);
      }
    } else if (authToken && user && !user.roles.includes('customer')) {
        url.pathname = '/dashboard';
        return NextResponse.redirect(url);
    } else if (authToken && user && user.roles) {
      // Check if token is expired
      if (isTokenExpired(authToken.value)) {
        // Redirect to appropriate login based on user role
        if (user.roles.includes('customer')) {
          url.pathname = '/customer/auth/login';
        } else {
          url.pathname = '/auth/login';
        }
        return NextResponse.redirect(url);
      } else {
        // Redirect to appropriate dashboard based on user role
        if (user.roles.includes('customer')) {
          url.pathname = '/customer';
        } else if (isAuthorizedForAdminRoutes(user)) {
          url.pathname = '/dashboard';
        } else {
          // User is not authorized for admin routes, redirect to customer login
          console.warn('Unauthorized user attempting to access admin routes from root:', {
            email: user.email,
            roles: user.roles
          });
          url.pathname = '/customer/auth/login';
        }
        return NextResponse.redirect(url);
      }
    } else {
      console.log('No auth token or invalid user data')
      // Only redirect if not already on a login page to prevent redirect loops
      if (!url.pathname.startsWith('/customer/auth/') && !url.pathname.startsWith('/auth/')) {
        url.pathname = '/customer/auth/login';
        return NextResponse.redirect(url);
      }
      // If already on auth page, allow through to prevent redirect loops
      return NextResponse.next();
    }
  }


  // Handle customer routes (excluding auth routes which are handled above)
  if (url.pathname.startsWith('/customer') && !url.pathname.startsWith('/customer/auth/')) {
    // Check if user exists and has customer role
    if (!user || !user.roles || !user.roles.includes('customer')) {
      url.pathname = '/customer/auth/login';
      return NextResponse.redirect(url);
    }

    // For other customer routes, check authentication and token expiry
    if (!authToken || !user) {
      url.pathname = '/customer/auth/login';
      return NextResponse.redirect(url);
    }

    // Check if token is expired
    if (isTokenExpired(authToken.value)) {
      url.pathname = '/customer/auth/login';
      return NextResponse.redirect(url);
    }

    // Allow authenticated customer users to access customer portal
    return NextResponse.next();
  }

  // Handle admin/staff dashboard routes
  if (url.pathname.startsWith('/dashboard')) {
    // If user is authenticated and is a customer, redirect to customer portal
    if (user && user.roles && user.roles.includes('customer')) {
      url.pathname = '/customer';
      return NextResponse.redirect(url);
    }

    // If user is not authenticated, check referrer to determine which login page
    if (!user || !user.roles) {
      const referer = request.headers.get('referer');

      // If coming from customer auth pages, redirect to customer login
      if (referer && referer.includes('/customer/auth')) {
        url.pathname = '/customer/auth/login';
        return NextResponse.redirect(url);
      }

      // Default to admin login for dashboard access
      url.pathname = '/auth/login';
      return NextResponse.redirect(url);
    }

    // Check authentication and token expiry for admin users
    if (!authToken) {
      url.pathname = '/auth/login';
      return NextResponse.redirect(url);
    }

    // Check if token is expired
    if (isTokenExpired(authToken.value)) {
      url.pathname = '/auth/login';
      return NextResponse.redirect(url);
    }

    // Extra security check: Verify user is authorized for admin routes
    if (!isAuthorizedForAdminRoutes(user)) {
      console.warn('Unauthorized access attempt to admin routes:', {
        email: user.email,
        roles: user.roles,
        path: url.pathname
      });
      url.pathname = '/auth/login';
      return NextResponse.redirect(url);
    }

    // Allow authenticated and authorized admin/staff users to access dashboard
    return NextResponse.next();
  }

  // Handle dashboard route specifically (root path is handled above)
  if (url.pathname === '/dashboard') {
    if (!user || !user.roles) {
      // Not authenticated, redirect to admin login
      url.pathname = '/auth/login';
      return NextResponse.redirect(url);
    }

    // Redirect based on user role
    if (user.roles.includes('customer')) {
      url.pathname = '/customer';
      return NextResponse.redirect(url);
    } else if (isAuthorizedForAdminRoutes(user)) {
      // Authorized admin/staff user - allow access to dashboard
      return NextResponse.next();
    } else {
      // User is not authorized for admin routes
      console.warn('Unauthorized access attempt to dashboard:', {
        email: user.email,
        roles: user.roles,
        path: url.pathname
      });
      url.pathname = '/auth/login';
      return NextResponse.redirect(url);
    }
  }

  // For other routes, allow access
  return NextResponse.next();

}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images (public images)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|images).*)',
  ],
};
