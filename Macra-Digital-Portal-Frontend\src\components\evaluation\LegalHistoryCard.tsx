import React, { useState, useEffect } from 'react';
import DataDisplayCard from './DataDisplayCard';
import { Application } from '@/types/license';
import { legalHistoryService } from '@/services/legalHistoryService';

interface LegalHistoryCardProps {
  application: Application | null;
  className?: string;
  showEmptyFields?: boolean;
  defaultCollapsed?: boolean;
}

const LegalHistoryCard: React.FC<LegalHistoryCardProps> = ({
  application,
  className = '',
  showEmptyFields = true,
  defaultCollapsed = false
}) => {
  const [legalHistory, setLegalHistory] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchLegalHistory = async () => {
      if (!application?.application_id) return;

      try {
        setLoading(true);
        const legalHistoryData = await legalHistoryService.getLegalHistoryByApplication(application.application_id);
        setLegalHistory(legalHistoryData ? [legalHistoryData] : []);
      } catch (err) {
        console.warn('Could not load legal history:', err);
        setLegalHistory([]);
      } finally {
        setLoading(false);
      }
    };

    fetchLegalHistory();
  }, [application?.application_id]);

  // Return empty fragment if no application or no legal history
  if (!application || loading || !legalHistory || legalHistory.length == 0) {
    return <></>;
  }

  return (
    <>
      {legalHistory.map((legalData, index) => (
        <DataDisplayCard
          key={index}
          title={`Legal History Information ${legalHistory.length > 1 ? `#${index + 1}` : ''}`}
          icon="ri-scales-line"
          className={className}
          showEmptyFields={showEmptyFields}
          defaultCollapsed={defaultCollapsed}
          creatorEmail={application?.applicant?.email}
          creatorName={application?.applicant?.name}
          showEmailButton={true}
          fields={[
            {
              label: 'Criminal History',
              value: legalData.criminal_history,
              type: 'boolean' as const,
              icon: 'ri-police-car-line'
            },
            ...(legalData.criminal_history && legalData.criminal_details ? [{
              label: 'Criminal History Details',
              value: legalData.criminal_details,
              icon: 'ri-information-line',
              fullWidth: true
            }] : []),
            {
              label: 'Bankruptcy History',
              value: legalData.bankruptcy_history,
              type: 'boolean' as const,
              icon: 'ri-bank-line'
            },
            ...(legalData.bankruptcy_history && legalData.bankruptcy_details ? [{
              label: 'Bankruptcy Details',
              value: legalData.bankruptcy_details,
              icon: 'ri-information-line',
              fullWidth: true
            }] : []),
            {
              label: 'Regulatory Actions',
              value: legalData.regulatory_actions,
              type: 'boolean' as const,
              icon: 'ri-government-line'
            },
            ...(legalData.regulatory_actions && legalData.regulatory_details ? [{
              label: 'Regulatory Action Details',
              value: legalData.regulatory_details,
              icon: 'ri-information-line',
              fullWidth: true
            }] : []),
            {
              label: 'Litigation History',
              value: legalData.litigation_history,
              type: 'boolean' as const,
              icon: 'ri-scales-3-line'
            },
            ...(legalData.litigation_history && legalData.litigation_details ? [{
              label: 'Litigation Details',
              value: legalData.litigation_details,
              icon: 'ri-information-line',
              fullWidth: true
            }] : []),
            {
              label: 'Created Date',
              value: legalData.created_at ? new Date(legalData.created_at).toLocaleDateString() : null,
              type: 'date' as const,
              icon: 'ri-calendar-line'
            }
          ]}
        />
      ))}
    </>
  );
};

export default LegalHistoryCard;
