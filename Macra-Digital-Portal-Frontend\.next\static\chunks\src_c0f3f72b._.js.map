{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/invoiceService.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { apiClient } from '@/lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { InvoiceFilters, CreateInvoiceDto, UpdateInvoiceDto, InvoiceItem, Invoice } from '@/types/invoice';\r\n\r\n\r\n\r\nclass InvoiceService {\r\n  private baseUrl = '/invoices';\r\n\r\n  async getInvoices(filters?: InvoiceFilters): Promise<Invoice[]> {\r\n    try {\r\n      const response = await apiClient.get(this.baseUrl, { params: filters });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to fetch invoices:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getInvoiceById(id: string): Promise<Invoice> {\r\n    try {\r\n      const response = await apiClient.get(`${this.baseUrl}/${id}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to fetch invoice:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getInvoicesByEntity(entityType: string, entityId: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`${this.baseUrl}/entity/${entityType}/${entityId}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to fetch invoices for entity:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async createInvoice(data: CreateInvoiceDto): Promise<Invoice> {\r\n    try {\r\n      const response = await apiClient.post(this.baseUrl, data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to create invoice:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async updateInvoice(id: string, data: UpdateInvoiceDto): Promise<Invoice> {\r\n    try {\r\n      const response = await apiClient.put(`${this.baseUrl}/${id}`, data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to update invoice:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async deleteInvoice(id: string): Promise<void> {\r\n    try {\r\n      await apiClient.delete(`${this.baseUrl}/${id}`);\r\n    } catch (error) {\r\n      console.error('Failed to delete invoice:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async sendInvoice(id: string): Promise<Invoice> {\r\n    try {\r\n      const response = await apiClient.post(`${this.baseUrl}/${id}/send`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to send invoice:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async markAsPaid(id: string): Promise<Invoice> {\r\n    try {\r\n      const response = await apiClient.post(`${this.baseUrl}/${id}/mark-paid`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to mark invoice as paid:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Helper method to generate invoice for application\r\n  async generateApplicationInvoice(applicationId: string, data: {\r\n    amount: number;\r\n    description: string;\r\n    items?: InvoiceItem[];\r\n  }): Promise<Invoice> {\r\n    try {\r\n      const response = await apiClient.post(`${this.baseUrl}/generate/application/${applicationId}`, data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to generate application invoice:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Helper method to get application invoice status\r\n  async getApplicationInvoiceStatus(applicationId: string): Promise<{\r\n    hasInvoice: boolean;\r\n    invoice?: Invoice;\r\n    status?: 'paid' | 'pending' | 'overdue' | 'none';\r\n  }> {\r\n    try {\r\n      const response = await this.getInvoicesByEntity('application', applicationId);\r\n      const invoices: Invoice[]  = response.data || response\r\n      \r\n      if (invoices.length === 0) {\r\n        return { hasInvoice: false, status: 'none' };\r\n      }\r\n\r\n      // Get the most recent invoice\r\n      const sortedInvoices = [...invoices].sort((a, b) =>\r\n        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n      );\r\n      const latestInvoice = sortedInvoices.length > 0 ? sortedInvoices[0] : null;\r\n\r\n      if (!latestInvoice) {\r\n        return { hasInvoice: false, status: 'none' };\r\n      }\r\n\r\n      let status: 'paid' | 'pending' | 'overdue' = 'pending';\r\n      \r\n      if (latestInvoice.status === 'paid') {\r\n        status = 'paid';\r\n      } else if (latestInvoice.status === 'overdue') {\r\n        status = 'overdue';\r\n      } else if (latestInvoice.status === 'sent' || latestInvoice.status === 'draft') {\r\n        // Check if overdue\r\n        const dueDate = new Date(latestInvoice.due_date);\r\n        const now = new Date();\r\n        if (now > dueDate) {\r\n          status = 'overdue';\r\n        } else {\r\n          status = 'pending';\r\n        }\r\n      }\r\n\r\n      return {\r\n        hasInvoice: true,\r\n        invoice: latestInvoice,\r\n        status\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to get application invoice status:', error);\r\n      return { hasInvoice: false, status: 'none' };\r\n    }\r\n  }\r\n\r\n  // Helper method to get application details for invoice generation\r\n  async getApplicationDetailsForInvoice(applicationId: string): Promise<{\r\n    application: any;\r\n    defaultInvoiceData: {\r\n      amount: number;\r\n      description: string;\r\n      items: InvoiceItem[];\r\n    };\r\n  }> {\r\n    try {\r\n      const response = await apiClient.get(`${this.baseUrl}/application/${applicationId}/details`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to get application details for invoice:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\nexport const invoiceService = new InvoiceService();\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAQA,MAAM;IAGJ,MAAM,YAAY,OAAwB,EAAsB;QAC9D,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE;gBAAE,QAAQ;YAAQ;YACrE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,MAAM,eAAe,EAAU,EAAoB;QACjD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAM,OAAH;YACxD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB,UAAkB,EAAE,QAAgB,EAAgB;QAC5E,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAyB,OAAvB,IAAI,CAAC,OAAO,EAAC,YAAwB,OAAd,YAAW,KAAY,OAAT;YAC7E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR;IACF;IAEA,MAAM,cAAc,IAAsB,EAAoB;QAC5D,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACpD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,IAAsB,EAAoB;QACxE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAM,OAAH,KAAM;YAC9D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,MAAM,cAAc,EAAU,EAAiB;QAC7C,IAAI;YACF,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAM,OAAH;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,MAAM,YAAY,EAAU,EAAoB;QAC9C,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAM,OAAH,IAAG;YAC5D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAM,OAAH,IAAG;YAC5D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA,oDAAoD;IACpD,MAAM,2BAA2B,aAAqB,EAAE,IAIvD,EAAoB;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,AAAC,GAAuC,OAArC,IAAI,CAAC,OAAO,EAAC,0BAAsC,OAAd,gBAAiB;YAC/F,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM;QACR;IACF;IAEA,kDAAkD;IAClD,MAAM,4BAA4B,aAAqB,EAIpD;QACD,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,mBAAmB,CAAC,eAAe;YAC/D,MAAM,WAAuB,SAAS,IAAI,IAAI;YAE9C,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,OAAO;oBAAE,YAAY;oBAAO,QAAQ;gBAAO;YAC7C;YAEA,8BAA8B;YAC9B,MAAM,iBAAiB;mBAAI;aAAS,CAAC,IAAI,CAAC,CAAC,GAAG,IAC5C,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;YAEnE,MAAM,gBAAgB,eAAe,MAAM,GAAG,IAAI,cAAc,CAAC,EAAE,GAAG;YAEtE,IAAI,CAAC,eAAe;gBAClB,OAAO;oBAAE,YAAY;oBAAO,QAAQ;gBAAO;YAC7C;YAEA,IAAI,SAAyC;YAE7C,IAAI,cAAc,MAAM,KAAK,QAAQ;gBACnC,SAAS;YACX,OAAO,IAAI,cAAc,MAAM,KAAK,WAAW;gBAC7C,SAAS;YACX,OAAO,IAAI,cAAc,MAAM,KAAK,UAAU,cAAc,MAAM,KAAK,SAAS;gBAC9E,mBAAmB;gBACnB,MAAM,UAAU,IAAI,KAAK,cAAc,QAAQ;gBAC/C,MAAM,MAAM,IAAI;gBAChB,IAAI,MAAM,SAAS;oBACjB,SAAS;gBACX,OAAO;oBACL,SAAS;gBACX;YACF;YAEA,OAAO;gBACL,YAAY;gBACZ,SAAS;gBACT;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;gBAAE,YAAY;gBAAO,QAAQ;YAAO;QAC7C;IACF;IAEA,kEAAkE;IAClE,MAAM,gCAAgC,aAAqB,EAOxD;QACD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAA8B,OAA5B,IAAI,CAAC,OAAO,EAAC,iBAA6B,OAAd,eAAc;YAClF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kDAAkD;YAChE,MAAM;QACR;IACF;;QApKA,+KAAQ,WAAU;;AAqKpB;AAEO,MAAM,iBAAiB,IAAI", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';\r\n\r\nexport const applicationService = {\r\n  // Get all applications with pagination and filters\r\n  async getApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filters?: ApplicationFilters;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    \r\n    // Add filters\r\n    if (params?.filters?.licenseTypeId) {\r\n      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);\r\n    }\r\n    if (params?.filters?.licenseCategoryId) {\r\n      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);\r\n    }\r\n    if (params?.filters?.status) {\r\n      queryParams.append('filter.status', params.filters.status);\r\n    }\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by license type (through license category)\r\n  async getApplicationsByLicenseType(licenseTypeId: string, params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    status?: ApplicationStatus;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    \r\n    // Filter by license type through license category\r\n    queryParams.append('filter.license_category.license_type_id', licenseTypeId);\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get single application by ID\r\n  async getApplication(id: string): Promise<Application> {\r\n    const response = await apiClient.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by applicant\r\n  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by status\r\n  async getApplicationsByStatus(status: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-status/${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application status\r\n  async updateApplicationStatus(id: string, status: string): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application progress\r\n  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {\r\n    const response = await apiClient.put(\r\n      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`\r\n    );\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get application statistics\r\n  async getApplicationStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/applications/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application\r\n  async createApplication(data: {\r\n    application_number: string;\r\n    applicant_id: string;\r\n    license_category_id: string;\r\n    status?: ApplicationStatus;\r\n    current_step?: number;\r\n    progress_percentage?: number;\r\n    submitted_at?: Date;\r\n  }): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.post('/applications', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application with improved error handling\r\n  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {\r\n    try {\r\n      console.log('Updating application:', id, 'with data:', data);\r\n      const response = await apiClient.put(`/applications/${id}`, data, {\r\n        timeout: 30000, // 30 second timeout\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('Error updating application:', error);\r\n\r\n      // Handle specific error cases\r\n      if (error.code === 'ECONNABORTED') {\r\n        throw new Error('Request timeout - please try again');\r\n      }\r\n\r\n      if (error.response?.status === 400) {\r\n        const message = error.response?.data?.message || 'Invalid application data';\r\n        console.error('400 Bad Request details:', error.response?.data);\r\n        throw new Error(`Bad Request: ${message}`);\r\n      }\r\n\r\n      if (error.response?.status === 429) {\r\n        throw new Error('Too many requests - please wait a moment and try again');\r\n      }\r\n\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete application\r\n  async deleteApplication(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application with applicant data\r\n  async createApplicationWithApplicant(data: {\r\n    license_type_id: string;\r\n    license_category_id: string;\r\n    applicant_data: Record<string, any>;\r\n    user_id: string;\r\n  }): Promise<Application> {\r\n    try {\r\n      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)\r\n      const now = new Date();\r\n      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');\r\n      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');\r\n      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();\r\n      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;\r\n\r\n      // Validate user_id is a proper UUID\r\n      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n      if (!uuidRegex.test(data.user_id)) {\r\n        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);\r\n      }\r\n\r\n      // Create application using user_id as applicant_id\r\n      // In most systems, the authenticated user is the applicant\r\n      const application = await this.createApplication({\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id, // Use user_id as applicant_id\r\n        license_category_id: data.license_category_id,\r\n        current_step: 1,\r\n        progress_percentage: 0 // Start with 0% progress\r\n      });\r\n\r\n      return application;\r\n    } catch (error) {\r\n      console.error('Error creating application with applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application section data\r\n  async saveApplicationSection(\r\n    applicationId: string,\r\n    sectionName: string,\r\n    sectionData: Record<string, any>\r\n  ): Promise<void> {\r\n    try {\r\n      // Try to save using form data service, but continue if it fails\r\n      let completedSections = 1; // At least one section is being saved\r\n\r\n      // Estimate progress based on section name\r\n      const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'];\r\n      const sectionIndex = sectionOrder.indexOf(sectionName);\r\n      completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;\r\n\r\n      // Calculate progress based on completed sections (excluding reviewSubmit from total)\r\n      const totalSections = 6; // Total number of form sections (excluding reviewSubmit)\r\n      const progressPercentage = Math.min(Math.round((completedSections / totalSections) * 100), 100);\r\n\r\n      // Update the application progress\r\n      await this.updateApplication(applicationId, {\r\n        progress_percentage: progressPercentage,\r\n        current_step: completedSections\r\n      });\r\n\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get application section data\r\n  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Submit application for review\r\n  async submitApplication(applicationId: string): Promise<Application> {\r\n    try {\r\n      // Update application status to submitted and set submission date\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        status: 'submitted',\r\n        submitted_at: new Date().toISOString(),\r\n        progress_percentage: 100,\r\n        current_step: 7\r\n      });\r\n\r\n      console.log('Application submitted successfully:', processApiResponse(response));\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error submitting application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get user's applications (filtered by authenticated user)\r\n  async getUserApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filter?: Record<string, string>;\r\n  }): Promise<any> {\r\n    try {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (params?.page) queryParams.append('page', params.page.toString());\r\n      if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n      if (params?.search) queryParams.append('search', params.search);\r\n      if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n      if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n\r\n      // Add filters\r\n      if (params?.filter) {\r\n        Object.entries(params.filter).forEach(([key, value]) => {\r\n          if (value) queryParams.append(`filter.${key}`, value);\r\n        });\r\n      }\r\n\r\n      const url = `/applications/user-applications${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const response = await apiClient.get(url);\r\n      const processedResponse = processApiResponse(response);\r\n\r\n      // Return paginated response structure for DataTable compatibility\r\n      if (processedResponse?.data && processedResponse?.meta) {\r\n        return processedResponse;\r\n      }\r\n\r\n      // Handle legacy non-paginated response\r\n      let applications = [];\r\n      if (processedResponse?.data) {\r\n        applications = Array.isArray(processedResponse.data) ? processedResponse.data : [];\r\n      } else if (Array.isArray(processedResponse)) {\r\n        applications = processedResponse;\r\n      } else if (processedResponse) {\r\n        applications = [processedResponse];\r\n      }\r\n\r\n      // Return in paginated format for backward compatibility\r\n      return {\r\n        data: applications,\r\n        meta: {\r\n          itemsPerPage: params?.limit || 10,\r\n          totalItems: applications.length,\r\n          currentPage: params?.page || 1,\r\n          totalPages: Math.ceil(applications.length / (params?.limit || 10)),\r\n          sortBy: [],\r\n          searchBy: [],\r\n          search: params?.search || '',\r\n          select: [],\r\n        },\r\n        links: { current: '' },\r\n      };\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application as draft\r\n  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        form_data: formData,\r\n        status: 'draft'\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error saving application as draft:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Validate application before submission\r\n  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {\r\n    try {\r\n      // Get data from entity-specific APIs for validation\r\n      let formData: Record<string, any> = {};\r\n\r\n      const errors: any[] = [];\r\n      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];\r\n\r\n      // Check if all required sections are completed\r\n      for (const section of requiredSections) {\r\n        if (!formData[section] || Object.keys(formData[section]).length === 0) {\r\n          errors.push(`${section} section is incomplete`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application status\r\n  async updateStatus(applicationId: string, status: string): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.patch(`/applications/${applicationId}/status`, { status });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error updating application status:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Assign application to an officer\r\n  async assignApplication(applicationId: string, assignedTo: string): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.patch(`/applications/${applicationId}/assign`, { assignedTo });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error assigning application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,qBAAqB;IAChC,mDAAmD;IACnD,MAAM,iBAAgB,MAOrB;YAUK,iBAGA,kBAGA;QAfJ,MAAM,cAAc,IAAI;QAExB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,mBAAA,6BAAA,OAAQ,SAAS,EAAE,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,cAAc;QACd,IAAI,mBAAA,8BAAA,kBAAA,OAAQ,OAAO,cAAf,sCAAA,gBAAiB,aAAa,EAAE;YAClC,YAAY,MAAM,CAAC,2CAA2C,OAAO,OAAO,CAAC,aAAa;QAC5F;QACA,IAAI,mBAAA,8BAAA,mBAAA,OAAQ,OAAO,cAAf,uCAAA,iBAAiB,iBAAiB,EAAE;YACtC,YAAY,MAAM,CAAC,8BAA8B,OAAO,OAAO,CAAC,iBAAiB;QACnF;QACA,IAAI,mBAAA,8BAAA,mBAAA,OAAQ,OAAO,cAAf,uCAAA,iBAAiB,MAAM,EAAE;YAC3B,YAAY,MAAM,CAAC,iBAAiB,OAAO,OAAO,CAAC,MAAM;QAC3D;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAAuC,OAAvB,YAAY,QAAQ;QAC1E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8DAA8D;IAC9D,MAAM,8BAA6B,aAAqB,EAAE,MAKzD;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QAErE,kDAAkD;QAClD,YAAY,MAAM,CAAC,2CAA2C;QAE9D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAAuC,OAAvB,YAAY,QAAQ;QAC1E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAAmB,OAAH;QACtD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,4BAA2B,WAAmB;QAClD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,8BAAyC,OAAZ;QACnE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM,yBAAwB,MAAc;QAC1C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,2BAAiC,OAAP;QAChE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,4BAA4B;IAC5B,MAAM,yBAAwB,EAAU,EAAE,MAAc;QACtD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAAoC,OAApB,IAAG,mBAAwB,OAAP;QAC1E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,2BAA0B,EAAU,EAAE,WAAmB,EAAE,kBAA0B;QACzF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAClC,AAAC,iBAA2C,OAA3B,IAAG,0BAA0D,OAAlC,aAAY,wBAAyC,OAAnB;QAEhF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,mBAAkB,IAQvB;QACC,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,kDAAkD;IAClD,MAAM,mBAAkB,EAAU,EAAE,IAA0B;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC,yBAAyB,IAAI,cAAc;YACvD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAAmB,OAAH,KAAM,MAAM;gBAChE,SAAS;YACX;YACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;gBAQf,iBAMA;YAbJ,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,8BAA8B;YAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;oBAClB,sBAAA,kBAC0B;gBAD1C,MAAM,UAAU,EAAA,mBAAA,MAAM,QAAQ,cAAd,wCAAA,uBAAA,iBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI;gBACjD,QAAQ,KAAK,CAAC,6BAA4B,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,IAAI;gBAC9D,MAAM,IAAI,MAAM,AAAC,gBAAuB,OAAR;YAClC;YAEA,IAAI,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,MAAK,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,iBAAmB,OAAH;QACzD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6CAA6C;IAC7C,MAAM,gCAA+B,IAKpC;QACC,IAAI;YACF,uEAAuE;YACvE,MAAM,MAAM,IAAI;YAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;YAC7D,MAAM,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;YAC7D,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;YACrE,MAAM,oBAAoB,AAAC,OAAiB,OAAX,SAAQ,KAAc,OAAX,SAAQ,KAAa,OAAV;YAEvD,oCAAoC;YACpC,MAAM,YAAY;YAClB,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,GAAG;gBACjC,MAAM,IAAI,MAAM,AAAC,2BAAuC,OAAb,KAAK,OAAO,EAAC;YAC1D;YAEA,mDAAmD;YACnD,2DAA2D;YAC3D,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/C,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,cAAc;gBACd,qBAAqB,EAAE,yBAAyB;YAClD;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,wBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,IAAI;YACF,gEAAgE;YAChE,IAAI,oBAAoB,GAAG,sCAAsC;YAEjE,0CAA0C;YAC1C,MAAM,eAAe;gBAAC;gBAAiB;gBAAkB;gBAAgB;gBAAgB;gBAAgB;gBAAgB;aAAe;YACxI,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,oBAAoB,gBAAgB,IAAI,eAAe,IAAI;YAE3D,qFAAqF;YACrF,MAAM,gBAAgB,GAAG,yDAAyD;YAClF,MAAM,qBAAqB,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB,MAAM;YAE3F,kCAAkC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAC1C,qBAAqB;gBACrB,cAAc;YAChB;QAEF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,uBAAsB,aAAqB,EAAE,WAAmB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAA0C,OAA1B,eAAc,cAAwB,OAAZ;YAChF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAkB,aAAqB;QAC3C,IAAI;YACF,iEAAiE;YACjE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAA8B,OAAd,gBAAiB;gBACrE,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,uCAAuC,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YACtE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,2DAA2D;IAC3D,MAAM,qBAAoB,MAOzB;QACC,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAC9D,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAC9D,IAAI,mBAAA,6BAAA,OAAQ,SAAS,EAAE,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;YAEvE,cAAc;YACd,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE;gBAClB,OAAO,OAAO,CAAC,OAAO,MAAM,EAAE,OAAO,CAAC;wBAAC,CAAC,KAAK,MAAM;oBACjD,IAAI,OAAO,YAAY,MAAM,CAAC,AAAC,UAAa,OAAJ,MAAO;gBACjD;YACF;YAEA,MAAM,MAAM,AAAC,kCAA4F,OAA3D,YAAY,QAAQ,KAAK,AAAC,IAA0B,OAAvB,YAAY,QAAQ,MAAO;YACtG,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,oBAAoB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7C,kEAAkE;YAClE,IAAI,CAAA,8BAAA,wCAAA,kBAAmB,IAAI,MAAI,8BAAA,wCAAA,kBAAmB,IAAI,GAAE;gBACtD,OAAO;YACT;YAEA,uCAAuC;YACvC,IAAI,eAAe,EAAE;YACrB,IAAI,8BAAA,wCAAA,kBAAmB,IAAI,EAAE;gBAC3B,eAAe,MAAM,OAAO,CAAC,kBAAkB,IAAI,IAAI,kBAAkB,IAAI,GAAG,EAAE;YACpF,OAAO,IAAI,MAAM,OAAO,CAAC,oBAAoB;gBAC3C,eAAe;YACjB,OAAO,IAAI,mBAAmB;gBAC5B,eAAe;oBAAC;iBAAkB;YACpC;YAEA,wDAAwD;YACxD,OAAO;gBACL,MAAM;gBACN,MAAM;oBACJ,cAAc,CAAA,mBAAA,6BAAA,OAAQ,KAAK,KAAI;oBAC/B,YAAY,aAAa,MAAM;oBAC/B,aAAa,CAAA,mBAAA,6BAAA,OAAQ,IAAI,KAAI;oBAC7B,YAAY,KAAK,IAAI,CAAC,aAAa,MAAM,GAAG,CAAC,CAAA,mBAAA,6BAAA,OAAQ,KAAK,KAAI,EAAE;oBAChE,QAAQ,EAAE;oBACV,UAAU,EAAE;oBACZ,QAAQ,CAAA,mBAAA,6BAAA,OAAQ,MAAM,KAAI;oBAC1B,QAAQ,EAAE;gBACZ;gBACA,OAAO;oBAAE,SAAS;gBAAG;YACvB;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAY,aAAqB,EAAE,QAA6B;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAA8B,OAAd,gBAAiB;gBACrE,WAAW;gBACX,QAAQ;YACV;YACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAoB,aAAqB;QAC7C,IAAI;YACF,oDAAoD;YACpD,IAAI,WAAgC,CAAC;YAErC,MAAM,SAAgB,EAAE;YACxB,MAAM,mBAAmB;gBAAC;gBAAiB;gBAAkB;gBAAgB;aAAe;YAE5F,+CAA+C;YAC/C,KAAK,MAAM,WAAW,iBAAkB;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG;oBACrE,OAAO,IAAI,CAAC,AAAC,GAAU,OAAR,SAAQ;gBACzB;YACF;YAEA,OAAO;gBACL,SAAS,OAAO,MAAM,KAAK;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,cAAa,aAAqB,EAAE,MAAc;QACtD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,AAAC,iBAA8B,OAAd,eAAc,YAAU;gBAAE;YAAO;YACzF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,mCAAmC;IACnC,MAAM,mBAAkB,aAAqB,EAAE,UAAkB;QAC/D,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,AAAC,iBAA8B,OAAd,eAAc,YAAU;gBAAE;YAAW;YAC7F,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/paymentService.ts"], "sourcesContent": ["import { customerApi } from '@/lib/customer-api';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { invoiceService } from './invoiceService';\r\nimport { applicationService } from './applicationService';\r\nimport { Payment, PaymentFilters, PaymentStatistics } from '@/types/invoice';\r\nimport { PaginatedResponse, PaginateQuery } from '@/types';\r\nimport { apiClient } from '@/lib';\r\n\r\nclass PaymentService {\r\n\r\n\r\n  // Helper method to get user application IDs\r\n  private async getUserApplicationIds(): Promise<string[]> {\r\n    try {\r\n      console.log('📋 Fetching user applications...');\r\n      const applicationsPromise = applicationService.getUserApplications();\r\n      const timeoutPromise = new Promise((_, reject) =>\r\n        setTimeout(() => reject(new Error('Applications fetch timeout')), 10000)\r\n      );\r\n\r\n      const userApplications = await Promise.race([applicationsPromise, timeoutPromise]);\r\n      const applicationIds = userApplications.data?.map((app: any) => app.application_id) || [];\r\n      console.log(`📋 Found ${applicationIds.length} user applications`);\r\n      return applicationIds;\r\n    } catch (error) {\r\n      console.warn('Failed to fetch user applications:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  // Helper method to create empty response\r\n  private createEmptyResponse(query: PaginateQuery & PaymentFilters): PaginatedResponse<Payment> {\r\n    return {\r\n      data: [],\r\n      meta: {\r\n        itemsPerPage: query.limit || 10,\r\n        totalItems: 0,\r\n        currentPage: query.page || 1,\r\n        totalPages: 0,\r\n        sortBy: [],\r\n        searchBy: [],\r\n        search: query.search || '',\r\n        select: [],\r\n      },\r\n      links: { current: '' },\r\n    };\r\n  }\r\n\r\n  // Helper method to apply filters\r\n  private applyFilters(payments: any[], query: PaginateQuery & PaymentFilters): any[] {\r\n    let filteredPayments = payments;\r\n\r\n    if (query.status) {\r\n      const allowedStatuses = query.status.split(',').map((s: string) => s.trim().toUpperCase());\r\n      filteredPayments = filteredPayments.filter(payment =>\r\n        allowedStatuses.includes(payment.status.toUpperCase())\r\n      );\r\n    }\r\n\r\n    if (query.payment_type) {\r\n      filteredPayments = filteredPayments.filter(payment =>\r\n        payment.payment_type === query.payment_type\r\n      );\r\n    }\r\n\r\n    if (query.search) {\r\n      const searchTerm = query.search.toLowerCase();\r\n      filteredPayments = filteredPayments.filter(payment =>\r\n        payment.invoice_number?.toLowerCase().includes(searchTerm) ||\r\n        payment.description?.toLowerCase().includes(searchTerm) ||\r\n        payment.payment_type?.toLowerCase().includes(searchTerm)\r\n      );\r\n    }\r\n\r\n    if (query.dateRange && typeof query.dateRange === 'object') {\r\n      const { start, end } = query.dateRange as { start?: string; end?: string };\r\n      if (start || end) {\r\n        filteredPayments = filteredPayments.filter(payment => {\r\n          const paymentDate = new Date(payment.created_at);\r\n          if (start && paymentDate < new Date(start)) return false;\r\n          if (end && paymentDate > new Date(end)) return false;\r\n          return true;\r\n        });\r\n      }\r\n    }\r\n\r\n    return filteredPayments;\r\n  }\r\n\r\n\r\n  // Helper method to get payments for applications\r\n  private async getPaymentsForApplications(applicationIds: string[]): Promise<any[]> {\r\n    const allPayments: any[] = [];\r\n\r\n    for (const applicationId of applicationIds) {\r\n      try {\r\n        console.log(`📄 Getting invoices for application: ${applicationId}`);\r\n        const response = await invoiceService.getInvoicesByEntity('application', applicationId);\r\n        const invoices = processApiResponse(response);\r\n        return invoices;\r\n      } catch (error) {\r\n        console.warn(`⚠️ Could not get invoices for application ${applicationId}:`, error);\r\n      }\r\n    }\r\n\r\n    return allPayments;\r\n  }\r\n  async getPayments(query: PaginateQuery & PaymentFilters, isCustomer?: boolean): Promise<PaginatedResponse<Payment>> {\r\n    try {\r\n      const params = this.buildQueryParams(query);\r\n      if (isCustomer) {\r\n        // Use customer-specific endpoint for customers\r\n        console.log('🔒 Using customer-specific payments endpoint');\r\n        return customerApi.getPayments(params);\r\n      } else {\r\n        // Use general payments endpoint for admin/staff (shows all payments)\r\n        const response = await apiClient.get('/payments', { params });\r\n        return processApiResponse(response);\r\n      }\r\n    } catch (error: any) {\r\n      // Handle specific error cases\r\n      if (error.response?.status === 403) {\r\n        return await this.getPaymentsByApplications(query);\r\n      }\r\n\r\n      if (error.response?.status === 404) {\r\n        return await this.getPaymentsByApplications(query);\r\n      }\r\n      return await this.getPaymentsByApplications(query);\r\n    }\r\n  }\r\n\r\n  // Helper method to build query parameters\r\n  private buildQueryParams(query: PaginateQuery & PaymentFilters): any {\r\n    const params: any = {};\r\n    if (query.page) params.page = query.page;\r\n    if (query.limit) params.limit = query.limit;\r\n    if (query.status) params.status = query.status;\r\n    if (query.payment_type) params.paymentType = query.payment_type;\r\n    if (query.search) params.search = query.search;\r\n    if (query.dateRange) params.dateRange = query.dateRange;\r\n    return params;\r\n  }\r\n\r\n  private async getPaymentsByApplications(query: PaginateQuery & PaymentFilters): Promise<PaginatedResponse<Payment>> {\r\n    try {\r\n      // Get user's applications to filter payments\r\n      const applicationIds = await this.getUserApplicationIds();\r\n\r\n      if (applicationIds.length === 0) {\r\n        console.log('ℹ️ No applications found for user, returning empty payments');\r\n        return this.createEmptyResponse(query);\r\n      }\r\n\r\n      // Get payments for user's applications\r\n      const allPayments = await this.getPaymentsForApplications(applicationIds);\r\n      console.log(`📊 Total payments found: ${allPayments.length}`);\r\n      return processApiResponse(allPayments);\r\n    } catch (error) {\r\n      console.error('❌ Error getting user payments:', error);\r\n      // Return empty data if API is not available\r\n      return this.createEmptyResponse(query);\r\n    }\r\n  }\r\n\r\n  async getInvoices(query: PaginateQuery & PaymentFilters, isCustomer: boolean = false): Promise<PaginatedResponse<Payment>> {\r\n\r\n\r\n    try {\r\n      const params: any = {};\r\n      if (query.page) params.page = query.page;\r\n      if (query.limit) params.limit = query.limit;\r\n      // For invoices, we want pending and overdue statuses\r\n      params.status = query.status || 'pending';\r\n      if (query.search) params.search = query.search;\r\n      if (isCustomer) {\r\n        // Use customer-specific endpoint for customers\r\n        return await customerApi.getInvoices(params);\r\n      } else {\r\n        // Use general invoices endpoint for admin/staff (shows all invoices)\r\n        const response = await apiClient.get('/invoices', { params });\r\n        return processApiResponse(response);\r\n      }\r\n    } catch (apiError: any) {\r\n      console.error('❌ Error in getInvoices:', apiError);\r\n\r\n      // Check if it's a permission error (403) or other API error\r\n      if (apiError?.response?.status === 403) {\r\n        console.warn('⚠️ Permission denied for invoices API, using application-based approach');\r\n      }\r\n      // Fallback: get invoices by applications\r\n      return await this.getInvoicesByApplications(query);\r\n    }\r\n  }\r\n\r\n  private async getInvoicesByApplications(query: PaginateQuery & PaymentFilters): Promise<PaginatedResponse<Payment>> {\r\n    try {\r\n      // Use the existing getInvoices endpoint which should handle user filtering on the backend\r\n      const params = this.buildQueryParams(query);\r\n      // Set default status for invoices if not specified\r\n      if (!params.status) {\r\n        params.status = 'draft,pending,overdue';\r\n      }\r\n\r\n      const response = await customerApi.getInvoices(params);\r\n      const processedResponse = processApiResponse(response);\r\n\r\n      // Return the processed response directly - backend should handle pagination and filtering\r\n      return processedResponse;\r\n    } catch (error) {\r\n      console.error('PaymentService.getInvoicesByApplications error:', error);\r\n      return this.createEmptyResponse(query);\r\n    }\r\n  }\r\n\r\n  async getPaymentById(id: string): Promise<Payment> {\r\n    try {\r\n      const response = await apiClient.get(`/payments/${id}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('PaymentService.getPaymentById error:', error);\r\n      throw new Error('Payment not found');\r\n    }\r\n  }\r\n\r\n  async getPaymentStatistics(isCustomer?: boolean): Promise<PaymentStatistics | null> {\r\n    try {\r\n      try {\r\n        let response;\r\n        if (isCustomer) {\r\n          // Use customer-specific endpoint for customers\r\n          response = await customerApi.getPaymentStatistics();\r\n        } else {\r\n          // Use general statistics endpoint for admin/staff (all statistics)\r\n          console.log('👨‍💼 Using admin/staff statistics endpoint (all statistics)');\r\n          response = await apiClient.get('/payments/statistics');\r\n        }\r\n\r\n        const processedResponse = processApiResponse(response).data;\r\n\r\n        // Transform backend response to our format\r\n        return {\r\n          total: processedResponse.totalPayments || 0,\r\n          pending: processedResponse.pendingPayments || 0,\r\n          paid: processedResponse.paidPayments || 0,\r\n          overdue: processedResponse.overduePayments || 0,\r\n          cancelled: processedResponse.cancelledPayments || 0,\r\n          totalAmount: processedResponse.totalAmount || 0,\r\n          pendingAmount: processedResponse.pendingAmount || 0,\r\n          paidAmount: processedResponse.paidAmount || 0,\r\n          overdueAmount: processedResponse.overdueAmount || 0,\r\n        };\r\n      } catch (apiError: any) {\r\n        console.warn('⚠️ Backend statistics API failed:', apiError);\r\n\r\n        // Check if it's a permission error (403) or other API error\r\n        if (apiError?.response?.status === 403) {\r\n          console.warn('⚠️ Permission denied for statistics API');\r\n        }\r\n        return null;\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error calculating payment statistics:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n\r\n  async getPaymentsByEntity(_entityType: string, _entityId: string, query: PaginateQuery): Promise<PaginatedResponse<Payment>| null> {\r\n    try {\r\n      // Customer API doesn't have entity-specific payments, so we'll filter from all payments\r\n      const params: any = {};\r\n      if (query.page) params.page = query.page;\r\n      if (query.limit) params.limit = query.limit;\r\n      const response = await customerApi.getPayments(params);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('PaymentService.getPaymentsByEntity error:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  async updatePayment(_id: string, _data: Partial<Payment>): Promise<Payment> {\r\n    try {\r\n      // Customer API doesn't have update payment method, so we'll throw an error\r\n      throw new Error('Payment updates not supported in customer portal');\r\n    } catch (error) {\r\n      throw new Error('Payment updates not supported in customer portal');\r\n    }\r\n  }\r\n\r\n  async createPayment(data: Omit<Payment, 'payment_id' | 'created_at' | 'updated_at'>): Promise<Payment> {\r\n    try {\r\n      const paymentData = {\r\n        amount: data.amount,\r\n        currency: data.currency,\r\n        dueDate: data.due_date,\r\n        issueDate: data.issue_date,\r\n        description: data.description,\r\n        paymentType: data.payment_type.replace('_', ' '),\r\n        clientName: data.user?.first_name + ' ' + data.user?.last_name || 'Customer',\r\n        clientEmail: data.user?.email || '<EMAIL>',\r\n        paymentMethod: data.payment_method,\r\n        notes: data.notes,\r\n      };\r\n\r\n      const response = await customerApi.createPayment(paymentData);\r\n      const processedResponse = processApiResponse(response);\r\n\r\n      // Transform response back to our format\r\n      return processedResponse;\r\n    } catch (error) {\r\n      throw new Error('Failed to create payment');\r\n    }\r\n  }\r\n\r\n  async deletePayment(_id: string): Promise<void> {\r\n    throw new Error('Payment deletion not supported in customer portal');\r\n  }\r\n\r\n  // Proof of payment upload\r\n  async uploadProofOfPayment(paymentId: string, file: File, data: any): Promise<any> {\r\n    try {\r\n      const formData = new FormData();\r\n\r\n      formData.append('file', file);\r\n      Object.keys(data).forEach(key => {\r\n        formData.append(key, data[key]);\r\n      });\r\n\r\n      const response = await customerApi.uploadProofOfPayment(paymentId, formData);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.log('Proof of payment API not available, simulating upload');\r\n\r\n      // Fallback to simulation if API is not available\r\n      await new Promise(resolve => setTimeout(resolve, 2000));\r\n\r\n      return {\r\n        success: true,\r\n        message: 'Proof of payment uploaded successfully',\r\n        uploadId: `upload_${Date.now()}`,\r\n        status: 'pending_review'\r\n      };\r\n    }\r\n  }\r\n\r\n  private mapInvoiceTypeToPaymentType(entityType: string): string {\r\n    switch (entityType) {\r\n      case 'application': return 'APPLICATION_FEE';\r\n      case 'license': return 'LICENSE_FEE';\r\n      case 'renewal': return 'RENEWAL_FEE';\r\n      case 'procurement': return 'PROCUREMENT_FEE';\r\n      default: return 'LICENSE_FEE';\r\n    }\r\n  }\r\n\r\n\r\n  async getInvoicePayments(invoiceId: string): Promise<any> {\r\n    try {\r\n      const response = await customerApi.getInvoicePayments(invoiceId);\r\n      const processedResponse = processApiResponse(response);\r\n      return {\r\n        success: true,\r\n        data: processedResponse.data || processedResponse,\r\n        message: 'Invoice payments retrieved successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('PaymentService.getInvoicePayments error:', error);\r\n      return {\r\n        success: false,\r\n        data: [],\r\n        message: 'Failed to get invoice payments'\r\n      };\r\n    }\r\n  }\r\n\r\n  async approvePayment(paymentId: string): Promise<any> {\r\n    try {\r\n      // This would be an admin/staff endpoint to approve payments\r\n      const response = await fetch(`/api/payments/${paymentId}/approve`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        }\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to approve payment');\r\n      }\r\n\r\n      const data = await response.json();\r\n      return {\r\n        success: true,\r\n        data,\r\n        message: 'Payment approved successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error approving payment:', error);\r\n      return {\r\n        success: false,\r\n        data: null,\r\n        message: error instanceof Error ? error.message : 'Failed to approve payment'\r\n      };\r\n    }\r\n  }\r\n\r\n  async rejectPayment(paymentId: string, reason: string): Promise<any> {\r\n    try {\r\n      // This would be an admin/staff endpoint to reject payments\r\n      const response = await fetch(`/api/payments/${paymentId}/reject`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        },\r\n        body: JSON.stringify({ reason })\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to reject payment');\r\n      }\r\n\r\n      const data = await response.json();\r\n      return {\r\n        success: true,\r\n        data,\r\n        message: 'Payment rejected successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error rejecting payment:', error);\r\n      return {\r\n        success: false,\r\n        data: null,\r\n        message: error instanceof Error ? error.message : 'Failed to reject payment'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nexport const paymentService = new PaymentService();\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AAGA;AAAA;;;;;;AAEA,MAAM;IAGJ,4CAA4C;IAC5C,MAAc,wBAA2C;QACvD,IAAI;gBAQqB;YAPvB,QAAQ,GAAG,CAAC;YACZ,MAAM,sBAAsB,wIAAA,CAAA,qBAAkB,CAAC,mBAAmB;YAClE,MAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG,SACrC,WAAW,IAAM,OAAO,IAAI,MAAM,gCAAgC;YAGpE,MAAM,mBAAmB,MAAM,QAAQ,IAAI,CAAC;gBAAC;gBAAqB;aAAe;YACjF,MAAM,iBAAiB,EAAA,yBAAA,iBAAiB,IAAI,cAArB,6CAAA,uBAAuB,GAAG,CAAC,CAAC,MAAa,IAAI,cAAc,MAAK,EAAE;YACzF,QAAQ,GAAG,CAAC,AAAC,YAAiC,OAAtB,eAAe,MAAM,EAAC;YAC9C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,sCAAsC;YACnD,OAAO,EAAE;QACX;IACF;IAEA,yCAAyC;IACjC,oBAAoB,KAAqC,EAA8B;QAC7F,OAAO;YACL,MAAM,EAAE;YACR,MAAM;gBACJ,cAAc,MAAM,KAAK,IAAI;gBAC7B,YAAY;gBACZ,aAAa,MAAM,IAAI,IAAI;gBAC3B,YAAY;gBACZ,QAAQ,EAAE;gBACV,UAAU,EAAE;gBACZ,QAAQ,MAAM,MAAM,IAAI;gBACxB,QAAQ,EAAE;YACZ;YACA,OAAO;gBAAE,SAAS;YAAG;QACvB;IACF;IAEA,iCAAiC;IACzB,aAAa,QAAe,EAAE,KAAqC,EAAS;QAClF,IAAI,mBAAmB;QAEvB,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,kBAAkB,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,EAAE,IAAI,GAAG,WAAW;YACvF,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,gBAAgB,QAAQ,CAAC,QAAQ,MAAM,CAAC,WAAW;QAEvD;QAEA,IAAI,MAAM,YAAY,EAAE;YACtB,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,QAAQ,YAAY,KAAK,MAAM,YAAY;QAE/C;QAEA,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,aAAa,MAAM,MAAM,CAAC,WAAW;YAC3C,mBAAmB,iBAAiB,MAAM,CAAC,CAAA;oBACzC,yBACA,sBACA;uBAFA,EAAA,0BAAA,QAAQ,cAAc,cAAtB,8CAAA,wBAAwB,WAAW,GAAG,QAAQ,CAAC,kBAC/C,uBAAA,QAAQ,WAAW,cAAnB,2CAAA,qBAAqB,WAAW,GAAG,QAAQ,CAAC,kBAC5C,wBAAA,QAAQ,YAAY,cAApB,4CAAA,sBAAsB,WAAW,GAAG,QAAQ,CAAC;;QAEjD;QAEA,IAAI,MAAM,SAAS,IAAI,OAAO,MAAM,SAAS,KAAK,UAAU;YAC1D,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,MAAM,SAAS;YACtC,IAAI,SAAS,KAAK;gBAChB,mBAAmB,iBAAiB,MAAM,CAAC,CAAA;oBACzC,MAAM,cAAc,IAAI,KAAK,QAAQ,UAAU;oBAC/C,IAAI,SAAS,cAAc,IAAI,KAAK,QAAQ,OAAO;oBACnD,IAAI,OAAO,cAAc,IAAI,KAAK,MAAM,OAAO;oBAC/C,OAAO;gBACT;YACF;QACF;QAEA,OAAO;IACT;IAGA,iDAAiD;IACjD,MAAc,2BAA2B,cAAwB,EAAkB;QACjF,MAAM,cAAqB,EAAE;QAE7B,KAAK,MAAM,iBAAiB,eAAgB;YAC1C,IAAI;gBACF,QAAQ,GAAG,CAAC,AAAC,wCAAqD,OAAd;gBACpD,MAAM,WAAW,MAAM,oIAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,eAAe;gBACzE,MAAM,WAAW,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;gBACpC,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,AAAC,6CAA0D,OAAd,eAAc,MAAI;YAC9E;QACF;QAEA,OAAO;IACT;IACA,MAAM,YAAY,KAAqC,EAAE,UAAoB,EAAuC;QAClH,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,gBAAgB,CAAC;YACrC,IAAI,YAAY;gBACd,+CAA+C;gBAC/C,QAAQ,GAAG,CAAC;gBACZ,OAAO,gIAAA,CAAA,cAAW,CAAC,WAAW,CAAC;YACjC,OAAO;gBACL,qEAAqE;gBACrE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,aAAa;oBAAE;gBAAO;gBAC3D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAC5B;QACF,EAAE,OAAO,OAAY;gBAEf,iBAIA;YALJ,8BAA8B;YAC9B,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;gBAClC,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC;YAC9C;YAEA,IAAI,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,MAAK,KAAK;gBAClC,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC;YAC9C;YACA,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC;QAC9C;IACF;IAEA,0CAA0C;IAClC,iBAAiB,KAAqC,EAAO;QACnE,MAAM,SAAc,CAAC;QACrB,IAAI,MAAM,IAAI,EAAE,OAAO,IAAI,GAAG,MAAM,IAAI;QACxC,IAAI,MAAM,KAAK,EAAE,OAAO,KAAK,GAAG,MAAM,KAAK;QAC3C,IAAI,MAAM,MAAM,EAAE,OAAO,MAAM,GAAG,MAAM,MAAM;QAC9C,IAAI,MAAM,YAAY,EAAE,OAAO,WAAW,GAAG,MAAM,YAAY;QAC/D,IAAI,MAAM,MAAM,EAAE,OAAO,MAAM,GAAG,MAAM,MAAM;QAC9C,IAAI,MAAM,SAAS,EAAE,OAAO,SAAS,GAAG,MAAM,SAAS;QACvD,OAAO;IACT;IAEA,MAAc,0BAA0B,KAAqC,EAAuC;QAClH,IAAI;YACF,6CAA6C;YAC7C,MAAM,iBAAiB,MAAM,IAAI,CAAC,qBAAqB;YAEvD,IAAI,eAAe,MAAM,KAAK,GAAG;gBAC/B,QAAQ,GAAG,CAAC;gBACZ,OAAO,IAAI,CAAC,mBAAmB,CAAC;YAClC;YAEA,uCAAuC;YACvC,MAAM,cAAc,MAAM,IAAI,CAAC,0BAA0B,CAAC;YAC1D,QAAQ,GAAG,CAAC,AAAC,4BAA8C,OAAnB,YAAY,MAAM;YAC1D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,4CAA4C;YAC5C,OAAO,IAAI,CAAC,mBAAmB,CAAC;QAClC;IACF;IAEA,MAAM,YAAY,KAAqC,EAAoE;YAAlE,aAAA,iEAAsB;QAG7E,IAAI;YACF,MAAM,SAAc,CAAC;YACrB,IAAI,MAAM,IAAI,EAAE,OAAO,IAAI,GAAG,MAAM,IAAI;YACxC,IAAI,MAAM,KAAK,EAAE,OAAO,KAAK,GAAG,MAAM,KAAK;YAC3C,qDAAqD;YACrD,OAAO,MAAM,GAAG,MAAM,MAAM,IAAI;YAChC,IAAI,MAAM,MAAM,EAAE,OAAO,MAAM,GAAG,MAAM,MAAM;YAC9C,IAAI,YAAY;gBACd,+CAA+C;gBAC/C,OAAO,MAAM,gIAAA,CAAA,cAAW,CAAC,WAAW,CAAC;YACvC,OAAO;gBACL,qEAAqE;gBACrE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,aAAa;oBAAE;gBAAO;gBAC3D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAC5B;QACF,EAAE,OAAO,UAAe;gBAIlB;YAHJ,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,4DAA4D;YAC5D,IAAI,CAAA,qBAAA,gCAAA,qBAAA,SAAU,QAAQ,cAAlB,yCAAA,mBAAoB,MAAM,MAAK,KAAK;gBACtC,QAAQ,IAAI,CAAC;YACf;YACA,yCAAyC;YACzC,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC;QAC9C;IACF;IAEA,MAAc,0BAA0B,KAAqC,EAAuC;QAClH,IAAI;YACF,0FAA0F;YAC1F,MAAM,SAAS,IAAI,CAAC,gBAAgB,CAAC;YACrC,mDAAmD;YACnD,IAAI,CAAC,OAAO,MAAM,EAAE;gBAClB,OAAO,MAAM,GAAG;YAClB;YAEA,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,WAAW,CAAC;YAC/C,MAAM,oBAAoB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7C,0FAA0F;YAC1F,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mDAAmD;YACjE,OAAO,IAAI,CAAC,mBAAmB,CAAC;QAClC;IACF;IAEA,MAAM,eAAe,EAAU,EAAoB;QACjD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,aAAe,OAAH;YAClD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,qBAAqB,UAAoB,EAAqC;QAClF,IAAI;YACF,IAAI;gBACF,IAAI;gBACJ,IAAI,YAAY;oBACd,+CAA+C;oBAC/C,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,oBAAoB;gBACnD,OAAO;oBACL,mEAAmE;oBACnE,QAAQ,GAAG,CAAC;oBACZ,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;gBACjC;gBAEA,MAAM,oBAAoB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI;gBAE3D,2CAA2C;gBAC3C,OAAO;oBACL,OAAO,kBAAkB,aAAa,IAAI;oBAC1C,SAAS,kBAAkB,eAAe,IAAI;oBAC9C,MAAM,kBAAkB,YAAY,IAAI;oBACxC,SAAS,kBAAkB,eAAe,IAAI;oBAC9C,WAAW,kBAAkB,iBAAiB,IAAI;oBAClD,aAAa,kBAAkB,WAAW,IAAI;oBAC9C,eAAe,kBAAkB,aAAa,IAAI;oBAClD,YAAY,kBAAkB,UAAU,IAAI;oBAC5C,eAAe,kBAAkB,aAAa,IAAI;gBACpD;YACF,EAAE,OAAO,UAAe;oBAIlB;gBAHJ,QAAQ,IAAI,CAAC,qCAAqC;gBAElD,4DAA4D;gBAC5D,IAAI,CAAA,qBAAA,gCAAA,qBAAA,SAAU,QAAQ,cAAlB,yCAAA,mBAAoB,MAAM,MAAK,KAAK;oBACtC,QAAQ,IAAI,CAAC;gBACf;gBACA,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO;QACT;IACF;IAGA,MAAM,oBAAoB,WAAmB,EAAE,SAAiB,EAAE,KAAoB,EAA6C;QACjI,IAAI;YACF,wFAAwF;YACxF,MAAM,SAAc,CAAC;YACrB,IAAI,MAAM,IAAI,EAAE,OAAO,IAAI,GAAG,MAAM,IAAI;YACxC,IAAI,MAAM,KAAK,EAAE,OAAO,KAAK,GAAG,MAAM,KAAK;YAC3C,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,WAAW,CAAC;YAC/C,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;QACT;IACF;IAEA,MAAM,cAAc,GAAW,EAAE,KAAuB,EAAoB;QAC1E,IAAI;YACF,2EAA2E;YAC3E,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,cAAc,IAA+D,EAAoB;QACrG,IAAI;gBAQY,YAA8B,aAC7B;YARf,MAAM,cAAc;gBAClB,QAAQ,KAAK,MAAM;gBACnB,UAAU,KAAK,QAAQ;gBACvB,SAAS,KAAK,QAAQ;gBACtB,WAAW,KAAK,UAAU;gBAC1B,aAAa,KAAK,WAAW;gBAC7B,aAAa,KAAK,YAAY,CAAC,OAAO,CAAC,KAAK;gBAC5C,YAAY,EAAA,aAAA,KAAK,IAAI,cAAT,iCAAA,WAAW,UAAU,IAAG,QAAM,cAAA,KAAK,IAAI,cAAT,kCAAA,YAAW,SAAS,KAAI;gBAClE,aAAa,EAAA,cAAA,KAAK,IAAI,cAAT,kCAAA,YAAW,KAAK,KAAI;gBACjC,eAAe,KAAK,cAAc;gBAClC,OAAO,KAAK,KAAK;YACnB;YAEA,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;YACjD,MAAM,oBAAoB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7C,wCAAwC;YACxC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,cAAc,GAAW,EAAiB;QAC9C,MAAM,IAAI,MAAM;IAClB;IAEA,0BAA0B;IAC1B,MAAM,qBAAqB,SAAiB,EAAE,IAAU,EAAE,IAAS,EAAgB;QACjF,IAAI;YACF,MAAM,WAAW,IAAI;YAErB,SAAS,MAAM,CAAC,QAAQ;YACxB,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAA;gBACxB,SAAS,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI;YAChC;YAEA,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,oBAAoB,CAAC,WAAW;YACnE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YAEZ,iDAAiD;YACjD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,UAAU,AAAC,UAAoB,OAAX,KAAK,GAAG;gBAC5B,QAAQ;YACV;QACF;IACF;IAEQ,4BAA4B,UAAkB,EAAU;QAC9D,OAAQ;YACN,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAGA,MAAM,mBAAmB,SAAiB,EAAgB;QACxD,IAAI;YACF,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,kBAAkB,CAAC;YACtD,MAAM,oBAAoB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAC7C,OAAO;gBACL,SAAS;gBACT,MAAM,kBAAkB,IAAI,IAAI;gBAChC,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,OAAO;gBACL,SAAS;gBACT,MAAM,EAAE;gBACR,SAAS;YACX;QACF;IACF;IAEA,MAAM,eAAe,SAAiB,EAAgB;QACpD,IAAI;YACF,4DAA4D;YAC5D,MAAM,WAAW,MAAM,MAAM,AAAC,iBAA0B,OAAV,WAAU,aAAW;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,UAAuC,OAA9B,aAAa,OAAO,CAAC;gBAClD;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBACL,SAAS;gBACT;gBACA,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA,MAAM,cAAc,SAAiB,EAAE,MAAc,EAAgB;QACnE,IAAI;YACF,2DAA2D;YAC3D,MAAM,WAAW,MAAM,MAAM,AAAC,iBAA0B,OAAV,WAAU,YAAU;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,UAAuC,OAA9B,aAAa,OAAO,CAAC;gBAClD;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBACL,SAAS;gBACT;gBACA,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;AACF;AAEO,MAAM,iBAAiB,IAAI", "debugId": null}}, {"offset": {"line": 920, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/adminPaymentService.ts"], "sourcesContent": ["import { apiClient } from '@/lib/apiClient';\r\nimport { Payment, PaymentFilters } from '@/types/invoice';\r\nimport { PaginatedResponse, PaginateQuery } from '@/types';\r\n\r\nexport interface PaymentUpdateData {\r\n  status?: 'pending' | 'approved' | 'cancelled' | 'overdue';\r\n  paid_date?: string;\r\n  notes?: string;\r\n  payment_method?: string;\r\n  transaction_reference?: string;\r\n}\r\n\r\nexport interface PaymentApprovalResult {\r\n  success: boolean;\r\n  message: string;\r\n  data?: {\r\n    payment: Payment;\r\n    invoice_fully_paid?: boolean;\r\n    total_paid_amount?: number;\r\n    remaining_amount?: number;\r\n    invoice_amount?: number;\r\n    task_closed?: boolean;\r\n  };\r\n  error?: string;\r\n}\r\n\r\nexport interface PaymentRejectionResult {\r\n  success: boolean;\r\n  message: string;\r\n  data?: {\r\n    payment: Payment;\r\n  };\r\n  error?: string;\r\n}\r\n\r\nclass AdminPaymentService {\r\n  /**\r\n   * Get all payments with admin privileges\r\n   */\r\n  async getPayments(query: PaginateQuery & PaymentFilters): Promise<PaginatedResponse<Payment>> {\r\n    try {\r\n      const params: any = {};\r\n\r\n      if (query.page) params.page = query.page;\r\n      if (query.limit) params.limit = query.limit;\r\n      if (query.status) params.status = query.status;\r\n      if (query.payment_type) params.paymentType = query.payment_type;\r\n      if (query.search) params.search = query.search;\r\n      if (query.dateRange) params.dateRange = query.dateRange;\r\n\r\n      const response = await apiClient.get('/payments', { params });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('❌ Error fetching payments:', error);\r\n      throw new Error('Failed to fetch payments');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get payment by ID\r\n   */\r\n  async getPaymentById(id: string): Promise<Payment> {\r\n    try {\r\n      const response = await apiClient.get(`/payments/${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`❌ Error fetching payment ${id}:`, error);\r\n      throw new Error('Payment not found');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update payment status and details\r\n   */\r\n  async updatePayment(id: string, data: PaymentUpdateData): Promise<PaymentApprovalResult> {\r\n    try {\r\n      const response = await apiClient.put(`/payments/${id}`, data);\r\n      return {\r\n        success: true,\r\n        message: response.data.message || 'Payment updated successfully',\r\n        data: response.data.data ? {\r\n          payment: response.data.data,\r\n          invoice_fully_paid: response.data.data.approval_result?.invoice_fully_paid,\r\n          total_paid_amount: response.data.data.approval_result?.total_paid_amount,\r\n          remaining_amount: response.data.data.approval_result?.remaining_amount,\r\n          invoice_amount: response.data.data.approval_result?.invoice_amount,\r\n          task_closed: response.data.data.approval_result?.task_closed\r\n        } : undefined\r\n      };\r\n    } catch (error: any) {\r\n      const errorMessage = error.response?.data?.message || 'Failed to update payment';\r\n      const errorCode = error.response?.data?.error || 'UNKNOWN_ERROR';\r\n\r\n      return {\r\n        success: false,\r\n        message: errorMessage,\r\n        error: errorCode\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Approve payment - sets status to 'paid' and handles invoice/task closure\r\n   */\r\n  async approvePayment(paymentId: string): Promise<PaymentApprovalResult> {\r\n    try {\r\n      const updateData: PaymentUpdateData = {\r\n        status: 'approved',\r\n        paid_date: new Date().toISOString()\r\n      };\r\n\r\n      const result = await this.updatePayment(paymentId, updateData);\r\n\r\n      if (result.success && result.data?.invoice_fully_paid) {\r\n        result.message = '✅ Payment approved and invoice marked as fully paid! Client has been notified via email.';\r\n      } else if (result.success && result.data?.remaining_amount) {\r\n        result.message = `Payment approved successfully. Remaining balance: MWK ${result.data.remaining_amount.toLocaleString()}`;\r\n      }\r\n\r\n      return result;\r\n    } catch (error) {\r\n      console.error(`❌ Error approving payment ${paymentId}:`, error);\r\n      return {\r\n        success: false,\r\n        message: 'Failed to approve payment',\r\n        error: 'APPROVAL_FAILED'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Reject payment - sets status to 'cancelled' with reason\r\n   */\r\n  async rejectPayment(paymentId: string, reason: string): Promise<PaymentRejectionResult> {\r\n    try {\r\n      console.log(`❌ Rejecting payment ${paymentId} with reason: ${reason}`);\r\n\r\n      const updateData: PaymentUpdateData = {\r\n        status: 'cancelled',\r\n        notes: reason.trim()\r\n      };\r\n\r\n      const result = await this.updatePayment(paymentId, updateData);\r\n\r\n      return {\r\n        success: result.success,\r\n        message: result.message || 'Payment rejected successfully',\r\n        data: result.data ? { payment: result.data.payment } : undefined,\r\n        error: result.error\r\n      };\r\n    } catch (error) {\r\n      console.error(`❌ Error rejecting payment ${paymentId}:`, error);\r\n      return {\r\n        success: false,\r\n        message: 'Failed to reject payment',\r\n        error: 'REJECTION_FAILED'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nexport const adminPaymentService = new AdminPaymentService();\r\n"], "names": [], "mappings": ";;;AAAA;;AAmCA,MAAM;IACJ;;GAEC,GACD,MAAM,YAAY,KAAqC,EAAuC;QAC5F,IAAI;YACF,MAAM,SAAc,CAAC;YAErB,IAAI,MAAM,IAAI,EAAE,OAAO,IAAI,GAAG,MAAM,IAAI;YACxC,IAAI,MAAM,KAAK,EAAE,OAAO,KAAK,GAAG,MAAM,KAAK;YAC3C,IAAI,MAAM,MAAM,EAAE,OAAO,MAAM,GAAG,MAAM,MAAM;YAC9C,IAAI,MAAM,YAAY,EAAE,OAAO,WAAW,GAAG,MAAM,YAAY;YAC/D,IAAI,MAAM,MAAM,EAAE,OAAO,MAAM,GAAG,MAAM,MAAM;YAC9C,IAAI,MAAM,SAAS,EAAE,OAAO,SAAS,GAAG,MAAM,SAAS;YAEvD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,aAAa;gBAAE;YAAO;YAC3D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,EAAU,EAAoB;QACjD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,aAAe,OAAH;YAClD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,AAAC,4BAA8B,OAAH,IAAG,MAAI;YACjD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,cAAc,EAAU,EAAE,IAAuB,EAAkC;QACvF,IAAI;gBAOsB,qCACD,sCACD,sCACF,sCACH;YAVjB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,aAAe,OAAH,KAAM;YACxD,OAAO;gBACL,SAAS;gBACT,SAAS,SAAS,IAAI,CAAC,OAAO,IAAI;gBAClC,MAAM,SAAS,IAAI,CAAC,IAAI,GAAG;oBACzB,SAAS,SAAS,IAAI,CAAC,IAAI;oBAC3B,kBAAkB,GAAE,sCAAA,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,cAAlC,0DAAA,oCAAoC,kBAAkB;oBAC1E,iBAAiB,GAAE,uCAAA,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,cAAlC,2DAAA,qCAAoC,iBAAiB;oBACxE,gBAAgB,GAAE,uCAAA,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,cAAlC,2DAAA,qCAAoC,gBAAgB;oBACtE,cAAc,GAAE,uCAAA,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,cAAlC,2DAAA,qCAAoC,cAAc;oBAClE,WAAW,GAAE,uCAAA,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,cAAlC,2DAAA,qCAAoC,WAAW;gBAC9D,IAAI;YACN;QACF,EAAE,OAAO,OAAY;gBACE,sBAAA,iBACH,uBAAA;YADlB,MAAM,eAAe,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI;YACtD,MAAM,YAAY,EAAA,mBAAA,MAAM,QAAQ,cAAd,wCAAA,wBAAA,iBAAgB,IAAI,cAApB,4CAAA,sBAAsB,KAAK,KAAI;YAEjD,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO;YACT;QACF;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,SAAiB,EAAkC;QACtE,IAAI;gBAQoB,cAEO;YAT7B,MAAM,aAAgC;gBACpC,QAAQ;gBACR,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,MAAM,SAAS,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW;YAEnD,IAAI,OAAO,OAAO,MAAI,eAAA,OAAO,IAAI,cAAX,mCAAA,aAAa,kBAAkB,GAAE;gBACrD,OAAO,OAAO,GAAG;YACnB,OAAO,IAAI,OAAO,OAAO,MAAI,gBAAA,OAAO,IAAI,cAAX,oCAAA,cAAa,gBAAgB,GAAE;gBAC1D,OAAO,OAAO,GAAG,AAAC,yDAAsG,OAA9C,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc;YACvH;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,AAAC,6BAAsC,OAAV,WAAU,MAAI;YACzD,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO;YACT;QACF;IACF;IAEA;;GAEC,GACD,MAAM,cAAc,SAAiB,EAAE,MAAc,EAAmC;QACtF,IAAI;YACF,QAAQ,GAAG,CAAC,AAAC,uBAAgD,OAA1B,WAAU,kBAAuB,OAAP;YAE7D,MAAM,aAAgC;gBACpC,QAAQ;gBACR,OAAO,OAAO,IAAI;YACpB;YAEA,MAAM,SAAS,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW;YAEnD,OAAO;gBACL,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,OAAO,IAAI;gBAC3B,MAAM,OAAO,IAAI,GAAG;oBAAE,SAAS,OAAO,IAAI,CAAC,OAAO;gBAAC,IAAI;gBACvD,OAAO,OAAO,KAAK;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,AAAC,6BAAsC,OAAV,WAAU,MAAI;YACzD,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO;YACT;QACF;IACF;AACF;AAEO,MAAM,sBAAsB,IAAI", "debugId": null}}, {"offset": {"line": 1049, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/utils/formatters.ts"], "sourcesContent": ["/**\r\n * Formats a number as currency with commas for better readability\r\n * For numbers over 5 digits, adds a comma after the first 2 figures\r\n * \r\n * @param amount - The amount to format\r\n * @param currency - The currency code (e.g., 'MWK', 'USD')\r\n * @param minimumFractionDigits - Minimum number of decimal places (default: 0)\r\n * @returns Formatted currency string\r\n */\r\nexport const formatCurrency = (\r\n  amount: number | string,\r\n  currency: string = 'USD',\r\n  minimumFractionDigits: number = 0\r\n): string => {\r\n  // Convert string to number if needed\r\n  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\r\n\r\n  if(currency == '$') currency = 'USD';\r\n  \r\n  // Get the currency symbol\r\n  const formatter = new Intl.NumberFormat('en-MW', {\r\n    style: 'currency',\r\n    currency: currency,\r\n    minimumFractionDigits: minimumFractionDigits,\r\n    useGrouping: false, // We'll handle grouping manually\r\n  });\r\n  \r\n  // Format without grouping to get the base string\r\n  const formatted = formatter.format(numericAmount);\r\n  \r\n  // Extract the numeric part (remove currency symbol and any spaces)\r\n  const parts = formatted.match(/([^\\d]*)(\\d+(?:\\.\\d+)?)(.*)/);\r\n  if (!parts) return formatted;\r\n  \r\n  const [, prefix, numericPart, suffix] = parts;\r\n  \r\n  // Format the number with custom grouping\r\n  let formattedNumber = numericPart;\r\n  \r\n  // For numbers with 5 or more digits, we want to ensure there's a comma after the first 2 figures\r\n  if (numericPart.replace(/\\D/g, '').length >= 5) {\r\n    // Split the integer and decimal parts\r\n    const [integerPart, decimalPart] = numericPart.split('.');\r\n    \r\n    // Format the integer part with commas\r\n    // First, add a comma after the first 2 digits\r\n    let formattedInteger = integerPart.slice(0, 2) + ',' + integerPart.slice(2);\r\n    \r\n    // Then add commas for the rest of the number every 3 digits\r\n    formattedInteger = formattedInteger.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n    \r\n    // Combine the parts back\r\n    formattedNumber = formattedInteger + (decimalPart ? '.' + decimalPart : '');\r\n  } else {\r\n    // For smaller numbers, use standard grouping (every 3 digits)\r\n    formattedNumber = numericPart.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n  }\r\n  \r\n  // Combine everything back\r\n  return prefix + formattedNumber + suffix;\r\n};\r\n\r\n// ============================================================================\r\n// ENHANCED AMOUNT FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format amount with currency (alternative to formatCurrency for consistency)\r\n * @param amount - The amount to format\r\n * @param currency - Currency code (default: 'MWK')\r\n * @param locale - Locale for formatting (default: 'en-US')\r\n */\r\nexport const formatAmount = (\r\n  amount: number | string,\r\n  currency: string = 'USD',\r\n  locale: string = 'en-US'\r\n): string => {\r\n  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\r\n  if (isNaN(numAmount)) return `${currency} 0.00`;\r\n\r\n  return `${currency} ${numAmount.toLocaleString(locale, {\r\n    minimumFractionDigits: 2,\r\n    maximumFractionDigits: 2\r\n  })}`;\r\n};\r\n\r\n/**\r\n * Format amount without currency symbol\r\n * @param amount - The amount to format\r\n * @param decimals - Number of decimal places (default: 2)\r\n */\r\nexport const formatNumber = (amount: number | string, decimals: number = 2): string => {\r\n  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\r\n  if (isNaN(numAmount)) return '0.00';\r\n\r\n  return numAmount.toLocaleString('en-US', {\r\n    minimumFractionDigits: decimals,\r\n    maximumFractionDigits: decimals\r\n  });\r\n};\r\n\r\n/**\r\n * Format license category fee with special handling for \"Short Code Allocation\"\r\n * @param fee - The fee amount (string or number)\r\n * @param categoryName - The name of the license category\r\n * @param currency - Currency code (default: 'MWK')\r\n */\r\nexport const formatLicenseCategoryFee = (\r\n  fee: string | number,\r\n  categoryName: string,\r\n): string => {\r\n  // Check if fee is 0 or empty\r\n  if (!fee || fee === '0' || parseFloat(fee.toString()) === 0) {\r\n    // Show \"Free\" for categories with 0 fee\r\n    return \"Free\";\r\n  }\r\n  // Format as currency for non-zero fees\r\n  return formatCurrency(fee);\r\n};\r\n\r\n/**\r\n * Format percentage\r\n * @param value - The value to format as percentage\r\n * @param decimals - Number of decimal places (default: 1)\r\n */\r\nexport const formatPercentage = (value: number | string, decimals: number = 1): string => {\r\n  const numValue = typeof value === 'string' ? parseFloat(value) : value;\r\n  if (isNaN(numValue)) return '0%';\r\n\r\n  return `${numValue.toFixed(decimals)}%`;\r\n};\r\n\r\n// ============================================================================\r\n// ENHANCED DATE & TIME FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Formats a date string to a readable format\r\n *\r\n * @param dateString - The date string to format\r\n * @param options - Intl.DateTimeFormatOptions\r\n * @returns Formatted date string\r\n */\r\nexport const formatDate = (\r\n  dateString: string | Date,\r\n  options: Intl.DateTimeFormatOptions = {\r\n    year: 'numeric',\r\n    month: 'short',\r\n    day: 'numeric'\r\n  }\r\n): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid date';\r\n\r\n  return new Intl.DateTimeFormat('en-MW', options).format(date);\r\n};\r\n\r\n/**\r\n * Format date in long format (e.g., \"January 15, 2024\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatDateLong = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid date';\r\n\r\n  return date.toLocaleDateString('en-US', {\r\n    year: 'numeric',\r\n    month: 'long',\r\n    day: 'numeric'\r\n  });\r\n};\r\n\r\n/**\r\n * Format time (e.g., \"2:30 PM\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatTime = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid time';\r\n\r\n  return date.toLocaleTimeString('en-US', {\r\n    hour: 'numeric',\r\n    minute: '2-digit',\r\n    hour12: true\r\n  });\r\n};\r\n\r\n/**\r\n * Format datetime (e.g., \"Jan 15, 2024 at 2:30 PM\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatDateTime = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid datetime';\r\n\r\n  return `${formatDate(date)} at ${formatTime(date)}`;\r\n};\r\n\r\n/**\r\n * Format relative time (e.g., \"2 hours ago\", \"in 3 days\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatRelativeTime = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid date';\r\n\r\n  const now = new Date();\r\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n  if (diffInSeconds < 60) return 'Just now';\r\n  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\r\n  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\r\n  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;\r\n  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;\r\n\r\n  return `${Math.floor(diffInSeconds / 31536000)} years ago`;\r\n};\r\n\r\n/**\r\n * Format date for input fields (YYYY-MM-DD)\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatDateForInput = (dateString: string | Date): string => {\r\n  if (!dateString) return '';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return '';\r\n\r\n  return date.toISOString().split('T')[0];\r\n};\r\n\r\n// ============================================================================\r\n// STRING CASE FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Convert string to camelCase\r\n * @param str - String to convert\r\n */\r\nexport const toCamelCase = (str: string): string => {\r\n  return str\r\n    .replace(/(?:^\\w|[A-Z]|\\b\\w)/g, (word, index) => {\r\n      return index === 0 ? word.toLowerCase() : word.toUpperCase();\r\n    })\r\n    .replace(/\\s+/g, '');\r\n};\r\n\r\n/**\r\n * Convert string to PascalCase\r\n * @param str - String to convert\r\n */\r\nexport const toPascalCase = (str: string): string => {\r\n  return str\r\n    .replace(/(?:^\\w|[A-Z]|\\b\\w)/g, (word) => {\r\n      return word.toUpperCase();\r\n    })\r\n    .replace(/\\s+/g, '');\r\n};\r\n\r\n/**\r\n * Convert string to kebab-case\r\n * @param str - String to convert\r\n */\r\nexport const toKebabCase = (str: string): string => {\r\n  return str\r\n    .replace(/([a-z])([A-Z])/g, '$1-$2')\r\n    .replace(/[\\s_]+/g, '-')\r\n    .toLowerCase();\r\n};\r\n\r\n/**\r\n * Convert string to snake_case\r\n * @param str - String to convert\r\n */\r\nexport const toSnakeCase = (str: string): string => {\r\n  return str\r\n    .replace(/([a-z])([A-Z])/g, '$1_$2')\r\n    .replace(/[\\s-]+/g, '_')\r\n    .toLowerCase();\r\n};\r\n\r\n/**\r\n * Convert string to Title Case\r\n * @param str - String to convert\r\n */\r\nexport const toTitleCase = (str: string): string => {\r\n  return str.replace(/\\w\\S*/g, (txt) => {\r\n    return txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase();\r\n  });\r\n};\r\n\r\n/**\r\n * Convert string to Sentence case\r\n * @param str - String to convert\r\n */\r\nexport const toSentenceCase = (str: string): string => {\r\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\r\n};\r\n\r\n// ============================================================================\r\n// TEXT FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Truncate text with ellipsis\r\n * @param text - Text to truncate\r\n * @param maxLength - Maximum length before truncation\r\n * @param suffix - Suffix to add (default: '...')\r\n */\r\nexport const truncateText = (text: string, maxLength: number, suffix: string = '...'): string => {\r\n  if (!text || text.length <= maxLength) return text || '';\r\n  return text.substring(0, maxLength - suffix.length) + suffix;\r\n};\r\n\r\n/**\r\n * Capitalize first letter of each word\r\n * @param str - String to capitalize\r\n */\r\nexport const capitalizeWords = (str: string): string => {\r\n  return str.replace(/\\b\\w/g, (char) => char.toUpperCase());\r\n};\r\n\r\n/**\r\n * Remove extra whitespace and normalize spacing\r\n * @param str - String to normalize\r\n */\r\nexport const normalizeWhitespace = (str: string): string => {\r\n  return str.replace(/\\s+/g, ' ').trim();\r\n};\r\n\r\n/**\r\n * Extract initials from a name\r\n * @param name - Full name\r\n * @param maxInitials - Maximum number of initials (default: 2)\r\n */\r\nexport const getInitials = (name: string, maxInitials: number = 2): string => {\r\n  if (!name) return '';\r\n\r\n  return name\r\n    .split(' ')\r\n    .filter(word => word.length > 0)\r\n    .slice(0, maxInitials)\r\n    .map(word => word.charAt(0).toUpperCase())\r\n    .join('');\r\n};\r\n\r\n/**\r\n * Convert text to slug format (URL-friendly)\r\n * @param text - Text to convert\r\n */\r\nexport const toSlug = (text: string): string => {\r\n  return text\r\n    .toLowerCase()\r\n    .replace(/[^\\w\\s-]/g, '') // Remove special characters\r\n    .replace(/[\\s_-]+/g, '-') // Replace spaces and underscores with hyphens\r\n    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens\r\n};\r\n\r\n/**\r\n * Highlight search terms in text\r\n * @param text - Text to highlight\r\n * @param searchTerm - Term to highlight\r\n * @param className - CSS class for highlighting (default: 'highlight')\r\n */\r\nexport const highlightText = (text: string, searchTerm: string, className: string = 'highlight'): string => {\r\n  if (!searchTerm) return text;\r\n\r\n  const regex = new RegExp(`(${searchTerm})`, 'gi');\r\n  return text.replace(regex, `<span class=\"${className}\">$1</span>`);\r\n};\r\n\r\n// ============================================================================\r\n// PHONE & EMAIL FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format phone number\r\n * @param phone - Phone number to format\r\n * @param format - Format type ('international' | 'national' | 'minimal')\r\n */\r\nexport const formatPhone = (phone: string, format: 'international' | 'national' | 'minimal' = 'national'): string => {\r\n  if (!phone) return '';\r\n\r\n  // Remove all non-digit characters\r\n  const digits = phone.replace(/\\D/g, '');\r\n\r\n  if (digits.length < 10) return phone; // Return original if too short\r\n\r\n  switch (format) {\r\n    case 'international':\r\n      return `+${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 9)} ${digits.slice(9)}`;\r\n    case 'national':\r\n      return `(${digits.slice(-10, -7)}) ${digits.slice(-7, -4)}-${digits.slice(-4)}`;\r\n    case 'minimal':\r\n      return `${digits.slice(-10, -7)}.${digits.slice(-7, -4)}.${digits.slice(-4)}`;\r\n    default:\r\n      return phone;\r\n  }\r\n};\r\n\r\n/**\r\n * Mask email for privacy (e.g., \"j***@example.com\")\r\n * @param email - Email to mask\r\n */\r\nexport const maskEmail = (email: string): string => {\r\n  if (!email || !email.includes('@')) return email;\r\n\r\n  const [username, domain] = email.split('@');\r\n  if (username.length <= 2) return email;\r\n\r\n  const maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);\r\n  return `${maskedUsername}@${domain}`;\r\n};\r\n\r\n/**\r\n * Validate email format\r\n * @param email - Email to validate\r\n */\r\nexport const isValidEmail = (email: string): boolean => {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(email);\r\n};\r\n\r\n// ============================================================================\r\n// ID & REFERENCE FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format application number with prefix\r\n * @param number - Application number\r\n * @param prefix - Prefix to add (default: 'APP')\r\n */\r\nexport const formatApplicationNumber = (number: string | number, prefix: string = 'APP'): string => {\r\n  if (!number) return '';\r\n  return `${prefix}-${String(number).padStart(6, '0')}`;\r\n};\r\n\r\n/**\r\n * Format invoice number with prefix\r\n * @param number - Invoice number\r\n * @param prefix - Prefix to add (default: 'INV')\r\n */\r\nexport const formatInvoiceNumber = (number: string | number, prefix: string = 'INV'): string => {\r\n  if (!number) return '';\r\n  return `${prefix}-${String(number).padStart(6, '0')}`;\r\n};\r\n\r\n/**\r\n * Format task number with prefix\r\n * @param number - Task number\r\n * @param prefix - Prefix to add (default: 'TASK')\r\n */\r\nexport const formatTaskNumber = (number: string | number, prefix: string = 'TASK'): string => {\r\n  if (!number) return '';\r\n  return `${prefix}-${String(number).padStart(6, '0')}`;\r\n};\r\n\r\n/**\r\n * Generate a random reference ID\r\n * @param length - Length of the ID (default: 8)\r\n * @param prefix - Optional prefix\r\n */\r\nexport const generateReferenceId = (length: number = 8, prefix?: string): string => {\r\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r\n  let result = '';\r\n\r\n  for (let i = 0; i < length; i++) {\r\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\r\n  }\r\n\r\n  return prefix ? `${prefix}-${result}` : result;\r\n};\r\n\r\n/**\r\n * Convert UUID to user-friendly reference ID for customers\r\n * @param uuid - The UUID to convert\r\n * @param prefix - Optional prefix (default: 'REF')\r\n */\r\nexport const formatCustomerReferenceId = (uuid: string, prefix: string = 'REF'): string => {\r\n  if (!uuid) return '';\r\n\r\n  // Take first 8 characters of UUID (without hyphens) and convert to uppercase\r\n  const cleanUuid = uuid.replace(/-/g, '').toUpperCase();\r\n  const shortId = cleanUuid.substring(0, 8);\r\n\r\n  return `${prefix}-${shortId}`;\r\n};\r\n\r\n/**\r\n * Mask sensitive ID (show only first and last 2 characters)\r\n * @param id - ID to mask\r\n */\r\nexport const maskId = (id: string): string => {\r\n  if (!id || id.length <= 4) return id;\r\n\r\n  const start = id.slice(0, 2);\r\n  const end = id.slice(-2);\r\n  const middle = '*'.repeat(id.length - 4);\r\n\r\n  return `${start}${middle}${end}`;\r\n};\r\n\r\n// ============================================================================\r\n// STATUS & BADGE FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format status text for display\r\n * @param status - Status to format\r\n */\r\nexport const formatStatus = (status: string): string => {\r\n  if (!status) return '';\r\n\r\n  return status\r\n    .replace(/_/g, ' ')\r\n    .replace(/\\b\\w/g, char => char.toUpperCase());\r\n};\r\n\r\n/**\r\n * Get status color class\r\n * @param status - Status to get color for\r\n */\r\n\r\n\r\nexport const getStatusColor = (status: any): string => {\r\n  const statusLower = status.toLowerCase();\r\n\r\n  switch (statusLower) {\r\n    case 'active':\r\n    case 'approved':\r\n    case 'completed':\r\n    case 'paid':\r\n    case 'success':\r\n      return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';\r\n\r\n    case 'pending':\r\n    case 'in_progress':\r\n    case 'processing':\r\n    case 'review':\r\n      return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20';\r\n\r\n    case 'rejected':\r\n    case 'failed':\r\n    case 'error':\r\n    case 'overdue':\r\n    case 'cancelled':\r\n      return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';\r\n\r\n    case 'draft':\r\n    case 'inactive':\r\n    case 'disabled':\r\n      return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';\r\n\r\n    case 'warning':\r\n    case 'attention':\r\n      return 'text-orange-600 bg-orange-100 dark:text-orange-400 dark:bg-orange-900/20';\r\n\r\n    default:\r\n      return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';\r\n  }\r\n};\r\n\r\n// ============================================================================\r\n// FILE SIZE & VALIDATION FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format file size in human readable format\r\n * @param bytes - File size in bytes\r\n * @param decimals - Number of decimal places (default: 2)\r\n */\r\nexport const formatFileSize = (bytes: number, decimals: number = 2): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n\r\n  const k = 1024;\r\n  const dm = decimals < 0 ? 0 : decimals;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\r\n};\r\n\r\n/**\r\n * Get file extension from filename\r\n * @param filename - Filename to extract extension from\r\n */\r\nexport const getFileExtension = (filename: string): string => {\r\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);\r\n};\r\n\r\n/**\r\n * Get file type icon class based on extension\r\n * @param filename - Filename to get icon for\r\n */\r\nexport const getFileTypeIcon = (filename: string): string => {\r\n  const extension = getFileExtension(filename).toLowerCase();\r\n\r\n  switch (extension) {\r\n    case 'pdf':\r\n      return 'ri-file-pdf-line text-red-500';\r\n    case 'doc':\r\n    case 'docx':\r\n      return 'ri-file-word-line text-blue-500';\r\n    case 'xls':\r\n    case 'xlsx':\r\n      return 'ri-file-excel-line text-green-500';\r\n    case 'ppt':\r\n    case 'pptx':\r\n      return 'ri-file-ppt-line text-orange-500';\r\n    case 'jpg':\r\n    case 'jpeg':\r\n    case 'png':\r\n    case 'gif':\r\n    case 'bmp':\r\n      return 'ri-image-line text-purple-500';\r\n    case 'zip':\r\n    case 'rar':\r\n    case '7z':\r\n      return 'ri-file-zip-line text-yellow-500';\r\n    case 'txt':\r\n      return 'ri-file-text-line text-gray-500';\r\n    default:\r\n      return 'ri-file-line text-gray-500';\r\n  }\r\n};\r\n\r\nexport const formatHumanReadable = (text : string, caseType = 'first') => {\r\n  if (!text || typeof text !== 'string') return '';\r\n  \r\n  // Clean and normalize the text\r\n  let formatted = text\r\n    .trim()\r\n    .replace(/[-_]+/g, ' ') // Replace hyphens and underscores with spaces\r\n    .replace(/\\s+/g, ' ')  // Collapse multiple spaces\r\n    .toLowerCase();\r\n  \r\n  // Split into words\r\n  const words = formatted.split(' ');\r\n  \r\n  // Format based on caseType\r\n  switch (caseType.toLowerCase()) {\r\n    case 'lower':\r\n      return formatted;\r\n      \r\n    case 'upper':\r\n      return words\r\n        .map(word => word.charAt(0).toUpperCase() + word.slice(1))\r\n        .join(' ');\r\n        \r\n    case 'first':\r\n    default:\r\n      return words\r\n        .map((word, index) => \r\n          index === 0 \r\n            ? word.charAt(0).toUpperCase() + word.slice(1)\r\n            : word\r\n        )\r\n        .join(' ');\r\n  }\r\n}"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACM,MAAM,iBAAiB,SAC5B;QACA,4EAAmB,OACnB,yFAAgC;IAEhC,qCAAqC;IACrC,MAAM,gBAAgB,OAAO,WAAW,WAAW,WAAW,UAAU;IAExE,IAAG,YAAY,KAAK,WAAW;IAE/B,0BAA0B;IAC1B,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,SAAS;QAC/C,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,aAAa;IACf;IAEA,iDAAiD;IACjD,MAAM,YAAY,UAAU,MAAM,CAAC;IAEnC,mEAAmE;IACnE,MAAM,QAAQ,UAAU,KAAK,CAAC;IAC9B,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,GAAG,QAAQ,aAAa,OAAO,GAAG;IAExC,yCAAyC;IACzC,IAAI,kBAAkB;IAEtB,iGAAiG;IACjG,IAAI,YAAY,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI,GAAG;QAC9C,sCAAsC;QACtC,MAAM,CAAC,aAAa,YAAY,GAAG,YAAY,KAAK,CAAC;QAErD,sCAAsC;QACtC,8CAA8C;QAC9C,IAAI,mBAAmB,YAAY,KAAK,CAAC,GAAG,KAAK,MAAM,YAAY,KAAK,CAAC;QAEzE,4DAA4D;QAC5D,mBAAmB,iBAAiB,OAAO,CAAC,yBAAyB;QAErE,yBAAyB;QACzB,kBAAkB,mBAAmB,CAAC,cAAc,MAAM,cAAc,EAAE;IAC5E,OAAO;QACL,8DAA8D;QAC9D,kBAAkB,YAAY,OAAO,CAAC,yBAAyB;IACjE;IAEA,0BAA0B;IAC1B,OAAO,SAAS,kBAAkB;AACpC;AAYO,MAAM,eAAe,SAC1B;QACA,4EAAmB,OACnB,0EAAiB;IAEjB,MAAM,YAAY,OAAO,WAAW,WAAW,WAAW,UAAU;IACpE,IAAI,MAAM,YAAY,OAAO,AAAC,GAAW,OAAT,UAAS;IAEzC,OAAO,AAAC,GAAc,OAAZ,UAAS,KAGhB,OAHmB,UAAU,cAAc,CAAC,QAAQ;QACrD,uBAAuB;QACvB,uBAAuB;IACzB;AACF;AAOO,MAAM,eAAe,SAAC;QAAyB,4EAAmB;IACvE,MAAM,YAAY,OAAO,WAAW,WAAW,WAAW,UAAU;IACpE,IAAI,MAAM,YAAY,OAAO;IAE7B,OAAO,UAAU,cAAc,CAAC,SAAS;QACvC,uBAAuB;QACvB,uBAAuB;IACzB;AACF;AAQO,MAAM,2BAA2B,CACtC,KACA;IAEA,6BAA6B;IAC7B,IAAI,CAAC,OAAO,QAAQ,OAAO,WAAW,IAAI,QAAQ,QAAQ,GAAG;QAC3D,wCAAwC;QACxC,OAAO;IACT;IACA,uCAAuC;IACvC,OAAO,eAAe;AACxB;AAOO,MAAM,mBAAmB,SAAC;QAAwB,4EAAmB;IAC1E,MAAM,WAAW,OAAO,UAAU,WAAW,WAAW,SAAS;IACjE,IAAI,MAAM,WAAW,OAAO;IAE5B,OAAO,AAAC,GAA6B,OAA3B,SAAS,OAAO,CAAC,WAAU;AACvC;AAaO,MAAM,aAAa,SACxB;QACA,2EAAsC;QACpC,MAAM;QACN,OAAO;QACP,KAAK;IACP;IAEA,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS,SAAS,MAAM,CAAC;AAC1D;AAMO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAMO,MAAM,aAAa,CAAC;IACzB,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF;AAMO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,AAAC,GAAyB,OAAvB,WAAW,OAAM,QAAuB,OAAjB,WAAW;AAC9C;AAMO,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,AAAC,GAAiC,OAA/B,KAAK,KAAK,CAAC,gBAAgB,KAAI;IACnE,IAAI,gBAAgB,OAAO,OAAO,AAAC,GAAmC,OAAjC,KAAK,KAAK,CAAC,gBAAgB,OAAM;IACtE,IAAI,gBAAgB,SAAS,OAAO,AAAC,GAAoC,OAAlC,KAAK,KAAK,CAAC,gBAAgB,QAAO;IACzE,IAAI,gBAAgB,UAAU,OAAO,AAAC,GAAsC,OAApC,KAAK,KAAK,CAAC,gBAAgB,UAAS;IAE5E,OAAO,AAAC,GAAuC,OAArC,KAAK,KAAK,CAAC,gBAAgB,WAAU;AACjD;AAMO,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;AACzC;AAUO,MAAM,cAAc,CAAC;IAC1B,OAAO,IACJ,OAAO,CAAC,uBAAuB,CAAC,MAAM;QACrC,OAAO,UAAU,IAAI,KAAK,WAAW,KAAK,KAAK,WAAW;IAC5D,GACC,OAAO,CAAC,QAAQ;AACrB;AAMO,MAAM,eAAe,CAAC;IAC3B,OAAO,IACJ,OAAO,CAAC,uBAAuB,CAAC;QAC/B,OAAO,KAAK,WAAW;IACzB,GACC,OAAO,CAAC,QAAQ;AACrB;AAMO,MAAM,cAAc,CAAC;IAC1B,OAAO,IACJ,OAAO,CAAC,mBAAmB,SAC3B,OAAO,CAAC,WAAW,KACnB,WAAW;AAChB;AAMO,MAAM,cAAc,CAAC;IAC1B,OAAO,IACJ,OAAO,CAAC,mBAAmB,SAC3B,OAAO,CAAC,WAAW,KACnB,WAAW;AAChB;AAMO,MAAM,cAAc,CAAC;IAC1B,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC;QAC5B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;IAC/D;AACF;AAMO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAYO,MAAM,eAAe,SAAC,MAAc;QAAmB,0EAAiB;IAC7E,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,WAAW,OAAO,QAAQ;IACtD,OAAO,KAAK,SAAS,CAAC,GAAG,YAAY,OAAO,MAAM,IAAI;AACxD;AAMO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,OAAS,KAAK,WAAW;AACxD;AAMO,MAAM,sBAAsB,CAAC;IAClC,OAAO,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI;AACtC;AAOO,MAAM,cAAc,SAAC;QAAc,+EAAsB;IAC9D,IAAI,CAAC,MAAM,OAAO;IAElB,OAAO,KACJ,KAAK,CAAC,KACN,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAC7B,KAAK,CAAC,GAAG,aACT,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC;AACV;AAMO,MAAM,SAAS,CAAC;IACrB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,4BAA4B;KACrD,OAAO,CAAC,YAAY,KAAK,8CAA8C;KACvE,OAAO,CAAC,YAAY,KAAK,kCAAkC;AAChE;AAQO,MAAM,gBAAgB,SAAC,MAAc;QAAoB,6EAAoB;IAClF,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,QAAQ,IAAI,OAAO,AAAC,IAAc,OAAX,YAAW,MAAI;IAC5C,OAAO,KAAK,OAAO,CAAC,OAAO,AAAC,gBAAyB,OAAV,WAAU;AACvD;AAWO,MAAM,cAAc,SAAC;QAAe,0EAAmD;IAC5F,IAAI,CAAC,OAAO,OAAO;IAEnB,kCAAkC;IAClC,MAAM,SAAS,MAAM,OAAO,CAAC,OAAO;IAEpC,IAAI,OAAO,MAAM,GAAG,IAAI,OAAO,OAAO,+BAA+B;IAErE,OAAQ;QACN,KAAK;YACH,OAAO,AAAC,IAAyB,OAAtB,OAAO,KAAK,CAAC,GAAG,IAAG,KAAyB,OAAtB,OAAO,KAAK,CAAC,GAAG,IAAG,KAAyB,OAAtB,OAAO,KAAK,CAAC,GAAG,IAAG,KAAmB,OAAhB,OAAO,KAAK,CAAC;QAC5F,KAAK;YACH,OAAO,AAAC,IAA6B,OAA1B,OAAO,KAAK,CAAC,CAAC,IAAI,CAAC,IAAG,MAA4B,OAAxB,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,IAAG,KAAoB,OAAjB,OAAO,KAAK,CAAC,CAAC;QAC7E,KAAK;YACH,OAAO,AAAC,GAA2B,OAAzB,OAAO,KAAK,CAAC,CAAC,IAAI,CAAC,IAAG,KAA2B,OAAxB,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,IAAG,KAAoB,OAAjB,OAAO,KAAK,CAAC,CAAC;QAC3E;YACE,OAAO;IACX;AACF;AAMO,MAAM,YAAY,CAAC;IACxB,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM,OAAO;IAE3C,MAAM,CAAC,UAAU,OAAO,GAAG,MAAM,KAAK,CAAC;IACvC,IAAI,SAAS,MAAM,IAAI,GAAG,OAAO;IAEjC,MAAM,iBAAiB,SAAS,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,CAAC,SAAS,MAAM,GAAG;IAChH,OAAO,AAAC,GAAoB,OAAlB,gBAAe,KAAU,OAAP;AAC9B;AAMO,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAWO,MAAM,0BAA0B,SAAC;QAAyB,0EAAiB;IAChF,IAAI,CAAC,QAAQ,OAAO;IACpB,OAAO,AAAC,GAAY,OAAV,QAAO,KAAmC,OAAhC,OAAO,QAAQ,QAAQ,CAAC,GAAG;AACjD;AAOO,MAAM,sBAAsB,SAAC;QAAyB,0EAAiB;IAC5E,IAAI,CAAC,QAAQ,OAAO;IACpB,OAAO,AAAC,GAAY,OAAV,QAAO,KAAmC,OAAhC,OAAO,QAAQ,QAAQ,CAAC,GAAG;AACjD;AAOO,MAAM,mBAAmB,SAAC;QAAyB,0EAAiB;IACzE,IAAI,CAAC,QAAQ,OAAO;IACpB,OAAO,AAAC,GAAY,OAAV,QAAO,KAAmC,OAAhC,OAAO,QAAQ,QAAQ,CAAC,GAAG;AACjD;AAOO,MAAM,sBAAsB;QAAC,0EAAiB,GAAG;IACtD,MAAM,QAAQ;IACd,IAAI,SAAS;IAEb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IAEA,OAAO,SAAS,AAAC,GAAY,OAAV,QAAO,KAAU,OAAP,UAAW;AAC1C;AAOO,MAAM,4BAA4B,SAAC;QAAc,0EAAiB;IACvE,IAAI,CAAC,MAAM,OAAO;IAElB,6EAA6E;IAC7E,MAAM,YAAY,KAAK,OAAO,CAAC,MAAM,IAAI,WAAW;IACpD,MAAM,UAAU,UAAU,SAAS,CAAC,GAAG;IAEvC,OAAO,AAAC,GAAY,OAAV,QAAO,KAAW,OAAR;AACtB;AAMO,MAAM,SAAS,CAAC;IACrB,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,GAAG,OAAO;IAElC,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG;IAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC;IACtB,MAAM,SAAS,IAAI,MAAM,CAAC,GAAG,MAAM,GAAG;IAEtC,OAAO,AAAC,GAAU,OAAR,OAAiB,OAAT,QAAa,OAAJ;AAC7B;AAUO,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,QAAQ,OAAO;IAEpB,OAAO,OACJ,OAAO,CAAC,MAAM,KACd,OAAO,CAAC,SAAS,CAAA,OAAQ,KAAK,WAAW;AAC9C;AAQO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,cAAc,OAAO,WAAW;IAEtC,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;YACH,OAAO;QAET;YACE,OAAO;IACX;AACF;AAWO,MAAM,iBAAiB,SAAC;QAAe,4EAAmB;IAC/D,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,KAAK,WAAW,IAAI,IAAI;IAC9B,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAE/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,MAAM,KAAK,CAAC,EAAE;AAC1E;AAMO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAMO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,YAAY,iBAAiB,UAAU,WAAW;IAExD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,MAAM,sBAAsB,SAAC;QAAe,4EAAW;IAC5D,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU,OAAO;IAE9C,+BAA+B;IAC/B,IAAI,YAAY,KACb,IAAI,GACJ,OAAO,CAAC,UAAU,KAAK,8CAA8C;KACrE,OAAO,CAAC,QAAQ,KAAM,2BAA2B;KACjD,WAAW;IAEd,mBAAmB;IACnB,MAAM,QAAQ,UAAU,KAAK,CAAC;IAE9B,2BAA2B;IAC3B,OAAQ,SAAS,WAAW;QAC1B,KAAK;YACH,OAAO;QAET,KAAK;YACH,OAAO,MACJ,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;QAEV,KAAK;QACL;YACE,OAAO,MACJ,GAAG,CAAC,CAAC,MAAM,QACV,UAAU,IACN,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,KAC1C,MAEL,IAAI,CAAC;IACZ;AACF", "debugId": null}}, {"offset": {"line": 1454, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/ConfirmationModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ReactNode } from 'react';\r\n\r\ninterface ConfirmationModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onConfirm: () => void;\r\n  title: string;\r\n  message: string | ReactNode;\r\n  confirmText?: string;\r\n  cancelText?: string;\r\n  confirmVariant?: 'danger' | 'primary' | 'warning';\r\n  loading?: boolean;\r\n  icon?: ReactNode;\r\n}\r\n\r\nexport default function ConfirmationModal({\r\n  isOpen,\r\n  onClose,\r\n  onConfirm,\r\n  title,\r\n  message,\r\n  confirmText = 'Confirm',\r\n  cancelText = 'Cancel',\r\n  confirmVariant = 'danger',\r\n  loading = false,\r\n  icon,\r\n}: ConfirmationModalProps) {\r\n  if (!isOpen) return null;\r\n\r\n  const getConfirmButtonStyles = () => {\r\n    switch (confirmVariant) {\r\n      case 'danger':\r\n        return 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500';\r\n      case 'primary':\r\n        return 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500';\r\n      case 'warning':\r\n        return 'bg-yellow-600 hover:bg-yellow-700 text-white focus:ring-yellow-500';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500';\r\n    }\r\n  };\r\n\r\n  const getDefaultIcon = () => {\r\n    switch (confirmVariant) {\r\n      case 'danger':\r\n        return (\r\n          <div className=\"flex-shrink-0\">\r\n            <div className=\"h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center\">\r\n              <i className=\"ri-delete-bin-line text-red-600 dark:text-red-400 text-xl\"></i>\r\n            </div>\r\n          </div>\r\n        );\r\n      case 'warning':\r\n        return (\r\n          <div className=\"flex-shrink-0\">\r\n            <div className=\"h-10 w-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center\">\r\n              <i className=\"ri-alert-line text-yellow-600 dark:text-yellow-400 text-xl\"></i>\r\n            </div>\r\n          </div>\r\n        );\r\n      default:\r\n        return (\r\n          <div className=\"flex-shrink-0\">\r\n            <div className=\"h-10 w-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center\">\r\n              <i className=\"ri-information-line text-blue-600 dark:text-blue-400 text-xl\"></i>\r\n            </div>\r\n          </div>\r\n        );\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all\">\r\n        <div className=\"p-6\">\r\n          {/* Header */}\r\n          <div className=\"flex items-start mb-4\">\r\n            {icon || getDefaultIcon()}\r\n            <div className=\"ml-4 flex-1\">\r\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\">\r\n                {title}\r\n              </h3>\r\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                {typeof message === 'string' ? (\r\n                  <p>{message}</p>\r\n                ) : (\r\n                  message\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Actions */}\r\n          <div className=\"flex space-x-3 mt-6\">\r\n            <button\r\n              onClick={onConfirm}\r\n              disabled={loading}\r\n              className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed ${getConfirmButtonStyles()}`}\r\n            >\r\n              {loading ? (\r\n                <div className=\"flex items-center justify-center\">\r\n                  <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                  </svg>\r\n                  Processing...\r\n                </div>\r\n              ) : (\r\n                confirmText\r\n              )}\r\n            </button>\r\n            <button\r\n              onClick={onClose}\r\n              disabled={loading}\r\n              className=\"flex-1 px-4 py-2 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              {cancelText}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAiBe,SAAS,kBAAkB,KAWjB;QAXiB,EACxC,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,OAAO,EACP,cAAc,SAAS,EACvB,aAAa,QAAQ,EACrB,iBAAiB,QAAQ,EACzB,UAAU,KAAK,EACf,IAAI,EACmB,GAXiB;IAYxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;YAIrB,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;YAIrB;gBACE,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;QAIvB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;4BACZ,QAAQ;0CACT,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX;;;;;;kDAEH,6LAAC;wCAAI,WAAU;kDACZ,OAAO,YAAY,yBAClB,6LAAC;sDAAG;;;;;mDAEJ;;;;;;;;;;;;;;;;;;kCAOR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAW,AAAC,sNAA8O,OAAzB;0CAEhO,wBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;4CAA6C,OAAM;4CAA6B,MAAK;4CAAO,SAAQ;;8DACjH,6LAAC;oDAAO,WAAU;oDAAa,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAe,aAAY;;;;;;8DACxF,6LAAC;oDAAK,WAAU;oDAAa,MAAK;oDAAe,GAAE;;;;;;;;;;;;wCAC/C;;;;;;2CAIR;;;;;;0CAGJ,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;KA5GwB", "debugId": null}}, {"offset": {"line": 1693, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/documents/DocumentPreviewModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { documentService } from '@/services/documentService';\r\n\r\ninterface Document {\r\n  document_id: string;\r\n  document_type: string;\r\n  file_name: string;\r\n  entity_type: string;\r\n  entity_id: string;\r\n  file_path: string;\r\n  file_size: number;\r\n  mime_type: string;\r\n  is_required: boolean;\r\n  created_at: string;\r\n  updated_at: string;\r\n  created_by: string;\r\n  updated_by?: string;\r\n}\r\n\r\ninterface DocumentPreviewModalProps {\r\n  document: Document | null;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onDownload?: (document: Document) => void;\r\n}\r\n\r\nconst DocumentPreviewModal: React.FC<DocumentPreviewModalProps> = ({\r\n  document,\r\n  isOpen,\r\n  onClose,\r\n  onDownload,\r\n}) => {\r\n  const [previewUrl, setPreviewUrl] = useState<string | null>(null);\r\n  const [loading, setLoading] = useState<boolean>(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // Format file size helper\r\n  const formatFileSize = (bytes: number): string => {\r\n    if (bytes === 0) return '0 Bytes';\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  };\r\n\r\n  // Format date helper\r\n  const formatDate = (dateString: string): string => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n    });\r\n  };\r\n\r\n  // Load preview when document changes\r\n  useEffect(() => {\r\n    if (document && isOpen) {\r\n      loadPreview();\r\n    } else {\r\n      // Clean up preview URL when modal closes\r\n      if (previewUrl) {\r\n        URL.revokeObjectURL(previewUrl);\r\n        setPreviewUrl(null);\r\n      }\r\n    }\r\n\r\n    return () => {\r\n      if (previewUrl) {\r\n        URL.revokeObjectURL(previewUrl);\r\n      }\r\n    };\r\n  }, [document, isOpen]);\r\n\r\n  const loadPreview = async () => {\r\n    if (!document) return;\r\n\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      // Check if document is previewable\r\n      if (!documentService.isPreviewable(document.mime_type)) {\r\n        setError(`Preview not available for ${document.mime_type} files. You can download the file instead.`);\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      const blob = await documentService.previewDocument(document.document_id);\r\n      const url = URL.createObjectURL(blob);\r\n      setPreviewUrl(url);\r\n    } catch (err: any) {\r\n      console.error('Error loading preview:', err);\r\n      setError(err.message || 'Failed to load document preview');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDownload = async () => {\r\n    if (!document) return;\r\n\r\n    try {\r\n      if (onDownload) {\r\n        onDownload(document);\r\n      } else {\r\n        // Default download behavior\r\n        const blob = await documentService.downloadDocument(document.document_id);\r\n        const url = URL.createObjectURL(blob);\r\n        const downloadLink = window.document.createElement('a');\r\n        downloadLink.href = url;\r\n        downloadLink.download = document.file_name;\r\n        window.document.body.appendChild(downloadLink);\r\n        downloadLink.click();\r\n        downloadLink.remove();\r\n        URL.revokeObjectURL(url);\r\n      }\r\n    } catch (err: any) {\r\n      console.error('Error downloading document:', err);\r\n      setError('Failed to download document');\r\n    }\r\n  };\r\n\r\n  if (!isOpen || !document) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n      <div className=\"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\r\n        {/* Background overlay */}\r\n        <div\r\n          className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\"\r\n          onClick={onClose}\r\n        ></div>\r\n\r\n        {/* Modal */}\r\n        <div className=\"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full\">\r\n          {/* Header */}\r\n          <div className=\"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"flex-shrink-0 h-10 w-10\">\r\n                  <div className=\"h-10 w-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center\">\r\n                    <i className=\"ri-file-text-line text-gray-500 dark:text-gray-400\"></i>\r\n                  </div>\r\n                </div>\r\n                <div className=\"ml-4\">\r\n                  <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n                    {document.file_name}\r\n                  </h3>\r\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                    {document.mime_type} • {formatFileSize(document.file_size)} • {formatDate(document.created_at)}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <button\r\n                  onClick={handleDownload}\r\n                  className=\"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                >\r\n                  <i className=\"ri-download-line mr-2\"></i>\r\n                  Download\r\n                </button>\r\n                <button\r\n                  onClick={onClose}\r\n                  className=\"inline-flex items-center p-2 border border-transparent rounded-md text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                >\r\n                  <i className=\"ri-close-line text-xl\"></i>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Content */}\r\n          <div className=\"bg-gray-50 dark:bg-gray-900 px-4 py-5 sm:p-6 overflow-auto\" style={{ minHeight: '500px', maxHeight: '70vh' }}>\r\n            {loading && (\r\n              <div className=\"flex items-center justify-center h-64\">\r\n                <div className=\"text-center\">\r\n                  <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n                  <p className=\"text-gray-600 dark:text-gray-400\">Loading preview...</p>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {error && (\r\n              <div className=\"flex items-center justify-center h-64\">\r\n                <div className=\"text-center\">\r\n                  <div className=\"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/20 mb-4\">\r\n                    <i className=\"ri-error-warning-line text-red-600 dark:text-red-400 text-xl\"></i>\r\n                  </div>\r\n                  <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    Preview Not Available\r\n                  </h3>\r\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400 mb-4\">\r\n                    {error}\r\n                  </p>\r\n                  <button\r\n                    onClick={handleDownload}\r\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                  >\r\n                    <i className=\"ri-download-line mr-2\"></i>\r\n                    Download File\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {previewUrl && !loading && !error && (\r\n              <div className=\"w-full h-full\">\r\n                {document.mime_type.startsWith('image/') ? (\r\n                  <img\r\n                    src={previewUrl}\r\n                    alt={document.file_name}\r\n                    className=\"max-w-full max-h-full mx-auto object-contain\"\r\n                  />\r\n                ) : document.mime_type === 'application/pdf' ? (\r\n                  <iframe\r\n                    src={previewUrl}\r\n                    className=\"w-full h-full min-h-96\"\r\n                    title={document.file_name}\r\n                  />\r\n                ) : document.mime_type.startsWith('text/') ? (\r\n                  <iframe\r\n                    src={previewUrl}\r\n                    className=\"w-full h-full min-h-96 bg-white dark:bg-gray-800\"\r\n                    title={document.file_name}\r\n                  />\r\n                ) : (\r\n                  <div className=\"flex items-center justify-center h-64\">\r\n                    <p className=\"text-gray-500 dark:text-gray-400\">\r\n                      Preview not supported for this file type\r\n                    </p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DocumentPreviewModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AA4BA,MAAM,uBAA4D;QAAC,EACjE,QAAQ,EACR,MAAM,EACN,OAAO,EACP,UAAU,EACX;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,0BAA0B;IAC1B,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBAAqB;IACrB,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,YAAY,QAAQ;gBACtB;YACF,OAAO;gBACL,yCAAyC;gBACzC,IAAI,YAAY;oBACd,IAAI,eAAe,CAAC;oBACpB,cAAc;gBAChB;YACF;YAEA;kDAAO;oBACL,IAAI,YAAY;wBACd,IAAI,eAAe,CAAC;oBACtB;gBACF;;QACF;yCAAG;QAAC;QAAU;KAAO;IAErB,MAAM,cAAc;QAClB,IAAI,CAAC,UAAU;QAEf,WAAW;QACX,SAAS;QAET,IAAI;YACF,mCAAmC;YACnC,IAAI,CAAC,qIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,SAAS,SAAS,GAAG;gBACtD,SAAS,AAAC,6BAA+C,OAAnB,SAAS,SAAS,EAAC;gBACzD,WAAW;gBACX;YACF;YAEA,MAAM,OAAO,MAAM,qIAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,SAAS,WAAW;YACvE,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,cAAc;QAChB,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,IAAI,YAAY;gBACd,WAAW;YACb,OAAO;gBACL,4BAA4B;gBAC5B,MAAM,OAAO,MAAM,qIAAA,CAAA,kBAAe,CAAC,gBAAgB,CAAC,SAAS,WAAW;gBACxE,MAAM,MAAM,IAAI,eAAe,CAAC;gBAChC,MAAM,eAAe,OAAO,QAAQ,CAAC,aAAa,CAAC;gBACnD,aAAa,IAAI,GAAG;gBACpB,aAAa,QAAQ,GAAG,SAAS,SAAS;gBAC1C,OAAO,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC;gBACjC,aAAa,KAAK;gBAClB,aAAa,MAAM;gBACnB,IAAI,eAAe,CAAC;YACtB;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS;QACX;IACF;IAEA,IAAI,CAAC,UAAU,CAAC,UAAU,OAAO;IAEjC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;0DAGjB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,SAAS,SAAS;;;;;;kEAErB,6LAAC;wDAAE,WAAU;;4DACV,SAAS,SAAS;4DAAC;4DAAI,eAAe,SAAS,SAAS;4DAAE;4DAAI,WAAW,SAAS,UAAU;;;;;;;;;;;;;;;;;;;kDAInG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC;wDAAE,WAAU;;;;;;oDAA4B;;;;;;;0DAG3C,6LAAC;gDACC,SAAS;gDACT,WAAU;0DAEV,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOrB,6LAAC;4BAAI,WAAU;4BAA6D,OAAO;gCAAE,WAAW;gCAAS,WAAW;4BAAO;;gCACxH,yBACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAE,WAAU;0DAAmC;;;;;;;;;;;;;;;;;gCAKrD,uBACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAE,WAAU;0DACV;;;;;;0DAEH,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC;wDAAE,WAAU;;;;;;oDAA4B;;;;;;;;;;;;;;;;;;gCAOhD,cAAc,CAAC,WAAW,CAAC,uBAC1B,6LAAC;oCAAI,WAAU;8CACZ,SAAS,SAAS,CAAC,UAAU,CAAC,0BAC7B,6LAAC;wCACC,KAAK;wCACL,KAAK,SAAS,SAAS;wCACvB,WAAU;;;;;mFAEV,SAAS,SAAS,KAAK,kCACzB,6LAAC;wCACC,KAAK;wCACL,WAAU;wCACV,OAAO,SAAS,SAAS;;;;;mFAEzB,SAAS,SAAS,CAAC,UAAU,CAAC,yBAChC,6LAAC;wCACC,KAAK;wCACL,WAAU;wCACV,OAAO,SAAS,SAAS;;;;;iGAG3B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYpE;GAvNM;KAAA;uCAyNS", "debugId": null}}, {"offset": {"line": 2122, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/tasks/PaymentTaskComponent.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { invoiceService } from '@/services/invoiceService';\r\nimport { paymentService } from '@/services/paymentService';\r\nimport { adminPaymentService } from '@/services/adminPaymentService';\r\nimport { documentService } from '@/services/documentService';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { formatCurrency } from '@/utils/formatters';\r\nimport { Invoice, Payment } from '@/types/invoice';\r\nimport { Task } from '@/types';\r\nimport ActivityHistory from '../common/ActivityHistory';\r\nimport ConfirmationModal from '../common/ConfirmationModal';\r\nimport DocumentPreviewModal from '../documents/DocumentPreviewModal';\r\nimport ActivityNotesModal from '../evaluation/ActivityNotesModal';\r\n\r\ninterface PaymentTaskComponentProps {\r\n  task: Task;\r\n  onTaskUpdate: () => void;\r\n}\r\n\r\n\r\nconst PaymentTaskComponent: React.FC<PaymentTaskComponentProps> = ({ task, onTaskUpdate }) => {\r\n  const { showToast } = useToast();\r\n  const [invoice, setInvoice] = useState<Invoice | null>(null);\r\n  const [payments, setPayments] = useState<Payment[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [processingPayment, setProcessingPayment] = useState<string | null>(null);\r\n  const [isCommunicationModalOpen, setIsCommunicationModalOpen] = useState(false);\r\n\r\n  // Confirmation modal states\r\n  const [showApproveModal, setShowApproveModal] = useState(false);\r\n  const [showRejectModal, setShowRejectModal] = useState(false);\r\n  const [selectedPaymentId, setSelectedPaymentId] = useState<string | null>(null);\r\n  const [rejectionReason, setRejectionReason] = useState('');\r\n\r\n  // Document preview states\r\n  const [selectedDocument, setSelectedDocument] = useState<any>(null);\r\n  const [isDocumentPreviewOpen, setIsDocumentPreviewOpen] = useState(false);\r\n  const [paymentDocuments, setPaymentDocuments] = useState<Record<string, any[]>>({});\r\n\r\n  useEffect(() => {\r\n    if (task && task.entity_type == 'payment' && task.entity_id) {\r\n      loadInvoiceAndPayments();\r\n    }\r\n  }, [task]);\r\n\r\n  const loadInvoiceAndPayments = async () => {\r\n    try {\r\n      setLoading(true);\r\n\r\n      // Load invoice details\r\n      const payment = await paymentService.getPaymentById(task.entity_id!);\r\n      const invoice = await invoiceService.getInvoiceById(payment.invoice_id!);\r\n      if(invoice) setInvoice(invoice);\r\n\r\n      // Load payments for this invoice\r\n      const paymentsResponse = await paymentService.getInvoicePayments(invoice.invoice_id!);\r\n      if (paymentsResponse.success && paymentsResponse.data) {\r\n        setPayments(paymentsResponse.data);\r\n        // Load documents for each payment\r\n        await loadPaymentDocuments(paymentsResponse.data);\r\n      }\r\n    } catch (error) {\r\n      showToast('Failed to load invoice and payment details', 'error');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Load documents for payments (proof of payment documents)\r\n  const loadPaymentDocuments = async (payments: Payment[]) => {\r\n    try {\r\n      const documentsMap: Record<string, any[]> = {};\r\n\r\n      for (const payment of payments) {\r\n        try {\r\n          // Fetch documents for this payment using polymorphic relationship\r\n          const response = await documentService.getDocumentsByEntity('payment', payment.payment_id);\r\n          if (response.success && response.data) {\r\n            documentsMap[payment.payment_id] = response.data;\r\n          }\r\n        } catch (error) {\r\n          // Continue loading other payment documents even if one fails\r\n          documentsMap[payment.payment_id] = [];\r\n        }\r\n      }\r\n\r\n      setPaymentDocuments(documentsMap);\r\n    } catch (error) {\r\n      console.error('Error loading payment documents:', error);\r\n    }\r\n  };\r\n\r\n  // Handle document preview\r\n  const handleDocumentPreview = (document: any) => {\r\n    setSelectedDocument(document);\r\n    setIsDocumentPreviewOpen(true);\r\n  };\r\n\r\n  // Close document preview\r\n  const handleCloseDocumentPreview = () => {\r\n    setIsDocumentPreviewOpen(false);\r\n    setSelectedDocument(null);\r\n  };\r\n\r\n  // Handler to show approve confirmation modal\r\n  const handleApproveClick = (paymentId: string) => {\r\n    setSelectedPaymentId(paymentId);\r\n    setShowApproveModal(true);\r\n  };\r\n\r\n  // Handler to show reject confirmation modal\r\n  const handleRejectClick = (paymentId: string) => {\r\n    setSelectedPaymentId(paymentId);\r\n    setRejectionReason('');\r\n    setShowRejectModal(true);\r\n  };\r\n\r\n  // Actual approve payment function\r\n  const handleApprovePayment = async () => {\r\n    if (!selectedPaymentId) return;\r\n\r\n\r\n\r\n    try {\r\n      setProcessingPayment(selectedPaymentId);\r\n\r\n      // Use the admin payment service for standardized API handling\r\n      const result = await adminPaymentService.approvePayment(selectedPaymentId);\r\n\r\n      if (!result.success) {\r\n        // Handle specific error responses from the API\r\n        const errorCode = result.error || 'UNKNOWN_ERROR';\r\n\r\n        switch (errorCode) {\r\n          case 'PAYMENT_NOT_FOUND':\r\n            showToast('Payment not found. It may have been deleted or moved.', 'error');\r\n            break;\r\n          case 'PAYMENT_UPDATE_CONFLICT':\r\n            showToast('Payment update conflict. Another user may have modified this payment.', 'error');\r\n            break;\r\n          case 'INVALID_PAYMENT_DATA':\r\n            showToast('Invalid payment data provided. Please check the payment details.', 'error');\r\n            break;\r\n          default:\r\n            showToast(result.message || 'Failed to approve payment', 'error');\r\n        }\r\n        return;\r\n      }\r\n\r\n      // Show success message based on the result\r\n      showToast(result.message, 'success');\r\n\r\n      await loadInvoiceAndPayments();\r\n      onTaskUpdate();\r\n      setShowApproveModal(false);\r\n      setSelectedPaymentId(null);\r\n    } catch (error) {\r\n      if (error instanceof TypeError && error.message.includes('fetch')) {\r\n        showToast('Network error. Please check your connection and try again.', 'error');\r\n      } else {\r\n        const errorMessage = error instanceof Error ? error.message : 'Failed to approve payment';\r\n        showToast(errorMessage, 'error');\r\n      }\r\n    } finally {\r\n      setProcessingPayment(null);\r\n    }\r\n  };\r\n\r\n  // Actual reject payment function\r\n  const handleRejectPayment = async () => {\r\n    if (!selectedPaymentId || !rejectionReason.trim()) {\r\n      showToast('Please provide a reason for rejection', 'error');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setProcessingPayment(selectedPaymentId);\r\n\r\n      // Use the admin payment service for standardized API handling\r\n      const result = await adminPaymentService.rejectPayment(selectedPaymentId, rejectionReason.trim());\r\n\r\n      if (!result.success) {\r\n        // Handle specific error responses from the API\r\n        const errorCode = result.error || 'UNKNOWN_ERROR';\r\n\r\n        switch (errorCode) {\r\n          case 'PAYMENT_NOT_FOUND':\r\n            showToast('Payment not found. It may have been deleted or moved.', 'error');\r\n            break;\r\n          case 'PAYMENT_UPDATE_CONFLICT':\r\n            showToast('Payment update conflict. Another user may have modified this payment.', 'error');\r\n            break;\r\n          case 'INVALID_PAYMENT_DATA':\r\n            showToast('Invalid payment data provided. Please check the payment details.', 'error');\r\n            break;\r\n          default:\r\n            showToast(result.message || 'Failed to reject payment', 'error');\r\n        }\r\n        return;\r\n      }\r\n\r\n      showToast(result.message, 'success');\r\n      await loadInvoiceAndPayments();\r\n      onTaskUpdate();\r\n      setShowRejectModal(false);\r\n      setSelectedPaymentId(null);\r\n      setRejectionReason('');\r\n    } catch (error) {\r\n      if (error instanceof TypeError && error.message.includes('fetch')) {\r\n        showToast('Network error. Please check your connection and try again.', 'error');\r\n      } else {\r\n        const errorMessage = error instanceof Error ? error.message : 'Failed to reject payment';\r\n        showToast(errorMessage, 'error');\r\n      }\r\n    } finally {\r\n      setProcessingPayment(null);\r\n    }\r\n  };\r\n\r\n  const handleEmailClient = () => {\r\n    setIsCommunicationModalOpen(true);\r\n  };\r\n\r\n  const getPaymentStatusColor = (status: string) => {\r\n    switch (status.toLowerCase()) {\r\n      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'approved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'rejected': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\r\n      case 'processing': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  };\r\n\r\n  const getInvoiceStatusColor = (status: string) => {\r\n    switch (status.toLowerCase()) {\r\n      case 'draft': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'sent': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\r\n      case 'approved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'overdue': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\r\n      case 'cancelled': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center py-8\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mr-3\"></div>\r\n        <span className=\"text-gray-600 dark:text-gray-400\">Loading payment details...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!invoice) {\r\n    return (\r\n      <div className=\"text-center py-8\">\r\n        <div className=\"bg-yellow-100 dark:bg-yellow-900/20 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center\">\r\n          <i className=\"ri-error-warning-line text-2xl text-yellow-600 dark:text-yellow-400\"></i>\r\n        </div>\r\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">Invoice Not Found</h3>\r\n        <p className=\"text-gray-600 dark:text-gray-400\">\r\n          The invoice associated with this task could not be found.\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n  const pendingPayments = payments.filter(payment => payment.status.toLowerCase() === 'pending');\r\n  const totalPaid = invoice.amount - (invoice.balance ?? 0);\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Invoice Details */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm\">\r\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\">\r\n              <i className=\"ri-file-list-3-line mr-2 text-blue-600\"></i>\r\n              Invoice Details\r\n            </h2>\r\n            {invoice.client && (\r\n              <button\r\n                onClick={handleEmailClient}\r\n                className=\"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\"\r\n                title=\"Email Client\"\r\n              >\r\n                <i className=\"ri-mail-line mr-2\"></i>\r\n                Email Client\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n        <div className=\"p-6\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n            <div>\r\n              <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400 mb-2\">Invoice Information</h3>\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Invoice Number:</span>\r\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{invoice.invoice_number}</span>\r\n                </div>\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Amount:</span>\r\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{formatCurrency(invoice.amount)}</span>\r\n                </div>\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Status:</span>\r\n                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getInvoiceStatusColor(invoice.status)}`}>\r\n                    {invoice.status.toUpperCase()}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Due Date:</span>\r\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                    {new Date(invoice.due_date).toLocaleDateString()}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Client Information */}\r\n            {invoice.client && (\r\n              <div>\r\n                <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400 mb-2\">Client Information</h3>\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"flex justify-between\">\r\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Client:</span>\r\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                      {`${invoice.client.name}`}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"flex justify-between\">\r\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Email:</span>\r\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                      {invoice.client.email}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Payment Summary */}\r\n          <div className=\"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700\">\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n              <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n                    <i className=\"ri-money-dollar-circle-line text-blue-600 dark:text-blue-400\"></i>\r\n                  </div>\r\n                  <div className=\"ml-3\">\r\n                    <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Amount</p>\r\n                    <p className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">{formatCurrency(invoice.amount)}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div className=\"bg-green-50 dark:bg-green-900/20 rounded-lg p-4\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n                    <i className=\"ri-check-line text-green-600 dark:text-green-400\"></i>\r\n                  </div>\r\n                  <div className=\"ml-3\">\r\n                    <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Paid Amount</p>\r\n                    <p className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">{formatCurrency(totalPaid)}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div className=\"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"p-2 bg-orange-100 dark:bg-orange-900 rounded-lg\">\r\n                    <i className=\"ri-time-line text-orange-600 dark:text-orange-400\"></i>\r\n                  </div>\r\n                  <div className=\"ml-3\">\r\n                    <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Remaining</p>\r\n                    <p className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">{formatCurrency(invoice.balance ?? 0)}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Pending Payments for Approval */}\r\n      {pendingPayments.length > 0 && (\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm\">\r\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\r\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\">\r\n              <i className=\"ri-time-line mr-2 text-orange-600\"></i>\r\n              Pending Payments ({pendingPayments.length})\r\n            </h2>\r\n          </div>\r\n          <div className=\"p-6\">\r\n            <div className=\"space-y-4\">\r\n              {pendingPayments.map((payment) => (\r\n                <div key={payment.payment_id} className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\r\n                  <div className=\"flex items-start justify-between\">\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                        <div>\r\n                          <div className=\"space-y-2\">\r\n                            <div className=\"flex justify-between\">\r\n                              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Amount:</span>\r\n                              <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{formatCurrency(payment.amount)}</span>\r\n                            </div>\r\n                            <div className=\"flex justify-between\">\r\n                              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Method:</span>\r\n                              <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{payment.payment_method}</span>\r\n                            </div>\r\n                            <div className=\"flex justify-between\">\r\n                              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Date:</span>\r\n                              <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                                {payment.paid_date ? new Date(payment.paid_date).toLocaleDateString() : new Date(payment.issue_date).toLocaleDateString()}\r\n                              </span>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                        <div>\r\n                          <div className=\"space-y-2\">\r\n                            {payment.transaction_reference && (\r\n                              <div className=\"flex justify-between\">\r\n                                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Reference:</span>\r\n                                <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{payment.transaction_reference}</span>\r\n                              </div>\r\n                            )}\r\n                            <div className=\"flex justify-between\">\r\n                              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Status:</span>\r\n                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentStatusColor(payment.status)}`}>\r\n                                {payment.status.toUpperCase()}\r\n                              </span>\r\n                            </div>\r\n                            <div className=\"flex justify-between\">\r\n                              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Submitted:</span>\r\n                              <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                                {new Date(payment.created_at).toLocaleDateString()}\r\n                              </span>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                      \r\n                      {payment.notes && (\r\n                        <div className=\"mt-3 pt-3 border-t border-gray-200 dark:border-gray-700\">\r\n                          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                            <strong>Notes:</strong> {payment.notes}\r\n                          </p>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Proof of Payment Documents */}\r\n                      {paymentDocuments[payment.payment_id] && paymentDocuments[payment.payment_id].length > 0 && (\r\n                        <div className=\"mt-3 pt-3 border-t border-gray-200 dark:border-gray-700\">\r\n                          <div className=\"space-y-2\">\r\n                            <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">Proof of Payment:</span>\r\n                            {paymentDocuments[payment.payment_id].map((document) => (\r\n                              <div key={document.document_id} className=\"flex items-center justify-between\">\r\n                                <span className=\"text-sm text-gray-600 dark:text-gray-400 truncate\">\r\n                                  {document.file_name}\r\n                                </span>\r\n                                <div className=\"flex items-center space-x-2\">\r\n                                  {documentService.isPreviewable(document.mime_type) && (\r\n                                    <button\r\n                                      onClick={() => handleDocumentPreview(document)}\r\n                                      className=\"inline-flex items-center text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\"\r\n                                      title=\"Preview document\"\r\n                                    >\r\n                                      <i className=\"ri-eye-line mr-1\"></i>\r\n                                      Preview\r\n                                    </button>\r\n                                  )}\r\n                                </div>\r\n                              </div>\r\n                            ))}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                    \r\n                    <div className=\"ml-4 flex flex-col space-y-2\">\r\n                      <button\r\n                        onClick={() => handleApproveClick(payment.payment_id)}\r\n                        disabled={processingPayment === payment.payment_id}\r\n                        className=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50\"\r\n                      >\r\n                        {processingPayment === payment.payment_id ? (\r\n                          <>\r\n                            <div className=\"animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1\"></div>\r\n                            Processing...\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <i className=\"ri-check-line mr-1\"></i>\r\n                            Approve\r\n                          </>\r\n                        )}\r\n                      </button>\r\n                      <button\r\n                        onClick={() => handleRejectClick(payment.payment_id)}\r\n                        disabled={processingPayment === payment.payment_id}\r\n                        className=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50\"\r\n                      >\r\n                        <i className=\"ri-close-line mr-1\"></i>\r\n                        Reject\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* All Payments History */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm\">\r\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\r\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\">\r\n            <i className=\"ri-history-line mr-2 text-purple-600\"></i>\r\n            Payment History ({payments.length})\r\n          </h2>\r\n        </div>\r\n        <div className=\"p-6\">\r\n          {payments.length === 0 ? (\r\n            <div className=\"text-center py-8\">\r\n              <i className=\"ri-money-dollar-circle-line text-4xl text-gray-400 mb-4\"></i>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">No Payments Yet</h3>\r\n              <p className=\"text-gray-600 dark:text-gray-400\">\r\n                No payments have been submitted for this invoice.\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"overflow-x-auto\">\r\n              <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n                <thead className=\"bg-gray-50 dark:bg-gray-700\">\r\n                  <tr>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                      Amount\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                      Method\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                      Reference\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                      Status\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                      Date\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                      Proof\r\n                    </th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n                  {payments.map((payment) => (\r\n                    <tr key={payment.payment_id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                        {formatCurrency(payment.amount)}\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100\">\r\n                        {payment.payment_method}\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\r\n                        {payment.transaction_reference || '-'}\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentStatusColor(payment.status)}`}>\r\n                          {payment.status.toUpperCase()}\r\n                        </span>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\r\n                        {payment.paid_date ? new Date(payment.paid_date).toLocaleDateString() : new Date(payment.issue_date).toLocaleDateString()}\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\r\n                        {paymentDocuments[payment.payment_id] && paymentDocuments[payment.payment_id].length > 0 ? (\r\n                          <div className=\"flex items-center space-x-2\">\r\n                            {paymentDocuments[payment.payment_id].slice(0, 1).map((document) => (\r\n                              <div key={document.document_id} className=\"flex items-center space-x-1\">\r\n                                {documentService.isPreviewable(document.mime_type) && (\r\n                                  <button\r\n                                    onClick={() => handleDocumentPreview(document)}\r\n                                    className=\"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\"\r\n                                    title={`Preview ${document.file_name}`}\r\n                                  >\r\n                                    <i className=\"ri-eye-line\"></i> View\r\n                                  </button>\r\n                                )}\r\n\r\n                              </div>\r\n                            ))}\r\n                            {paymentDocuments[payment.payment_id].length > 1 && (\r\n                              <span className=\"text-xs text-gray-400\">\r\n                                +{paymentDocuments[payment.payment_id].length - 1} more\r\n                              </span>\r\n                            )}\r\n                          </div>\r\n                        ) : (\r\n                          <span className=\"text-gray-400\">No documents</span>\r\n                        )}\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* All Activity notes History */}\r\n      <ActivityHistory\r\n        entityType=\"invoice\"\r\n        entityId={task.entity_id!}\r\n        title=\"Payment Activity History\"\r\n        showSearch={true}\r\n        showFilters={false}\r\n        maxHeight=\"max-h-96\"\r\n        className=\"border-0 rounded-none\">\r\n      </ActivityHistory>\r\n\r\n      {/* Communication Center Modal */}\r\n      {invoice && invoice.invoice_id && (\r\n        <ActivityNotesModal\r\n          isOpen={isCommunicationModalOpen}\r\n          onClose={() => setIsCommunicationModalOpen(false)}\r\n          entityType=\"invoice\"\r\n          entityId={invoice.invoice_id}\r\n          initialEmails={invoice.client?.email}\r\n        />\r\n      )}\r\n\r\n      {/* Approve Payment Confirmation Modal */}\r\n      <ConfirmationModal\r\n        isOpen={showApproveModal}\r\n        onClose={() => {\r\n          setShowApproveModal(false);\r\n          setSelectedPaymentId(null);\r\n        }}\r\n        onConfirm={handleApprovePayment}\r\n        title=\"Approve Payment\"\r\n        message={\r\n          <div className=\"space-y-3\">\r\n            <p>Are you sure you want to approve this payment?</p>\r\n            <div className=\"bg-green-50 dark:bg-green-900/20 rounded-lg p-3 text-sm\">\r\n              <p className=\"font-medium text-green-900 dark:text-green-100 mb-2\">This action will:</p>\r\n              <ul className=\"text-green-700 dark:text-green-300 space-y-1\">\r\n                <li>• Mark the payment as approved and paid</li>\r\n                <li>• Update the invoice status if fully paid</li>\r\n                <li>• Send email notification to the client</li>\r\n                <li>• Create an activity log entry</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        }\r\n        confirmText=\"Approve Payment\"\r\n        confirmVariant=\"primary\"\r\n        loading={processingPayment === selectedPaymentId}\r\n        icon={\r\n          <div className=\"flex-shrink-0\">\r\n            <div className=\"h-10 w-10 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center\">\r\n              <i className=\"ri-check-line text-green-600 dark:text-green-400 text-xl\"></i>\r\n            </div>\r\n          </div>\r\n        }\r\n      />\r\n\r\n      {/* Reject Payment Confirmation Modal */}\r\n      <ConfirmationModal\r\n        isOpen={showRejectModal}\r\n        onClose={() => {\r\n          setShowRejectModal(false);\r\n          setSelectedPaymentId(null);\r\n          setRejectionReason('');\r\n        }}\r\n        onConfirm={handleRejectPayment}\r\n        title=\"Reject Payment\"\r\n        message={\r\n          <div className=\"space-y-3\">\r\n            <p>Are you sure you want to reject this payment?</p>\r\n            <div className=\"bg-red-50 dark:bg-red-900/20 rounded-lg p-3 text-sm\">\r\n              <p className=\"font-medium text-red-900 dark:text-red-100 mb-2\">This action will:</p>\r\n              <ul className=\"text-red-700 dark:text-red-300 space-y-1\">\r\n                <li>• Mark the payment as cancelled/rejected</li>\r\n                <li>• Notify the client of the rejection</li>\r\n                <li>• Create an activity log entry with the reason</li>\r\n              </ul>\r\n            </div>\r\n            <div className=\"mt-4\">\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Reason for rejection *\r\n              </label>\r\n              <textarea\r\n                value={rejectionReason}\r\n                onChange={(e) => setRejectionReason(e.target.value)}\r\n                rows={3}\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-100 resize-none\"\r\n                placeholder=\"Please provide a detailed reason for rejecting this payment...\"\r\n                required\r\n              />\r\n            </div>\r\n          </div>\r\n        }\r\n        confirmText=\"Reject Payment\"\r\n        confirmVariant=\"danger\"\r\n        loading={processingPayment === selectedPaymentId}\r\n        icon={\r\n          <div className=\"flex-shrink-0\">\r\n            <div className=\"h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center\">\r\n              <i className=\"ri-close-line text-red-600 dark:text-red-400 text-xl\"></i>\r\n            </div>\r\n          </div>\r\n        }\r\n      />\r\n\r\n      {/* Document Preview Modal */}\r\n      <DocumentPreviewModal\r\n        document={selectedDocument}\r\n        isOpen={isDocumentPreviewOpen}\r\n        onClose={handleCloseDocumentPreview}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PaymentTaskComponent;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;;AAsBA,MAAM,uBAA4D;QAAC,EAAE,IAAI,EAAE,YAAY,EAAE;QAgmBhE;;IA/lBvB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzE,4BAA4B;IAC5B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,0BAA0B;IAC1B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC9D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB,CAAC;IAEjF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,QAAQ,KAAK,WAAW,IAAI,aAAa,KAAK,SAAS,EAAE;gBAC3D;YACF;QACF;yCAAG;QAAC;KAAK;IAET,MAAM,yBAAyB;QAC7B,IAAI;YACF,WAAW;YAEX,uBAAuB;YACvB,MAAM,UAAU,MAAM,oIAAA,CAAA,iBAAc,CAAC,cAAc,CAAC,KAAK,SAAS;YAClE,MAAM,UAAU,MAAM,oIAAA,CAAA,iBAAc,CAAC,cAAc,CAAC,QAAQ,UAAU;YACtE,IAAG,SAAS,WAAW;YAEvB,iCAAiC;YACjC,MAAM,mBAAmB,MAAM,oIAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,QAAQ,UAAU;YACnF,IAAI,iBAAiB,OAAO,IAAI,iBAAiB,IAAI,EAAE;gBACrD,YAAY,iBAAiB,IAAI;gBACjC,kCAAkC;gBAClC,MAAM,qBAAqB,iBAAiB,IAAI;YAClD;QACF,EAAE,OAAO,OAAO;YACd,UAAU,8CAA8C;QAC1D,SAAU;YACR,WAAW;QACb;IACF;IAEA,2DAA2D;IAC3D,MAAM,uBAAuB,OAAO;QAClC,IAAI;YACF,MAAM,eAAsC,CAAC;YAE7C,KAAK,MAAM,WAAW,SAAU;gBAC9B,IAAI;oBACF,kEAAkE;oBAClE,MAAM,WAAW,MAAM,qIAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC,WAAW,QAAQ,UAAU;oBACzF,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;wBACrC,YAAY,CAAC,QAAQ,UAAU,CAAC,GAAG,SAAS,IAAI;oBAClD;gBACF,EAAE,OAAO,OAAO;oBACd,6DAA6D;oBAC7D,YAAY,CAAC,QAAQ,UAAU,CAAC,GAAG,EAAE;gBACvC;YACF;YAEA,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,0BAA0B;IAC1B,MAAM,wBAAwB,CAAC;QAC7B,oBAAoB;QACpB,yBAAyB;IAC3B;IAEA,yBAAyB;IACzB,MAAM,6BAA6B;QACjC,yBAAyB;QACzB,oBAAoB;IACtB;IAEA,6CAA6C;IAC7C,MAAM,qBAAqB,CAAC;QAC1B,qBAAqB;QACrB,oBAAoB;IACtB;IAEA,4CAA4C;IAC5C,MAAM,oBAAoB,CAAC;QACzB,qBAAqB;QACrB,mBAAmB;QACnB,mBAAmB;IACrB;IAEA,kCAAkC;IAClC,MAAM,uBAAuB;QAC3B,IAAI,CAAC,mBAAmB;QAIxB,IAAI;YACF,qBAAqB;YAErB,8DAA8D;YAC9D,MAAM,SAAS,MAAM,yIAAA,CAAA,sBAAmB,CAAC,cAAc,CAAC;YAExD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,+CAA+C;gBAC/C,MAAM,YAAY,OAAO,KAAK,IAAI;gBAElC,OAAQ;oBACN,KAAK;wBACH,UAAU,yDAAyD;wBACnE;oBACF,KAAK;wBACH,UAAU,yEAAyE;wBACnF;oBACF,KAAK;wBACH,UAAU,oEAAoE;wBAC9E;oBACF;wBACE,UAAU,OAAO,OAAO,IAAI,6BAA6B;gBAC7D;gBACA;YACF;YAEA,2CAA2C;YAC3C,UAAU,OAAO,OAAO,EAAE;YAE1B,MAAM;YACN;YACA,oBAAoB;YACpB,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,aAAa,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;gBACjE,UAAU,8DAA8D;YAC1E,OAAO;gBACL,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,UAAU,cAAc;YAC1B;QACF,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,iCAAiC;IACjC,MAAM,sBAAsB;QAC1B,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,IAAI,IAAI;YACjD,UAAU,yCAAyC;YACnD;QACF;QAEA,IAAI;YACF,qBAAqB;YAErB,8DAA8D;YAC9D,MAAM,SAAS,MAAM,yIAAA,CAAA,sBAAmB,CAAC,aAAa,CAAC,mBAAmB,gBAAgB,IAAI;YAE9F,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,+CAA+C;gBAC/C,MAAM,YAAY,OAAO,KAAK,IAAI;gBAElC,OAAQ;oBACN,KAAK;wBACH,UAAU,yDAAyD;wBACnE;oBACF,KAAK;wBACH,UAAU,yEAAyE;wBACnF;oBACF,KAAK;wBACH,UAAU,oEAAoE;wBAC9E;oBACF;wBACE,UAAU,OAAO,OAAO,IAAI,4BAA4B;gBAC5D;gBACA;YACF;YAEA,UAAU,OAAO,OAAO,EAAE;YAC1B,MAAM;YACN;YACA,mBAAmB;YACnB,qBAAqB;YACrB,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,aAAa,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;gBACjE,UAAU,8DAA8D;YAC1E,OAAO;gBACL,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,UAAU,cAAc;YAC1B;QACF,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,oBAAoB;QACxB,4BAA4B;IAC9B;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ,OAAO,WAAW;YACxB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAc,OAAO;YAC1B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ,OAAO,WAAW;YACxB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BAAmC;;;;;;;;;;;;IAGzD;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;;;;;;;;;;8BAEf,6LAAC;oBAAG,WAAU;8BAA4D;;;;;;8BAC1E,6LAAC;oBAAE,WAAU;8BAAmC;;;;;;;;;;;;IAKtD;IACA,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,CAAC,WAAW,OAAO;QAChD;IAApC,MAAM,YAAY,QAAQ,MAAM,GAAG,CAAC,CAAA,mBAAA,QAAQ,OAAO,cAAf,8BAAA,mBAAmB,CAAC;QAyGgD;IAxGxG,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAE,WAAU;;;;;;wCAA6C;;;;;;;gCAG3D,QAAQ,MAAM,kBACb,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;;sDAEN,6LAAC;4CAAE,WAAU;;;;;;wCAAwB;;;;;;;;;;;;;;;;;;kCAM7C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAC1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAU;0EAAwD,QAAQ,cAAc;;;;;;;;;;;;kEAEhG,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAU;0EAAwD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;;;;;;;;;;;;kEAEvG,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAW,AAAC,2EAAgH,OAAtC,sBAAsB,QAAQ,MAAM;0EAC7H,QAAQ,MAAM,CAAC,WAAW;;;;;;;;;;;;kEAG/B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAU;0EACb,IAAI,KAAK,QAAQ,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;oCAOrD,QAAQ,MAAM,kBACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAC1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAU;0EACb,AAAC,GAAsB,OAApB,QAAQ,MAAM,CAAC,IAAI;;;;;;;;;;;;kEAG3B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAU;0EACb,QAAQ,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASjC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAE,WAAU;;;;;;;;;;;kEAEf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAuD;;;;;;0EACpE,6LAAC;gEAAE,WAAU;0EAA0D,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;sDAI1G,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAE,WAAU;;;;;;;;;;;kEAEf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAuD;;;;;;0EACpE,6LAAC;gEAAE,WAAU;0EAA0D,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;;sDAI5F,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAE,WAAU;;;;;;;;;;;kEAEf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAuD;;;;;;0EACpE,6LAAC;gEAAE,WAAU;0EAA0D,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,oBAAA,QAAQ,OAAO,cAAf,+BAAA,oBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUtH,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAE,WAAU;;;;;;gCAAwC;gCAClC,gBAAgB,MAAM;gCAAC;;;;;;;;;;;;kCAG9C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,wBACpB,6LAAC;oCAA6B,WAAU;8CACtC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EACC,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;8FAA2C;;;;;;8FAC3D,6LAAC;oFAAK,WAAU;8FAAwD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;;;;;;;;;;;;sFAEvG,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;8FAA2C;;;;;;8FAC3D,6LAAC;oFAAK,WAAU;8FAAwD,QAAQ,cAAc;;;;;;;;;;;;sFAEhG,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;8FAA2C;;;;;;8FAC3D,6LAAC;oFAAK,WAAU;8FACb,QAAQ,SAAS,GAAG,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,KAAK,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;0EAK/H,6LAAC;0EACC,cAAA,6LAAC;oEAAI,WAAU;;wEACZ,QAAQ,qBAAqB,kBAC5B,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;8FAA2C;;;;;;8FAC3D,6LAAC;oFAAK,WAAU;8FAAwD,QAAQ,qBAAqB;;;;;;;;;;;;sFAGzG,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;8FAA2C;;;;;;8FAC3D,6LAAC;oFAAK,WAAW,AAAC,2EAAgH,OAAtC,sBAAsB,QAAQ,MAAM;8FAC7H,QAAQ,MAAM,CAAC,WAAW;;;;;;;;;;;;sFAG/B,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;8FAA2C;;;;;;8FAC3D,6LAAC;oFAAK,WAAU;8FACb,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oDAOzD,QAAQ,KAAK,kBACZ,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAE,WAAU;;8EACX,6LAAC;8EAAO;;;;;;gEAAe;gEAAE,QAAQ,KAAK;;;;;;;;;;;;oDAM3C,gBAAgB,CAAC,QAAQ,UAAU,CAAC,IAAI,gBAAgB,CAAC,QAAQ,UAAU,CAAC,CAAC,MAAM,GAAG,mBACrF,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAuD;;;;;;gEACtE,gBAAgB,CAAC,QAAQ,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,yBACzC,6LAAC;wEAA+B,WAAU;;0FACxC,6LAAC;gFAAK,WAAU;0FACb,SAAS,SAAS;;;;;;0FAErB,6LAAC;gFAAI,WAAU;0FACZ,qIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,SAAS,SAAS,mBAC/C,6LAAC;oFACC,SAAS,IAAM,sBAAsB;oFACrC,WAAU;oFACV,OAAM;;sGAEN,6LAAC;4FAAE,WAAU;;;;;;wFAAuB;;;;;;;;;;;;;uEAXlC,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;0DAuBxC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,mBAAmB,QAAQ,UAAU;wDACpD,UAAU,sBAAsB,QAAQ,UAAU;wDAClD,WAAU;kEAET,sBAAsB,QAAQ,UAAU,iBACvC;;8EACE,6LAAC;oEAAI,WAAU;;;;;;gEAAuE;;yFAIxF;;8EACE,6LAAC;oEAAE,WAAU;;;;;;gEAAyB;;;;;;;;kEAK5C,6LAAC;wDACC,SAAS,IAAM,kBAAkB,QAAQ,UAAU;wDACnD,UAAU,sBAAsB,QAAQ,UAAU;wDAClD,WAAU;;0EAEV,6LAAC;gEAAE,WAAU;;;;;;4DAAyB;;;;;;;;;;;;;;;;;;;mCA1GpC,QAAQ,UAAU;;;;;;;;;;;;;;;;;;;;;0BAuHtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAE,WAAU;;;;;;gCAA2C;gCACtC,SAAS,MAAM;gCAAC;;;;;;;;;;;;kCAGtC,6LAAC;wBAAI,WAAU;kCACZ,SAAS,MAAM,KAAK,kBACnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;;;;;8CACb,6LAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAC1E,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;qFAKlD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;8DAGlH,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;8DAGlH,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;8DAGlH,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;8DAGlH,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;8DAGlH,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;;;;;;;;;;;;kDAKtH,6LAAC;wCAAM,WAAU;kDACd,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;gDAA4B,WAAU;;kEACrC,6LAAC;wDAAG,WAAU;kEACX,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;;;;;;kEAEhC,6LAAC;wDAAG,WAAU;kEACX,QAAQ,cAAc;;;;;;kEAEzB,6LAAC;wDAAG,WAAU;kEACX,QAAQ,qBAAqB,IAAI;;;;;;kEAEpC,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAW,AAAC,2EAAgH,OAAtC,sBAAsB,QAAQ,MAAM;sEAC7H,QAAQ,MAAM,CAAC,WAAW;;;;;;;;;;;kEAG/B,6LAAC;wDAAG,WAAU;kEACX,QAAQ,SAAS,GAAG,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,KAAK,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB;;;;;;kEAEzH,6LAAC;wDAAG,WAAU;kEACX,gBAAgB,CAAC,QAAQ,UAAU,CAAC,IAAI,gBAAgB,CAAC,QAAQ,UAAU,CAAC,CAAC,MAAM,GAAG,kBACrF,6LAAC;4DAAI,WAAU;;gEACZ,gBAAgB,CAAC,QAAQ,UAAU,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBACrD,6LAAC;wEAA+B,WAAU;kFACvC,qIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,SAAS,SAAS,mBAC/C,6LAAC;4EACC,SAAS,IAAM,sBAAsB;4EACrC,WAAU;4EACV,OAAO,AAAC,WAA6B,OAAnB,SAAS,SAAS;;8FAEpC,6LAAC;oFAAE,WAAU;;;;;;gFAAkB;;;;;;;uEAP3B,SAAS,WAAW;;;;;gEAa/B,gBAAgB,CAAC,QAAQ,UAAU,CAAC,CAAC,MAAM,GAAG,mBAC7C,6LAAC;oEAAK,WAAU;;wEAAwB;wEACpC,gBAAgB,CAAC,QAAQ,UAAU,CAAC,CAAC,MAAM,GAAG;wEAAE;;;;;;;;;;;;qHAKxD,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;+CA1C7B,QAAQ,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAuDzC,6LAAC,kJAAA,CAAA,UAAe;gBACd,YAAW;gBACX,UAAU,KAAK,SAAS;gBACxB,OAAM;gBACN,YAAY;gBACZ,aAAa;gBACb,WAAU;gBACV,WAAU;;;;;;YAIX,WAAW,QAAQ,UAAU,kBAC5B,6LAAC,yJAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,4BAA4B;gBAC3C,YAAW;gBACX,UAAU,QAAQ,UAAU;gBAC5B,aAAa,GAAE,kBAAA,QAAQ,MAAM,cAAd,sCAAA,gBAAgB,KAAK;;;;;;0BAKxC,6LAAC,oJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS;oBACP,oBAAoB;oBACpB,qBAAqB;gBACvB;gBACA,WAAW;gBACX,OAAM;gBACN,uBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAE;;;;;;sCACH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAsD;;;;;;8CACnE,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;gBAKZ,aAAY;gBACZ,gBAAe;gBACf,SAAS,sBAAsB;gBAC/B,oBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;;;;;0BAOrB,6LAAC,oJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS;oBACP,mBAAmB;oBACnB,qBAAqB;oBACrB,mBAAmB;gBACrB;gBACA,WAAW;gBACX,OAAM;gBACN,uBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAE;;;;;;sCACH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAkD;;;;;;8CAC/D,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAGR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,MAAM;oCACN,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;;;;;;;gBAKhB,aAAY;gBACZ,gBAAe;gBACf,SAAS,sBAAsB;gBAC/B,oBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;;;;;0BAOrB,6LAAC,0JAAA,CAAA,UAAoB;gBACnB,UAAU;gBACV,QAAQ;gBACR,SAAS;;;;;;;;;;;;AAIjB;GA/rBM;;QACkB,mIAAA,CAAA,WAAQ;;;KAD1B;uCAisBS", "debugId": null}}, {"offset": {"line": 3883, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/StatusCard.tsx"], "sourcesContent": ["import React from 'react';\r\nimport Link from 'next/link';\r\nimport { formatStatus, getStatusColor } from '@/utils/formatters';\r\n\r\ninterface StatusCardProps {\r\n  title: string;\r\n  value: string;\r\n  icon: React.ReactNode;\r\n  bgColor: string;\r\n  iconBgColor: string;\r\n  iconTextColor: string;\r\n  linkText: string;\r\n  linkHref: string;\r\n}\r\n\r\nconst StatusCard: React.FC<StatusCardProps> = ({\r\n  title,\r\n  value,\r\n  icon,\r\n  bgColor,\r\n  iconBgColor,\r\n  iconTextColor,\r\n  linkText,\r\n  linkHref\r\n}) => {\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700 transition-colors duration-200\">\r\n      <div className=\"p-5\">\r\n        <div className=\"flex items-center\">\r\n          <div className={`flex-shrink-0 ${iconBgColor} dark:bg-opacity-20 rounded-md p-3 transition-colors duration-200`}>\r\n            <div className={`h-6 w-6 ${iconTextColor}`}>\r\n              {icon}\r\n            </div>\r\n          </div>\r\n          <div className=\"ml-5 w-0 flex-1\">\r\n            <dl>\r\n              <dt className=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate transition-colors duration-200\">{title}</dt>\r\n              <dd>\r\n                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(value)}`}>\r\n                  { formatStatus(value)}\r\n                </span>\r\n              </dd>\r\n            </dl>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"bg-gray-50 dark:bg-gray-700 px-5 py-3 border-t border-gray-200 dark:border-gray-600 transition-colors duration-200\">\r\n        <div className=\"text-sm\">\r\n          <Link href={linkHref} className=\"font-medium text-primary hover:text-primary-dark dark:text-primary-light dark:hover:text-primary transition-colors duration-200\">\r\n            {linkText}\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StatusCard;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAaA,MAAM,aAAwC;QAAC,EAC7C,KAAK,EACL,KAAK,EACL,IAAI,EACJ,OAAO,EACP,WAAW,EACX,aAAa,EACb,QAAQ,EACR,QAAQ,EACT;IAEC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAW,AAAC,iBAA4B,OAAZ,aAAY;sCAC3C,cAAA,6LAAC;gCAAI,WAAW,AAAC,WAAwB,OAAd;0CACxB;;;;;;;;;;;sCAGL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAgG;;;;;;kDAC9G,6LAAC;kDACC,cAAA,6LAAC;4CAAK,WAAW,AAAC,uEAA4F,OAAtB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;sDACnG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAM;wBAAU,WAAU;kCAC7B;;;;;;;;;;;;;;;;;;;;;;AAMb;KAzCM;uCA2CS", "debugId": null}}, {"offset": {"line": 4008, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/evaluation/DataDisplayCard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport ActivityNotesModal from './ActivityNotesModal';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\n\r\ninterface DataField {\r\n  label: string;\r\n  value: any;\r\n  type?: 'text' | 'email' | 'phone' | 'url' | 'date' | 'currency' | 'number' | 'boolean' | 'array' | 'object';\r\n  icon?: string;\r\n  fullWidth?: boolean;\r\n}\r\n\r\ninterface DataDisplayCardProps {\r\n  title: string;\r\n  icon?: string;\r\n  fields: DataField[];\r\n  className?: string;\r\n  showEmptyFields?: boolean;\r\n  defaultCollapsed?: boolean;\r\n  // Email functionality props\r\n  creatorEmail?: string;\r\n  creatorName?: string;\r\n  showEmailButton?: boolean;\r\n}\r\n\r\nconst DataDisplayCard: React.FC<DataDisplayCardProps> = ({\r\n  title,\r\n  icon,\r\n  fields,\r\n  className = '',\r\n  showEmptyFields = true,\r\n  defaultCollapsed = false,\r\n  creatorEmail,\r\n  creatorName,\r\n  showEmailButton = true\r\n}) => {\r\n  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);\r\n  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);\r\n  const {user} = useAuth();\r\n\r\n  const isValidEmail = (email: string) => {\r\n    return email != user?.email;\r\n  };\r\n  \r\n  const formatValue = (value: any, type: string = 'text'): string => {\r\n    if (value === null || value === undefined || value === '') {\r\n      return 'Not provided';\r\n    }\r\n\r\n    switch (type) {\r\n      case 'date':\r\n        return new Date(value).toLocaleDateString('en-US', {\r\n          year: 'numeric',\r\n          month: 'long',\r\n          day: 'numeric'\r\n        });\r\n      case 'currency':\r\n        return new Intl.NumberFormat('en-US', {\r\n          style: 'currency',\r\n          currency: 'USD'\r\n        }).format(value);\r\n      case 'number':\r\n        return new Intl.NumberFormat('en-US').format(value);\r\n      case 'boolean':\r\n        return value ? 'Yes' : 'No';\r\n      case 'array':\r\n        if (Array.isArray(value)) {\r\n          return value.length > 0 ? value.join(', ') : 'None';\r\n        }\r\n        return 'None';\r\n      case 'object':\r\n        if (typeof value === 'object') {\r\n          return JSON.stringify(value, null, 2);\r\n        }\r\n        return String(value);\r\n      case 'email':\r\n      case 'phone':\r\n      case 'url':\r\n      case 'text':\r\n      default:\r\n        return String(value);\r\n    }\r\n  };\r\n\r\n  const getValueColor = (value: any): string => {\r\n    if (value === null || value === undefined || value === '' || value === 'Not provided') {\r\n      return 'text-gray-400 dark:text-gray-500 italic';\r\n    }\r\n    return 'text-gray-900 dark:text-gray-100';\r\n  };\r\n\r\n  const renderValue = (field: DataField) => {\r\n    const formattedValue = formatValue(field.value, field.type);\r\n    const colorClass = getValueColor(field.value);\r\n\r\n    if (field.type === 'email' && field.value && isValidEmail(field.value)) {\r\n      return (\r\n        <a\r\n          href={`mailto:${field.value}`}\r\n          className=\"text-blue-600 dark:text-blue-400 hover:underline\"\r\n        >\r\n          {formattedValue}\r\n        </a>\r\n      );\r\n    }\r\n\r\n    if (field.type === 'phone' && field.value) {\r\n      return (\r\n        <a\r\n          href={`tel:${field.value}`}\r\n          className=\"text-blue-600 dark:text-blue-400 hover:underline\"\r\n        >\r\n          {formattedValue}\r\n        </a>\r\n      );\r\n    }\r\n\r\n    if (field.type === 'url' && field.value) {\r\n      return (\r\n        <a\r\n          href={field.value}\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n          className=\"text-blue-600 dark:text-blue-400 hover:underline\"\r\n        >\r\n          {formattedValue}\r\n        </a>\r\n      );\r\n    }\r\n\r\n    if (field.type === 'object' && field.value) {\r\n      return (\r\n        <pre className=\"text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded overflow-auto max-h-32\">\r\n          {formattedValue}\r\n        </pre>\r\n      );\r\n    }\r\n\r\n    return <span className={colorClass}>{formattedValue}</span>;\r\n  };\r\n\r\n  const visibleFields = showEmptyFields \r\n    ? fields \r\n    : fields.filter(field => field.value !== null && field.value !== undefined && field.value !== '');\r\n\r\n  if (visibleFields.length === 0 && !showEmptyFields) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>\r\n      {/* Header */}\r\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center\">\r\n            {icon && (\r\n              <i className={`${icon} text-xl text-red-600 dark:text-red-400 mr-3`}></i>\r\n            )}\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n              {title}\r\n            </h3>\r\n          </div>\r\n          <div className=\"flex items-center space-x-2\">\r\n            {/* Email Creator Button */}\r\n            {showEmailButton && creatorEmail && (\r\n              <button\r\n                onClick={() => setIsEmailModalOpen(true)}\r\n                className=\"flex items-center justify-center w-8 h-8 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900 transition-colors duration-200\"\r\n                title={`Email ${creatorName || 'Creator'}`}\r\n              >\r\n                <i className=\"ri-mail-send-line text-lg text-blue-600 dark:text-blue-400\"></i>\r\n              </button>\r\n            )}\r\n            {/* Collapse Button */}\r\n            <button\r\n              onClick={() => setIsCollapsed(!isCollapsed)}\r\n              className=\"flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200\"\r\n              title={isCollapsed ? 'Expand' : 'Collapse'}\r\n            >\r\n              <i className={`${isCollapsed ? 'ri-add-line' : 'ri-subtract-line'} text-lg text-gray-500 dark:text-gray-400`}></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Content */}\r\n      <div className={`transition-all duration-300 ease-in-out overflow-hidden ${isCollapsed ? 'max-h-0' : 'max-h-none'}`}>\r\n        <div className=\"p-6\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n            {visibleFields.map((field, index) => (\r\n              <div\r\n                key={index}\r\n                className={field.fullWidth ? 'md:col-span-2' : ''}\r\n              >\r\n                <div className=\"flex items-start space-x-3\">\r\n                  {field.icon && (\r\n                    <i className={`${field.icon} text-gray-400 dark:text-gray-500 mt-1 flex-shrink-0`}></i>\r\n                  )}\r\n                  <div className=\"flex-1 min-w-0\">\r\n                    <label className=\"block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1\">\r\n                      {field.label}\r\n                    </label>\r\n                    <div className=\"text-sm\">\r\n                      {renderValue(field)}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          {visibleFields.length === 0 && showEmptyFields && (\r\n            <div className=\"text-center py-8\">\r\n              <i className=\"ri-information-line text-3xl text-gray-300 dark:text-gray-600 mb-2\"></i>\r\n              <p className=\"text-gray-500 dark:text-gray-400\">No data available for this section</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Activity Notes Modal for Email */}\r\n      {creatorEmail && (\r\n        <ActivityNotesModal\r\n          isOpen={isEmailModalOpen}\r\n          onClose={() => setIsEmailModalOpen(false)}\r\n          entityId=\"general\"\r\n          entityType=\"general\"\r\n          initialEmails={creatorEmail || ''}\r\n          title={`Email ${creatorName || 'Creator'} - ${title}`}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DataDisplayCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;;AAuBA,MAAM,kBAAkD;QAAC,EACvD,KAAK,EACL,IAAI,EACJ,MAAM,EACN,YAAY,EAAE,EACd,kBAAkB,IAAI,EACtB,mBAAmB,KAAK,EACxB,YAAY,EACZ,WAAW,EACX,kBAAkB,IAAI,EACvB;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,EAAC,IAAI,EAAC,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAErB,MAAM,eAAe,CAAC;QACpB,OAAO,UAAS,iBAAA,2BAAA,KAAM,KAAK;IAC7B;IAEA,MAAM,cAAc,SAAC;YAAY,wEAAe;QAC9C,IAAI,UAAU,QAAQ,UAAU,aAAa,UAAU,IAAI;YACzD,OAAO;QACT;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,KAAK,OAAO,kBAAkB,CAAC,SAAS;oBACjD,MAAM;oBACN,OAAO;oBACP,KAAK;gBACP;YACF,KAAK;gBACH,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;oBACpC,OAAO;oBACP,UAAU;gBACZ,GAAG,MAAM,CAAC;YACZ,KAAK;gBACH,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;YAC/C,KAAK;gBACH,OAAO,QAAQ,QAAQ;YACzB,KAAK;gBACH,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,OAAO,MAAM,MAAM,GAAG,IAAI,MAAM,IAAI,CAAC,QAAQ;gBAC/C;gBACA,OAAO;YACT,KAAK;gBACH,IAAI,OAAO,UAAU,UAAU;oBAC7B,OAAO,KAAK,SAAS,CAAC,OAAO,MAAM;gBACrC;gBACA,OAAO,OAAO;YAChB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL;gBACE,OAAO,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,UAAU,QAAQ,UAAU,aAAa,UAAU,MAAM,UAAU,gBAAgB;YACrF,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,iBAAiB,YAAY,MAAM,KAAK,EAAE,MAAM,IAAI;QAC1D,MAAM,aAAa,cAAc,MAAM,KAAK;QAE5C,IAAI,MAAM,IAAI,KAAK,WAAW,MAAM,KAAK,IAAI,aAAa,MAAM,KAAK,GAAG;YACtE,qBACE,6LAAC;gBACC,MAAM,AAAC,UAAqB,OAAZ,MAAM,KAAK;gBAC3B,WAAU;0BAET;;;;;;QAGP;QAEA,IAAI,MAAM,IAAI,KAAK,WAAW,MAAM,KAAK,EAAE;YACzC,qBACE,6LAAC;gBACC,MAAM,AAAC,OAAkB,OAAZ,MAAM,KAAK;gBACxB,WAAU;0BAET;;;;;;QAGP;QAEA,IAAI,MAAM,IAAI,KAAK,SAAS,MAAM,KAAK,EAAE;YACvC,qBACE,6LAAC;gBACC,MAAM,MAAM,KAAK;gBACjB,QAAO;gBACP,KAAI;gBACJ,WAAU;0BAET;;;;;;QAGP;QAEA,IAAI,MAAM,IAAI,KAAK,YAAY,MAAM,KAAK,EAAE;YAC1C,qBACE,6LAAC;gBAAI,WAAU;0BACZ;;;;;;QAGP;QAEA,qBAAO,6LAAC;YAAK,WAAW;sBAAa;;;;;;IACvC;IAEA,MAAM,gBAAgB,kBAClB,SACA,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,KAAK,aAAa,MAAM,KAAK,KAAK;IAEhG,IAAI,cAAc,MAAM,KAAK,KAAK,CAAC,iBAAiB;QAClD,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,8FAAuG,OAAV;;0BAE5G,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ,sBACC,6LAAC;oCAAE,WAAW,AAAC,GAAO,OAAL,MAAK;;;;;;8CAExB,6LAAC;oCAAG,WAAU;8CACX;;;;;;;;;;;;sCAGL,6LAAC;4BAAI,WAAU;;gCAEZ,mBAAmB,8BAClB,6LAAC;oCACC,SAAS,IAAM,oBAAoB;oCACnC,WAAU;oCACV,OAAO,AAAC,SAAiC,OAAzB,eAAe;8CAE/B,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAIjB,6LAAC;oCACC,SAAS,IAAM,eAAe,CAAC;oCAC/B,WAAU;oCACV,OAAO,cAAc,WAAW;8CAEhC,cAAA,6LAAC;wCAAE,WAAW,AAAC,GAAmD,OAAjD,cAAc,gBAAgB,oBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1E,6LAAC;gBAAI,WAAW,AAAC,2DAAiG,OAAvC,cAAc,YAAY;0BACnG,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC;oCAEC,WAAW,MAAM,SAAS,GAAG,kBAAkB;8CAE/C,cAAA,6LAAC;wCAAI,WAAU;;4CACZ,MAAM,IAAI,kBACT,6LAAC;gDAAE,WAAW,AAAC,GAAa,OAAX,MAAM,IAAI,EAAC;;;;;;0DAE9B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEACd,MAAM,KAAK;;;;;;kEAEd,6LAAC;wDAAI,WAAU;kEACZ,YAAY;;;;;;;;;;;;;;;;;;mCAZd;;;;;;;;;;wBAoBV,cAAc,MAAM,KAAK,KAAK,iCAC7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;;;;;8CACb,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;;;;;;YAOvD,8BACC,6LAAC,yJAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,oBAAoB;gBACnC,UAAS;gBACT,YAAW;gBACX,eAAe,gBAAgB;gBAC/B,OAAO,AAAC,SAAsC,OAA9B,eAAe,WAAU,OAAW,OAAN;;;;;;;;;;;;AAKxD;GAhNM;;QAaW,kIAAA,CAAA,UAAO;;;KAblB;uCAkNS", "debugId": null}}, {"offset": {"line": 4351, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/evaluation/ApplicationInfoCard.tsx"], "sourcesContent": ["import React from 'react';\r\nimport DataDisplayCard from './DataDisplayCard';\r\nimport { Application } from '@/types/license';\r\n\r\n\r\ninterface ApplicationInfoCardProps {\r\n  application: Application | null;\r\n  className?: string;\r\n  showEmptyFields?: boolean;\r\n}\r\n\r\nconst ApplicationInfoCard: React.FC<ApplicationInfoCardProps> = ({\r\n  application,\r\n  className = '',\r\n  showEmptyFields = true\r\n}) => {\r\n\r\n  // Format status for display\r\n  const formatStatus = (status: string) => {\r\n    return status.replaceAll(\"_\", \"\");\r\n  };\r\n\r\n  // Get status color\r\n  const getStatusColor = (status: string) => {\r\n    switch (status?.toLowerCase()) {\r\n      case 'draft':\r\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n      case 'pending':\r\n        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'submitted':\r\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\r\n      case 'under_review':\r\n        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';\r\n      case 'evaluation':\r\n        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200';\r\n      case 'approved':\r\n        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'rejected':\r\n        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\r\n      case 'withdrawn':\r\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n      default:\r\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  };\r\n\r\n  const fields = [\r\n    {\r\n      label: 'Application Number',\r\n      value: application?.application_number,\r\n      icon: 'ri-hashtag'\r\n    },\r\n    {\r\n      label: 'License Category',\r\n      value: application?.license_category?.name,\r\n      icon: 'ri-award-line'\r\n    },\r\n    {\r\n      label: 'License Type',\r\n      value: application?.license_category?.license_type?.name,\r\n      icon: 'ri-bookmark-line'\r\n    },\r\n    {\r\n      label: 'Status',\r\n      value: application?.status,\r\n      icon: 'ri-flag-line'\r\n    },\r\n    {\r\n      label: 'Current Step',\r\n      value: application?.current_step,\r\n      icon: 'ri-footprint-line'\r\n    },\r\n    {\r\n      label: 'Submitted Date',\r\n      value: application?.submitted_at ? new Date(application.submitted_at).toLocaleDateString() : 'Not submitted',\r\n      type: 'date' as const,\r\n      icon: 'ri-calendar-check-line'\r\n    },\r\n    {\r\n      label: 'Created Date',\r\n      value: application?.created_at ? new Date(application.created_at).toLocaleDateString() : null,\r\n      type: 'date' as const,\r\n      icon: 'ri-calendar-line'\r\n    },\r\n    {\r\n      label: 'Assigned To',\r\n      value: application?.assignee ? `${application.assignee.first_name} ${application.assignee.last_name}` : 'Unassigned',\r\n      icon: 'ri-user-settings-line'\r\n    },\r\n    {\r\n      label: 'Assignment Date',\r\n      value: application?.assigned_at ? new Date(application.assigned_at).toLocaleDateString() : null,\r\n      type: 'date' as const,\r\n      icon: 'ri-calendar-event-line'\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <DataDisplayCard\r\n      title=\"Application Information\"\r\n      icon=\"ri-file-text-line\"\r\n      fields={fields}\r\n      className={className}\r\n      showEmptyFields={showEmptyFields}\r\n      creatorEmail={application?.applicant?.email}\r\n      creatorName={application?.applicant?.name}\r\n      showEmailButton={true}\r\n    />\r\n  );\r\n};\r\n\r\nexport default ApplicationInfoCard;\r\n"], "names": [], "mappings": ";;;;AACA;;;AAUA,MAAM,sBAA0D;QAAC,EAC/D,WAAW,EACX,YAAY,EAAE,EACd,kBAAkB,IAAI,EACvB;QAuCY,+BAKA,4CAAA,gCA6CO,wBACD;IAxFjB,4BAA4B;IAC5B,MAAM,eAAe,CAAC;QACpB,OAAO,OAAO,UAAU,CAAC,KAAK;IAChC;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,OAAQ,mBAAA,6BAAA,OAAQ,WAAW;YACzB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,SAAS;QACb;YACE,OAAO;YACP,KAAK,EAAE,wBAAA,kCAAA,YAAa,kBAAkB;YACtC,MAAM;QACR;QACA;YACE,OAAO;YACP,KAAK,EAAE,wBAAA,mCAAA,gCAAA,YAAa,gBAAgB,cAA7B,oDAAA,8BAA+B,IAAI;YAC1C,MAAM;QACR;QACA;YACE,OAAO;YACP,KAAK,EAAE,wBAAA,mCAAA,iCAAA,YAAa,gBAAgB,cAA7B,sDAAA,6CAAA,+BAA+B,YAAY,cAA3C,iEAAA,2CAA6C,IAAI;YACxD,MAAM;QACR;QACA;YACE,OAAO;YACP,KAAK,EAAE,wBAAA,kCAAA,YAAa,MAAM;YAC1B,MAAM;QACR;QACA;YACE,OAAO;YACP,KAAK,EAAE,wBAAA,kCAAA,YAAa,YAAY;YAChC,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO,CAAA,wBAAA,kCAAA,YAAa,YAAY,IAAG,IAAI,KAAK,YAAY,YAAY,EAAE,kBAAkB,KAAK;YAC7F,MAAM;YACN,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO,CAAA,wBAAA,kCAAA,YAAa,UAAU,IAAG,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB,KAAK;YACzF,MAAM;YACN,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO,CAAA,wBAAA,kCAAA,YAAa,QAAQ,IAAG,AAAC,GAAqC,OAAnC,YAAY,QAAQ,CAAC,UAAU,EAAC,KAAkC,OAA/B,YAAY,QAAQ,CAAC,SAAS,IAAK;YACxG,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO,CAAA,wBAAA,kCAAA,YAAa,WAAW,IAAG,IAAI,KAAK,YAAY,WAAW,EAAE,kBAAkB,KAAK;YAC3F,MAAM;YACN,MAAM;QACR;KACD;IAED,qBACE,6LAAC,sJAAA,CAAA,UAAe;QACd,OAAM;QACN,MAAK;QACL,QAAQ;QACR,WAAW;QACX,iBAAiB;QACjB,YAAY,EAAE,wBAAA,mCAAA,yBAAA,YAAa,SAAS,cAAtB,6CAAA,uBAAwB,KAAK;QAC3C,WAAW,EAAE,wBAAA,mCAAA,0BAAA,YAAa,SAAS,cAAtB,8CAAA,wBAAwB,IAAI;QACzC,iBAAiB;;;;;;AAGvB;KAlGM;uCAoGS", "debugId": null}}, {"offset": {"line": 4466, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/evaluation/ApplicantInfoCard.tsx"], "sourcesContent": ["import React from 'react';\r\nimport DataDisplayCard from './DataDisplayCard';\r\nimport { Applicant } from '@/types/license';\r\n\r\ninterface ApplicantInfoCardProps {\r\n  applicant: Applicant | null;\r\n  className?: string;\r\n  showEmptyFields?: boolean;\r\n}\r\n\r\nconst ApplicantInfoCard: React.FC<ApplicantInfoCardProps> = ({\r\n  applicant,\r\n  className = '',\r\n  showEmptyFields = true\r\n}) => {\r\n  const fields = [\r\n    {\r\n      label: 'Organization Name',\r\n      value: applicant?.name,\r\n      icon: 'ri-building-line'\r\n    },\r\n    {\r\n      label: 'Business Registration Number',\r\n      value: applicant?.business_registration_number,\r\n      icon: 'ri-file-list-line'\r\n    },\r\n    {\r\n      label: 'TPIN',\r\n      value: applicant?.tpin,\r\n      icon: 'ri-number-1'\r\n    },\r\n    {\r\n      label: 'Email Address',\r\n      value: applicant?.email,\r\n      type: 'email' as const,\r\n      icon: 'ri-mail-line'\r\n    },\r\n    {\r\n      label: 'Phone Number',\r\n      value: applicant?.phone,\r\n      type: 'phone' as const,\r\n      icon: 'ri-phone-line'\r\n    },\r\n    {\r\n      label: 'Website',\r\n      value: applicant?.website,\r\n      type: 'url' as const,\r\n      icon: 'ri-global-line'\r\n    },\r\n    {\r\n      label: 'Fax Number',\r\n      value: applicant?.fax,\r\n      type: 'phone' as const,\r\n      icon: 'ri-printer-line'\r\n    },\r\n    {\r\n      label: 'Date of Incorporation',\r\n      value: applicant?.date_incorporation ? new Date(applicant.date_incorporation).toLocaleDateString() : null,\r\n      type: 'date' as const,\r\n      icon: 'ri-calendar-line'\r\n    },\r\n    {\r\n      label: 'Place of Incorporation',\r\n      value: applicant?.place_incorporation,\r\n      icon: 'ri-map-pin-line'\r\n    },\r\n    {\r\n      label: 'Level of Insurance Cover',\r\n      value: applicant?.level_of_insurance_cover,\r\n      icon: 'ri-shield-check-line',\r\n      fullWidth: true\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <DataDisplayCard\r\n      title=\"Applicant Information\"\r\n      icon=\"ri-user-line\"\r\n      fields={fields}\r\n      className={className}\r\n      showEmptyFields={showEmptyFields}\r\n      creatorEmail={applicant?.email}\r\n      creatorName={applicant?.name}\r\n      showEmailButton={true}\r\n    />\r\n  );\r\n};\r\n\r\nexport default ApplicantInfoCard;\r\n"], "names": [], "mappings": ";;;;AACA;;;AASA,MAAM,oBAAsD;QAAC,EAC3D,SAAS,EACT,YAAY,EAAE,EACd,kBAAkB,IAAI,EACvB;IACC,MAAM,SAAS;QACb;YACE,OAAO;YACP,KAAK,EAAE,sBAAA,gCAAA,UAAW,IAAI;YACtB,MAAM;QACR;QACA;YACE,OAAO;YACP,KAAK,EAAE,sBAAA,gCAAA,UAAW,4BAA4B;YAC9C,MAAM;QACR;QACA;YACE,OAAO;YACP,KAAK,EAAE,sBAAA,gCAAA,UAAW,IAAI;YACtB,MAAM;QACR;QACA;YACE,OAAO;YACP,KAAK,EAAE,sBAAA,gCAAA,UAAW,KAAK;YACvB,MAAM;YACN,MAAM;QACR;QACA;YACE,OAAO;YACP,KAAK,EAAE,sBAAA,gCAAA,UAAW,KAAK;YACvB,MAAM;YACN,MAAM;QACR;QACA;YACE,OAAO;YACP,KAAK,EAAE,sBAAA,gCAAA,UAAW,OAAO;YACzB,MAAM;YACN,MAAM;QACR;QACA;YACE,OAAO;YACP,KAAK,EAAE,sBAAA,gCAAA,UAAW,GAAG;YACrB,MAAM;YACN,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO,CAAA,sBAAA,gCAAA,UAAW,kBAAkB,IAAG,IAAI,KAAK,UAAU,kBAAkB,EAAE,kBAAkB,KAAK;YACrG,MAAM;YACN,MAAM;QACR;QACA;YACE,OAAO;YACP,KAAK,EAAE,sBAAA,gCAAA,UAAW,mBAAmB;YACrC,MAAM;QACR;QACA;YACE,OAAO;YACP,KAAK,EAAE,sBAAA,gCAAA,UAAW,wBAAwB;YAC1C,MAAM;YACN,WAAW;QACb;KACD;IAED,qBACE,6LAAC,sJAAA,CAAA,UAAe;QACd,OAAM;QACN,MAAK;QACL,QAAQ;QACR,WAAW;QACX,iBAAiB;QACjB,YAAY,EAAE,sBAAA,gCAAA,UAAW,KAAK;QAC9B,WAAW,EAAE,sBAAA,gCAAA,UAAW,IAAI;QAC5B,iBAAiB;;;;;;AAGvB;KA5EM;uCA8ES", "debugId": null}}, {"offset": {"line": 4561, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useAddressing.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { customerApi } from '@/lib/customer-api';\r\nimport { useState, useEffect, useCallback, useRef } from 'react';\r\nimport { toast } from 'react-hot-toast';\r\nimport { useDebouncedCallback } from 'use-debounce';\r\nimport { Address, SearchPostcodes, PostalCodeLookupResult, CreateAddressDto, EditAddressData, SequentialAddressData } from '@/types/address_types';\r\n\r\n// Re-export DTO types for backward compatibility\r\nexport type {\r\n  CreateAddressDto as CreateAddressData,\r\n  EditAddressData\r\n} from '@/types/address_types';\r\n\r\n// Re-export types from the centralized types file\r\nexport type { SearchPostcodes, PostalCodeLookupResult } from '@/types/address_types';\r\n\r\nexport const initialAddressData: CreateAddressDto = {\r\n  address_type: 'business',\r\n  entity_type: 'applicant',\r\n  entity_id: '',\r\n  address_line_1: '',\r\n  address_line_2: '',\r\n  address_line_3: '',\r\n  postal_code: '',\r\n  country: '',\r\n  city: ''\r\n};\r\n\r\n// Address interface is imported from types\r\n\r\n// Cache for addresses and postcode suggestions\r\ninterface CacheEntry<T> {\r\n  data: T;\r\n  timestamp: number;\r\n  expiry: number;\r\n}\r\n\r\nclass AddressCache {\r\n  private cache = new Map<string, CacheEntry<any>>();\r\n  private readonly DEFAULT_TTL = 1480 * 60 * 1000; // 24 hours since location data does not frequently change\r\n\r\n  set<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): void {\r\n    this.cache.set(key, {\r\n      data,\r\n      timestamp: Date.now(),\r\n      expiry: Date.now() + ttl,\r\n    });\r\n  }\r\n\r\n  get<T>(key: string): T | null {\r\n    const entry = this.cache.get(key);\r\n    if (!entry) return null;\r\n\r\n    if (Date.now() > entry.expiry) {\r\n      this.cache.delete(key);\r\n      return null;\r\n    }\r\n\r\n    return entry.data;\r\n  }\r\n\r\n  clear(): void {\r\n    this.cache.clear();\r\n  }\r\n\r\n  invalidatePattern(pattern: string): void {\r\n    const regex = new RegExp(pattern);\r\n    for (const key of this.cache.keys()) {\r\n      if (regex.test(key)) {\r\n        this.cache.delete(key);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// Global cache instance\r\nconst addressCache = new AddressCache();\r\n\r\n// Address service using customer API\r\nexport const addressService = {\r\n  async createAddress(data: CreateAddressDto): Promise<any> {\r\n    const response = await customerApi.createAddress(data);\r\n    // Invalidate relevant caches after creation\r\n    addressCache.invalidatePattern(`addresses_.*`);\r\n    addressCache.invalidatePattern(`entity_addresses_${data.entity_type}_${data.entity_id}`);\r\n    return response;\r\n  },\r\n\r\n  async getAddress(id: string): Promise<any> {\r\n    const cacheKey = `address_${id}`;\r\n    const cached = addressCache.get(cacheKey);\r\n    if (cached) return cached;\r\n\r\n    const response = await customerApi.getAddress(id);\r\n    addressCache.set(cacheKey, response);\r\n    return response;\r\n  },\r\n\r\n  async editAddress(data: EditAddressData): Promise<any> {\r\n    const response = await customerApi.editAddress(data);\r\n    // Invalidate relevant caches after update\r\n    addressCache.invalidatePattern(`address_${data.address_id}`);\r\n    addressCache.invalidatePattern(`addresses_.*`);\r\n    if (data.entity_type && data.entity_id) {\r\n      addressCache.invalidatePattern(`entity_addresses_${data.entity_type}_${data.entity_id}`);\r\n    }\r\n    return response;\r\n  },\r\n\r\n  async getAddressesByEntity(entityType: string, entityId: string): Promise<any> {\r\n    const cacheKey = `entity_addresses_${entityType}_${entityId}`;\r\n    const cached = addressCache.get(cacheKey);\r\n    if (cached) return cached;\r\n\r\n    const response = await customerApi.getAddressesByEntity(entityType, entityId);\r\n    addressCache.set(cacheKey, response);\r\n    return response;\r\n  },\r\n\r\n  async getAllAddresses(): Promise<any> {\r\n    const cacheKey = 'addresses_all';\r\n    const cached = addressCache.get(cacheKey);\r\n    if (cached) return cached;\r\n\r\n    const response = await customerApi.getAddresses();\r\n    addressCache.set(cacheKey, response);\r\n    return response;\r\n  },\r\n\r\n  // Prefetch all postal code data for caching with pagination\r\n  async prefetchPostalCodeData(): Promise<any> {\r\n    const cacheKey = 'all_postal_codes';\r\n    const cached = addressCache.get(cacheKey);\r\n    if (cached) {\r\n      console.log('Using cached postal code data');\r\n      return cached;\r\n    }\r\n\r\n    console.log('Prefetching all postal code data...');\r\n\r\n    // Fetch all postal codes using pagination\r\n    let allPostalCodes: any[] = [];\r\n    let currentPage = 1;\r\n    let hasMorePages = true;\r\n    const limit = 1000; // Fetch 1000 records per page\r\n    let totalRecords = 0;\r\n\r\n    while (hasMorePages) {\r\n      try {\r\n        console.log(`Requesting page ${currentPage} with limit ${limit}`);\r\n        const response = await customerApi.getAllPostcodes({\r\n          page: Number(currentPage),\r\n          limit: Number(limit)\r\n        });\r\n\r\n        if (response.data && response.data.length > 0) {\r\n          allPostalCodes = [...allPostalCodes, ...response.data];\r\n          console.log(`Fetched page ${currentPage}: ${response.data.length} records`);\r\n\r\n          // Use pagination metadata if available, otherwise fallback to data length check\r\n          if (response.meta) {\r\n            totalRecords = response.meta.total;\r\n            hasMorePages = response.meta.hasNext;\r\n            console.log(`Pagination info: ${allPostalCodes.length}/${totalRecords} records, hasNext: ${hasMorePages}`);\r\n          } else {\r\n            // Fallback: assume more pages if we got a full page\r\n            hasMorePages = response.data.length === limit;\r\n          }\r\n\r\n          currentPage++;\r\n        } else {\r\n          hasMorePages = false;\r\n        }\r\n      } catch (error: any) {\r\n        console.error(`Failed to fetch page ${currentPage}:`, error);\r\n        console.error('Error details:', {\r\n          message: error.message,\r\n          response: error.response?.data,\r\n          status: error.response?.status,\r\n          params: { page: currentPage, limit: limit }\r\n        });\r\n        hasMorePages = false;\r\n      }\r\n    }\r\n\r\n    const consolidatedResponse = {\r\n      data: allPostalCodes,\r\n      meta: {\r\n        total: totalRecords || allPostalCodes.length,\r\n        cached: true,\r\n        pages_fetched: currentPage - 1\r\n      }\r\n    };\r\n    addressCache.set(cacheKey, consolidatedResponse, 1480 * 60 * 1000); // Cache for 24 hours\r\n    console.log(`Postal code data prefetched and cached: ${allPostalCodes.length} total records from ${currentPage - 1} pages`);\r\n    return consolidatedResponse;\r\n  },\r\n\r\n  // Fetch a single postcode entry by postal_code and return region, district and location to fill edit address modal\r\n  async getPostcodeDetails(postalCode: string): Promise<PostalCodeLookupResult | null> {\r\n    if (!postalCode?.trim()) {\r\n      console.warn('getPostcodeDetails: No postal code provided');\r\n      return null;\r\n    }\r\n\r\n    const cacheKey = `postcode_details_${postalCode}`;\r\n    const cached = addressCache.get(cacheKey) as PostalCodeLookupResult | undefined;\r\n    if (cached) {\r\n      console.log(`Postcode details for ${postalCode} retrieved from cache`);\r\n      return cached;\r\n    }\r\n\r\n    try {\r\n      console.log(`Fetching postcode details for: ${postalCode}`);\r\n      const response = await customerApi.getPostcodeByPostalCode(postalCode);\r\n\r\n      if (response) {\r\n        // Cache the result for 30 minutes\r\n        addressCache.set(cacheKey, response, 30 * 60 * 1000);\r\n        console.log(`Postcode details cached for ${postalCode}:`, {\r\n          region: response.region,\r\n          district: response.district,\r\n          location: response.location,\r\n          postal_code: response.postal_code\r\n        });\r\n        return response;\r\n      } else {\r\n        console.warn(`No postcode details found for: ${postalCode}`);\r\n        return null;\r\n      }\r\n    } catch (error: any) {\r\n      console.error(`Failed to fetch postcode details for ${postalCode}:`, error);\r\n\r\n      // Return null on error to allow graceful handling\r\n      return null;\r\n    }\r\n  },\r\n\r\n  // Helper function to populate address form with postcode details\r\n  async populateAddressFromPostcode(postalCode: string): Promise<Partial<SequentialAddressData> | null> {\r\n    const postcodeDetails = await this.getPostcodeDetails(postalCode);\r\n\r\n    if (!postcodeDetails) {\r\n      return null;\r\n    }\r\n\r\n    // Convert PostalCodeLookupResult to SequentialAddressData format\r\n    const addressData: Partial<SequentialAddressData> = {\r\n      country: 'Malawi', // Assuming all postal codes are for Malawi\r\n      region: postcodeDetails.region,\r\n      district: postcodeDetails.district,\r\n      location: postcodeDetails.location,\r\n      postal_code: postcodeDetails.postal_code,\r\n      // Keep existing address lines if any\r\n      address_line_1: '',\r\n      address_line_2: '',\r\n    };\r\n\r\n    console.log(`Address data populated from postcode ${postalCode}:`, addressData);\r\n    return addressData;\r\n  },\r\n\r\n\r\n  async searchPostcodes(searchParams: SearchPostcodes): Promise<any> {\r\n    const cacheKey = `postcodes_${JSON.stringify(searchParams)}`;\r\n    const cached = addressCache.get(cacheKey);\r\n    if (cached) return cached;\r\n\r\n    const response = await customerApi.searchPostcodes(searchParams);\r\n    // Cache postcode searches for longer (24 hours) as they change less frequently\r\n    addressCache.set(cacheKey, response, 1480 * 60 * 1000);\r\n    return response;\r\n  },\r\n\r\n\r\n  // Enhanced methods for sequential address building using cached data\r\n  async getRegions(): Promise<any> {\r\n    const cacheKey = 'regions_all';\r\n    const cached = addressCache.get(cacheKey);\r\n    if (cached) return cached;\r\n\r\n    // Get all postal code data from cache or fetch if not available\r\n    const allPostalData = await this.prefetchPostalCodeData();\r\n\r\n    // Extract unique regions and cache them\r\n    const allRegions = allPostalData.data?.map((pc: any) => pc.region).filter(Boolean) || [];\r\n\r\n    const uniqueRegions = [...new Set(allRegions)];\r\n\r\n    // Sort regions for consistent ordering\r\n    const sortedRegions = uniqueRegions.sort();\r\n\r\n    const regionResponse = { data: sortedRegions };\r\n\r\n    addressCache.set(cacheKey, regionResponse, 1480 * 60 * 1000); // 24 hours\r\n    console.log('Regions extracted from cached data:', sortedRegions);\r\n    return regionResponse;\r\n  },\r\n\r\n  async getDistrictsByRegion(region: string): Promise<any> {\r\n    const cacheKey = `districts_${region}`;\r\n    const cached = addressCache.get(cacheKey);\r\n    if (cached) return cached;\r\n\r\n    // Get all postal code data from cache\r\n    const allPostalData = await this.prefetchPostalCodeData();\r\n\r\n    // Filter by region and extract unique districts\r\n    const filteredData = allPostalData.data?.filter((pc: any) => pc.region === region) || [];\r\n    const allDistricts = filteredData.map((pc: any) => pc.district).filter(Boolean);\r\n    const uniqueDistricts = [...new Set(allDistricts)];\r\n\r\n    // Sort districts for consistent ordering\r\n    const sortedDistricts = uniqueDistricts.sort();\r\n\r\n    const districtResponse = { data: sortedDistricts };\r\n    addressCache.set(cacheKey, districtResponse, 1480 * 60 * 1000); // 24 hours\r\n    console.log(`Districts for ${region} extracted from cached data:`, sortedDistricts);\r\n    console.log(`Total unique districts found for ${region}:`, sortedDistricts.length);\r\n    return districtResponse;\r\n  },\r\n\r\n  async getLocationsByDistrict(region: string, district: string): Promise<any> {\r\n    const cacheKey = `locations_${region}_${district}`;\r\n    const cached = addressCache.get(cacheKey);\r\n    if (cached) return cached;\r\n\r\n    // Get all postal code data from cache\r\n    const allPostalData = await this.prefetchPostalCodeData();\r\n\r\n    // Filter by region and district, then extract unique locations\r\n    const filteredData = allPostalData.data?.filter((pc: any) =>\r\n      pc.region === region && pc.district === district\r\n    ) || [];\r\n    const allLocations = filteredData.map((pc: any) => pc.location).filter(Boolean);\r\n    const uniqueLocations = [...new Set(allLocations)];\r\n\r\n    // Sort locations for consistent ordering\r\n    const sortedLocations = uniqueLocations.sort();\r\n\r\n    const locationResponse = { data: sortedLocations };\r\n    addressCache.set(cacheKey, locationResponse, 1480 * 60 * 1000); // 24 hours\r\n    console.log(`Locations for ${region}/${district} extracted from cached data:`, sortedLocations);\r\n    console.log(`Total unique locations found for ${region}/${district}:`, sortedLocations.length);\r\n    return locationResponse;\r\n  },\r\n\r\n  async getPostcodesByLocation(region: string, district: string, location: string): Promise<any> {\r\n    const cacheKey = `postcodes_${region}_${district}_${location}`;\r\n    const cached = addressCache.get(cacheKey);\r\n    if (cached) return cached;\r\n\r\n    // Get all postal code data from cache\r\n    const allPostalData = await this.prefetchPostalCodeData();\r\n\r\n    // Filter by region, district, and location\r\n    const filteredData = allPostalData.data?.filter((pc: any) =>\r\n      pc.region === region && pc.district === district && pc.location === location\r\n    ) || [];\r\n\r\n    // Sort postcodes for consistent ordering\r\n    const sortedPostcodes = filteredData.sort((a: any, b: any) => {\r\n      return a.postal_code.localeCompare(b.postal_code);\r\n    });\r\n\r\n    const postcodeResponse = { data: sortedPostcodes };\r\n    addressCache.set(cacheKey, postcodeResponse, 1480 * 60 * 1000); // 24 hours\r\n    console.log(`Postcodes for ${region}/${district}/${location} extracted from cached data:`, sortedPostcodes.length, 'records');\r\n    return postcodeResponse;\r\n  },\r\n\r\n  // Cache management methods\r\n  clearCache(): void {\r\n    addressCache.clear();\r\n  },\r\n\r\n  invalidateAddressCache(addressId?: string): void {\r\n    if (addressId) {\r\n      addressCache.invalidatePattern(`address_${addressId}`);\r\n    }\r\n    addressCache.invalidatePattern(`addresses_.*`);\r\n  },\r\n}\r\n\r\n\r\n// Actual hook\r\nexport const useAddresses = (initialSearchParams?: SearchPostcodes) => {\r\n  const [addresses, setAddresses] = useState<Address[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [searchParams, setSearchParams] = useState<SearchPostcodes>(initialSearchParams || {});\r\n\r\n  const [postcodeSuggestions, setPostcodeSuggestions] = useState<PostalCodeLookupResult[]>([]);\r\n  const [searching, setSearching] = useState(false);\r\n  const [prefetching, setPrefetching] = useState(false);\r\n\r\n  // Track if component is mounted to prevent state updates after unmount\r\n  const isMountedRef = useRef(true);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      isMountedRef.current = false;\r\n    };\r\n  }, []);\r\n\r\n  // Fetch address list when searchParams change\r\n  useEffect(() => {\r\n    const fetchAddresses = async () => {\r\n      setLoading(true);\r\n      setError(null);\r\n      try {\r\n        // Use getAllAddresses for general listing, searchPostcodes for specific searches\r\n        const response = Object.keys(searchParams).length > 0\r\n          ? await addressService.searchPostcodes(searchParams)\r\n          : await addressService.getAllAddresses();\r\n\r\n        if (isMountedRef.current) {\r\n          setAddresses(response.data || []);\r\n        }\r\n      } catch (err: any) {\r\n        console.error('Address fetch error:', err);\r\n        if (isMountedRef.current) {\r\n          setError(err.message || 'Failed to fetch addresses');\r\n          toast.error('Failed to fetch addresses');\r\n        }\r\n      } finally {\r\n        if (isMountedRef.current) {\r\n          setLoading(false);\r\n        }\r\n      }\r\n    };\r\n\r\n    fetchAddresses();\r\n  }, [searchParams]);\r\n\r\n  // Postcode suggestions (live lookup, debounced)\r\n  const debouncedSearchPostcodes = useDebouncedCallback(async (params: SearchPostcodes) => {\r\n    if (!isMountedRef.current) return;\r\n\r\n    setSearching(true);\r\n    try {\r\n      const response = await addressService.searchPostcodes(params);\r\n      if (isMountedRef.current) {\r\n        setPostcodeSuggestions(response.data || []);\r\n      }\r\n    } catch (err) {\r\n      console.error('Postcode search failed:', err);\r\n      if (isMountedRef.current) {\r\n        setPostcodeSuggestions([]);\r\n      }\r\n    } finally {\r\n      if (isMountedRef.current) {\r\n        setSearching(false);\r\n      }\r\n    }\r\n  }, 500); // debounce for 500ms\r\n\r\n  // Manual search trigger to update addresses based on params\r\n  const searchAddresses = (params: SearchPostcodes) => {\r\n    setSearchParams(params);\r\n  };\r\n\r\n  // Create new address with enhanced caching\r\n  const createAddress = useCallback(async (data: CreateAddressDto) => {\r\n    setLoading(true);\r\n    setError(null);\r\n    try {\r\n      const response = await addressService.createAddress(data);\r\n      const newAddress = response.data || response;\r\n\r\n      if (isMountedRef.current) {\r\n        setAddresses(prev => [newAddress, ...prev]);\r\n        toast.success('Address created successfully');\r\n      }\r\n      return response;\r\n    } catch (err: any) {\r\n      console.error('Address create error:', err);\r\n      if (isMountedRef.current) {\r\n        setError(err.message || 'Failed to create address');\r\n        toast.error('Failed to create address');\r\n      }\r\n      throw err;\r\n    } finally {\r\n      if (isMountedRef.current) {\r\n        setLoading(false);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Edit existing address with enhanced caching\r\n  const editAddress = useCallback(async (data: EditAddressData) => {\r\n    setLoading(true);\r\n    setError(null);\r\n    try {\r\n      const response = await addressService.editAddress(data);\r\n      const updatedAddress = response.data || response;\r\n\r\n      if (isMountedRef.current) {\r\n        setAddresses(prev =>\r\n          prev.map(addr => (addr.address_id === data.address_id ? updatedAddress : addr))\r\n        );\r\n        toast.success('Address updated successfully');\r\n      }\r\n      return response;\r\n    } catch (err: any) {\r\n      console.error('Address edit error:', err);\r\n      if (isMountedRef.current) {\r\n        setError(err.message || 'Failed to update address');\r\n        toast.error('Failed to update address');\r\n      }\r\n      throw err;\r\n    } finally {\r\n      if (isMountedRef.current) {\r\n        setLoading(false);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Get addresses by entity with caching\r\n  const getAddressesByEntity = useCallback(async (entityType: string, entityId: string) => {\r\n    try {\r\n      const response = await addressService.getAddressesByEntity(entityType, entityId);\r\n      return response;\r\n    } catch (error: any) {\r\n      console.error('Get addresses by entity error:', error);\r\n      const message = error.response?.data?.message || error.message || 'Failed to fetch addresses';\r\n      toast.error(message);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Clear cache manually\r\n  const clearAddressCache = useCallback(() => {\r\n    addressService.clearCache();\r\n    toast.success('Address cache cleared');\r\n  }, []);\r\n\r\n  // Prefetch postal code data\r\n  const prefetchPostalCodeData = useCallback(async () => {\r\n    if (prefetching) return; // Prevent multiple simultaneous prefetch calls\r\n\r\n    setPrefetching(true);\r\n    try {\r\n      await addressService.prefetchPostalCodeData();\r\n    } catch (error) {\r\n      console.error('Failed to prefetch postal code data:', error);\r\n    } finally {\r\n      if (isMountedRef.current) {\r\n        setPrefetching(false);\r\n      }\r\n    }\r\n  }, [prefetching]);\r\n\r\n  \r\n  return {\r\n    // State\r\n    addresses,\r\n    postcodeSuggestions,\r\n    searching,\r\n    prefetching,\r\n    loading,\r\n    error,\r\n    searchParams,\r\n\r\n    // Setters / Triggers\r\n    setSearchParams,\r\n    debouncedSearchPostcodes,\r\n    searchAddresses,\r\n\r\n    // CRUD operations\r\n    createAddress,\r\n    editAddress,\r\n    getAddressesByEntity,\r\n\r\n    // Cache management\r\n    clearAddressCache,\r\n    prefetchPostalCodeData,\r\n\r\n    // Sequential address building helpers\r\n    getRegions: addressService.getRegions,\r\n    getDistrictsByRegion: addressService.getDistrictsByRegion,\r\n    getLocationsByDistrict: addressService.getLocationsByDistrict,\r\n    getPostcodesByLocation: addressService.getPostcodesByLocation,\r\n\r\n    // Postcode lookup helpers\r\n    getPostcodeDetails: addressService.getPostcodeDetails,\r\n    populateAddressFromPostcode: addressService.populateAddressFromPostcode,\r\n\r\n    // Raw service (if needed)\r\n    addressService,\r\n  };\r\n};"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAiBO,MAAM,qBAAuC;IAClD,cAAc;IACd,aAAa;IACb,WAAW;IACX,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,aAAa;IACb,SAAS;IACT,MAAM;AACR;AAWA,MAAM;IAIJ,IAAO,GAAW,EAAE,IAAO,EAAwC;YAAtC,MAAA,iEAAc,IAAI,CAAC,WAAW;QACzD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YAClB;YACA,WAAW,KAAK,GAAG;YACnB,QAAQ,KAAK,GAAG,KAAK;QACvB;IACF;IAEA,IAAO,GAAW,EAAY;QAC5B,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC7B,IAAI,CAAC,OAAO,OAAO;QAEnB,IAAI,KAAK,GAAG,KAAK,MAAM,MAAM,EAAE;YAC7B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,OAAO,MAAM,IAAI;IACnB;IAEA,QAAc;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA,kBAAkB,OAAe,EAAQ;QACvC,MAAM,QAAQ,IAAI,OAAO;QACzB,KAAK,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,GAAI;YACnC,IAAI,MAAM,IAAI,CAAC,MAAM;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACpB;QACF;IACF;;QAlCA,+KAAQ,SAAQ,IAAI;QACpB,+KAAiB,eAAc,OAAO,KAAK,OAAM,0DAA0D;;AAkC7G;AAEA,wBAAwB;AACxB,MAAM,eAAe,IAAI;AAGlB,MAAM,iBAAiB;IAC5B,MAAM,eAAc,IAAsB;QACxC,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;QACjD,4CAA4C;QAC5C,aAAa,iBAAiB,CAAE;QAChC,aAAa,iBAAiB,CAAC,AAAC,oBAAuC,OAApB,KAAK,WAAW,EAAC,KAAkB,OAAf,KAAK,SAAS;QACrF,OAAO;IACT;IAEA,MAAM,YAAW,EAAU;QACzB,MAAM,WAAW,AAAC,WAAa,OAAH;QAC5B,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,IAAI,QAAQ,OAAO;QAEnB,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;QAC9C,aAAa,GAAG,CAAC,UAAU;QAC3B,OAAO;IACT;IAEA,MAAM,aAAY,IAAqB;QACrC,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,WAAW,CAAC;QAC/C,0CAA0C;QAC1C,aAAa,iBAAiB,CAAC,AAAC,WAA0B,OAAhB,KAAK,UAAU;QACzD,aAAa,iBAAiB,CAAE;QAChC,IAAI,KAAK,WAAW,IAAI,KAAK,SAAS,EAAE;YACtC,aAAa,iBAAiB,CAAC,AAAC,oBAAuC,OAApB,KAAK,WAAW,EAAC,KAAkB,OAAf,KAAK,SAAS;QACvF;QACA,OAAO;IACT;IAEA,MAAM,sBAAqB,UAAkB,EAAE,QAAgB;QAC7D,MAAM,WAAW,AAAC,oBAAiC,OAAd,YAAW,KAAY,OAAT;QACnD,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,IAAI,QAAQ,OAAO;QAEnB,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,oBAAoB,CAAC,YAAY;QACpE,aAAa,GAAG,CAAC,UAAU;QAC3B,OAAO;IACT;IAEA,MAAM;QACJ,MAAM,WAAW;QACjB,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,IAAI,QAAQ,OAAO;QAEnB,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,YAAY;QAC/C,aAAa,GAAG,CAAC,UAAU;QAC3B,OAAO;IACT;IAEA,4DAA4D;IAC5D,MAAM;QACJ,MAAM,WAAW;QACjB,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,IAAI,QAAQ;YACV,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC;QAEZ,0CAA0C;QAC1C,IAAI,iBAAwB,EAAE;QAC9B,IAAI,cAAc;QAClB,IAAI,eAAe;QACnB,MAAM,QAAQ,MAAM,8BAA8B;QAClD,IAAI,eAAe;QAEnB,MAAO,aAAc;YACnB,IAAI;gBACF,QAAQ,GAAG,CAAC,AAAC,mBAA4C,OAA1B,aAAY,gBAAoB,OAAN;gBACzD,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,eAAe,CAAC;oBACjD,MAAM,OAAO;oBACb,OAAO,OAAO;gBAChB;gBAEA,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,GAAG;oBAC7C,iBAAiB;2BAAI;2BAAmB,SAAS,IAAI;qBAAC;oBACtD,QAAQ,GAAG,CAAC,AAAC,gBAA+B,OAAhB,aAAY,MAAyB,OAArB,SAAS,IAAI,CAAC,MAAM,EAAC;oBAEjE,gFAAgF;oBAChF,IAAI,SAAS,IAAI,EAAE;wBACjB,eAAe,SAAS,IAAI,CAAC,KAAK;wBAClC,eAAe,SAAS,IAAI,CAAC,OAAO;wBACpC,QAAQ,GAAG,CAAC,AAAC,oBAA4C,OAAzB,eAAe,MAAM,EAAC,KAAqC,OAAlC,cAAa,uBAAkC,OAAb;oBAC7F,OAAO;wBACL,oDAAoD;wBACpD,eAAe,SAAS,IAAI,CAAC,MAAM,KAAK;oBAC1C;oBAEA;gBACF,OAAO;oBACL,eAAe;gBACjB;YACF,EAAE,OAAO,OAAY;oBAIP,iBACF;gBAJV,QAAQ,KAAK,CAAC,AAAC,wBAAmC,OAAZ,aAAY,MAAI;gBACtD,QAAQ,KAAK,CAAC,kBAAkB;oBAC9B,SAAS,MAAM,OAAO;oBACtB,QAAQ,GAAE,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,IAAI;oBAC9B,MAAM,GAAE,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM;oBAC9B,QAAQ;wBAAE,MAAM;wBAAa,OAAO;oBAAM;gBAC5C;gBACA,eAAe;YACjB;QACF;QAEA,MAAM,uBAAuB;YAC3B,MAAM;YACN,MAAM;gBACJ,OAAO,gBAAgB,eAAe,MAAM;gBAC5C,QAAQ;gBACR,eAAe,cAAc;YAC/B;QACF;QACA,aAAa,GAAG,CAAC,UAAU,sBAAsB,OAAO,KAAK,OAAO,qBAAqB;QACzF,QAAQ,GAAG,CAAC,AAAC,2CAAsF,OAA5C,eAAe,MAAM,EAAC,wBAAsC,OAAhB,cAAc,GAAE;QACnH,OAAO;IACT;IAEA,mHAAmH;IACnH,MAAM,oBAAmB,UAAkB;QACzC,IAAI,EAAC,uBAAA,iCAAA,WAAY,IAAI,KAAI;YACvB,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,MAAM,WAAW,AAAC,oBAA8B,OAAX;QACrC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,IAAI,QAAQ;YACV,QAAQ,GAAG,CAAC,AAAC,wBAAkC,OAAX,YAAW;YAC/C,OAAO;QACT;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,AAAC,kCAA4C,OAAX;YAC9C,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,uBAAuB,CAAC;YAE3D,IAAI,UAAU;gBACZ,kCAAkC;gBAClC,aAAa,GAAG,CAAC,UAAU,UAAU,KAAK,KAAK;gBAC/C,QAAQ,GAAG,CAAC,AAAC,+BAAyC,OAAX,YAAW,MAAI;oBACxD,QAAQ,SAAS,MAAM;oBACvB,UAAU,SAAS,QAAQ;oBAC3B,UAAU,SAAS,QAAQ;oBAC3B,aAAa,SAAS,WAAW;gBACnC;gBACA,OAAO;YACT,OAAO;gBACL,QAAQ,IAAI,CAAC,AAAC,kCAA4C,OAAX;gBAC/C,OAAO;YACT;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,AAAC,wCAAkD,OAAX,YAAW,MAAI;YAErE,kDAAkD;YAClD,OAAO;QACT;IACF;IAEA,iEAAiE;IACjE,MAAM,6BAA4B,UAAkB;QAClD,MAAM,kBAAkB,MAAM,IAAI,CAAC,kBAAkB,CAAC;QAEtD,IAAI,CAAC,iBAAiB;YACpB,OAAO;QACT;QAEA,iEAAiE;QACjE,MAAM,cAA8C;YAClD,SAAS;YACT,QAAQ,gBAAgB,MAAM;YAC9B,UAAU,gBAAgB,QAAQ;YAClC,UAAU,gBAAgB,QAAQ;YAClC,aAAa,gBAAgB,WAAW;YACxC,qCAAqC;YACrC,gBAAgB;YAChB,gBAAgB;QAClB;QAEA,QAAQ,GAAG,CAAC,AAAC,wCAAkD,OAAX,YAAW,MAAI;QACnE,OAAO;IACT;IAGA,MAAM,iBAAgB,YAA6B;QACjD,MAAM,WAAW,AAAC,aAAyC,OAA7B,KAAK,SAAS,CAAC;QAC7C,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,IAAI,QAAQ,OAAO;QAEnB,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,eAAe,CAAC;QACnD,+EAA+E;QAC/E,aAAa,GAAG,CAAC,UAAU,UAAU,OAAO,KAAK;QACjD,OAAO;IACT;IAGA,qEAAqE;IACrE,MAAM;YASe;QARnB,MAAM,WAAW;QACjB,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,IAAI,QAAQ,OAAO;QAEnB,gEAAgE;QAChE,MAAM,gBAAgB,MAAM,IAAI,CAAC,sBAAsB;QAEvD,wCAAwC;QACxC,MAAM,aAAa,EAAA,sBAAA,cAAc,IAAI,cAAlB,0CAAA,oBAAoB,GAAG,CAAC,CAAC,KAAY,GAAG,MAAM,EAAE,MAAM,CAAC,aAAY,EAAE;QAExF,MAAM,gBAAgB;eAAI,IAAI,IAAI;SAAY;QAE9C,uCAAuC;QACvC,MAAM,gBAAgB,cAAc,IAAI;QAExC,MAAM,iBAAiB;YAAE,MAAM;QAAc;QAE7C,aAAa,GAAG,CAAC,UAAU,gBAAgB,OAAO,KAAK,OAAO,WAAW;QACzE,QAAQ,GAAG,CAAC,uCAAuC;QACnD,OAAO;IACT;IAEA,MAAM,sBAAqB,MAAc;YASlB;QARrB,MAAM,WAAW,AAAC,aAAmB,OAAP;QAC9B,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,IAAI,QAAQ,OAAO;QAEnB,sCAAsC;QACtC,MAAM,gBAAgB,MAAM,IAAI,CAAC,sBAAsB;QAEvD,gDAAgD;QAChD,MAAM,eAAe,EAAA,sBAAA,cAAc,IAAI,cAAlB,0CAAA,oBAAoB,MAAM,CAAC,CAAC,KAAY,GAAG,MAAM,KAAK,YAAW,EAAE;QACxF,MAAM,eAAe,aAAa,GAAG,CAAC,CAAC,KAAY,GAAG,QAAQ,EAAE,MAAM,CAAC;QACvE,MAAM,kBAAkB;eAAI,IAAI,IAAI;SAAc;QAElD,yCAAyC;QACzC,MAAM,kBAAkB,gBAAgB,IAAI;QAE5C,MAAM,mBAAmB;YAAE,MAAM;QAAgB;QACjD,aAAa,GAAG,CAAC,UAAU,kBAAkB,OAAO,KAAK,OAAO,WAAW;QAC3E,QAAQ,GAAG,CAAC,AAAC,iBAAuB,OAAP,QAAO,iCAA+B;QACnE,QAAQ,GAAG,CAAC,AAAC,oCAA0C,OAAP,QAAO,MAAI,gBAAgB,MAAM;QACjF,OAAO;IACT;IAEA,MAAM,wBAAuB,MAAc,EAAE,QAAgB;YAStC;QARrB,MAAM,WAAW,AAAC,aAAsB,OAAV,QAAO,KAAY,OAAT;QACxC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,IAAI,QAAQ,OAAO;QAEnB,sCAAsC;QACtC,MAAM,gBAAgB,MAAM,IAAI,CAAC,sBAAsB;QAEvD,+DAA+D;QAC/D,MAAM,eAAe,EAAA,sBAAA,cAAc,IAAI,cAAlB,0CAAA,oBAAoB,MAAM,CAAC,CAAC,KAC/C,GAAG,MAAM,KAAK,UAAU,GAAG,QAAQ,KAAK,cACrC,EAAE;QACP,MAAM,eAAe,aAAa,GAAG,CAAC,CAAC,KAAY,GAAG,QAAQ,EAAE,MAAM,CAAC;QACvE,MAAM,kBAAkB;eAAI,IAAI,IAAI;SAAc;QAElD,yCAAyC;QACzC,MAAM,kBAAkB,gBAAgB,IAAI;QAE5C,MAAM,mBAAmB;YAAE,MAAM;QAAgB;QACjD,aAAa,GAAG,CAAC,UAAU,kBAAkB,OAAO,KAAK,OAAO,WAAW;QAC3E,QAAQ,GAAG,CAAC,AAAC,iBAA0B,OAAV,QAAO,KAAY,OAAT,UAAS,iCAA+B;QAC/E,QAAQ,GAAG,CAAC,AAAC,oCAA6C,OAAV,QAAO,KAAY,OAAT,UAAS,MAAI,gBAAgB,MAAM;QAC7F,OAAO;IACT;IAEA,MAAM,wBAAuB,MAAc,EAAE,QAAgB,EAAE,QAAgB;YASxD;QARrB,MAAM,WAAW,AAAC,aAAsB,OAAV,QAAO,KAAe,OAAZ,UAAS,KAAY,OAAT;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,IAAI,QAAQ,OAAO;QAEnB,sCAAsC;QACtC,MAAM,gBAAgB,MAAM,IAAI,CAAC,sBAAsB;QAEvD,2CAA2C;QAC3C,MAAM,eAAe,EAAA,sBAAA,cAAc,IAAI,cAAlB,0CAAA,oBAAoB,MAAM,CAAC,CAAC,KAC/C,GAAG,MAAM,KAAK,UAAU,GAAG,QAAQ,KAAK,YAAY,GAAG,QAAQ,KAAK,cACjE,EAAE;QAEP,yCAAyC;QACzC,MAAM,kBAAkB,aAAa,IAAI,CAAC,CAAC,GAAQ;YACjD,OAAO,EAAE,WAAW,CAAC,aAAa,CAAC,EAAE,WAAW;QAClD;QAEA,MAAM,mBAAmB;YAAE,MAAM;QAAgB;QACjD,aAAa,GAAG,CAAC,UAAU,kBAAkB,OAAO,KAAK,OAAO,WAAW;QAC3E,QAAQ,GAAG,CAAC,AAAC,iBAA0B,OAAV,QAAO,KAAe,OAAZ,UAAS,KAAY,OAAT,UAAS,iCAA+B,gBAAgB,MAAM,EAAE;QACnH,OAAO;IACT;IAEA,2BAA2B;IAC3B;QACE,aAAa,KAAK;IACpB;IAEA,wBAAuB,SAAkB;QACvC,IAAI,WAAW;YACb,aAAa,iBAAiB,CAAC,AAAC,WAAoB,OAAV;QAC5C;QACA,aAAa,iBAAiB,CAAE;IAClC;AACF;AAIO,MAAM,eAAe,CAAC;;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,uBAAuB,CAAC;IAE1F,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B,EAAE;IAC3F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,uEAAuE;IACvE,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;0CAAO;oBACL,aAAa,OAAO,GAAG;gBACzB;;QACF;iCAAG,EAAE;IAEL,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;yDAAiB;oBACrB,WAAW;oBACX,SAAS;oBACT,IAAI;wBACF,iFAAiF;wBACjF,MAAM,WAAW,OAAO,IAAI,CAAC,cAAc,MAAM,GAAG,IAChD,MAAM,eAAe,eAAe,CAAC,gBACrC,MAAM,eAAe,eAAe;wBAExC,IAAI,aAAa,OAAO,EAAE;4BACxB,aAAa,SAAS,IAAI,IAAI,EAAE;wBAClC;oBACF,EAAE,OAAO,KAAU;wBACjB,QAAQ,KAAK,CAAC,wBAAwB;wBACtC,IAAI,aAAa,OAAO,EAAE;4BACxB,SAAS,IAAI,OAAO,IAAI;4BACxB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACd;oBACF,SAAU;wBACR,IAAI,aAAa,OAAO,EAAE;4BACxB,WAAW;wBACb;oBACF;gBACF;;YAEA;QACF;iCAAG;QAAC;KAAa;IAEjB,gDAAgD;IAChD,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,uBAAoB,AAAD;uEAAE,OAAO;YAC3D,IAAI,CAAC,aAAa,OAAO,EAAE;YAE3B,aAAa;YACb,IAAI;gBACF,MAAM,WAAW,MAAM,eAAe,eAAe,CAAC;gBACtD,IAAI,aAAa,OAAO,EAAE;oBACxB,uBAAuB,SAAS,IAAI,IAAI,EAAE;gBAC5C;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,IAAI,aAAa,OAAO,EAAE;oBACxB,uBAAuB,EAAE;gBAC3B;YACF,SAAU;gBACR,IAAI,aAAa,OAAO,EAAE;oBACxB,aAAa;gBACf;YACF;QACF;sEAAG,MAAM,qBAAqB;IAE9B,4DAA4D;IAC5D,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;IAClB;IAEA,2CAA2C;IAC3C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,OAAO;YACvC,WAAW;YACX,SAAS;YACT,IAAI;gBACF,MAAM,WAAW,MAAM,eAAe,aAAa,CAAC;gBACpD,MAAM,aAAa,SAAS,IAAI,IAAI;gBAEpC,IAAI,aAAa,OAAO,EAAE;oBACxB;mEAAa,CAAA,OAAQ;gCAAC;mCAAe;6BAAK;;oBAC1C,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;gBACA,OAAO;YACT,EAAE,OAAO,KAAU;gBACjB,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,IAAI,aAAa,OAAO,EAAE;oBACxB,SAAS,IAAI,OAAO,IAAI;oBACxB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;gBACA,MAAM;YACR,SAAU;gBACR,IAAI,aAAa,OAAO,EAAE;oBACxB,WAAW;gBACb;YACF;QACF;kDAAG,EAAE;IAEL,8CAA8C;IAC9C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,OAAO;YACrC,WAAW;YACX,SAAS;YACT,IAAI;gBACF,MAAM,WAAW,MAAM,eAAe,WAAW,CAAC;gBAClD,MAAM,iBAAiB,SAAS,IAAI,IAAI;gBAExC,IAAI,aAAa,OAAO,EAAE;oBACxB;iEAAa,CAAA,OACX,KAAK,GAAG;yEAAC,CAAA,OAAS,KAAK,UAAU,KAAK,KAAK,UAAU,GAAG,iBAAiB;;;oBAE3E,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;gBACA,OAAO;YACT,EAAE,OAAO,KAAU;gBACjB,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,IAAI,aAAa,OAAO,EAAE;oBACxB,SAAS,IAAI,OAAO,IAAI;oBACxB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;gBACA,MAAM;YACR,SAAU;gBACR,IAAI,aAAa,OAAO,EAAE;oBACxB,WAAW;gBACb;YACF;QACF;gDAAG,EAAE;IAEL,uCAAuC;IACvC,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,OAAO,YAAoB;YAClE,IAAI;gBACF,MAAM,WAAW,MAAM,eAAe,oBAAoB,CAAC,YAAY;gBACvE,OAAO;YACT,EAAE,OAAO,OAAY;oBAEH,sBAAA;gBADhB,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI,MAAM,OAAO,IAAI;gBAClE,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR;QACF;yDAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YACpC,eAAe,UAAU;YACzB,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;sDAAG,EAAE;IAEL,4BAA4B;IAC5B,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACzC,IAAI,aAAa,QAAQ,+CAA+C;YAExE,eAAe;YACf,IAAI;gBACF,MAAM,eAAe,sBAAsB;YAC7C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;YACxD,SAAU;gBACR,IAAI,aAAa,OAAO,EAAE;oBACxB,eAAe;gBACjB;YACF;QACF;2DAAG;QAAC;KAAY;IAGhB,OAAO;QACL,QAAQ;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,qBAAqB;QACrB;QACA;QACA;QAEA,kBAAkB;QAClB;QACA;QACA;QAEA,mBAAmB;QACnB;QACA;QAEA,sCAAsC;QACtC,YAAY,eAAe,UAAU;QACrC,sBAAsB,eAAe,oBAAoB;QACzD,wBAAwB,eAAe,sBAAsB;QAC7D,wBAAwB,eAAe,sBAAsB;QAE7D,0BAA0B;QAC1B,oBAAoB,eAAe,kBAAkB;QACrD,6BAA6B,eAAe,2BAA2B;QAEvE,0BAA0B;QAC1B;IACF;AACF;GA7Ma;;QAkDsB,6JAAA,CAAA,uBAAoB", "debugId": null}}, {"offset": {"line": 5124, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/evaluation/AddressInfoCard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport DataDisplayCard from './DataDisplayCard';\r\nimport { Application } from '@/types/license';\r\nimport { addressService } from '@/hooks/useAddressing';\r\n\r\ninterface AddressInfoCardProps {\r\n  application: Application | null;\r\n  className?: string;\r\n  showEmptyFields?: boolean;\r\n  defaultCollapsed?: boolean;\r\n}\r\n\r\nconst AddressInfoCard: React.FC<AddressInfoCardProps> = ({\r\n  application,\r\n  className = '',\r\n  showEmptyFields = true,\r\n  defaultCollapsed = false\r\n}) => {\r\n  const [addresses, setAddresses] = useState<any[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const fetchAddresses = async () => {\r\n      if (!application?.applicant_id) return;\r\n\r\n      try {\r\n        setLoading(true);\r\n        const addressResponse = await addressService.getAddressesByEntity('applicant', application.applicant_id);\r\n        const addressData = addressResponse?.data || addressResponse || [];\r\n        setAddresses(addressData);\r\n      } catch (err) {\r\n        console.warn('Could not load addresses:', err);\r\n        setAddresses([]);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchAddresses();\r\n  }, [application?.applicant_id]);\r\n\r\n  // Return empty fragment if no application or no addresses\r\n  if (!application || loading || !addresses || addresses.length === 0) {\r\n    return <></>;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {addresses.map((address, index) => (\r\n        <DataDisplayCard\r\n          key={index}\r\n          title={`${address.address_type || 'Address'} ${addresses.length > 1 ? `#${index + 1}` : ''}`}\r\n          icon=\"ri-map-pin-line\"\r\n          className={className}\r\n          showEmptyFields={showEmptyFields}\r\n          defaultCollapsed={defaultCollapsed}\r\n          creatorEmail={application?.applicant?.email}\r\n          creatorName={application?.applicant?.name}\r\n          showEmailButton={true}\r\n          fields={[\r\n            {\r\n              label: 'Street Address',\r\n              value: address.street_address,\r\n              icon: 'ri-road-map-line'\r\n            },\r\n            {\r\n              label: 'City',\r\n              value: address.city,\r\n              icon: 'ri-building-line'\r\n            },\r\n            {\r\n              label: 'State/Province',\r\n              value: address.state_province,\r\n              icon: 'ri-map-line'\r\n            },\r\n            {\r\n              label: 'Postal Code',\r\n              value: address.postal_code,\r\n              icon: 'ri-mail-line'\r\n            },\r\n            {\r\n              label: 'Country',\r\n              value: address.country,\r\n              icon: 'ri-earth-line'\r\n            },\r\n            {\r\n              label: 'Address Type',\r\n              value: address.address_type,\r\n              icon: 'ri-home-line'\r\n            },\r\n            {\r\n              label: 'Full Address',\r\n              value: `${address.street_address || ''} ${address.city || ''} ${address.state_province || ''} ${address.postal_code || ''}`.trim(),\r\n              fullWidth: true,\r\n              icon: 'ri-map-pin-2-line'\r\n            }\r\n          ]}\r\n        />\r\n      ))}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AddressInfoCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;;AASA,MAAM,kBAAkD;QAAC,EACvD,WAAW,EACX,YAAY,EAAE,EACd,kBAAkB,IAAI,EACtB,mBAAmB,KAAK,EACzB;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;4DAAiB;oBACrB,IAAI,EAAC,wBAAA,kCAAA,YAAa,YAAY,GAAE;oBAEhC,IAAI;wBACF,WAAW;wBACX,MAAM,kBAAkB,MAAM,gIAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,aAAa,YAAY,YAAY;wBACvG,MAAM,cAAc,CAAA,4BAAA,sCAAA,gBAAiB,IAAI,KAAI,mBAAmB,EAAE;wBAClE,aAAa;oBACf,EAAE,OAAO,KAAK;wBACZ,QAAQ,IAAI,CAAC,6BAA6B;wBAC1C,aAAa,EAAE;oBACjB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;oCAAG;QAAC,wBAAA,kCAAA,YAAa,YAAY;KAAC;IAE9B,0DAA0D;IAC1D,IAAI,CAAC,eAAe,WAAW,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;QACnE,qBAAO;IACT;IAEA,qBACE;kBACG,UAAU,GAAG,CAAC,CAAC,SAAS;gBAQP,wBACD;iCARf,6LAAC,sJAAA,CAAA,UAAe;gBAEd,OAAO,AAAC,GAAuC,OAArC,QAAQ,YAAY,IAAI,WAAU,KAA+C,OAA5C,UAAU,MAAM,GAAG,IAAI,AAAC,IAAa,OAAV,QAAQ,KAAM;gBACxF,MAAK;gBACL,WAAW;gBACX,iBAAiB;gBACjB,kBAAkB;gBAClB,YAAY,EAAE,wBAAA,mCAAA,yBAAA,YAAa,SAAS,cAAtB,6CAAA,uBAAwB,KAAK;gBAC3C,WAAW,EAAE,wBAAA,mCAAA,0BAAA,YAAa,SAAS,cAAtB,8CAAA,wBAAwB,IAAI;gBACzC,iBAAiB;gBACjB,QAAQ;oBACN;wBACE,OAAO;wBACP,OAAO,QAAQ,cAAc;wBAC7B,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,QAAQ,IAAI;wBACnB,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,QAAQ,cAAc;wBAC7B,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,QAAQ,WAAW;wBAC1B,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,QAAQ,OAAO;wBACtB,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,QAAQ,YAAY;wBAC3B,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,AAAC,GAAkC,OAAhC,QAAQ,cAAc,IAAI,IAAG,KAAyB,OAAtB,QAAQ,IAAI,IAAI,IAAG,KAAmC,OAAhC,QAAQ,cAAc,IAAI,IAAG,KAA6B,OAA1B,QAAQ,WAAW,IAAI,IAAK,IAAI;wBAChI,WAAW;wBACX,MAAM;oBACR;iBACD;eA9CI;;;;;;;AAmDf;GAzFM;KAAA;uCA2FS", "debugId": null}}, {"offset": {"line": 5240, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/contactPersonService.ts"], "sourcesContent": ["import { CustomerApiService } from '@/lib/customer-api';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\n\r\nconst customerApi = new CustomerApiService();\r\n\r\nexport interface ContactPerson {\r\n  contact_id: string;\r\n  application_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  middle_name?: string;\r\n  designation: string;\r\n  email: string;\r\n  phone: string;\r\n  is_primary: boolean;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface CreateContactPersonData {\r\n  application_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  middle_name?: string;\r\n  designation: string;\r\n  email: string;\r\n  phone: string;\r\n  is_primary?: boolean;\r\n}\r\n\r\nexport interface UpdateContactPersonData {\r\n  contact_id: string;\r\n  first_name?: string;\r\n  last_name?: string;\r\n  middle_name?: string;\r\n  designation?: string;\r\n  email?: string;\r\n  phone?: string;\r\n  is_primary?: boolean;\r\n}\r\n\r\nexport interface ContactPersonsByType {\r\n  primary: ContactPerson | null;\r\n  secondary: ContactPerson[];\r\n}\r\n\r\nexport const contactPersonService = {\r\n  async createContactPerson(data: CreateContactPersonData): Promise<ContactPerson> {\r\n    const response = await customerApi.api.post('/contact-persons', data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  async getContactPerson(contactId: string): Promise<ContactPerson> {\r\n    const response = await customerApi.api.get(`/contact-persons/${contactId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  async updateContactPerson(data: UpdateContactPersonData): Promise<ContactPerson> {\r\n    const { contact_id, ...updateData } = data;\r\n    const response = await customerApi.api.put(`/contact-persons/${contact_id}`, updateData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  async deleteContactPerson(contactId: string): Promise<void> {\r\n    await customerApi.api.delete(`/contact-persons/${contactId}`);\r\n  },\r\n\r\n  async getContactPersonsByApplication(applicationId: string) {\r\n    const response = await customerApi.api.get(`/contact-persons/application/${applicationId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  async getContactPersonsByApplicationGrouped(applicationId: string): Promise<ContactPersonsByType> {\r\n    const response = await customerApi.api.get(`/contact-persons/application/${applicationId}/grouped`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Legacy method for backward compatibility\r\n  async getContactPersonsByApplicant(applicantId: string) {\r\n    // This method is deprecated - use getContactPersonsByApplication instead\r\n    console.warn('getContactPersonsByApplicant is deprecated. Use getContactPersonsByApplication instead.');\r\n    const response = await customerApi.api.get(`/contact-persons/application/${applicantId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  async getContactPersonsByApplicantGrouped(applicantId: string): Promise<ContactPersonsByType> {\r\n    // This method is deprecated - use getContactPersonsByApplicationGrouped instead\r\n    console.warn('getContactPersonsByApplicantGrouped is deprecated. Use getContactPersonsByApplicationGrouped instead.');\r\n    const response = await customerApi.api.get(`/contact-persons/application/${applicantId}/grouped`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  async setPrimaryContact(applicationId: string, contactPersonId: string): Promise<ContactPerson> {\r\n    const response = await customerApi.api.put(`/contact-persons/${contactPersonId}/set-primary`, {\r\n      application_id: applicationId\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  async searchContactPersons(searchTerm: string) {\r\n    const response = await customerApi.api.get(`/contact-persons/search?q=${encodeURIComponent(searchTerm)}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Helper methods for creating different types of contact persons\r\n  async createPrimaryContact(applicationId: string, contactData: Omit<CreateContactPersonData, 'application_id' | 'is_primary'>): Promise<ContactPerson> {\r\n    return this.createContactPerson({\r\n      ...contactData,\r\n      application_id: applicationId,\r\n      is_primary: true\r\n    });\r\n  },\r\n\r\n  async createSecondaryContact(applicationId: string, contactData: Omit<CreateContactPersonData, 'application_id' | 'is_primary'>): Promise<ContactPerson> {\r\n    return this.createContactPerson({\r\n      ...contactData,\r\n      application_id: applicationId,\r\n      is_primary: false\r\n    });\r\n  },\r\n\r\n\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,cAAc,IAAI,gIAAA,CAAA,qBAAkB;AA2CnC,MAAM,uBAAuB;IAClC,MAAM,qBAAoB,IAA6B;QACrD,MAAM,WAAW,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,oBAAoB;QAChE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,kBAAiB,SAAiB;QACtC,MAAM,WAAW,MAAM,YAAY,GAAG,CAAC,GAAG,CAAC,AAAC,oBAA6B,OAAV;QAC/D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,qBAAoB,IAA6B;QACrD,MAAM,EAAE,UAAU,EAAE,GAAG,YAAY,GAAG;QACtC,MAAM,WAAW,MAAM,YAAY,GAAG,CAAC,GAAG,CAAC,AAAC,oBAA8B,OAAX,aAAc;QAC7E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,qBAAoB,SAAiB;QACzC,MAAM,YAAY,GAAG,CAAC,MAAM,CAAC,AAAC,oBAA6B,OAAV;IACnD;IAEA,MAAM,gCAA+B,aAAqB;QACxD,MAAM,WAAW,MAAM,YAAY,GAAG,CAAC,GAAG,CAAC,AAAC,gCAA6C,OAAd;QAC3E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uCAAsC,aAAqB;QAC/D,MAAM,WAAW,MAAM,YAAY,GAAG,CAAC,GAAG,CAAC,AAAC,gCAA6C,OAAd,eAAc;QACzF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,2CAA2C;IAC3C,MAAM,8BAA6B,WAAmB;QACpD,yEAAyE;QACzE,QAAQ,IAAI,CAAC;QACb,MAAM,WAAW,MAAM,YAAY,GAAG,CAAC,GAAG,CAAC,AAAC,gCAA2C,OAAZ;QAC3E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,qCAAoC,WAAmB;QAC3D,gFAAgF;QAChF,QAAQ,IAAI,CAAC;QACb,MAAM,WAAW,MAAM,YAAY,GAAG,CAAC,GAAG,CAAC,AAAC,gCAA2C,OAAZ,aAAY;QACvF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,mBAAkB,aAAqB,EAAE,eAAuB;QACpE,MAAM,WAAW,MAAM,YAAY,GAAG,CAAC,GAAG,CAAC,AAAC,oBAAmC,OAAhB,iBAAgB,iBAAe;YAC5F,gBAAgB;QAClB;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,sBAAqB,UAAkB;QAC3C,MAAM,WAAW,MAAM,YAAY,GAAG,CAAC,GAAG,CAAC,AAAC,6BAA2D,OAA/B,mBAAmB;QAC3F,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,iEAAiE;IACjE,MAAM,sBAAqB,aAAqB,EAAE,WAA2E;QAC3H,OAAO,IAAI,CAAC,mBAAmB,CAAC;YAC9B,GAAG,WAAW;YACd,gBAAgB;YAChB,YAAY;QACd;IACF;IAEA,MAAM,wBAAuB,aAAqB,EAAE,WAA2E;QAC7H,OAAO,IAAI,CAAC,mBAAmB,CAAC;YAC9B,GAAG,WAAW;YACd,gBAAgB;YAChB,YAAY;QACd;IACF;AAGF", "debugId": null}}, {"offset": {"line": 5321, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/evaluation/ContactInfoCard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport DataDisplayCard from './DataDisplayCard';\r\nimport { Application } from '@/types/license';\r\nimport { contactPersonService } from '@/services/contactPersonService';\r\n\r\ninterface ContactInfoCardProps {\r\n  application: Application | null;\r\n  className?: string;\r\n  showEmptyFields?: boolean;\r\n  defaultCollapsed?: boolean;\r\n}\r\n\r\nconst ContactInfoCard: React.FC<ContactInfoCardProps> = ({\r\n  application,\r\n  className = '',\r\n  showEmptyFields = true,\r\n  defaultCollapsed = false\r\n}) => {\r\n  const [contactPersons, setContactPersons] = useState<any[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const fetchContactPersons = async () => {\r\n      if (!application?.application_id) return;\r\n\r\n      try {\r\n        setLoading(true);\r\n        const contactResponse = await contactPersonService.getContactPersonsByApplication(application.application_id);\r\n        const contactData = contactResponse?.data || contactResponse || [];\r\n        setContactPersons(contactData);\r\n      } catch (err) {\r\n        console.warn('Could not load contact persons:', err);\r\n        setContactPersons([]);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchContactPersons();\r\n  }, [application?.application_id]);\r\n\r\n  // Return empty fragment if no application or no contact persons\r\n  if (!application || loading || !contactPersons || contactPersons.length === 0) {\r\n    return <></>;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {contactPersons.map((contact, index) => (\r\n        <DataDisplayCard\r\n          key={index}\r\n          title={`${contact.is_primary ? 'Primary' : 'Secondary'} Contact Person ${contactPersons.length > 1 ? `#${index + 1}` : ''}`}\r\n          icon=\"ri-user-line\"\r\n          className={className}\r\n          showEmptyFields={showEmptyFields}\r\n          defaultCollapsed={defaultCollapsed}\r\n          creatorEmail={contact.email}\r\n          creatorName={`${contact.first_name} ${contact.last_name}`.trim()}\r\n          showEmailButton={true}\r\n          fields={[\r\n            {\r\n              label: 'First Name',\r\n              value: contact.first_name,\r\n              icon: 'ri-user-3-line'\r\n            },\r\n            {\r\n              label: 'Last Name',\r\n              value: contact.last_name,\r\n              icon: 'ri-user-3-line'\r\n            },\r\n            {\r\n              label: 'Middle Name',\r\n              value: contact.middle_name,\r\n              icon: 'ri-user-3-line'\r\n            },\r\n            {\r\n              label: 'Designation',\r\n              value: contact.designation,\r\n              icon: 'ri-briefcase-line'\r\n            },\r\n            {\r\n              label: 'Email Address',\r\n              value: contact.email,\r\n              type: 'email' as const,\r\n              icon: 'ri-mail-line'\r\n            },\r\n            {\r\n              label: 'Phone Number',\r\n              value: contact.phone,\r\n              type: 'phone' as const,\r\n              icon: 'ri-phone-line'\r\n            },\r\n            {\r\n              label: 'Primary Contact',\r\n              value: contact.is_primary,\r\n              type: 'boolean' as const,\r\n              icon: 'ri-star-line'\r\n            },\r\n            ...(contact.notes ? [{\r\n              label: 'Additional Notes',\r\n              value: contact.notes,\r\n              icon: 'ri-sticky-note-line',\r\n              fullWidth: true\r\n            }] : [])\r\n          ]}\r\n        />\r\n      ))}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ContactInfoCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;;AASA,MAAM,kBAAkD;QAAC,EACvD,WAAW,EACX,YAAY,EAAE,EACd,kBAAkB,IAAI,EACtB,mBAAmB,KAAK,EACzB;;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;iEAAsB;oBAC1B,IAAI,EAAC,wBAAA,kCAAA,YAAa,cAAc,GAAE;oBAElC,IAAI;wBACF,WAAW;wBACX,MAAM,kBAAkB,MAAM,0IAAA,CAAA,uBAAoB,CAAC,8BAA8B,CAAC,YAAY,cAAc;wBAC5G,MAAM,cAAc,CAAA,4BAAA,sCAAA,gBAAiB,IAAI,KAAI,mBAAmB,EAAE;wBAClE,kBAAkB;oBACpB,EAAE,OAAO,KAAK;wBACZ,QAAQ,IAAI,CAAC,mCAAmC;wBAChD,kBAAkB,EAAE;oBACtB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;oCAAG;QAAC,wBAAA,kCAAA,YAAa,cAAc;KAAC;IAEhC,gEAAgE;IAChE,IAAI,CAAC,eAAe,WAAW,CAAC,kBAAkB,eAAe,MAAM,KAAK,GAAG;QAC7E,qBAAO;IACT;IAEA,qBACE;kBACG,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,6LAAC,sJAAA,CAAA,UAAe;gBAEd,OAAO,AAAC,GAAiE,OAA/D,QAAQ,UAAU,GAAG,YAAY,aAAY,oBAAmE,OAAjD,eAAe,MAAM,GAAG,IAAI,AAAC,IAAa,OAAV,QAAQ,KAAM;gBACvH,MAAK;gBACL,WAAW;gBACX,iBAAiB;gBACjB,kBAAkB;gBAClB,cAAc,QAAQ,KAAK;gBAC3B,aAAa,AAAC,GAAwB,OAAtB,QAAQ,UAAU,EAAC,KAAqB,OAAlB,QAAQ,SAAS,EAAG,IAAI;gBAC9D,iBAAiB;gBACjB,QAAQ;oBACN;wBACE,OAAO;wBACP,OAAO,QAAQ,UAAU;wBACzB,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,QAAQ,SAAS;wBACxB,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,QAAQ,WAAW;wBAC1B,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,QAAQ,WAAW;wBAC1B,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,QAAQ,KAAK;wBACpB,MAAM;wBACN,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,QAAQ,KAAK;wBACpB,MAAM;wBACN,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,QAAQ,UAAU;wBACzB,MAAM;wBACN,MAAM;oBACR;uBACI,QAAQ,KAAK,GAAG;wBAAC;4BACnB,OAAO;4BACP,OAAO,QAAQ,KAAK;4BACpB,MAAM;4BACN,WAAW;wBACb;qBAAE,GAAG,EAAE;iBACR;eAtDI;;;;;;AA2Df;GAjGM;KAAA;uCAmGS", "debugId": null}}, {"offset": {"line": 5444, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/scopeOfServiceService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\n\r\nexport interface ScopeOfServiceData {\r\n  scope_of_service_id?: string;\r\n  application_id?: string;\r\n  nature_of_service: string;\r\n  premises: string;\r\n  transport_type: string;\r\n  customer_assistance: string;\r\n  created_at?: string;\r\n  updated_at?: string;\r\n}\r\n\r\nexport interface CreateScopeOfServiceData {\r\n  application_id: string;\r\n  nature_of_service: string;\r\n  premises: string;\r\n  transport_type: string;\r\n  customer_assistance: string;\r\n}\r\n\r\nexport interface UpdateScopeOfServiceData {\r\n  scope_of_service_id: string;\r\n  nature_of_service?: string;\r\n  premises?: string;\r\n  transport_type?: string;\r\n  customer_assistance?: string;\r\n}\r\n\r\nexport const scopeOfServiceService = {\r\n  // Create new scope of service\r\n  async createScopeOfService(data: CreateScopeOfServiceData): Promise<ScopeOfServiceData> {\r\n    try {\r\n      console.log('Creating scope of service with data:', data);\r\n      const response = await apiClient.post('/scope-of-service', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('ScopeOfServiceService.createScopeOfService error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get scope of service by ID\r\n  async getScopeOfService(id: string): Promise<ScopeOfServiceData> {\r\n    try {\r\n      // TODO: Backend scope of service endpoints not available yet\r\n      const response = await apiClient.get(`/scope-of-service/${id}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('ScopeOfServiceService.getScopeOfService error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get scope of service by application ID\r\n  async getScopeOfServiceByApplication(applicationId: string): Promise<ScopeOfServiceData | null> {\r\n    try {\r\n      console.log('🔧 Getting scope of service for application:', applicationId);\r\n\r\n      // Validate applicationId format\r\n      if (!applicationId || typeof applicationId !== 'string') {\r\n        throw new Error(`Invalid application ID: ${applicationId}`);\r\n      }\r\n\r\n      const response = await apiClient.get(`/scope-of-service/application/${applicationId}`);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      if (error.response?.status === 404) {\r\n        console.log('📝 No scope of service found for application:', applicationId);\r\n        return null;\r\n      }\r\n\r\n      // Enhanced error logging for 500 errors\r\n      if (error.response?.status === 500) {\r\n        console.error('❌ Scope of service 500 error:', {\r\n          applicationId,\r\n          url: `/scope-of-service/application/${applicationId}`,\r\n          status: error.response.status,\r\n          statusText: error.response.statusText,\r\n          data: error.response.data,\r\n          message: error.message\r\n        });\r\n      } else {\r\n        console.error('❌ Error getting scope of service:', error);\r\n      }\r\n\r\n      return null; // Return null instead of throwing\r\n    }\r\n  },\r\n\r\n  // Update scope of service\r\n  async updateScopeOfService(data: UpdateScopeOfServiceData): Promise<ScopeOfServiceData> {\r\n    try {\r\n      console.log('Updating scope of service:', data.scope_of_service_id, data);\r\n      // TODO: Backend scope of service endpoints not available yet\r\n      console.warn('Scope of service endpoints not implemented yet - returning mock data');\r\n\r\n      const response = await apiClient.put(`/scope-of-service/${data.scope_of_service_id}`, data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('ScopeOfServiceService.updateScopeOfService error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Create or update scope of service for an application\r\n  async createOrUpdateScopeOfService(applicationId: string, data: Omit<CreateScopeOfServiceData, 'application_id'>): Promise<ScopeOfServiceData> {\r\n    try {\r\n      // Use the backend's combined create/update endpoint\r\n      const response = await apiClient.post(`/scope-of-service/application/${applicationId}`, data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('ScopeOfServiceService.createOrUpdateScopeOfService error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA6BO,MAAM,wBAAwB;IACnC,8BAA8B;IAC9B,MAAM,sBAAqB,IAA8B;QACvD,IAAI;YACF,QAAQ,GAAG,CAAC,wCAAwC;YACpD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,qBAAqB;YAC3D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qDAAqD;YACnE,MAAM;QACR;IACF;IAEA,6BAA6B;IAC7B,MAAM,mBAAkB,EAAU;QAChC,IAAI;YACF,6DAA6D;YAC7D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,qBAAuB,OAAH;YAC1D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kDAAkD;YAChE,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,gCAA+B,aAAqB;QACxD,IAAI;YACF,QAAQ,GAAG,CAAC,gDAAgD;YAE5D,gCAAgC;YAChC,IAAI,CAAC,iBAAiB,OAAO,kBAAkB,UAAU;gBACvD,MAAM,IAAI,MAAM,AAAC,2BAAwC,OAAd;YAC7C;YAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iCAA8C,OAAd;YACtE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;gBACf,iBAMA;YANJ,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;gBAClC,QAAQ,GAAG,CAAC,iDAAiD;gBAC7D,OAAO;YACT;YAEA,wCAAwC;YACxC,IAAI,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,MAAK,KAAK;gBAClC,QAAQ,KAAK,CAAC,iCAAiC;oBAC7C;oBACA,KAAK,AAAC,iCAA8C,OAAd;oBACtC,QAAQ,MAAM,QAAQ,CAAC,MAAM;oBAC7B,YAAY,MAAM,QAAQ,CAAC,UAAU;oBACrC,MAAM,MAAM,QAAQ,CAAC,IAAI;oBACzB,SAAS,MAAM,OAAO;gBACxB;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,qCAAqC;YACrD;YAEA,OAAO,MAAM,kCAAkC;QACjD;IACF;IAEA,0BAA0B;IAC1B,MAAM,sBAAqB,IAA8B;QACvD,IAAI;YACF,QAAQ,GAAG,CAAC,8BAA8B,KAAK,mBAAmB,EAAE;YACpE,6DAA6D;YAC7D,QAAQ,IAAI,CAAC;YAEb,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,qBAA6C,OAAzB,KAAK,mBAAmB,GAAI;YACtF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qDAAqD;YACnE,MAAM;QACR;IACF;IAEA,uDAAuD;IACvD,MAAM,8BAA6B,aAAqB,EAAE,IAAsD;QAC9G,IAAI;YACF,oDAAoD;YACpD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,AAAC,iCAA8C,OAAd,gBAAiB;YACxF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6DAA6D;YAC3E,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 5540, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/evaluation/ServiceScopeCard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport DataDisplayCard from './DataDisplayCard';\r\nimport { Application } from '@/types/license';\r\nimport { scopeOfServiceService } from '@/services/scopeOfServiceService';\r\n\r\ninterface ServiceScopeCardProps {\r\n  application: Application | null;\r\n  className?: string;\r\n  showEmptyFields?: boolean;\r\n  defaultCollapsed?: boolean;\r\n}\r\n\r\nconst ServiceScopeCard: React.FC<ServiceScopeCardProps> = ({\r\n  application,\r\n  className = '',\r\n  showEmptyFields = true,\r\n  defaultCollapsed = false\r\n}) => {\r\n  const [scopeOfServices, setScopeOfServices] = useState<any[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const fetchScopeOfServices = async () => {\r\n      if (!application?.application_id) return;\r\n      try {\r\n        setLoading(true);\r\n        const scopeOfService = await scopeOfServiceService.getScopeOfServiceByApplication(application.application_id);\r\n        setScopeOfServices(scopeOfService ? [scopeOfService] : []);\r\n      } catch (err) {\r\n        console.warn('Could not load scope of services:', err);\r\n        setScopeOfServices([]);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchScopeOfServices();\r\n  }, [application?.application_id]);\r\n\r\n  // Return empty fragment if no application or no scope of services\r\n  if (!application || loading || !scopeOfServices || scopeOfServices.length === 0) {\r\n    return <></>;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {scopeOfServices.map((scopeData, index) => (\r\n        <DataDisplayCard\r\n          key={index}\r\n          title={`Service Scope Information ${scopeOfServices.length > 1 ? `#${index + 1}` : ''}`}\r\n          icon=\"ri-service-line\"\r\n          className={className}\r\n          showEmptyFields={showEmptyFields}\r\n          defaultCollapsed={defaultCollapsed}\r\n          creatorEmail={application?.applicant?.email}\r\n          creatorName={application?.applicant?.name}\r\n          showEmailButton={true}\r\n          fields={[\r\n            {\r\n              label: 'Nature of Service',\r\n              value: scopeData.nature_of_service,\r\n              icon: 'ri-service-line'\r\n            },\r\n            {\r\n              label: 'Premises',\r\n              value: scopeData.premises,\r\n              icon: 'ri-building-line'\r\n            },\r\n            {\r\n              label: 'Transport Type',\r\n              value: scopeData.transport_type,\r\n              icon: 'ri-truck-line'\r\n            },\r\n            {\r\n              label: 'Customer Assistance',\r\n              value: scopeData.customer_assistance,\r\n              icon: 'ri-customer-service-line'\r\n            },\r\n            {\r\n              label: 'Created Date',\r\n              value: scopeData.created_at ? new Date(scopeData.created_at).toLocaleDateString() : null,\r\n              type: 'date' as const,\r\n              icon: 'ri-calendar-line'\r\n            },\r\n            ...(scopeData.description ? [{\r\n              label: 'Description',\r\n              value: scopeData.description,\r\n              icon: 'ri-information-line',\r\n              fullWidth: true\r\n            }] : [])\r\n          ]}\r\n        />\r\n      ))}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ServiceScopeCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;;AASA,MAAM,mBAAoD;QAAC,EACzD,WAAW,EACX,YAAY,EAAE,EACd,kBAAkB,IAAI,EACtB,mBAAmB,KAAK,EACzB;;IACC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;mEAAuB;oBAC3B,IAAI,EAAC,wBAAA,kCAAA,YAAa,cAAc,GAAE;oBAClC,IAAI;wBACF,WAAW;wBACX,MAAM,iBAAiB,MAAM,2IAAA,CAAA,wBAAqB,CAAC,8BAA8B,CAAC,YAAY,cAAc;wBAC5G,mBAAmB,iBAAiB;4BAAC;yBAAe,GAAG,EAAE;oBAC3D,EAAE,OAAO,KAAK;wBACZ,QAAQ,IAAI,CAAC,qCAAqC;wBAClD,mBAAmB,EAAE;oBACvB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;qCAAG;QAAC,wBAAA,kCAAA,YAAa,cAAc;KAAC;IAEhC,kEAAkE;IAClE,IAAI,CAAC,eAAe,WAAW,CAAC,mBAAmB,gBAAgB,MAAM,KAAK,GAAG;QAC/E,qBAAO;IACT;IAEA,qBACE;kBACG,gBAAgB,GAAG,CAAC,CAAC,WAAW;gBAQf,wBACD;iCARf,6LAAC,sJAAA,CAAA,UAAe;gBAEd,OAAO,AAAC,6BAA8E,OAAlD,gBAAgB,MAAM,GAAG,IAAI,AAAC,IAAa,OAAV,QAAQ,KAAM;gBACnF,MAAK;gBACL,WAAW;gBACX,iBAAiB;gBACjB,kBAAkB;gBAClB,YAAY,EAAE,wBAAA,mCAAA,yBAAA,YAAa,SAAS,cAAtB,6CAAA,uBAAwB,KAAK;gBAC3C,WAAW,EAAE,wBAAA,mCAAA,0BAAA,YAAa,SAAS,cAAtB,8CAAA,wBAAwB,IAAI;gBACzC,iBAAiB;gBACjB,QAAQ;oBACN;wBACE,OAAO;wBACP,OAAO,UAAU,iBAAiB;wBAClC,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,UAAU,QAAQ;wBACzB,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,UAAU,cAAc;wBAC/B,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,UAAU,mBAAmB;wBACpC,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,UAAU,UAAU,GAAG,IAAI,KAAK,UAAU,UAAU,EAAE,kBAAkB,KAAK;wBACpF,MAAM;wBACN,MAAM;oBACR;uBACI,UAAU,WAAW,GAAG;wBAAC;4BAC3B,OAAO;4BACP,OAAO,UAAU,WAAW;4BAC5B,MAAM;4BACN,WAAW;wBACb;qBAAE,GAAG,EAAE;iBACR;eA1CI;;;;;;;AA+Cf;GAnFM;KAAA;uCAqFS", "debugId": null}}, {"offset": {"line": 5655, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/legalHistoryService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\n\r\nexport interface LegalHistoryData {\r\n  legal_history_id?: string;\r\n  application_id?: string;\r\n  criminal_history: boolean;\r\n  criminal_details?: string;\r\n  bankruptcy_history: boolean;\r\n  bankruptcy_details?: string;\r\n  regulatory_actions: boolean;\r\n  regulatory_details?: string;\r\n  litigation_history: boolean;\r\n  litigation_details?: string;\r\n  compliance_record?: string;\r\n  previous_licenses?: string;\r\n  declaration_accepted: boolean;\r\n  created_at?: string;\r\n  updated_at?: string;\r\n}\r\n\r\nexport interface CreateLegalHistoryData {\r\n  application_id: string;\r\n  criminal_history: boolean;\r\n  criminal_details?: string;\r\n  bankruptcy_history: boolean;\r\n  bankruptcy_details?: string;\r\n  regulatory_actions: boolean;\r\n  regulatory_details?: string;\r\n  litigation_history: boolean;\r\n  litigation_details?: string;\r\n  compliance_record?: string;\r\n  previous_licenses?: string;\r\n  declaration_accepted: boolean;\r\n}\r\n\r\nexport interface UpdateLegalHistoryData {\r\n  legal_history_id: string;\r\n  criminal_history?: boolean;\r\n  criminal_details?: string;\r\n  bankruptcy_history?: boolean;\r\n  bankruptcy_details?: string;\r\n  regulatory_actions?: boolean;\r\n  regulatory_details?: string;\r\n  litigation_history?: boolean;\r\n  litigation_details?: string;\r\n  compliance_record?: string;\r\n  previous_licenses?: string;\r\n  declaration_accepted?: boolean;\r\n}\r\n\r\nclass LegalHistoryService {\r\n  private baseUrl = '/legal-history';\r\n\r\n  // Create new legal history\r\n  async createLegalHistory(data: CreateLegalHistoryData): Promise<LegalHistoryData> {\r\n    try {\r\n      console.log('🔧 Creating legal history record:', data);\r\n      const response = await apiClient.post(this.baseUrl, data);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('❌ Error creating legal history:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Update legal history\r\n  async updateLegalHistory(data: UpdateLegalHistoryData): Promise<LegalHistoryData> {\r\n    try {\r\n      console.log('🔧 Updating legal history record:', data.legal_history_id);\r\n      const response = await apiClient.put(`${this.baseUrl}/${data.legal_history_id}`, data);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('❌ Error updating legal history:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Get legal history by application ID\r\n  async getLegalHistoryByApplication(applicationId: string): Promise<LegalHistoryData | null> {\r\n    try {\r\n      console.log('🔧 Getting legal history for application:', applicationId);\r\n      const response = await apiClient.get(`${this.baseUrl}/application/${applicationId}`);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      if (error.response?.status === 404) {\r\n        console.log('📝 No legal history found for application:', applicationId);\r\n        return null;\r\n      }\r\n      console.error('❌ Error getting legal history:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Get legal history by ID\r\n  async getLegalHistory(legalHistoryId: string): Promise<LegalHistoryData> {\r\n    try {\r\n      console.log('🔧 Getting legal history:', legalHistoryId);\r\n      const response = await apiClient.get(`${this.baseUrl}/${legalHistoryId}`);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('❌ Error getting legal history:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Delete legal history\r\n  async deleteLegalHistory(legalHistoryId: string): Promise<void> {\r\n    try {\r\n      console.log('🔧 Deleting legal history:', legalHistoryId);\r\n      const response = await apiClient.delete(`${this.baseUrl}/${legalHistoryId}`);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('❌ Error deleting legal history:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Get all legal history (admin only)\r\n  async getAllLegalHistory(): Promise<LegalHistoryData[]> {\r\n    try {\r\n      console.log('🔧 Getting all legal history');\r\n      const response = await apiClient.get(this.baseUrl);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('❌ Error getting all legal history:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Create or update legal history for an application\r\n  async createOrUpdateLegalHistory(applicationId: string, data: Omit<CreateLegalHistoryData, 'application_id'>): Promise<LegalHistoryData> {\r\n    try {\r\n      // Use the backend's combined create/update endpoint\r\n      const response = await apiClient.post(`${this.baseUrl}/application/${applicationId}`, data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('LegalHistoryService.createOrUpdateLegalHistory error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\nexport const legalHistoryService = new LegalHistoryService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAkDA,MAAM;IAGJ,2BAA2B;IAC3B,MAAM,mBAAmB,IAA4B,EAA6B;QAChF,IAAI;YACF,QAAQ,GAAG,CAAC,qCAAqC;YACjD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACpD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA,uBAAuB;IACvB,MAAM,mBAAmB,IAA4B,EAA6B;QAChF,IAAI;YACF,QAAQ,GAAG,CAAC,qCAAqC,KAAK,gBAAgB;YACtE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAyB,OAAtB,KAAK,gBAAgB,GAAI;YACjF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA,sCAAsC;IACtC,MAAM,6BAA6B,aAAqB,EAAoC;QAC1F,IAAI;YACF,QAAQ,GAAG,CAAC,6CAA6C;YACzD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAA8B,OAA5B,IAAI,CAAC,OAAO,EAAC,iBAA6B,OAAd;YACpE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;gBACf;YAAJ,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;gBAClC,QAAQ,GAAG,CAAC,8CAA8C;gBAC1D,OAAO;YACT;YACA,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,cAAsB,EAA6B;QACvE,IAAI;YACF,QAAQ,GAAG,CAAC,6BAA6B;YACzC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAkB,OAAf;YACxD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA,uBAAuB;IACvB,MAAM,mBAAmB,cAAsB,EAAiB;QAC9D,IAAI;YACF,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAkB,OAAf;YAC3D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA,qCAAqC;IACrC,MAAM,qBAAkD;QACtD,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO;YACjD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,oDAAoD;IACpD,MAAM,2BAA2B,aAAqB,EAAE,IAAoD,EAA6B;QACvI,IAAI;YACF,oDAAoD;YACpD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,AAAC,GAA8B,OAA5B,IAAI,CAAC,OAAO,EAAC,iBAA6B,OAAd,gBAAiB;YACtF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yDAAyD;YACvE,MAAM;QACR;IACF;;QAxFA,+KAAQ,WAAU;;AAyFpB;AAEO,MAAM,sBAAsB,IAAI", "debugId": null}}, {"offset": {"line": 5761, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/evaluation/LegalHistoryCard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport DataDisplayCard from './DataDisplayCard';\r\nimport { Application } from '@/types/license';\r\nimport { legalHistoryService } from '@/services/legalHistoryService';\r\n\r\ninterface LegalHistoryCardProps {\r\n  application: Application | null;\r\n  className?: string;\r\n  showEmptyFields?: boolean;\r\n  defaultCollapsed?: boolean;\r\n}\r\n\r\nconst LegalHistoryCard: React.FC<LegalHistoryCardProps> = ({\r\n  application,\r\n  className = '',\r\n  showEmptyFields = true,\r\n  defaultCollapsed = false\r\n}) => {\r\n  const [legalHistory, setLegalHistory] = useState<any[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const fetchLegalHistory = async () => {\r\n      if (!application?.application_id) return;\r\n\r\n      try {\r\n        setLoading(true);\r\n        const legalHistoryData = await legalHistoryService.getLegalHistoryByApplication(application.application_id);\r\n        setLegalHistory(legalHistoryData ? [legalHistoryData] : []);\r\n      } catch (err) {\r\n        console.warn('Could not load legal history:', err);\r\n        setLegalHistory([]);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchLegalHistory();\r\n  }, [application?.application_id]);\r\n\r\n  // Return empty fragment if no application or no legal history\r\n  if (!application || loading || !legalHistory || legalHistory.length == 0) {\r\n    return <></>;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {legalHistory.map((legalData, index) => (\r\n        <DataDisplayCard\r\n          key={index}\r\n          title={`Legal History Information ${legalHistory.length > 1 ? `#${index + 1}` : ''}`}\r\n          icon=\"ri-scales-line\"\r\n          className={className}\r\n          showEmptyFields={showEmptyFields}\r\n          defaultCollapsed={defaultCollapsed}\r\n          creatorEmail={application?.applicant?.email}\r\n          creatorName={application?.applicant?.name}\r\n          showEmailButton={true}\r\n          fields={[\r\n            {\r\n              label: 'Criminal History',\r\n              value: legalData.criminal_history,\r\n              type: 'boolean' as const,\r\n              icon: 'ri-police-car-line'\r\n            },\r\n            ...(legalData.criminal_history && legalData.criminal_details ? [{\r\n              label: 'Criminal History Details',\r\n              value: legalData.criminal_details,\r\n              icon: 'ri-information-line',\r\n              fullWidth: true\r\n            }] : []),\r\n            {\r\n              label: 'Bankruptcy History',\r\n              value: legalData.bankruptcy_history,\r\n              type: 'boolean' as const,\r\n              icon: 'ri-bank-line'\r\n            },\r\n            ...(legalData.bankruptcy_history && legalData.bankruptcy_details ? [{\r\n              label: 'Bankruptcy Details',\r\n              value: legalData.bankruptcy_details,\r\n              icon: 'ri-information-line',\r\n              fullWidth: true\r\n            }] : []),\r\n            {\r\n              label: 'Regulatory Actions',\r\n              value: legalData.regulatory_actions,\r\n              type: 'boolean' as const,\r\n              icon: 'ri-government-line'\r\n            },\r\n            ...(legalData.regulatory_actions && legalData.regulatory_details ? [{\r\n              label: 'Regulatory Action Details',\r\n              value: legalData.regulatory_details,\r\n              icon: 'ri-information-line',\r\n              fullWidth: true\r\n            }] : []),\r\n            {\r\n              label: 'Litigation History',\r\n              value: legalData.litigation_history,\r\n              type: 'boolean' as const,\r\n              icon: 'ri-scales-3-line'\r\n            },\r\n            ...(legalData.litigation_history && legalData.litigation_details ? [{\r\n              label: 'Litigation Details',\r\n              value: legalData.litigation_details,\r\n              icon: 'ri-information-line',\r\n              fullWidth: true\r\n            }] : []),\r\n            {\r\n              label: 'Created Date',\r\n              value: legalData.created_at ? new Date(legalData.created_at).toLocaleDateString() : null,\r\n              type: 'date' as const,\r\n              icon: 'ri-calendar-line'\r\n            }\r\n          ]}\r\n        />\r\n      ))}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default LegalHistoryCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;;AASA,MAAM,mBAAoD;QAAC,EACzD,WAAW,EACX,YAAY,EAAE,EACd,kBAAkB,IAAI,EACtB,mBAAmB,KAAK,EACzB;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;gEAAoB;oBACxB,IAAI,EAAC,wBAAA,kCAAA,YAAa,cAAc,GAAE;oBAElC,IAAI;wBACF,WAAW;wBACX,MAAM,mBAAmB,MAAM,yIAAA,CAAA,sBAAmB,CAAC,4BAA4B,CAAC,YAAY,cAAc;wBAC1G,gBAAgB,mBAAmB;4BAAC;yBAAiB,GAAG,EAAE;oBAC5D,EAAE,OAAO,KAAK;wBACZ,QAAQ,IAAI,CAAC,iCAAiC;wBAC9C,gBAAgB,EAAE;oBACpB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;qCAAG;QAAC,wBAAA,kCAAA,YAAa,cAAc;KAAC;IAEhC,8DAA8D;IAC9D,IAAI,CAAC,eAAe,WAAW,CAAC,gBAAgB,aAAa,MAAM,IAAI,GAAG;QACxE,qBAAO;IACT;IAEA,qBACE;kBACG,aAAa,GAAG,CAAC,CAAC,WAAW;gBAQZ,wBACD;iCARf,6LAAC,sJAAA,CAAA,UAAe;gBAEd,OAAO,AAAC,6BAA2E,OAA/C,aAAa,MAAM,GAAG,IAAI,AAAC,IAAa,OAAV,QAAQ,KAAM;gBAChF,MAAK;gBACL,WAAW;gBACX,iBAAiB;gBACjB,kBAAkB;gBAClB,YAAY,EAAE,wBAAA,mCAAA,yBAAA,YAAa,SAAS,cAAtB,6CAAA,uBAAwB,KAAK;gBAC3C,WAAW,EAAE,wBAAA,mCAAA,0BAAA,YAAa,SAAS,cAAtB,8CAAA,wBAAwB,IAAI;gBACzC,iBAAiB;gBACjB,QAAQ;oBACN;wBACE,OAAO;wBACP,OAAO,UAAU,gBAAgB;wBACjC,MAAM;wBACN,MAAM;oBACR;uBACI,UAAU,gBAAgB,IAAI,UAAU,gBAAgB,GAAG;wBAAC;4BAC9D,OAAO;4BACP,OAAO,UAAU,gBAAgB;4BACjC,MAAM;4BACN,WAAW;wBACb;qBAAE,GAAG,EAAE;oBACP;wBACE,OAAO;wBACP,OAAO,UAAU,kBAAkB;wBACnC,MAAM;wBACN,MAAM;oBACR;uBACI,UAAU,kBAAkB,IAAI,UAAU,kBAAkB,GAAG;wBAAC;4BAClE,OAAO;4BACP,OAAO,UAAU,kBAAkB;4BACnC,MAAM;4BACN,WAAW;wBACb;qBAAE,GAAG,EAAE;oBACP;wBACE,OAAO;wBACP,OAAO,UAAU,kBAAkB;wBACnC,MAAM;wBACN,MAAM;oBACR;uBACI,UAAU,kBAAkB,IAAI,UAAU,kBAAkB,GAAG;wBAAC;4BAClE,OAAO;4BACP,OAAO,UAAU,kBAAkB;4BACnC,MAAM;4BACN,WAAW;wBACb;qBAAE,GAAG,EAAE;oBACP;wBACE,OAAO;wBACP,OAAO,UAAU,kBAAkB;wBACnC,MAAM;wBACN,MAAM;oBACR;uBACI,UAAU,kBAAkB,IAAI,UAAU,kBAAkB,GAAG;wBAAC;4BAClE,OAAO;4BACP,OAAO,UAAU,kBAAkB;4BACnC,MAAM;4BACN,WAAW;wBACb;qBAAE,GAAG,EAAE;oBACP;wBACE,OAAO;wBACP,OAAO,UAAU,UAAU,GAAG,IAAI,KAAK,UAAU,UAAU,EAAE,kBAAkB,KAAK;wBACpF,MAAM;wBACN,MAAM;oBACR;iBACD;eAhEI;;;;;;;AAqEf;GA1GM;KAAA;uCA4GS", "debugId": null}}, {"offset": {"line": 5904, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/stakeholderService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\n\r\nexport interface StakeholderData {\r\n  stakeholder_id?: string;\r\n  application_id?: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  middle_name?: string;\r\n  nationality: string;\r\n  position: string;\r\n  profile: string;\r\n  contact_id?: string;\r\n  cv_document_id?: string;\r\n  created_at?: string;\r\n  updated_at?: string;\r\n}\r\n\r\nexport interface CreateStakeholderData {\r\n  application_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  middle_name?: string;\r\n  nationality: string;\r\n  position: string;\r\n  profile: string;\r\n  contact_id?: string;\r\n  cv_document_id?: string;\r\n}\r\n\r\nexport interface UpdateStakeholderData {\r\n  stakeholder_id: string;\r\n  first_name?: string;\r\n  last_name?: string;\r\n  middle_name?: string;\r\n  nationality?: string;\r\n  position?: string;\r\n  profile?: string;\r\n  contact_id?: string;\r\n  cv_document_id?: string;\r\n}\r\n\r\nconst processApiResponse = (response: any) => {\r\n  if (response.data?.path) {\r\n    return response.data;\r\n  }\r\n\r\n  if (response.data?.data) {\r\n    return response.data.data;\r\n  }\r\n  return response.data;\r\n};\r\n\r\nexport const stakeholderService = {\r\n  // Create new stakeholder\r\n  async createStakeholder(data: CreateStakeholderData): Promise<StakeholderData> {\r\n    try {\r\n      console.log('Creating stakeholder with data:', data);\r\n      const response = await apiClient.post('/stakeholders', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('StakeholderService.createStakeholder error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get stakeholder by ID\r\n  async getStakeholder(id: string): Promise<StakeholderData> {\r\n    try {\r\n      const response = await apiClient.get(`/stakeholders/${id}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('StakeholderService.getStakeholder error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get stakeholders by applicant ID\r\n  async getStakeholdersByApplication(applicationId: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/stakeholders/application/${applicationId}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      return []; // Return empty array instead of throwing\r\n    }\r\n  },\r\n\r\n  // Update stakeholder\r\n  async updateStakeholder(id: string, data: Partial<UpdateStakeholderData>): Promise<StakeholderData> {\r\n    try {\r\n      const response = await apiClient.put(`/stakeholders/${id}`, data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete stakeholder\r\n  async deleteStakeholder(id: string): Promise<void> {\r\n    try {\r\n      await apiClient.delete(`/stakeholders/${id}`);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get all stakeholders with pagination\r\n  async getStakeholders(params?: { page?: number; limit?: number }): Promise<{ data: StakeholderData[]; meta: any }> {\r\n    try {\r\n      const response = await apiClient.get('/stakeholders', { params });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Bulk create stakeholders for an applicant\r\n  async createStakeholdersForApplicant(applicantId: string, stakeholders: Omit<CreateStakeholderData, 'applicant_id'>[]): Promise<StakeholderData[]> {\r\n    try {\r\n      const stakeholdersWithApplicantId = stakeholders.map(stakeholder => ({\r\n        ...stakeholder,\r\n        applicant_id: applicantId\r\n      }));\r\n\r\n      const createdStakeholders: StakeholderData[] = [];\r\n      \r\n      // Create stakeholders one by one to handle errors individually\r\n      for (const stakeholderData of stakeholdersWithApplicantId) {\r\n        try {\r\n          const created = await this.createStakeholder(stakeholderData);\r\n          createdStakeholders.push(created);\r\n        } catch (error) {\r\n          console.error('Error creating stakeholder:', stakeholderData, error);\r\n          // Continue with other stakeholders even if one fails\r\n        }\r\n      }\r\n\r\n      return createdStakeholders;\r\n    } catch (error) {\r\n      console.error('StakeholderService.createStakeholdersForApplicant error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update multiple stakeholders for an applicant\r\n  async updateStakeholdersForApplicant(applicantId: string, stakeholders: StakeholderData[]): Promise<StakeholderData[]> {\r\n    try {\r\n      const updatedStakeholders: StakeholderData[] = [];\r\n\r\n      for (const stakeholder of stakeholders) {\r\n        try {\r\n          if (stakeholder.stakeholder_id) {\r\n            // Update existing stakeholder\r\n            const updated = await this.updateStakeholder(stakeholder.stakeholder_id, stakeholder);\r\n            updatedStakeholders.push(updated);\r\n          } else {\r\n            // Create new stakeholder\r\n            const created = await this.createStakeholder({\r\n              ...stakeholder,\r\n              application_id: applicantId\r\n            });\r\n            updatedStakeholders.push(created);\r\n          }\r\n        } catch (error) {\r\n          console.error('Error updating/creating stakeholder:', stakeholder, error);\r\n          // Continue with other stakeholders even if one fails\r\n        }\r\n      }\r\n\r\n      return updatedStakeholders;\r\n    } catch (error) {\r\n      console.error('StakeholderService.updateStakeholdersForApplicant error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAyCA,MAAM,qBAAqB,CAAC;QACtB,gBAIA;IAJJ,KAAI,iBAAA,SAAS,IAAI,cAAb,qCAAA,eAAe,IAAI,EAAE;QACvB,OAAO,SAAS,IAAI;IACtB;IAEA,KAAI,kBAAA,SAAS,IAAI,cAAb,sCAAA,gBAAe,IAAI,EAAE;QACvB,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B;IACA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,qBAAqB;IAChC,yBAAyB;IACzB,MAAM,mBAAkB,IAA2B;QACjD,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;YAC/C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,OAAO,mBAAmB;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,MAAM;QACR;IACF;IAEA,wBAAwB;IACxB,MAAM,gBAAe,EAAU;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAAmB,OAAH;YACtD,OAAO,mBAAmB;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,MAAM;QACR;IACF;IAEA,mCAAmC;IACnC,MAAM,8BAA6B,aAAqB;QACtD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,6BAA0C,OAAd;YAClE,OAAO,mBAAmB;QAC5B,EAAE,OAAO,OAAO;YACd,OAAO,EAAE,EAAE,yCAAyC;QACtD;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU,EAAE,IAAoC;QACtE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAAmB,OAAH,KAAM;YAC5D,OAAO,mBAAmB;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,IAAI;YACF,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,iBAAmB,OAAH;QAC1C,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,uCAAuC;IACvC,MAAM,iBAAgB,MAA0C;QAC9D,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,iBAAiB;gBAAE;YAAO;YAC/D,OAAO,mBAAmB;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,4CAA4C;IAC5C,MAAM,gCAA+B,WAAmB,EAAE,YAA2D;QACnH,IAAI;YACF,MAAM,8BAA8B,aAAa,GAAG,CAAC,CAAA,cAAe,CAAC;oBACnE,GAAG,WAAW;oBACd,cAAc;gBAChB,CAAC;YAED,MAAM,sBAAyC,EAAE;YAEjD,+DAA+D;YAC/D,KAAK,MAAM,mBAAmB,4BAA6B;gBACzD,IAAI;oBACF,MAAM,UAAU,MAAM,IAAI,CAAC,iBAAiB,CAAC;oBAC7C,oBAAoB,IAAI,CAAC;gBAC3B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,+BAA+B,iBAAiB;gBAC9D,qDAAqD;gBACvD;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4DAA4D;YAC1E,MAAM;QACR;IACF;IAEA,gDAAgD;IAChD,MAAM,gCAA+B,WAAmB,EAAE,YAA+B;QACvF,IAAI;YACF,MAAM,sBAAyC,EAAE;YAEjD,KAAK,MAAM,eAAe,aAAc;gBACtC,IAAI;oBACF,IAAI,YAAY,cAAc,EAAE;wBAC9B,8BAA8B;wBAC9B,MAAM,UAAU,MAAM,IAAI,CAAC,iBAAiB,CAAC,YAAY,cAAc,EAAE;wBACzE,oBAAoB,IAAI,CAAC;oBAC3B,OAAO;wBACL,yBAAyB;wBACzB,MAAM,UAAU,MAAM,IAAI,CAAC,iBAAiB,CAAC;4BAC3C,GAAG,WAAW;4BACd,gBAAgB;wBAClB;wBACA,oBAAoB,IAAI,CAAC;oBAC3B;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,wCAAwC,aAAa;gBACnE,qDAAqD;gBACvD;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4DAA4D;YAC1E,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 6041, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/evaluation/ManagementCard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport DataDisplayCard from './DataDisplayCard';\r\nimport { Application } from '@/types/license';\r\nimport { stakeholderService } from '@/services/stakeholderService';\r\n\r\ninterface ManagementCardProps {\r\n  application: Application | null;\r\n  className?: string;\r\n  showEmptyFields?: boolean;\r\n  defaultCollapsed?: boolean;\r\n}\r\n\r\nconst ManagementCard: React.FC<ManagementCardProps> = ({\r\n  application,\r\n  className = '',\r\n  showEmptyFields = true,\r\n  defaultCollapsed = false\r\n}) => {\r\n  const [stakeholders, setStakeholders] = useState<any[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const fetchStakeholders = async () => {\r\n      if (!application?.application_id) return;\r\n\r\n      try {\r\n        setLoading(true);\r\n        const stakeholderResponse = await stakeholderService.getStakeholdersByApplication(application.application_id);\r\n        const stakeholderData = stakeholderResponse?.data || stakeholderResponse || [];\r\n        setStakeholders(stakeholderData);\r\n      } catch (err) {\r\n        console.warn('Could not load stakeholders:', err);\r\n        setStakeholders([]);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchStakeholders();\r\n  }, [application?.application_id]);\r\n\r\n  // Return empty fragment if no application or no stakeholders\r\n  if (!application || loading || !stakeholders || stakeholders.length === 0) {\r\n    return <></>;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {stakeholders.map((member, index) => (\r\n        <DataDisplayCard\r\n          key={index}\r\n          title={`${member.stakeholder_type || 'Management'} - ${member.full_name || 'Unnamed Member'}`}\r\n          icon=\"ri-user-star-line\"\r\n          className={className}\r\n          showEmptyFields={showEmptyFields}\r\n          defaultCollapsed={defaultCollapsed}\r\n          creatorEmail={member.email}\r\n          creatorName={member.full_name}\r\n          showEmailButton={true}\r\n          fields={[\r\n            {\r\n              label: 'Full Name',\r\n              value: member.full_name,\r\n              icon: 'ri-user-3-line'\r\n            },\r\n            {\r\n              label: 'Position/Title',\r\n              value: member.position,\r\n              icon: 'ri-briefcase-line'\r\n            },\r\n            {\r\n              label: 'Stakeholder Type',\r\n              value: member.stakeholder_type,\r\n              icon: 'ri-vip-crown-line'\r\n            },\r\n            {\r\n              label: 'Email Address',\r\n              value: member.email,\r\n              type: 'email' as const,\r\n              icon: 'ri-mail-line'\r\n            },\r\n            {\r\n              label: 'Phone Number',\r\n              value: member.phone,\r\n              type: 'phone' as const,\r\n              icon: 'ri-phone-line'\r\n            },\r\n            {\r\n              label: 'Ownership Percentage',\r\n              value: member.ownership_percentage ? `${member.ownership_percentage}%` : null,\r\n              icon: 'ri-pie-chart-line'\r\n            },\r\n            {\r\n              label: 'Date of Birth',\r\n              value: member.date_of_birth,\r\n              type: 'date' as const,\r\n              icon: 'ri-calendar-line'\r\n            },\r\n            {\r\n              label: 'Nationality',\r\n              value: member.nationality,\r\n              icon: 'ri-earth-line'\r\n            },\r\n            {\r\n              label: 'Qualifications',\r\n              value: member.qualifications,\r\n              icon: 'ri-medal-line',\r\n              fullWidth: true\r\n            },\r\n            {\r\n              label: 'Experience',\r\n              value: member.experience,\r\n              icon: 'ri-time-line',\r\n              fullWidth: true\r\n            },\r\n            ...(member.notes ? [{\r\n              label: 'Additional Notes',\r\n              value: member.notes,\r\n              icon: 'ri-sticky-note-line',\r\n              fullWidth: true\r\n            }] : [])\r\n          ]}\r\n        />\r\n      ))}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ManagementCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;;AASA,MAAM,iBAAgD;QAAC,EACrD,WAAW,EACX,YAAY,EAAE,EACd,kBAAkB,IAAI,EACtB,mBAAmB,KAAK,EACzB;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;8DAAoB;oBACxB,IAAI,EAAC,wBAAA,kCAAA,YAAa,cAAc,GAAE;oBAElC,IAAI;wBACF,WAAW;wBACX,MAAM,sBAAsB,MAAM,wIAAA,CAAA,qBAAkB,CAAC,4BAA4B,CAAC,YAAY,cAAc;wBAC5G,MAAM,kBAAkB,CAAA,gCAAA,0CAAA,oBAAqB,IAAI,KAAI,uBAAuB,EAAE;wBAC9E,gBAAgB;oBAClB,EAAE,OAAO,KAAK;wBACZ,QAAQ,IAAI,CAAC,gCAAgC;wBAC7C,gBAAgB,EAAE;oBACpB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;mCAAG;QAAC,wBAAA,kCAAA,YAAa,cAAc;KAAC;IAEhC,6DAA6D;IAC7D,IAAI,CAAC,eAAe,WAAW,CAAC,gBAAgB,aAAa,MAAM,KAAK,GAAG;QACzE,qBAAO;IACT;IAEA,qBACE;kBACG,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,6LAAC,sJAAA,CAAA,UAAe;gBAEd,OAAO,AAAC,GAA+C,OAA7C,OAAO,gBAAgB,IAAI,cAAa,OAA0C,OAArC,OAAO,SAAS,IAAI;gBAC3E,MAAK;gBACL,WAAW;gBACX,iBAAiB;gBACjB,kBAAkB;gBAClB,cAAc,OAAO,KAAK;gBAC1B,aAAa,OAAO,SAAS;gBAC7B,iBAAiB;gBACjB,QAAQ;oBACN;wBACE,OAAO;wBACP,OAAO,OAAO,SAAS;wBACvB,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,QAAQ;wBACtB,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,gBAAgB;wBAC9B,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,KAAK;wBACnB,MAAM;wBACN,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,KAAK;wBACnB,MAAM;wBACN,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,oBAAoB,GAAG,AAAC,GAA8B,OAA5B,OAAO,oBAAoB,EAAC,OAAK;wBACzE,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,aAAa;wBAC3B,MAAM;wBACN,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,WAAW;wBACzB,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,cAAc;wBAC5B,MAAM;wBACN,WAAW;oBACb;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,UAAU;wBACxB,MAAM;wBACN,WAAW;oBACb;uBACI,OAAO,KAAK,GAAG;wBAAC;4BAClB,OAAO;4BACP,OAAO,OAAO,KAAK;4BACnB,MAAM;4BACN,WAAW;wBACb;qBAAE,GAAG,EAAE;iBACR;eAvEI;;;;;;AA4Ef;GAlHM;KAAA;uCAoHS", "debugId": null}}, {"offset": {"line": 6181, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationDeviceService.ts"], "sourcesContent": ["import { apiClient, processApiResponse } from '@/lib';\r\nimport { PaginatedResponse } from '@/types';\r\n\r\nexport interface ApplicationDevice {\r\n  device_id: string;\r\n  application_id: string;\r\n\r\n  // Manufacturer Information (embedded)\r\n  manufacturer_name: string;\r\n  manufacturer_address?: string;\r\n  manufacturer_country: string;\r\n\r\n  // Equipment Information (embedded)\r\n  brand_trade_name?: string;\r\n  product_type_name?: string;\r\n\r\n  equipment_category_id?: string;\r\n  equipment_model?: string;\r\n  imei: string;\r\n  device_type: string;\r\n  model_name: string;\r\n  device_serial_number: string;\r\n  approval_status: string;\r\n  device_approval_number?: string;\r\n  device_approval_date?: string;\r\n  approval_notes?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n\r\n  equipment_category?: {\r\n    category_id: string;\r\n    name: string;\r\n    description: string;\r\n  };\r\n}\r\n\r\nexport interface CreateApplicationDeviceDto {\r\n  application_id: string;\r\n\r\n  // Manufacturer Information (embedded)\r\n  manufacturer_name: string;\r\n  manufacturer_address?: string;\r\n  manufacturer_country: string;\r\n\r\n  // Equipment Information (embedded)\r\n  brand_trade_name?: string;\r\n  product_type_name?: string;\r\n\r\n  equipment_category_id?: string;\r\n  imei?: string;\r\n  approval_status?: string;\r\n  device_approval_number?: string;\r\n  device_approval_date?: string;\r\n  approval_notes?: string;\r\n}\r\n\r\nexport interface UpdateApplicationDeviceDto {\r\n  // Manufacturer Information (embedded)\r\n  manufacturer_name?: string;\r\n  manufacturer_address?: string;\r\n  manufacturer_country?: string;\r\n\r\n  // Equipment Information (embedded)\r\n  brand_trade_name?: string;\r\n  product_type_name?: string;\r\n\r\n  equipment_category_id?: string;\r\n  imei?: string;\r\n  device_type?: string;\r\n  model_name?: string;\r\n  device_serial_number?: string;\r\n  approval_status?: string;\r\n  device_approval_number?: string;\r\n  device_approval_date?: string;\r\n  approval_notes?: string;\r\n}\r\n\r\nclass ApplicationDeviceService {\r\n  private baseUrl = '/devices';\r\n\r\n  /**\r\n   * Clean device payload by converting empty strings to undefined for optional fields\r\n   */\r\n  private cleanDevicePayload(payload: any): any {\r\n    const cleaned = { ...payload };\r\n\r\n    // Convert empty strings to undefined for optional fields\r\n    const optionalFields = [\r\n      'manufacturer_address',\r\n      'brand_trade_name',\r\n      'product_type_name',\r\n      'equipment_category',\r\n      'approval_notes',\r\n      'device_approval_number',\r\n      'device_approval_date'\r\n    ];\r\n\r\n    optionalFields.forEach(field => {\r\n      if (cleaned[field] === '') {\r\n        cleaned[field] = undefined;\r\n      }\r\n    });\r\n\r\n    return cleaned;\r\n  }\r\n\r\n  /**\r\n   * Get devices for a specific application\r\n   */\r\n  async getDevicesByApplication(applicationId: string): Promise<PaginatedResponse<any>> {\r\n    try {\r\n      // Use the new backend endpoint that queries by application_id directly\r\n      const response = await apiClient.get(`${this.baseUrl}/application/${applicationId}`);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('❌ Error fetching application devices:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get a specific device by ID\r\n   */\r\n  async getDevice(deviceId: string): Promise<ApplicationDevice> {\r\n    try {\r\n      console.log('🔍 Fetching device:', deviceId);\r\n      \r\n      const response = await apiClient.get(`${this.baseUrl}/${deviceId}`);\r\n      const device = processApiResponse(response);\r\n      \r\n      console.log('✅ Found device:', device);\r\n      return device;\r\n    } catch (error: any) {\r\n      console.error('❌ Error fetching device:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create a new device for an application\r\n   */\r\n  async createDevice(deviceData: CreateApplicationDeviceDto): Promise<ApplicationDevice> {\r\n    try {\r\n      console.log('🔍 Creating device for application:', deviceData.application_id);\r\n      console.log('📝 Device data:', deviceData);\r\n      \r\n      const cleanedData = this.cleanDevicePayload({\r\n        ...deviceData,\r\n        approval_status: deviceData.approval_status || 'pending'\r\n      });\r\n\r\n      const response = await apiClient.post(this.baseUrl, cleanedData);\r\n      \r\n      const createdDevice = processApiResponse(response);\r\n      console.log('✅ Device created:', createdDevice);\r\n      \r\n      return createdDevice;\r\n    } catch (error: any) {\r\n      console.error('❌ Error creating device:', error);\r\n      \r\n      // Handle specific error cases\r\n      if (error.response?.status === 409) {\r\n        const message = error.response?.data?.message || 'Device with this IMEI or serial number already exists';\r\n        throw new Error(message);\r\n      }\r\n      \r\n      if (error.response?.status === 400) {\r\n        const message = error.response?.data?.message || 'Invalid device data provided';\r\n        throw new Error(message);\r\n      }\r\n      \r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update an existing device\r\n   */\r\n  async updateDevice(deviceId: string, deviceData: UpdateApplicationDeviceDto): Promise<ApplicationDevice> {\r\n    try {\r\n      console.log('🔍 Updating device:', deviceId);\r\n      console.log('📝 Update data:', deviceData);\r\n      \r\n      const cleanedData = this.cleanDevicePayload(deviceData);\r\n      const response = await apiClient.put(`${this.baseUrl}/${deviceId}`, cleanedData);\r\n      const updatedDevice = processApiResponse(response);\r\n      \r\n      console.log('✅ Device updated:', updatedDevice);\r\n      return updatedDevice;\r\n    } catch (error: any) {\r\n      console.error('❌ Error updating device:', error);\r\n      \r\n      // Handle specific error cases\r\n      if (error.response?.status === 409) {\r\n        const message = error.response?.data?.message || 'Device with this IMEI or serial number already exists';\r\n        throw new Error(message);\r\n      }\r\n      \r\n      if (error.response?.status === 404) {\r\n        throw new Error('Device not found');\r\n      }\r\n      \r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete a device\r\n   */\r\n  async deleteDevice(deviceId: string): Promise<void> {\r\n    try {\r\n      console.log('🔍 Deleting device:', deviceId);\r\n      \r\n      await apiClient.delete(`${this.baseUrl}/${deviceId}`);\r\n      console.log('✅ Device deleted successfully');\r\n    } catch (error: any) {\r\n      console.error('❌ Error deleting device:', error);\r\n      \r\n      if (error.response?.status === 404) {\r\n        throw new Error('Device not found');\r\n      }\r\n      \r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get device by IMEI\r\n   */\r\n  async getDeviceByImei(imei: string): Promise<ApplicationDevice | null> {\r\n    try {\r\n      console.log('🔍 Fetching device by IMEI:', imei);\r\n      \r\n      const response = await apiClient.get(`${this.baseUrl}/imei/${imei}`);\r\n      const device = processApiResponse(response);\r\n      \r\n      console.log('✅ Found device by IMEI:', device);\r\n      return device;\r\n    } catch (error: any) {\r\n      console.error('❌ Error fetching device by IMEI:', error);\r\n      \r\n      // Return null if device not found\r\n      if (error.response?.status === 404) {\r\n        return null;\r\n      }\r\n      \r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate IMEI and get device information if exists\r\n   */\r\n  async validateImei(imei: string): Promise<{\r\n    isValid: boolean;\r\n    exists: boolean;\r\n    device?: ApplicationDevice;\r\n    message: string;\r\n  }> {\r\n    try {\r\n      console.log('🔍 Validating IMEI:', imei);\r\n      \r\n      // Clean IMEI (remove any non-digit characters)\r\n      const cleanImei = imei.replace(/\\D/g, '');\r\n      \r\n      if (cleanImei.length !== 15) {\r\n        return {\r\n          isValid: false,\r\n          exists: false,\r\n          message: 'IMEI must be exactly 15 digits'\r\n        };\r\n      }\r\n      \r\n      // Try to find device by IMEI\r\n      const device = await this.getDeviceByImei(cleanImei);\r\n      \r\n      if (device) {\r\n        return {\r\n          isValid: true,\r\n          exists: true,\r\n          device,\r\n          message: 'IMEI found in database'\r\n        };\r\n      } else {\r\n        return {\r\n          isValid: true,\r\n          exists: false,\r\n          message: 'IMEI is valid but not found in database'\r\n        };\r\n      }\r\n    } catch (error: any) {\r\n      console.error('❌ Error validating IMEI:', error);\r\n      \r\n      return {\r\n        isValid: false,\r\n        exists: false,\r\n        message: 'Error validating IMEI'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nexport const applicationDeviceService = new ApplicationDeviceService();\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;;;AA6EA,MAAM;IAGJ;;GAEC,GACD,AAAQ,mBAAmB,OAAY,EAAO;QAC5C,MAAM,UAAU;YAAE,GAAG,OAAO;QAAC;QAE7B,yDAAyD;QACzD,MAAM,iBAAiB;YACrB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,eAAe,OAAO,CAAC,CAAA;YACrB,IAAI,OAAO,CAAC,MAAM,KAAK,IAAI;gBACzB,OAAO,CAAC,MAAM,GAAG;YACnB;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,wBAAwB,aAAqB,EAAmC;QACpF,IAAI;YACF,uEAAuE;YACvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAA8B,OAA5B,IAAI,CAAC,OAAO,EAAC,iBAA6B,OAAd;YACpE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,UAAU,QAAgB,EAA8B;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAY,OAAT;YACxD,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAElC,QAAQ,GAAG,CAAC,mBAAmB;YAC/B,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,UAAsC,EAA8B;QACrF,IAAI;YACF,QAAQ,GAAG,CAAC,uCAAuC,WAAW,cAAc;YAC5E,QAAQ,GAAG,CAAC,mBAAmB;YAE/B,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC;gBAC1C,GAAG,UAAU;gBACb,iBAAiB,WAAW,eAAe,IAAI;YACjD;YAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YAEpD,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YACzC,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,OAAO;QACT,EAAE,OAAO,OAAY;gBAIf,iBAKA;YARJ,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,8BAA8B;YAC9B,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;oBAClB,sBAAA;gBAAhB,MAAM,UAAU,EAAA,mBAAA,MAAM,QAAQ,cAAd,wCAAA,uBAAA,iBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI;gBACjD,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,MAAK,KAAK;oBAClB,uBAAA;gBAAhB,MAAM,UAAU,EAAA,mBAAA,MAAM,QAAQ,cAAd,wCAAA,wBAAA,iBAAgB,IAAI,cAApB,4CAAA,sBAAsB,OAAO,KAAI;gBACjD,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,QAAgB,EAAE,UAAsC,EAA8B;QACvG,IAAI;YACF,QAAQ,GAAG,CAAC,uBAAuB;YACnC,QAAQ,GAAG,CAAC,mBAAmB;YAE/B,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC;YAC5C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAY,OAAT,WAAY;YACpE,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAEzC,QAAQ,GAAG,CAAC,qBAAqB;YACjC,OAAO;QACT,EAAE,OAAO,OAAY;gBAIf,iBAKA;YARJ,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,8BAA8B;YAC9B,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;oBAClB,sBAAA;gBAAhB,MAAM,UAAU,EAAA,mBAAA,MAAM,QAAQ,cAAd,wCAAA,uBAAA,iBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI;gBACjD,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,MAAK,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,QAAgB,EAAiB;QAClD,IAAI;YACF,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAY,OAAT;YAC1C,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAY;gBAGf;YAFJ,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,IAAY,EAAqC;QACrE,IAAI;YACF,QAAQ,GAAG,CAAC,+BAA+B;YAE3C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAuB,OAArB,IAAI,CAAC,OAAO,EAAC,UAAa,OAAL;YAC7D,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAElC,QAAQ,GAAG,CAAC,2BAA2B;YACvC,OAAO;QACT,EAAE,OAAO,OAAY;gBAIf;YAHJ,QAAQ,KAAK,CAAC,oCAAoC;YAElD,kCAAkC;YAClC,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;gBAClC,OAAO;YACT;YAEA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,IAAY,EAK5B;QACD,IAAI;YACF,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,+CAA+C;YAC/C,MAAM,YAAY,KAAK,OAAO,CAAC,OAAO;YAEtC,IAAI,UAAU,MAAM,KAAK,IAAI;gBAC3B,OAAO;oBACL,SAAS;oBACT,QAAQ;oBACR,SAAS;gBACX;YACF;YAEA,6BAA6B;YAC7B,MAAM,SAAS,MAAM,IAAI,CAAC,eAAe,CAAC;YAE1C,IAAI,QAAQ;gBACV,OAAO;oBACL,SAAS;oBACT,QAAQ;oBACR;oBACA,SAAS;gBACX;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,QAAQ;oBACR,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,SAAS;YACX;QACF;IACF;;QA7NA,+KAAQ,WAAU;;AA8NpB;AAEO,MAAM,2BAA2B,IAAI", "debugId": null}}, {"offset": {"line": 6385, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/evaluation/EquipmentDetailsCard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport DataDisplayCard from './DataDisplayCard';\r\nimport { Application } from '@/types/license';\r\nimport { ApplicationDevice, applicationDeviceService } from '@/services/applicationDeviceService';\r\n\r\ninterface EquipmentDetailsCardProps {\r\n  application: Application | null;\r\n  className?: string;\r\n  showEmptyFields?: boolean;\r\n  defaultCollapsed?: boolean;\r\n}\r\n\r\nconst EquipmentDetailsCard: React.FC<EquipmentDetailsCardProps> = ({\r\n  application,\r\n  className = '',\r\n  showEmptyFields = true,\r\n  defaultCollapsed = false\r\n}) => {\r\n  const [devices, setDevices] = useState<ApplicationDevice[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const fetchDevices = async () => {\r\n      if (!application?.application_id) return;\r\n\r\n      try {\r\n        setLoading(true);\r\n        const response = await applicationDeviceService.getDevicesByApplication(application.application_id);\r\n        const deviceData = response.data || [];\r\n        setDevices(deviceData);\r\n      } catch (err) {\r\n        console.warn('Could not load devices:', err);\r\n        setDevices([]);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchDevices();\r\n  }, [application?.application_id]);\r\n\r\n  // Return empty fragment if no application or no devices\r\n  if (!application || loading || !devices || devices.length === 0) {\r\n    return <></>;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {devices.map((device, index) => (\r\n        <DataDisplayCard\r\n          key={device.device_id || index}\r\n          title={`Equipment Details ${devices.length > 1 ? `#${index + 1}` : ''}`}\r\n          icon=\"ri-smartphone-line\"\r\n          className={className}\r\n          showEmptyFields={showEmptyFields}\r\n          defaultCollapsed={defaultCollapsed}\r\n          creatorEmail={application?.applicant?.email}\r\n          creatorName={application?.applicant?.name}\r\n          showEmailButton={true}\r\n          fields={[\r\n            {\r\n              label: 'IMEI',\r\n              value: device.imei,\r\n              icon: 'ri-barcode-line'\r\n            },\r\n            {\r\n              label: 'Device Type',\r\n              value: device.device_type,\r\n              icon: 'ri-device-line'\r\n            },\r\n            {\r\n              label: 'Model Name',\r\n              value: device.equipment_model || device.model_name,\r\n              icon: 'ri-smartphone-line'\r\n            },\r\n            {\r\n              label: 'Device Serial Number',\r\n              value: device.device_serial_number,\r\n              icon: 'ri-hashtag'\r\n            },\r\n            {\r\n              label: 'Manufacturer Name',\r\n              value: device.manufacturer_name,\r\n              icon: 'ri-building-line'\r\n            },\r\n            {\r\n              label: 'Manufacturer Country',\r\n              value: device.manufacturer_country,\r\n              icon: 'ri-earth-line'\r\n            },\r\n            {\r\n              label: 'Manufacturer Address',\r\n              value: device.manufacturer_address,\r\n              icon: 'ri-map-pin-line',\r\n              fullWidth: true\r\n            },\r\n            {\r\n              label: 'Brand/Trade Name',\r\n              value: device.brand_trade_name,\r\n              icon: 'ri-price-tag-line'\r\n            },\r\n            {\r\n              label: 'Product Type',\r\n              value: device.product_type_name,\r\n              icon: 'ri-product-hunt-line'\r\n            },\r\n            {\r\n              label: 'Equipment Category',\r\n              value: device.equipment_category?.name || 'N/A',\r\n              icon: 'ri-folder-line'\r\n            },\r\n            {\r\n              label: 'Approval Status',\r\n              value: device.approval_status,\r\n              icon: 'ri-checkbox-circle-line'\r\n            },\r\n            {\r\n              label: 'Device Approval Number',\r\n              value: device.device_approval_number,\r\n              icon: 'ri-file-list-line'\r\n            },\r\n            {\r\n              label: 'Device Approval Date',\r\n              value: device.device_approval_date ? new Date(device.device_approval_date).toLocaleDateString() : null,\r\n              type: 'date' as const,\r\n              icon: 'ri-calendar-check-line'\r\n            },\r\n            {\r\n              label: 'Approval Notes',\r\n              value: device.approval_notes,\r\n              icon: 'ri-sticky-note-line',\r\n              fullWidth: true\r\n            },\r\n            {\r\n              label: 'Created Date',\r\n              value: device.created_at ? new Date(device.created_at).toLocaleDateString() : null,\r\n              type: 'date' as const,\r\n              icon: 'ri-calendar-line'\r\n            }\r\n          ]}\r\n        />\r\n      ))}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default EquipmentDetailsCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;;AASA,MAAM,uBAA4D;QAAC,EACjE,WAAW,EACX,YAAY,EAAE,EACd,kBAAkB,IAAI,EACtB,mBAAmB,KAAK,EACzB;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;+DAAe;oBACnB,IAAI,EAAC,wBAAA,kCAAA,YAAa,cAAc,GAAE;oBAElC,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,8IAAA,CAAA,2BAAwB,CAAC,uBAAuB,CAAC,YAAY,cAAc;wBAClG,MAAM,aAAa,SAAS,IAAI,IAAI,EAAE;wBACtC,WAAW;oBACb,EAAE,OAAO,KAAK;wBACZ,QAAQ,IAAI,CAAC,2BAA2B;wBACxC,WAAW,EAAE;oBACf,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;yCAAG;QAAC,wBAAA,kCAAA,YAAa,cAAc;KAAC;IAEhC,wDAAwD;IACxD,IAAI,CAAC,eAAe,WAAW,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QAC/D,qBAAO;IACT;IAEA,qBACE;kBACG,QAAQ,GAAG,CAAC,CAAC,QAAQ;gBAQJ,wBACD,yBAmDF;iCA3Db,6LAAC,sJAAA,CAAA,UAAe;gBAEd,OAAO,AAAC,qBAA8D,OAA1C,QAAQ,MAAM,GAAG,IAAI,AAAC,IAAa,OAAV,QAAQ,KAAM;gBACnE,MAAK;gBACL,WAAW;gBACX,iBAAiB;gBACjB,kBAAkB;gBAClB,YAAY,EAAE,wBAAA,mCAAA,yBAAA,YAAa,SAAS,cAAtB,6CAAA,uBAAwB,KAAK;gBAC3C,WAAW,EAAE,wBAAA,mCAAA,0BAAA,YAAa,SAAS,cAAtB,8CAAA,wBAAwB,IAAI;gBACzC,iBAAiB;gBACjB,QAAQ;oBACN;wBACE,OAAO;wBACP,OAAO,OAAO,IAAI;wBAClB,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,WAAW;wBACzB,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,eAAe,IAAI,OAAO,UAAU;wBAClD,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,oBAAoB;wBAClC,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,iBAAiB;wBAC/B,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,oBAAoB;wBAClC,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,oBAAoB;wBAClC,MAAM;wBACN,WAAW;oBACb;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,gBAAgB;wBAC9B,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,iBAAiB;wBAC/B,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,EAAA,6BAAA,OAAO,kBAAkB,cAAzB,iDAAA,2BAA2B,IAAI,KAAI;wBAC1C,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,eAAe;wBAC7B,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,sBAAsB;wBACpC,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,oBAAoB,GAAG,IAAI,KAAK,OAAO,oBAAoB,EAAE,kBAAkB,KAAK;wBAClG,MAAM;wBACN,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,cAAc;wBAC5B,MAAM;wBACN,WAAW;oBACb;oBACA;wBACE,OAAO;wBACP,OAAO,OAAO,UAAU,GAAG,IAAI,KAAK,OAAO,UAAU,EAAE,kBAAkB,KAAK;wBAC9E,MAAM;wBACN,MAAM;oBACR;iBACD;eAzFI,OAAO,SAAS,IAAI;;;;;;;AA8FnC;GApIM;KAAA;uCAsIS", "debugId": null}}, {"offset": {"line": 6544, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/evaluation/DocumentCard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Application } from '@/types/license';\r\nimport { DocumentData, documentService } from '@/services/documentService';\r\nimport DocumentPreviewModal from '@/components/documents/DocumentPreviewModal';\r\n\r\ninterface DocumentCardProps {\r\n  application: Application | null;\r\n  className?: string;\r\n  defaultCollapsed?: boolean;\r\n}\r\n\r\nconst DocumentCard: React.FC<DocumentCardProps> = ({\r\n  application,\r\n  className = '',\r\n  defaultCollapsed = false\r\n}) => {\r\n  const [documents, setDocuments] = useState<DocumentData[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);\r\n  const [selectedDocument, setSelectedDocument] = useState<DocumentData | null>(null);\r\n  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const fetchDocuments = async () => {\r\n      if (!application?.application_id) return;\r\n\r\n      try {\r\n        setLoading(true);\r\n        let documentData: DocumentData[] = [];\r\n\r\n        // Try primary method first\r\n        try {\r\n          const response = await documentService.getDocumentsByApplication(application.application_id);\r\n          documentData = response.data;\r\n        } catch (primaryErr) {\r\n          // Try alternative method\r\n          try {\r\n            const altResponse = await documentService.getDocumentsByEntity('application', application.application_id);\r\n            documentData = altResponse.data;\r\n          } catch (altErr) {\r\n            console.warn('Could not load documents:', altErr);\r\n          }\r\n        }\r\n\r\n        setDocuments(documentData || []);\r\n      } catch (err) {\r\n        console.warn('Could not load documents:', err);\r\n        setDocuments([]);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchDocuments();\r\n  }, [application?.application_id]);\r\n\r\n  // Return empty fragment if no application or no documents\r\n  if (!application || loading || !documents || documents.length === 0) {\r\n    return <></>;\r\n  }\r\n\r\n  const handleDocumentDownload = async (documentId: string, fileName: string) => {\r\n    try {\r\n      const blob = await documentService.downloadDocument(documentId);\r\n      const url = window.URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(url);\r\n    } catch (err) {\r\n      console.error('Error downloading document:', err);\r\n    }\r\n  };\r\n\r\n  const handleDocumentPreview = (document: DocumentData) => {\r\n    // Only preview if document has required fields\r\n    if (document.document_id) {\r\n      setSelectedDocument(document);\r\n      setIsPreviewModalOpen(true);\r\n    }\r\n  };\r\n\r\n  const handleClosePreview = () => {\r\n    setIsPreviewModalOpen(false);\r\n    setSelectedDocument(null);\r\n  };\r\n\r\n  const getFileIcon = (fileName: string) => {\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    switch (extension) {\r\n      case 'pdf':\r\n        return 'ri-file-pdf-line text-red-500';\r\n      case 'doc':\r\n      case 'docx':\r\n        return 'ri-file-word-line text-blue-500';\r\n      case 'xls':\r\n      case 'xlsx':\r\n        return 'ri-file-excel-line text-green-500';\r\n      case 'jpg':\r\n      case 'jpeg':\r\n      case 'png':\r\n      case 'gif':\r\n        return 'ri-image-line text-purple-500';\r\n      default:\r\n        return 'ri-file-line text-gray-500';\r\n    }\r\n  };\r\n\r\n  const formatFileSize = (bytes: number) => {\r\n    if (bytes === 0) return '0 Bytes';\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>\r\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center\">\r\n              <i className=\"ri-folder-line text-xl text-blue-600 dark:text-blue-400 mr-3\"></i>\r\n              <div>\r\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n                  Application Documents\r\n                </h3>\r\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                  {documents && documents.length > 0\r\n                    ? `${documents.length} document${documents.length !== 1 ? 's' : ''} uploaded`\r\n                    : 'No documents uploaded'\r\n                  }\r\n                </p>\r\n              </div>\r\n            </div>\r\n            <button\r\n              onClick={() => setIsCollapsed(!isCollapsed)}\r\n              className=\"flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200\"\r\n              title={isCollapsed ? 'Expand' : 'Collapse'}\r\n            >\r\n              <i className={`${isCollapsed ? 'ri-add-line' : 'ri-subtract-line'} text-lg text-gray-500 dark:text-gray-400`}></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className={`transition-all duration-300 ease-in-out overflow-hidden ${isCollapsed ? 'max-h-0' : 'max-h-none'}`}>\r\n          <div className=\"p-6\">\r\n          {documents && documents.length > 0 ? (\r\n            <div className=\"grid gap-4\">\r\n              {documents.map((document) => (\r\n                <div\r\n                  key={document.document_id}\r\n                  className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors\"\r\n                >\r\n                  <div className=\"flex items-center space-x-4 flex-1\">\r\n                    <div className=\"flex-shrink-0\">\r\n                      <i className={`${getFileIcon(document.file_name)} text-2xl`}></i>\r\n                    </div>\r\n\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <div className=\"flex items-center space-x-2 mb-1\">\r\n                        <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                          {document.file_name}\r\n                        </h4>\r\n                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${\r\n                          (document as any).is_verified ?\r\n                            'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :\r\n                            'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\r\n                        }`}>\r\n                          {(document as any).is_verified ? 'Verified' : 'Pending'}\r\n                        </span>\r\n                      </div>\r\n\r\n                      <div className=\"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400\">\r\n                        <span className=\"flex items-center\">\r\n                          <i className=\"ri-file-line mr-1\"></i>\r\n                          {document.file_size ? formatFileSize(document.file_size) : 'Unknown size'}\r\n                        </span>\r\n                        <span className=\"flex items-center\">\r\n                          <i className=\"ri-calendar-line mr-1\"></i>\r\n                          {document.created_at ? new Date(document.created_at).toLocaleDateString() : 'Unknown date'}\r\n                        </span>\r\n                        {document.document_type && (\r\n                          <span className=\"flex items-center\">\r\n                            <i className=\"ri-price-tag-3-line mr-1\"></i>\r\n                            {documentService.formatDocumentType(document.document_type)}\r\n                          </span>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center space-x-2 ml-4\">\r\n                    {/* Preview Button */}\r\n                    {documentService.isPreviewable(document.mime_type) && (\r\n                      <button\r\n                        onClick={() => handleDocumentPreview(document)}\r\n                        className=\"inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/30 rounded-md transition-colors\"\r\n                        title=\"Preview document\"\r\n                      >\r\n                        <i className=\"ri-eye-line mr-1\"></i>\r\n                        Preview\r\n                      </button>\r\n                    )}\r\n\r\n                    {/* Download Button */}\r\n                    <button\r\n                      onClick={() => handleDocumentDownload(document.document_id!, document.file_name)}\r\n                      className=\"inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 bg-gray-50 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 rounded-md transition-colors\"\r\n                      title=\"Download document\"\r\n                    >\r\n                      <i className=\"ri-download-line mr-1\"></i>\r\n                      Download\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          ) : (\r\n            <div className=\"text-center py-12\">\r\n              <i className=\"ri-file-search-line text-4xl text-gray-400 dark:text-gray-500 mb-4\"></i>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                No Documents Found\r\n              </h3>\r\n              <p className=\"text-gray-500 dark:text-gray-400\">\r\n                No documents have been uploaded for this application yet.\r\n              </p>\r\n            </div>\r\n          )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Document Preview Modal */}\r\n      {selectedDocument && selectedDocument.document_id && (\r\n        <DocumentPreviewModal\r\n          isOpen={isPreviewModalOpen}\r\n          onClose={handleClosePreview}\r\n          document={selectedDocument as any} // Type assertion since we've checked document_id exists\r\n        />\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default DocumentCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;;;;;;AAQA,MAAM,eAA4C;QAAC,EACjD,WAAW,EACX,YAAY,EAAE,EACd,mBAAmB,KAAK,EACzB;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;yDAAiB;oBACrB,IAAI,EAAC,wBAAA,kCAAA,YAAa,cAAc,GAAE;oBAElC,IAAI;wBACF,WAAW;wBACX,IAAI,eAA+B,EAAE;wBAErC,2BAA2B;wBAC3B,IAAI;4BACF,MAAM,WAAW,MAAM,qIAAA,CAAA,kBAAe,CAAC,yBAAyB,CAAC,YAAY,cAAc;4BAC3F,eAAe,SAAS,IAAI;wBAC9B,EAAE,OAAO,YAAY;4BACnB,yBAAyB;4BACzB,IAAI;gCACF,MAAM,cAAc,MAAM,qIAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC,eAAe,YAAY,cAAc;gCACxG,eAAe,YAAY,IAAI;4BACjC,EAAE,OAAO,QAAQ;gCACf,QAAQ,IAAI,CAAC,6BAA6B;4BAC5C;wBACF;wBAEA,aAAa,gBAAgB,EAAE;oBACjC,EAAE,OAAO,KAAK;wBACZ,QAAQ,IAAI,CAAC,6BAA6B;wBAC1C,aAAa,EAAE;oBACjB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;iCAAG;QAAC,wBAAA,kCAAA,YAAa,cAAc;KAAC;IAEhC,0DAA0D;IAC1D,IAAI,CAAC,eAAe,WAAW,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;QACnE,qBAAO;IACT;IAEA,MAAM,yBAAyB,OAAO,YAAoB;QACxD,IAAI;YACF,MAAM,OAAO,MAAM,qIAAA,CAAA,kBAAe,CAAC,gBAAgB,CAAC;YACpD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG;YAChB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;QAC7B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,+CAA+C;QAC/C,IAAI,UAAS,WAAW,EAAE;YACxB,oBAAoB;YACpB,sBAAsB;QACxB;IACF;IAEA,MAAM,qBAAqB;QACzB,sBAAsB;QACtB,oBAAoB;IACtB;IAEA,MAAM,cAAc,CAAC;YACD;QAAlB,MAAM,aAAY,sBAAA,SAAS,KAAK,CAAC,KAAK,GAAG,gBAAvB,0CAAA,oBAA2B,WAAW;QACxD,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE;;0BACE,6LAAC;gBAAI,WAAW,AAAC,8FAAuG,OAAV;;kCAC5G,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAyD;;;;;;8DAGvE,6LAAC;oDAAE,WAAU;8DACV,aAAa,UAAU,MAAM,GAAG,IAC7B,AAAC,GAA8B,OAA5B,UAAU,MAAM,EAAC,aAA6C,OAAlC,UAAU,MAAM,KAAK,IAAI,MAAM,IAAG,eACjE;;;;;;;;;;;;;;;;;;8CAKV,6LAAC;oCACC,SAAS,IAAM,eAAe,CAAC;oCAC/B,WAAU;oCACV,OAAO,cAAc,WAAW;8CAEhC,cAAA,6LAAC;wCAAE,WAAW,AAAC,GAAmD,OAAjD,cAAc,gBAAgB,oBAAmB;;;;;;;;;;;;;;;;;;;;;;kCAKxE,6LAAC;wBAAI,WAAW,AAAC,2DAAiG,OAAvC,cAAc,YAAY;kCACnG,cAAA,6LAAC;4BAAI,WAAU;sCACd,aAAa,UAAU,MAAM,GAAG,kBAC/B,6LAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,0BACd,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAE,WAAW,AAAC,GAAkC,OAAhC,YAAY,UAAS,SAAS,GAAE;;;;;;;;;;;kEAGnD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFACX,UAAS,SAAS;;;;;;kFAErB,6LAAC;wEAAK,WAAW,AAAC,yEAIjB,OAHC,AAAC,UAAiB,WAAW,GAC3B,sEACA;kFAED,AAAC,UAAiB,WAAW,GAAG,aAAa;;;;;;;;;;;;0EAIlD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;;0FACd,6LAAC;gFAAE,WAAU;;;;;;4EACZ,UAAS,SAAS,GAAG,eAAe,UAAS,SAAS,IAAI;;;;;;;kFAE7D,6LAAC;wEAAK,WAAU;;0FACd,6LAAC;gFAAE,WAAU;;;;;;4EACZ,UAAS,UAAU,GAAG,IAAI,KAAK,UAAS,UAAU,EAAE,kBAAkB,KAAK;;;;;;;oEAE7E,UAAS,aAAa,kBACrB,6LAAC;wEAAK,WAAU;;0FACd,6LAAC;gFAAE,WAAU;;;;;;4EACZ,qIAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC,UAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;0DAOpE,6LAAC;gDAAI,WAAU;;oDAEZ,qIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,UAAS,SAAS,mBAC/C,6LAAC;wDACC,SAAS,IAAM,sBAAsB;wDACrC,WAAU;wDACV,OAAM;;0EAEN,6LAAC;gEAAE,WAAU;;;;;;4DAAuB;;;;;;;kEAMxC,6LAAC;wDACC,SAAS,IAAM,uBAAuB,UAAS,WAAW,EAAG,UAAS,SAAS;wDAC/E,WAAU;wDACV,OAAM;;0EAEN,6LAAC;gEAAE,WAAU;;;;;;4DAA4B;;;;;;;;;;;;;;uCA5DxC,UAAS,WAAW;;;;;;;;;yFAoE/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;;;;;kDACb,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,6LAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUvD,oBAAoB,iBAAiB,WAAW,kBAC/C,6LAAC,0JAAA,CAAA,UAAoB;gBACnB,QAAQ;gBACR,SAAS;gBACT,UAAU;;;;;;;;AAKpB;GA3OM;KAAA;uCA6OS", "debugId": null}}, {"offset": {"line": 6996, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/evaluation/index.ts"], "sourcesContent": ["// Export evaluation components\r\nexport { default as DataDisplayCard } from './DataDisplayCard';\r\nexport { default as ApplicationInfoCard } from './ApplicationInfoCard';\r\nexport { default as ApplicantInfoCard } from './ApplicantInfoCard';\r\nexport { default as AddressInfoCard } from './AddressInfoCard';\r\nexport { default as ContactInfoCard } from './ContactInfoCard';\r\nexport { default as ServiceScopeCard } from './ServiceScopeCard';\r\nexport { default as LegalHistoryCard } from './LegalHistoryCard';\r\nexport { default as ManagementCard } from './ManagementCard';\r\nexport { default as EquipmentDetailsCard } from './EquipmentDetailsCard';\r\nexport { default as DocumentCard } from './DocumentCard';\r\n"], "names": [], "mappings": "AAAA,+BAA+B;;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 7099, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationStatusService.ts"], "sourcesContent": ["'use client';\r\n\r\n// Types for application status tracking\r\nexport interface ApplicationStatusTrackingData {\r\n  id: string;\r\n  applicationId: string;\r\n  currentStatus: string;\r\n  statusHistory: StatusHistoryEntry[];\r\n  submittedDate: string;\r\n  lastUpdated: string;\r\n  estimatedCompletion?: string;\r\n  assignedOfficer?: string;\r\n  notes?: string;\r\n}\r\n\r\nexport interface StatusHistoryEntry {\r\n  status: string;\r\n  timestamp: string;\r\n  updatedBy: string;\r\n  notes?: string;\r\n}\r\n\r\nexport interface UpdateApplicationStatusData {\r\n  status: string;\r\n  comments?: string;\r\n  reason?: string;\r\n  send_email?: boolean;\r\n  changed_by?: string;\r\n  estimated_completion_date?: string;\r\n}\r\n\r\n// Application status constants\r\nexport const APPLICATION_STATUSES = {\r\n  SUBMITTED: 'SUBMITTED',\r\n  UNDER_REVIEW: 'UNDER_REVIEW', \r\n  ADDITIONAL_INFO_REQUIRED: 'ADDITIONAL_INFO_REQUIRED',\r\n  TECHNICAL_REVIEW: 'TECHNICAL_REVIEW',\r\n  PENDING_PAYMENT: 'PENDING_PAYMENT',\r\n  APPROVED: 'APPROVED',\r\n  REJECTED: 'REJECTED',\r\n  WITHDRAWN: 'WITHDRAWN'\r\n} as const;\r\n\r\nexport type ApplicationStatus = typeof APPLICATION_STATUSES[keyof typeof APPLICATION_STATUSES];\r\n\r\n// Status display configuration\r\nconst STATUS_CONFIG = {\r\n  [APPLICATION_STATUSES.SUBMITTED]: {\r\n    displayName: 'Submitted',\r\n    colorClass: 'text-blue-600 bg-blue-100',\r\n    progressPercentage: 10\r\n  },\r\n  [APPLICATION_STATUSES.UNDER_REVIEW]: {\r\n    displayName: 'Under Review',\r\n    colorClass: 'text-yellow-600 bg-yellow-100',\r\n    progressPercentage: 30\r\n  },\r\n  [APPLICATION_STATUSES.ADDITIONAL_INFO_REQUIRED]: {\r\n    displayName: 'Additional Information Required',\r\n    colorClass: 'text-orange-600 bg-orange-100',\r\n    progressPercentage: 25\r\n  },\r\n  [APPLICATION_STATUSES.TECHNICAL_REVIEW]: {\r\n    displayName: 'Technical Review',\r\n    colorClass: 'text-purple-600 bg-purple-100',\r\n    progressPercentage: 60\r\n  },\r\n  [APPLICATION_STATUSES.PENDING_PAYMENT]: {\r\n    displayName: 'Pending Payment',\r\n    colorClass: 'text-indigo-600 bg-indigo-100',\r\n    progressPercentage: 80\r\n  },\r\n  [APPLICATION_STATUSES.APPROVED]: {\r\n    displayName: 'Approved',\r\n    colorClass: 'text-green-600 bg-green-100',\r\n    progressPercentage: 100\r\n  },\r\n  [APPLICATION_STATUSES.REJECTED]: {\r\n    displayName: 'Rejected',\r\n    colorClass: 'text-red-600 bg-red-100',\r\n    progressPercentage: 100\r\n  },\r\n  [APPLICATION_STATUSES.WITHDRAWN]: {\r\n    displayName: 'Withdrawn',\r\n    colorClass: 'text-gray-600 bg-gray-100',\r\n    progressPercentage: 0\r\n  }\r\n};\r\n\r\n// Roles that can update application status\r\nconst UPDATE_ROLES = [\r\n  'ADMIN',\r\n  'LICENSING_OFFICER',\r\n  'TECHNICAL_REVIEWER',\r\n  'SUPERVISOR'\r\n];\r\n\r\nclass ApplicationStatusService {\r\n  private baseUrl: string;\r\n\r\n  constructor() {\r\n    this.baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';\r\n  }\r\n\r\n  // Get application status tracking data\r\n  async getApplicationStatusTracking(applicationId: string): Promise<ApplicationStatusTrackingData> {\r\n    try {\r\n      const response = await fetch(`${this.baseUrl}/applications/${applicationId}/status-tracking`, {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${this.getAuthToken()}`\r\n        }\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Failed to fetch application status: ${response.statusText}`);\r\n      }\r\n\r\n      const result = await response.json();\r\n      return result.data; // Extract data from the standardized response format\r\n    } catch (error) {\r\n      console.error('Error fetching application status:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Update application status with comprehensive tracking\r\n  async updateApplicationStatus(\r\n    applicationId: string,\r\n    updateData: UpdateApplicationStatusData\r\n  ): Promise<void> {\r\n    try {\r\n      const response = await fetch(`${this.baseUrl}/applications/${applicationId}/status-tracking`, {\r\n        method: 'PUT',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${this.getAuthToken()}`\r\n        },\r\n        body: JSON.stringify(updateData)\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Failed to update application status: ${response.statusText}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating application status:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Get application status history\r\n  async getApplicationStatusHistory(applicationId: string): Promise<StatusHistoryEntry[]> {\r\n    try {\r\n      const response = await fetch(`${this.baseUrl}/applications/${applicationId}/status-history`, {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${this.getAuthToken()}`\r\n        }\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Failed to fetch application status history: ${response.statusText}`);\r\n      }\r\n\r\n      const result = await response.json();\r\n      return result.data; // Extract data from the standardized response format\r\n    } catch (error) {\r\n      console.error('Error fetching application status history:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Get applications by status with tracking\r\n  async getApplicationsByStatus(status: string): Promise<ApplicationStatusTrackingData[]> {\r\n    try {\r\n      const response = await fetch(`${this.baseUrl}/applications/by-status-tracking/${status}`, {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${this.getAuthToken()}`\r\n        }\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Failed to fetch applications by status: ${response.statusText}`);\r\n      }\r\n\r\n      const result = await response.json();\r\n      return result.data; // Extract data from the standardized response format\r\n    } catch (error) {\r\n      console.error('Error fetching applications by status:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Check if user can update status based on roles\r\n  canUpdateStatus(userRoles: string[]): boolean {\r\n    return userRoles.some(role => UPDATE_ROLES.includes(role.toUpperCase()));\r\n  }\r\n\r\n  // Get display name for status\r\n  getStatusDisplayName(status: string): string {\r\n    return STATUS_CONFIG[status as ApplicationStatus]?.displayName || status;\r\n  }\r\n\r\n  // Get color class for status\r\n  getStatusColorClass(status: string): string {\r\n    return STATUS_CONFIG[status as ApplicationStatus]?.colorClass || 'text-gray-600 bg-gray-100';\r\n  }\r\n\r\n  // Calculate progress percentage\r\n  calculateProgressPercentage(status: string): number {\r\n    return STATUS_CONFIG[status as ApplicationStatus]?.progressPercentage || 0;\r\n  }\r\n\r\n  // Get estimated completion date\r\n  getEstimatedCompletion(status: string, submittedDate?: string): string | null {\r\n    if (!submittedDate || status === APPLICATION_STATUSES.APPROVED || status === APPLICATION_STATUSES.REJECTED) {\r\n      return null;\r\n    }\r\n\r\n    // Estimated processing times in days\r\n    const processingTimes: Record<\r\n      | typeof APPLICATION_STATUSES.SUBMITTED\r\n      | typeof APPLICATION_STATUSES.UNDER_REVIEW\r\n      | typeof APPLICATION_STATUSES.ADDITIONAL_INFO_REQUIRED\r\n      | typeof APPLICATION_STATUSES.TECHNICAL_REVIEW\r\n      | typeof APPLICATION_STATUSES.PENDING_PAYMENT,\r\n      number\r\n    > = {\r\n      [APPLICATION_STATUSES.SUBMITTED]: 14,\r\n      [APPLICATION_STATUSES.UNDER_REVIEW]: 10,\r\n      [APPLICATION_STATUSES.ADDITIONAL_INFO_REQUIRED]: 21,\r\n      [APPLICATION_STATUSES.TECHNICAL_REVIEW]: 7,\r\n      [APPLICATION_STATUSES.PENDING_PAYMENT]: 3\r\n    };\r\n\r\n    const daysToAdd =\r\n      status in processingTimes\r\n        ? processingTimes[status as keyof typeof processingTimes]\r\n        : 14;\r\n    const submitted = new Date(submittedDate);\r\n    const estimated = new Date(submitted);\r\n    estimated.setDate(estimated.getDate() + daysToAdd);\r\n\r\n    return estimated.toISOString().split('T')[0]; // Return YYYY-MM-DD format\r\n  }\r\n\r\n  // Get all available statuses\r\n  getAllStatuses(): { value: string; label: string }[] {\r\n    return Object.values(APPLICATION_STATUSES).map(status => ({\r\n      value: status,\r\n      label: this.getStatusDisplayName(status)\r\n    }));\r\n  }\r\n\r\n  // Get status history for an application\r\n  async getStatusHistory(applicationId: string): Promise<StatusHistoryEntry[]> {\r\n    try {\r\n      const response = await fetch(`${this.baseUrl}/applications/${applicationId}/status/history`, {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${this.getAuthToken()}`\r\n        }\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Failed to fetch status history: ${response.statusText}`);\r\n      }\r\n\r\n      return await response.json();\r\n    } catch (error) {\r\n      console.error('Error fetching status history:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Helper method to get auth token\r\n  private getAuthToken(): string {\r\n    try {\r\n      const authData = localStorage.getItem('auth_token');\r\n      return authData || '';\r\n    } catch {\r\n      return '';\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const applicationStatusService = new ApplicationStatusService();\r\n\r\n// Export default\r\nexport default applicationStatusService;"], "names": [], "mappings": ";;;;;AAqGmB;;AArGnB;;AAgCO,MAAM,uBAAuB;IAClC,WAAW;IACX,cAAc;IACd,0BAA0B;IAC1B,kBAAkB;IAClB,iBAAiB;IACjB,UAAU;IACV,UAAU;IACV,WAAW;AACb;AAIA,+BAA+B;AAC/B,MAAM,gBAAgB;IACpB,CAAC,qBAAqB,SAAS,CAAC,EAAE;QAChC,aAAa;QACb,YAAY;QACZ,oBAAoB;IACtB;IACA,CAAC,qBAAqB,YAAY,CAAC,EAAE;QACnC,aAAa;QACb,YAAY;QACZ,oBAAoB;IACtB;IACA,CAAC,qBAAqB,wBAAwB,CAAC,EAAE;QAC/C,aAAa;QACb,YAAY;QACZ,oBAAoB;IACtB;IACA,CAAC,qBAAqB,gBAAgB,CAAC,EAAE;QACvC,aAAa;QACb,YAAY;QACZ,oBAAoB;IACtB;IACA,CAAC,qBAAqB,eAAe,CAAC,EAAE;QACtC,aAAa;QACb,YAAY;QACZ,oBAAoB;IACtB;IACA,CAAC,qBAAqB,QAAQ,CAAC,EAAE;QAC/B,aAAa;QACb,YAAY;QACZ,oBAAoB;IACtB;IACA,CAAC,qBAAqB,QAAQ,CAAC,EAAE;QAC/B,aAAa;QACb,YAAY;QACZ,oBAAoB;IACtB;IACA,CAAC,qBAAqB,SAAS,CAAC,EAAE;QAChC,aAAa;QACb,YAAY;QACZ,oBAAoB;IACtB;AACF;AAEA,2CAA2C;AAC3C,MAAM,eAAe;IACnB;IACA;IACA;IACA;CACD;AAED,MAAM;IAOJ,uCAAuC;IACvC,MAAM,6BAA6B,aAAqB,EAA0C;QAChG,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,GAA+B,OAA7B,IAAI,CAAC,OAAO,EAAC,kBAA8B,OAAd,eAAc,qBAAmB;gBAC5F,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,UAA6B,OAApB,IAAI,CAAC,YAAY;gBAC9C;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,AAAC,uCAA0D,OAApB,SAAS,UAAU;YAC5E;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,OAAO,OAAO,IAAI,EAAE,qDAAqD;QAC3E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,wDAAwD;IACxD,MAAM,wBACJ,aAAqB,EACrB,UAAuC,EACxB;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,GAA+B,OAA7B,IAAI,CAAC,OAAO,EAAC,kBAA8B,OAAd,eAAc,qBAAmB;gBAC5F,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,UAA6B,OAApB,IAAI,CAAC,YAAY;gBAC9C;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,AAAC,wCAA2D,OAApB,SAAS,UAAU;YAC7E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,iCAAiC;IACjC,MAAM,4BAA4B,aAAqB,EAAiC;QACtF,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,GAA+B,OAA7B,IAAI,CAAC,OAAO,EAAC,kBAA8B,OAAd,eAAc,oBAAkB;gBAC3F,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,UAA6B,OAApB,IAAI,CAAC,YAAY;gBAC9C;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,AAAC,+CAAkE,OAApB,SAAS,UAAU;YACpF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,OAAO,OAAO,IAAI,EAAE,qDAAqD;QAC3E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,2CAA2C;IAC3C,MAAM,wBAAwB,MAAc,EAA4C;QACtF,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,GAAkD,OAAhD,IAAI,CAAC,OAAO,EAAC,qCAA0C,OAAP,SAAU;gBACxF,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,UAA6B,OAApB,IAAI,CAAC,YAAY;gBAC9C;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,AAAC,2CAA8D,OAApB,SAAS,UAAU;YAChF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,OAAO,OAAO,IAAI,EAAE,qDAAqD;QAC3E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,MAAM;QACR;IACF;IAEA,iDAAiD;IACjD,gBAAgB,SAAmB,EAAW;QAC5C,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,aAAa,QAAQ,CAAC,KAAK,WAAW;IACtE;IAEA,8BAA8B;IAC9B,qBAAqB,MAAc,EAAU;YACpC;QAAP,OAAO,EAAA,wBAAA,aAAa,CAAC,OAA4B,cAA1C,4CAAA,sBAA4C,WAAW,KAAI;IACpE;IAEA,6BAA6B;IAC7B,oBAAoB,MAAc,EAAU;YACnC;QAAP,OAAO,EAAA,wBAAA,aAAa,CAAC,OAA4B,cAA1C,4CAAA,sBAA4C,UAAU,KAAI;IACnE;IAEA,gCAAgC;IAChC,4BAA4B,MAAc,EAAU;YAC3C;QAAP,OAAO,EAAA,wBAAA,aAAa,CAAC,OAA4B,cAA1C,4CAAA,sBAA4C,kBAAkB,KAAI;IAC3E;IAEA,gCAAgC;IAChC,uBAAuB,MAAc,EAAE,aAAsB,EAAiB;QAC5E,IAAI,CAAC,iBAAiB,WAAW,qBAAqB,QAAQ,IAAI,WAAW,qBAAqB,QAAQ,EAAE;YAC1G,OAAO;QACT;QAEA,qCAAqC;QACrC,MAAM,kBAOF;YACF,CAAC,qBAAqB,SAAS,CAAC,EAAE;YAClC,CAAC,qBAAqB,YAAY,CAAC,EAAE;YACrC,CAAC,qBAAqB,wBAAwB,CAAC,EAAE;YACjD,CAAC,qBAAqB,gBAAgB,CAAC,EAAE;YACzC,CAAC,qBAAqB,eAAe,CAAC,EAAE;QAC1C;QAEA,MAAM,YACJ,UAAU,kBACN,eAAe,CAAC,OAAuC,GACvD;QACN,MAAM,YAAY,IAAI,KAAK;QAC3B,MAAM,YAAY,IAAI,KAAK;QAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAExC,OAAO,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,2BAA2B;IAC3E;IAEA,6BAA6B;IAC7B,iBAAqD;QACnD,OAAO,OAAO,MAAM,CAAC,sBAAsB,GAAG,CAAC,CAAA,SAAU,CAAC;gBACxD,OAAO;gBACP,OAAO,IAAI,CAAC,oBAAoB,CAAC;YACnC,CAAC;IACH;IAEA,wCAAwC;IACxC,MAAM,iBAAiB,aAAqB,EAAiC;QAC3E,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,GAA+B,OAA7B,IAAI,CAAC,OAAO,EAAC,kBAA8B,OAAd,eAAc,oBAAkB;gBAC3F,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,UAA6B,OAApB,IAAI,CAAC,YAAY;gBAC9C;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,AAAC,mCAAsD,OAApB,SAAS,UAAU;YACxE;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA,kCAAkC;IAC1B,eAAuB;QAC7B,IAAI;YACF,MAAM,WAAW,aAAa,OAAO,CAAC;YACtC,OAAO,YAAY;QACrB,EAAE,UAAM;YACN,OAAO;QACT;IACF;IA5LA,aAAc;QAFd,+KAAQ,WAAR,KAAA;QAGE,IAAI,CAAC,OAAO,GAAG,6DAAwC;IACzD;AA2LF;AAGO,MAAM,2BAA2B,IAAI;uCAG7B", "debugId": null}}, {"offset": {"line": 7337, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/licenseService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { License, PaginatedResponse } from '@/types';\r\n\r\n\r\nexport const licenseService = {\r\n  // Get all licenses with pagination\r\n  async getLicenses(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    status?: string;\r\n    licenseType?: string;\r\n    dateRange?: string;\r\n  }): Promise<PaginatedResponse<License> > {\r\n    const queryParams = new URLSearchParams();\r\n\r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    if (params?.licenseType) queryParams.append('filter.licenseType', params.licenseType);\r\n    if (params?.dateRange) queryParams.append('filter.dateRange', params.dateRange);\r\n\r\n    const response = await apiClient.get(`/licenses?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get single license by ID\r\n  async getLicense(id: string): Promise<License> {\r\n    const response = await apiClient.get(`/licenses/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get license by application ID\r\n  async getLicenseByApplication(applicationId: string): Promise<License | null> {\r\n    try {\r\n      // Since there's no direct endpoint, we'll get all licenses and filter by application_id\r\n      // In a real implementation, you'd want to add this endpoint to the backend\r\n      const response = await apiClient.get(`/licenses?filter.application_id=${applicationId}`);\r\n      const result = processApiResponse(response);\r\n      \r\n      if (result.data && result.data.length > 0) {\r\n        return result.data[0]; // Return the first (and should be only) license for this application\r\n      }\r\n      \r\n      return null;\r\n    } catch (error) {\r\n      console.error('Error getting license by application:', error);\r\n      return null;\r\n    }\r\n  },\r\n\r\n  // Get licenses by applicant\r\n  async getLicensesByApplicant(applicantId: string): Promise<License[]> {\r\n    const response = await apiClient.get(`/licenses/by-applicant/${applicantId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get license by license number\r\n  async getLicenseByNumber(licenseNumber: string): Promise<License> {\r\n    const response = await apiClient.get(`/licenses/by-number/${licenseNumber}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Download license PDF\r\n  async downloadLicensePDF(licenseId: string): Promise<Blob> {\r\n    try {\r\n      const response = await apiClient.get(`/licenses/${licenseId}/pdf`, {\r\n        responseType: 'blob',\r\n        headers: {\r\n          'Accept': 'application/pdf',\r\n        },\r\n      });\r\n      \r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error downloading license PDF:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get license statistics\r\n  async getLicenseStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/licenses/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get licenses expiring soon\r\n  async getExpiringSoon(days: number = 30): Promise<License[]> {\r\n    const response = await apiClient.get(`/licenses/expiring-soon?days=${days}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new license (admin only)\r\n  async createLicense(data: {\r\n    license_number: string;\r\n    application_id: string;\r\n    applicant_id: string;\r\n    license_type_id: string;\r\n    status?: string;\r\n    issue_date: string;\r\n    expiry_date: string;\r\n    issued_by: string;\r\n    conditions?: string;\r\n  }): Promise<License> {\r\n    const response = await apiClient.post('/licenses', data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update license (admin only)\r\n  async updateLicense(id: string, data: Partial<License>): Promise<License> {\r\n    const response = await apiClient.put(`/licenses/${id}`, data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete license (admin only)\r\n  async deleteLicense(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/licenses/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIO,MAAM,iBAAiB;IAC5B,mCAAmC;IACnC,MAAM,aAAY,MASjB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,mBAAA,6BAAA,OAAQ,SAAS,EAAE,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QACvE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QACrE,IAAI,mBAAA,6BAAA,OAAQ,WAAW,EAAE,YAAY,MAAM,CAAC,sBAAsB,OAAO,WAAW;QACpF,IAAI,mBAAA,6BAAA,OAAQ,SAAS,EAAE,YAAY,MAAM,CAAC,oBAAoB,OAAO,SAAS;QAE9E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,aAAmC,OAAvB,YAAY,QAAQ;QACtE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,2BAA2B;IAC3B,MAAM,YAAW,EAAU;QACzB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,aAAe,OAAH;QAClD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,yBAAwB,aAAqB;QACjD,IAAI;YACF,wFAAwF;YACxF,2EAA2E;YAC3E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,mCAAgD,OAAd;YACxE,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAElC,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,GAAG;gBACzC,OAAO,OAAO,IAAI,CAAC,EAAE,EAAE,qEAAqE;YAC9F;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,MAAM,wBAAuB,WAAmB;QAC9C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,0BAAqC,OAAZ;QAC/D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,oBAAmB,aAAqB;QAC5C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,uBAAoC,OAAd;QAC5D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB;IACvB,MAAM,oBAAmB,SAAiB;QACxC,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,aAAsB,OAAV,WAAU,SAAO;gBACjE,cAAc;gBACd,SAAS;oBACP,UAAU;gBACZ;YACF;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA,yBAAyB;IACzB,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM;YAAgB,OAAA,iEAAe;QACnC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,gCAAoC,OAAL;QACrE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,kCAAkC;IAClC,MAAM,eAAc,IAUnB;QACC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,aAAa;QACnD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,eAAc,EAAU,EAAE,IAAsB;QACpD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,aAAe,OAAH,KAAM;QACxD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,eAAc,EAAU;QAC5B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,aAAe,OAAH;QACrD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 7441, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/evaluation/ApplicationStatusButtons.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo } from 'react';\r\nimport { ApplicationStatus, Application, StatusAction } from '@/types/license';\r\nimport { applicationStatusService } from '@/services/applicationStatusService';\r\nimport { activityNotesService } from '@/services/activityNotesService';\r\nimport { licenseService } from '@/services/licenseService';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\n\r\ninterface ApplicationStatusButtonsProps {\r\n  application: Application;\r\n  onStatusChange?: (newStatus: string, updatedApplication: Application) => void;\r\n  disabled?: boolean;\r\n  className?: string;\r\n}\r\n\r\n\r\nconst ApplicationStatusButtons: React.FC<ApplicationStatusButtonsProps> = ({\r\n  application,\r\n  onStatusChange,\r\n  disabled = false,\r\n  className = ''\r\n}) => {\r\n  const [isUpdating, setIsUpdating] = useState(false);\r\n  const [showConfirmDialog, setShowConfirmDialog] = useState<StatusAction | null>(null);\r\n  const [localApplication, setLocalApplication] = useState<Application>(application);\r\n  const [statusComment, setStatusComment] = useState<string>('');\r\n\r\n  // Toast notifications\r\n  const { showSuccess, showError } = useToast();\r\n  const { user } = useAuth();\r\n\r\n  // Sync local state when application prop changes\r\n  useEffect(() => {\r\n    console.log('📥 ApplicationStatusButtons: Application prop changed, updating local state to status:', application.status);\r\n    setLocalApplication(application);\r\n  }, [application]);\r\n\r\n  // Define available status actions based on current status\r\n  const getAvailableActions = (status: string): StatusAction[] => {\r\n    const actions: StatusAction[] = [];\r\n\r\n    switch (status.toLowerCase()) {\r\n      case 'submitted':\r\n        actions.push({\r\n          status: 'evaluation' as ApplicationStatus,\r\n          label: 'Start Review',\r\n          icon: 'ri-eye-line',\r\n          roles: ['officer', 'administrator'],\r\n          color: 'bg-blue-600 hover:bg-blue-700',\r\n          hoverColor: 'hover:bg-blue-700',\r\n          description: 'Move application to under review status'\r\n        });\r\n        break;\r\n\r\n      case 'under_review':\r\n        actions.push({\r\n          status: 'evaluation' as ApplicationStatus,\r\n          label: 'Start Evaluation',\r\n          icon: 'ri-file-list-3-line',\r\n          roles: ['officer', 'administrator'],\r\n          color: 'bg-purple-600 hover:bg-purple-700',\r\n          hoverColor: 'hover:bg-purple-700',\r\n          description: 'Move application to evaluation phase'\r\n        });\r\n        actions.push({\r\n          status: 'rejected' as ApplicationStatus,\r\n          label: 'Reject',\r\n          icon: 'ri-close-circle-line',\r\n          color: 'bg-red-600 hover:bg-red-700',\r\n          hoverColor: 'hover:bg-red-700',\r\n          roles: ['officer', 'administrator'],\r\n          description: 'Reject the application',\r\n          confirmMessage: 'Are you sure you want to reject this application? This action cannot be undone.'\r\n        });\r\n        break;\r\n\r\n      case 'evaluation':\r\n        actions.push({\r\n          status: 'pass_evaluation' as ApplicationStatus,\r\n          label: 'Pass Evaluation',\r\n          icon: 'ri-checkbox-circle-line',\r\n          roles: ['officer', 'administrator'],\r\n          color: 'bg-green-600 hover:bg-green-700',\r\n          hoverColor: 'hover:bg-green-700',\r\n          description: 'Mark evaluation as passed and proceed to payment'\r\n        });\r\n        actions.push({\r\n          status: 'rejected' as ApplicationStatus,\r\n          label: 'Fail Evaluation',\r\n          icon: 'ri-close-circle-line',\r\n          roles: ['officer', 'administrator'],\r\n          color: 'bg-red-600 hover:bg-red-700',\r\n          hoverColor: 'hover:bg-red-700',\r\n          description: 'Fail the evaluation and reject application',\r\n          confirmMessage: 'Are you sure you want to fail this evaluation? This will reject the application.'\r\n        });\r\n        break;\r\n\r\n      // case 'pass_evaluation':\r\n      //   actions.push({\r\n      //     status: 'pending_payment' as ApplicationStatus,\r\n      //     label: 'Request Payment',\r\n      //     roles: ['finance', 'administrator'],\r\n      //     icon: 'ri-money-dollar-circle-line',\r\n      //     color: 'bg-yellow-600 hover:bg-yellow-700',\r\n      //     hoverColor: 'hover:bg-yellow-700',\r\n      //     description: 'Request payment from applicant'\r\n      //   });\r\n      //   break;\r\n\r\n      case 'waiting_for_approval':\r\n        actions.push({\r\n          status: 'approved' as ApplicationStatus,\r\n          label: 'Approve',\r\n          icon: 'ri-check-double-line',\r\n          color: 'bg-green-600 hover:bg-green-700',\r\n          roles: ['finance', 'administrator'],\r\n          hoverColor: 'hover:bg-green-700',\r\n          description: 'Approve the application (payment confirmed)',\r\n          confirmMessage: 'Are you sure you want to approve this application? This action cannot be undone.'\r\n        });\r\n        break;\r\n\r\n      default:\r\n        // No actions available for other statuses\r\n        break;\r\n    }\r\n\r\n    return actions;\r\n  };\r\n\r\n  // Filter actions based on user roles\r\n  const filterActionsByUserRole = (actions: StatusAction[]): StatusAction[] => {\r\n    if (!user || !user.roles) {\r\n      console.log('ApplicationStatusButtons: No user or user roles found');\r\n      return [];\r\n    }\r\n\r\n    const filteredActions = actions.filter(action => {\r\n      // If no roles specified for the action, show to all users\r\n      if (!action.roles || action.roles.length === 0) {\r\n        console.log(`ApplicationStatusButtons: Action \"${action.label}\" has no role restrictions, showing to all users`);\r\n        return true;\r\n      }\r\n\r\n      // Check if user has any of the required roles\r\n      const hasRequiredRole = action.roles.some(requiredRole =>\r\n        user.roles?.some(userRole =>\r\n          userRole.toLowerCase() === requiredRole.toLowerCase()\r\n        ) || false\r\n      );\r\n\r\n      return hasRequiredRole;\r\n    });\r\n    return filteredActions;\r\n  };\r\n\r\n  const handleStatusChange = async (action: StatusAction) => {\r\n    // Check if comment is required\r\n    if (!statusComment.trim()) {\r\n      const errorMessage = 'Please provide a comment for this status change';\r\n      showError(errorMessage, 5000);\r\n      return;\r\n    }\r\n\r\n    if (action.confirmMessage) {\r\n      setShowConfirmDialog(action);\r\n      return;\r\n    }\r\n\r\n    await executeStatusChange(action);\r\n  };\r\n\r\n  const executeStatusChange = async (action: StatusAction) => {\r\n    try {\r\n      setIsUpdating(true);\r\n\r\n      // Validate comment if required\r\n      if (!statusComment.trim()) {\r\n        const errorMessage = 'Please provide a comment for this status change';\r\n        showError(errorMessage, 5000);\r\n        return;\r\n      }\r\n\r\n      // Update the application status via comprehensive tracking API\r\n      console.log(`🔄 Updating application ${localApplication.application_id} status to: ${action.status}`);\r\n\r\n      await applicationStatusService.updateApplicationStatus(\r\n        localApplication.application_id,\r\n        {\r\n          status: action.status,\r\n          comments: statusComment.trim(),\r\n          reason: action.description,\r\n          send_email: true, // Always send email notification\r\n          changed_by: undefined // Will be set from auth token\r\n        }\r\n      );\r\n\r\n      console.log(`✅ Application status updated successfully to: ${action.status}`);\r\n\r\n      // Special handling for approved status\r\n      if (action.status === 'approved') {\r\n        console.log(`🏷️ Application approved - license generation should be triggered automatically by backend`);\r\n\r\n        // Wait a moment for license generation, then verify\r\n        setTimeout(async () => {\r\n          try {\r\n            const license = await licenseService.getLicenseByApplication(localApplication.application_id);\r\n            if (license) {\r\n              console.log(`✅ License verified: ${license.license_number}`);\r\n              showSuccess(\r\n                `Application approved successfully! License ${license.license_number} has been generated and the applicant will be notified.`,\r\n                10000\r\n              );\r\n            } else {\r\n              console.warn(`⚠️ License not found after approval for application: ${localApplication.application_id}`);\r\n              showSuccess(\r\n                'Application approved successfully! License generation is in progress and the applicant will be notified.',\r\n                8000\r\n              );\r\n            }\r\n          } catch (error) {\r\n            console.error('Error verifying license creation:', error);\r\n            showSuccess(\r\n              'Application approved successfully! License generation is in progress and the applicant will be notified.',\r\n              8000\r\n            );\r\n          }\r\n        }, 2000); // Wait 2 seconds for backend processing\r\n      }\r\n\r\n      // Create activity note with status update (this will email the applicant)\r\n      await activityNotesService.createStatusUpdate(\r\n        localApplication.application_id,\r\n        `Application status changed from \"${localApplication.status.replace('_', ' ')}\" to \"${action.status.replace('_', ' ')}\"${statusComment.trim() ? `: ${statusComment.trim()}` : ''}`,\r\n        {\r\n          previous_status: localApplication.status,\r\n          new_status: action.status,\r\n          action_taken: action.label,\r\n          comment: statusComment.trim()\r\n        }\r\n      );\r\n\r\n      // Update local state\r\n      const newApplication = { ...localApplication, status: action.status };\r\n      console.log('✅ ApplicationStatusButtons: Updating local application status from', localApplication.status, 'to', action.status);\r\n      setLocalApplication(newApplication);\r\n\r\n      // Clear the comment field\r\n      setStatusComment('');\r\n\r\n      // Call the callback if provided\r\n      if (onStatusChange) {\r\n        onStatusChange(action.status, newApplication);\r\n      }\r\n\r\n      // Show success toast (unless it's approval which has its own message)\r\n      if (action.status !== 'approved') {\r\n        showSuccess(\r\n          `Application status successfully changed to \"${action.status.replace('_', ' ')}\" and notification sent to applicant.`,\r\n          6000\r\n        );\r\n      }\r\n    } catch (err: any) {\r\n      const errorMessage = err.message || 'Failed to update application status';\r\n      console.error('Error updating application status:', err);\r\n\r\n      // Special error handling for approval failures\r\n      if (action.status === 'approved') {\r\n        showError(\r\n          `Failed to approve application: ${errorMessage}. This may be due to license generation issues. Please check the application details and try again.`,\r\n          10000\r\n        );\r\n      } else {\r\n        showError(\r\n          `Failed to update application status: ${errorMessage}`,\r\n          8000\r\n        );\r\n      }\r\n    } finally {\r\n      setIsUpdating(false);\r\n      setShowConfirmDialog(null);\r\n    }\r\n  };\r\n\r\n  // Calculate available actions based on current status and user roles\r\n  const availableActions = useMemo(() => {\r\n    console.log('🔄 ApplicationStatusButtons: Recalculating available actions for status:', localApplication.status);\r\n    const allActions = getAvailableActions(localApplication.status);\r\n    const filtered = filterActionsByUserRole(allActions);\r\n    console.log('🔄 ApplicationStatusButtons: Available actions after filtering:', filtered.map(a => a.label));\r\n    return filtered;\r\n  }, [localApplication.status, user]);\r\n\r\n  console.log('🎯 ApplicationStatusButtons: Rendering with status:', localApplication.status, 'Available actions:', availableActions.length);\r\n\r\n  if (availableActions.length === 0) {\r\n    console.log('❌ ApplicationStatusButtons: No available actions, hiding component');\r\n    return null;\r\n  }\r\n\r\n  if (!user || user.isCustomer) {\r\n    console.log('❌ ApplicationStatusButtons: User is customer or not logged in, hiding component');\r\n    return (<></>);\r\n  }\r\n\r\n  return (\r\n    <div className={`space-y-4 ${className}`}>\r\n\r\n      {/* Status Action Buttons */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6\">\r\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center\">\r\n          <i className=\"ri-settings-3-line mr-2\"></i>\r\n          Application Status Actions\r\n        </h3>\r\n\r\n        {/* Comment Field */}\r\n        <div className=\"mb-6\">\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Status Change Comment <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <textarea\r\n            value={statusComment}\r\n            onChange={(e) => setStatusComment(e.target.value)}\r\n            rows={3}\r\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100\"\r\n            placeholder=\"Provide a comment explaining the reason for this status change. This will be emailed to the applicant and saved as an activity note.\"\r\n            disabled={disabled || isUpdating}\r\n          />\r\n          <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n            <i className=\"ri-mail-line mr-1\"></i>\r\n            This comment will be emailed to the applicant and saved in the application activity log.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"flex flex-wrap gap-3\">\r\n          {availableActions.map((action, index) => (\r\n            <button\r\n              key={index}\r\n              onClick={() => handleStatusChange(action)}\r\n              disabled={disabled || isUpdating || !statusComment.trim()}\r\n              className={`inline-flex items-center px-4 py-2.5 rounded-lg border border-transparent transition-all duration-200 ${action.color} text-white disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-lg hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-opacity-50 text-sm font-medium`}\r\n              title={action.description}\r\n            >\r\n              <i className={`${action.icon} text-base mr-2`}></i>\r\n              <span>{action.label}</span>\r\n\r\n              {isUpdating ? (\r\n                <i className=\"ri-loader-4-line animate-spin text-base ml-2\"></i>\r\n              ) : (\r\n                <i className=\"ri-arrow-right-line text-base ml-2 opacity-70\"></i>\r\n              )}\r\n            </button>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Action descriptions */}\r\n        <div className=\"mt-4 space-y-2\">\r\n          {availableActions.map((action, index) => (\r\n            <div key={index} className=\"flex items-start space-x-2 text-sm text-gray-600 dark:text-gray-400\">\r\n              <i className={`${action.icon} text-base mt-0.5 flex-shrink-0`}></i>\r\n              <div>\r\n                <span className=\"font-medium text-gray-700 dark:text-gray-300\">{action.label}:</span>\r\n                <span className=\"ml-1\">{action.description}</span>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Help text when comment is missing */}\r\n        {!statusComment.trim() && (\r\n          <div className=\"mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\">\r\n            <div className=\"flex items-center text-yellow-800 dark:text-yellow-200 text-sm\">\r\n              <i className=\"ri-information-line mr-2\"></i>\r\n              <span>Please provide a comment above to enable status change actions.</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Current Status Display */}\r\n        <div className=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center justify-between text-sm\">\r\n            <span className=\"text-gray-600 dark:text-gray-400\">Current Status:</span>\r\n            <span className=\"font-medium text-gray-900 dark:text-gray-100 capitalize\">\r\n              {localApplication.status.replace('_', ' ')}\r\n            </span>\r\n          </div>\r\n          <div className=\"flex items-center justify-between text-sm mt-2\">\r\n            <span className=\"text-gray-600 dark:text-gray-400\">Application Number:</span>\r\n            <span className=\"font-mono text-xs text-gray-900 dark:text-gray-100\">\r\n              {localApplication.application_number}\r\n            </span>\r\n          </div>\r\n          <div className=\"flex items-center justify-between text-sm mt-2\">\r\n            <span className=\"text-gray-600 dark:text-gray-400\">Applicant:</span>\r\n            <span className=\"text-gray-900 dark:text-gray-100\">\r\n              {localApplication.applicant?.name}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Confirmation Dialog */}\r\n      {showConfirmDialog && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4\">\r\n            <div className=\"flex items-center mb-4\">\r\n              <i className={`${showConfirmDialog.icon} text-2xl mr-3 text-yellow-500`}></i>\r\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n                Confirm Action\r\n              </h3>\r\n            </div>\r\n            \r\n            <div className=\"mb-6\">\r\n              <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\r\n                {showConfirmDialog.confirmMessage}\r\n              </p>\r\n\r\n              {statusComment.trim() && (\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-3\">\r\n                  <p className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                    Comment to be sent to applicant:\r\n                  </p>\r\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 italic\">\r\n                    \"{statusComment.trim()}\"\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex space-x-3\">\r\n              <button\r\n                onClick={() => {\r\n                  setShowConfirmDialog(null);\r\n                  // Don't clear comment on cancel - user might want to try again\r\n                }}\r\n                className=\"flex-1 inline-flex items-center justify-center px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500\"\r\n              >\r\n                <i className=\"ri-close-line mr-2\"></i>\r\n                Cancel\r\n              </button>\r\n              <button\r\n                onClick={() => executeStatusChange(showConfirmDialog)}\r\n                disabled={isUpdating}\r\n                className={`flex-1 inline-flex items-center justify-center px-4 py-2.5 rounded-lg text-sm font-medium text-white transition-all duration-200 ${showConfirmDialog.color} disabled:opacity-50 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-opacity-50`}\r\n              >\r\n                {isUpdating ? (\r\n                  <>\r\n                    <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\r\n                    Updating...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <i className=\"ri-check-line mr-2\"></i>\r\n                    Confirm\r\n                  </>\r\n                )}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ApplicationStatusButtons;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;AAkBA,MAAM,2BAAoE;QAAC,EACzE,WAAW,EACX,cAAc,EACd,WAAW,KAAK,EAChB,YAAY,EAAE,EACf;QAuXc;;IAtXb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAChF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACtE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,sBAAsB;IACtB,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,QAAQ,GAAG,CAAC,0FAA0F,YAAY,MAAM;YACxH,oBAAoB;QACtB;6CAAG;QAAC;KAAY;IAEhB,0DAA0D;IAC1D,MAAM,sBAAsB,CAAC;QAC3B,MAAM,UAA0B,EAAE;QAElC,OAAQ,OAAO,WAAW;YACxB,KAAK;gBACH,QAAQ,IAAI,CAAC;oBACX,QAAQ;oBACR,OAAO;oBACP,MAAM;oBACN,OAAO;wBAAC;wBAAW;qBAAgB;oBACnC,OAAO;oBACP,YAAY;oBACZ,aAAa;gBACf;gBACA;YAEF,KAAK;gBACH,QAAQ,IAAI,CAAC;oBACX,QAAQ;oBACR,OAAO;oBACP,MAAM;oBACN,OAAO;wBAAC;wBAAW;qBAAgB;oBACnC,OAAO;oBACP,YAAY;oBACZ,aAAa;gBACf;gBACA,QAAQ,IAAI,CAAC;oBACX,QAAQ;oBACR,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,OAAO;wBAAC;wBAAW;qBAAgB;oBACnC,aAAa;oBACb,gBAAgB;gBAClB;gBACA;YAEF,KAAK;gBACH,QAAQ,IAAI,CAAC;oBACX,QAAQ;oBACR,OAAO;oBACP,MAAM;oBACN,OAAO;wBAAC;wBAAW;qBAAgB;oBACnC,OAAO;oBACP,YAAY;oBACZ,aAAa;gBACf;gBACA,QAAQ,IAAI,CAAC;oBACX,QAAQ;oBACR,OAAO;oBACP,MAAM;oBACN,OAAO;wBAAC;wBAAW;qBAAgB;oBACnC,OAAO;oBACP,YAAY;oBACZ,aAAa;oBACb,gBAAgB;gBAClB;gBACA;YAEF,0BAA0B;YAC1B,mBAAmB;YACnB,sDAAsD;YACtD,gCAAgC;YAChC,2CAA2C;YAC3C,2CAA2C;YAC3C,kDAAkD;YAClD,yCAAyC;YACzC,oDAAoD;YACpD,QAAQ;YACR,WAAW;YAEX,KAAK;gBACH,QAAQ,IAAI,CAAC;oBACX,QAAQ;oBACR,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,OAAO;wBAAC;wBAAW;qBAAgB;oBACnC,YAAY;oBACZ,aAAa;oBACb,gBAAgB;gBAClB;gBACA;YAEF;gBAEE;QACJ;QAEA,OAAO;IACT;IAEA,qCAAqC;IACrC,MAAM,0BAA0B,CAAC;QAC/B,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE;YACxB,QAAQ,GAAG,CAAC;YACZ,OAAO,EAAE;QACX;QAEA,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;YACrC,0DAA0D;YAC1D,IAAI,CAAC,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,GAAG;gBAC9C,QAAQ,GAAG,CAAC,AAAC,qCAAiD,OAAb,OAAO,KAAK,EAAC;gBAC9D,OAAO;YACT;YAEA,8CAA8C;YAC9C,MAAM,kBAAkB,OAAO,KAAK,CAAC,IAAI,CAAC,CAAA;oBACxC;uBAAA,EAAA,cAAA,KAAK,KAAK,cAAV,kCAAA,YAAY,IAAI,CAAC,CAAA,WACf,SAAS,WAAW,OAAO,aAAa,WAAW,QAChD;;YAGP,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,qBAAqB,OAAO;QAChC,+BAA+B;QAC/B,IAAI,CAAC,cAAc,IAAI,IAAI;YACzB,MAAM,eAAe;YACrB,UAAU,cAAc;YACxB;QACF;QAEA,IAAI,OAAO,cAAc,EAAE;YACzB,qBAAqB;YACrB;QACF;QAEA,MAAM,oBAAoB;IAC5B;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,cAAc;YAEd,+BAA+B;YAC/B,IAAI,CAAC,cAAc,IAAI,IAAI;gBACzB,MAAM,eAAe;gBACrB,UAAU,cAAc;gBACxB;YACF;YAEA,+DAA+D;YAC/D,QAAQ,GAAG,CAAC,AAAC,2BAAwE,OAA9C,iBAAiB,cAAc,EAAC,gBAA4B,OAAd,OAAO,MAAM;YAElG,MAAM,8IAAA,CAAA,2BAAwB,CAAC,uBAAuB,CACpD,iBAAiB,cAAc,EAC/B;gBACE,QAAQ,OAAO,MAAM;gBACrB,UAAU,cAAc,IAAI;gBAC5B,QAAQ,OAAO,WAAW;gBAC1B,YAAY;gBACZ,YAAY,UAAU,8BAA8B;YACtD;YAGF,QAAQ,GAAG,CAAC,AAAC,iDAA8D,OAAd,OAAO,MAAM;YAE1E,uCAAuC;YACvC,IAAI,OAAO,MAAM,KAAK,YAAY;gBAChC,QAAQ,GAAG,CAAE;gBAEb,oDAAoD;gBACpD,WAAW;oBACT,IAAI;wBACF,MAAM,UAAU,MAAM,oIAAA,CAAA,iBAAc,CAAC,uBAAuB,CAAC,iBAAiB,cAAc;wBAC5F,IAAI,SAAS;4BACX,QAAQ,GAAG,CAAC,AAAC,uBAA6C,OAAvB,QAAQ,cAAc;4BACzD,YACE,AAAC,8CAAoE,OAAvB,QAAQ,cAAc,EAAC,4DACrE;wBAEJ,OAAO;4BACL,QAAQ,IAAI,CAAC,AAAC,wDAAuF,OAAhC,iBAAiB,cAAc;4BACpG,YACE,4GACA;wBAEJ;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,qCAAqC;wBACnD,YACE,4GACA;oBAEJ;gBACF,GAAG,OAAO,wCAAwC;YACpD;YAEA,0EAA0E;YAC1E,MAAM,0IAAA,CAAA,uBAAoB,CAAC,kBAAkB,CAC3C,iBAAiB,cAAc,EAC/B,AAAC,oCAAqF,OAAlD,iBAAiB,MAAM,CAAC,OAAO,CAAC,KAAK,MAAK,UAA2C,OAAnC,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,MAAK,KAA2D,OAAxD,cAAc,IAAI,KAAK,AAAC,KAAyB,OAArB,cAAc,IAAI,MAAO,KAC9K;gBACE,iBAAiB,iBAAiB,MAAM;gBACxC,YAAY,OAAO,MAAM;gBACzB,cAAc,OAAO,KAAK;gBAC1B,SAAS,cAAc,IAAI;YAC7B;YAGF,qBAAqB;YACrB,MAAM,iBAAiB;gBAAE,GAAG,gBAAgB;gBAAE,QAAQ,OAAO,MAAM;YAAC;YACpE,QAAQ,GAAG,CAAC,sEAAsE,iBAAiB,MAAM,EAAE,MAAM,OAAO,MAAM;YAC9H,oBAAoB;YAEpB,0BAA0B;YAC1B,iBAAiB;YAEjB,gCAAgC;YAChC,IAAI,gBAAgB;gBAClB,eAAe,OAAO,MAAM,EAAE;YAChC;YAEA,sEAAsE;YACtE,IAAI,OAAO,MAAM,KAAK,YAAY;gBAChC,YACE,AAAC,+CAA8E,OAAhC,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,MAAK,0CAC/E;YAEJ;QACF,EAAE,OAAO,KAAU;YACjB,MAAM,eAAe,IAAI,OAAO,IAAI;YACpC,QAAQ,KAAK,CAAC,sCAAsC;YAEpD,+CAA+C;YAC/C,IAAI,OAAO,MAAM,KAAK,YAAY;gBAChC,UACE,AAAC,kCAA8C,OAAb,cAAa,wGAC/C;YAEJ,OAAO;gBACL,UACE,AAAC,wCAAoD,OAAb,eACxC;YAEJ;QACF,SAAU;YACR,cAAc;YACd,qBAAqB;QACvB;IACF;IAEA,qEAAqE;IACrE,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8DAAE;YAC/B,QAAQ,GAAG,CAAC,4EAA4E,iBAAiB,MAAM;YAC/G,MAAM,aAAa,oBAAoB,iBAAiB,MAAM;YAC9D,MAAM,WAAW,wBAAwB;YACzC,QAAQ,GAAG,CAAC,mEAAmE,SAAS,GAAG;sEAAC,CAAA,IAAK,EAAE,KAAK;;YACxG,OAAO;QACT;6DAAG;QAAC,iBAAiB,MAAM;QAAE;KAAK;IAElC,QAAQ,GAAG,CAAC,uDAAuD,iBAAiB,MAAM,EAAE,sBAAsB,iBAAiB,MAAM;IAEzI,IAAI,iBAAiB,MAAM,KAAK,GAAG;QACjC,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,IAAI,CAAC,QAAQ,KAAK,UAAU,EAAE;QAC5B,QAAQ,GAAG,CAAC;QACZ,qBAAQ;IACV;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,aAAsB,OAAV;;0BAG3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAE,WAAU;;;;;;4BAA8B;;;;;;;kCAK7C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;;oCAAkE;kDAC3D,6LAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAEvD,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAChD,MAAM;gCACN,WAAU;gCACV,aAAY;gCACZ,UAAU,YAAY;;;;;;0CAExB,6LAAC;gCAAE,WAAU;;kDACX,6LAAC;wCAAE,WAAU;;;;;;oCAAwB;;;;;;;;;;;;;kCAKzC,6LAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,QAAQ,sBAC7B,6LAAC;gCAEC,SAAS,IAAM,mBAAmB;gCAClC,UAAU,YAAY,cAAc,CAAC,cAAc,IAAI;gCACvD,WAAW,AAAC,yGAAqH,OAAb,OAAO,KAAK,EAAC;gCACjI,OAAO,OAAO,WAAW;;kDAEzB,6LAAC;wCAAE,WAAW,AAAC,GAAc,OAAZ,OAAO,IAAI,EAAC;;;;;;kDAC7B,6LAAC;kDAAM,OAAO,KAAK;;;;;;oCAElB,2BACC,6LAAC;wCAAE,WAAU;;;;;iGAEb,6LAAC;wCAAE,WAAU;;;;;;;+BAZV;;;;;;;;;;kCAmBX,6LAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,QAAQ,sBAC7B,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC;wCAAE,WAAW,AAAC,GAAc,OAAZ,OAAO,IAAI,EAAC;;;;;;kDAC7B,6LAAC;;0DACC,6LAAC;gDAAK,WAAU;;oDAAgD,OAAO,KAAK;oDAAC;;;;;;;0DAC7E,6LAAC;gDAAK,WAAU;0DAAQ,OAAO,WAAW;;;;;;;;;;;;;+BAJpC;;;;;;;;;;oBAWb,CAAC,cAAc,IAAI,oBAClB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;;;;;8CACb,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAMZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAmC;;;;;;kDACnD,6LAAC;wCAAK,WAAU;kDACb,iBAAiB,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;0CAG1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAmC;;;;;;kDACnD,6LAAC;wCAAK,WAAU;kDACb,iBAAiB,kBAAkB;;;;;;;;;;;;0CAGxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAmC;;;;;;kDACnD,6LAAC;wCAAK,WAAU;mDACb,8BAAA,iBAAiB,SAAS,cAA1B,kDAAA,4BAA4B,IAAI;;;;;;;;;;;;;;;;;;;;;;;;YAOxC,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAW,AAAC,GAAyB,OAAvB,kBAAkB,IAAI,EAAC;;;;;;8CACxC,6LAAC;oCAAG,WAAU;8CAAyD;;;;;;;;;;;;sCAKzE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CACV,kBAAkB,cAAc;;;;;;gCAGlC,cAAc,IAAI,oBACjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA4D;;;;;;sDAGzE,6LAAC;4CAAE,WAAU;;gDAAkD;gDAC3D,cAAc,IAAI;gDAAG;;;;;;;;;;;;;;;;;;;sCAM/B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;wCACP,qBAAqB;oCACrB,+DAA+D;oCACjE;oCACA,WAAU;;sDAEV,6LAAC;4CAAE,WAAU;;;;;;wCAAyB;;;;;;;8CAGxC,6LAAC;oCACC,SAAS,IAAM,oBAAoB;oCACnC,UAAU;oCACV,WAAW,AAAC,oIAA2J,OAAxB,kBAAkB,KAAK,EAAC;8CAEtK,2BACC;;0DACE,6LAAC;gDAAE,WAAU;;;;;;4CAAyC;;qEAIxD;;0DACE,6LAAC;gDAAE,WAAU;;;;;;4CAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1D;GAhcM;;QAY+B,mIAAA,CAAA,WAAQ;QAC1B,kIAAA,CAAA,UAAO;;;KAbpB;uCAkcS", "debugId": null}}, {"offset": {"line": 8187, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/evaluation/InvoiceStatusCard.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { invoiceService } from '@/services/invoiceService';\r\nimport { Invoice, InvoiceItem } from '@/types/invoice';\r\nimport { taskService } from '@/services/task-assignment';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Application, TaskStatus } from '@/types';\r\nimport { formatStatus } from '@/utils/formatters';\r\n\r\ninterface InvoiceStatusCardProps {\r\n  application: Application;\r\n  onInvoiceGenerated?: (invoice: Invoice) => void;\r\n  onStatusChange?: (status: {\r\n    hasInvoice: boolean;\r\n    status?: 'paid' | 'pending' | 'overdue' | 'none';\r\n  }) => void;\r\n}\r\n\r\nconst InvoiceStatusCard: React.FC<InvoiceStatusCardProps> = ({\r\n  application,\r\n  onInvoiceGenerated,\r\n  onStatusChange\r\n}) => {\r\n  const [loading, setLoading] = useState(true);\r\n  const [invoiceStatus, setInvoiceStatus] = useState<{\r\n    hasInvoice: boolean;\r\n    invoice?: Invoice;\r\n    status?: 'paid' | 'pending' | 'overdue' | 'none';\r\n  }>({ hasInvoice: false, status: 'none' });\r\n  const [allInvoices, setAllInvoices] = useState<Invoice[]>([]);\r\n  const [loadingInvoices, setLoadingInvoices] = useState(false);\r\n  const [showGenerateForm, setShowGenerateForm] = useState(false);\r\n  const [generating, setGenerating] = useState(false);\r\n  const [formData, setFormData] = useState({\r\n    amount: '',\r\n    description: '',\r\n    items: [] as InvoiceItem[]\r\n  });\r\n  const [loadingDefaults, setLoadingDefaults] = useState(false);\r\n  const [errorMessage, setErrorMessage] = useState<string | null>(null);\r\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\r\n\r\n  // Load data when component mounts or application ID changes\r\n  useEffect(() => {\r\n    if (application?.application_id) {\r\n      console.log('🔄 Loading invoice data for application:', application.application_id);\r\n      loadInvoiceStatus();\r\n      loadAllInvoices();\r\n    }\r\n  }, [application?.application_id]);\r\n\r\n  // Also load data on component mount to ensure it runs when modal is shown\r\n  useEffect(() => {\r\n    if (application?.application_id) {\r\n      console.log('🚀 Component mounted, loading invoice data for application:', application.application_id);\r\n      loadInvoiceStatus();\r\n      loadAllInvoices();\r\n    }\r\n  }, []); // Empty dependency array means this runs once on mount\r\n\r\n  const loadInvoiceStatus = async () => {\r\n    console.log('📋 loadInvoiceStatus called for application:', application.application_id);\r\n    setLoading(true);\r\n    try {\r\n      const status = await invoiceService.getApplicationInvoiceStatus(application.application_id);\r\n      console.log('✅ Invoice status loaded:', status);\r\n      setInvoiceStatus(status);\r\n      // Notify parent component of status change\r\n      onStatusChange?.(status);\r\n    } catch (error) {\r\n      console.log('❌ Error loading invoice status:', error);\r\n      const errorStatus = { hasInvoice: false, status: 'none' as const };\r\n      setInvoiceStatus(errorStatus);\r\n      // Notify parent component of status change\r\n      onStatusChange?.(errorStatus);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const loadAllInvoices = async () => {\r\n    console.log('📄 loadAllInvoices called for application:', application.application_id);\r\n    setLoadingInvoices(true);\r\n    try {\r\n      const response = await invoiceService.getInvoicesByEntity('application', application.application_id);\r\n      console.log('📄 Invoice response:', response);\r\n      const invoices = response.data || response || [];\r\n      console.log('✅ All invoices loaded:', invoices.length, 'invoices');\r\n      setAllInvoices(invoices);\r\n    } catch (error) {\r\n      console.log('❌ Error loading all invoices:', error);\r\n      setAllInvoices([]);\r\n    } finally {\r\n      setLoadingInvoices(false);\r\n    }\r\n  };\r\n\r\n  const loadDefaultInvoiceData = async () => {\r\n    setLoadingDefaults(true);\r\n    try {\r\n      const { defaultInvoiceData } = await invoiceService.getApplicationDetailsForInvoice(application.application_id);\r\n      setFormData({\r\n        amount: defaultInvoiceData.amount.toString(),\r\n        description: defaultInvoiceData.description,\r\n        items: defaultInvoiceData.items\r\n      });\r\n    } catch (error) {\r\n      // Set basic defaults if API fails\r\n      setFormData({\r\n        amount: '0',\r\n        description: 'License Application Fee',\r\n        items: [{\r\n          item_id: `license_fee_${Date.now()}`,\r\n          description: 'License Application Fee',\r\n          quantity: 1,\r\n          unit_price: 0\r\n        }]\r\n      });\r\n    } finally {\r\n      setLoadingDefaults(false);\r\n    }\r\n  };\r\n\r\n  const handleGenerateInvoice = async () => {\r\n    if (!formData.amount || !formData.description) {\r\n      setErrorMessage('Please fill in all required fields');\r\n      setSuccessMessage(null);\r\n      return;\r\n    }\r\n\r\n    // Calculate total amount from items\r\n    const totalAmount = formData.items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0);\r\n\r\n    setGenerating(true);\r\n    try {\r\n      const invoice = await invoiceService.generateApplicationInvoice(application.application_id, {\r\n        amount: totalAmount,\r\n        description: formData.description,\r\n        items: formData.items.length > 0 ? formData.items : undefined\r\n      });\r\n\r\n      const newStatus = {\r\n        hasInvoice: true,\r\n        invoice,\r\n        status: 'pending' as const\r\n      };\r\n\r\n      setInvoiceStatus(newStatus);\r\n      // Notify parent component of status change\r\n      onStatusChange?.(newStatus);\r\n\r\n      setShowGenerateForm(false);\r\n      setFormData({ amount: '', description: '', items: [] });\r\n      setErrorMessage(null); // Clear any previous errors\r\n\r\n      // Show success message\r\n      const successMsg = invoice?.invoice_number\r\n        ? `Invoice ${invoice.invoice_number} processed successfully!`\r\n        : 'Invoice processed successfully!';\r\n\r\n      setSuccessMessage(successMsg);\r\n\r\n      // Auto-hide success message after 5 seconds\r\n      setTimeout(() => {\r\n        setSuccessMessage(null);\r\n      }, 5000);\r\n\r\n      if (onInvoiceGenerated) {\r\n        onInvoiceGenerated(invoice);\r\n      }\r\n\r\n      // Refresh the invoices list\r\n      await loadAllInvoices();\r\n\r\n      // Close the task for this application\r\n      await closeApplicationTask();\r\n    } catch (error: any) {\r\n      console.error('Failed to generate invoice:', error);\r\n\r\n      // Extract error message from API response\r\n      let errorMessage = 'Failed to generate invoice. Please try again.';\r\n      if (error?.response?.data?.message) {\r\n        errorMessage = error.response.data.message;\r\n      } else if (error?.message) {\r\n        errorMessage = error.message;\r\n      }\r\n\r\n      setErrorMessage(errorMessage);\r\n      setSuccessMessage(null); // Clear any success message\r\n    } finally {\r\n      setGenerating(false);\r\n    }\r\n  };\r\n\r\n  const closeApplicationTask = async () => {\r\n    try {\r\n      console.log('🔄 Attempting to close task for application:', application.applicant_id);\r\n\r\n      // Get the task for this application\r\n      const task = await taskService.getTaskForApplication(application.applicant_id);\r\n\r\n      if (task && task.status !== TaskStatus.COMPLETED) {\r\n        console.log('📋 Found task to close:', task.task_id);\r\n\r\n        // Update the task status to completed\r\n        await taskService.updateTask(task.task_id, {\r\n          status: TaskStatus.COMPLETED,\r\n          completion_notes: 'Task completed automatically when invoice was generated'\r\n        });\r\n\r\n        console.log('✅ Task closed successfully:', task.task_id);\r\n      } else if (task && task.status === TaskStatus.COMPLETED) {\r\n        console.log('ℹ️ Task is already completed:', task.task_id);\r\n      } else {\r\n        console.log('ℹ️ No task found for application:', application.applicant_id);\r\n      }\r\n    } catch (error) {\r\n      console.error('⚠️ Failed to close application task:', error);\r\n      // Don't throw error here - invoice generation should still succeed even if task closure fails\r\n    }\r\n  };\r\n\r\n  const addInvoiceItem = () => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      items: [\r\n        ...prev.items,\r\n        {\r\n          item_id: `item_${Date.now()}`,\r\n          description: '',\r\n          quantity: 1,\r\n          unit_price: 0\r\n        }\r\n      ]\r\n    }));\r\n  };\r\n\r\n  const updateInvoiceItem = (index: number, field: keyof InvoiceItem, value: any) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      items: prev.items.map((item, i) => \r\n        i === index ? { ...item, [field]: value } : item\r\n      )\r\n    }));\r\n  };\r\n\r\n  const removeInvoiceItem = (index: number) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      items: prev.items.filter((_, i) => i !== index)\r\n    }));\r\n  };\r\n\r\n  const getStatusIcon = () => {\r\n    switch (invoiceStatus.status) {\r\n      case 'paid':\r\n        return 'ri-check-circle-line text-green-600';\r\n      case 'pending':\r\n        return 'ri-time-line text-yellow-600';\r\n      case 'overdue':\r\n        return 'ri-error-warning-line text-red-600';\r\n      default:\r\n        return 'ri-file-list-line text-gray-600';\r\n    }\r\n  };\r\n\r\n  const getStatusText = () => {\r\n    switch (invoiceStatus.status) {\r\n      case 'paid':\r\n        return 'Invoice Paid';\r\n      case 'pending':\r\n        return 'Pending Payment from Applicant';\r\n      case 'overdue':\r\n        return 'Payment Overdue';\r\n      default:\r\n        return 'No Invoice Generated';\r\n    }\r\n  };\r\n\r\n  const getStatusColor = () => {\r\n    switch (invoiceStatus.status) {\r\n      case 'paid':\r\n        return 'text-green-700 bg-green-50 border-green-200';\r\n      case 'pending':\r\n        return 'text-yellow-700 bg-yellow-50 border-yellow-200';\r\n      case 'overdue':\r\n        return 'text-red-700 bg-red-50 border-red-200';\r\n      default:\r\n        return 'text-gray-700 bg-gray-50 border-gray-200';\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700\">\r\n        <div className=\"flex items-center justify-center py-4\">\r\n          <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3\"></div>\r\n          <span className=\"text-gray-600 dark:text-gray-400\">Loading invoice status...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700\">\r\n      <div className=\"flex items-center justify-between mb-4\">\r\n        <h4 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\">\r\n          <i className=\"ri-bill-line mr-2 text-blue-600\"></i>\r\n          Invoice Status\r\n        </h4>\r\n      </div>\r\n\r\n      {/* Success Message */}\r\n      {successMessage && (\r\n        <div className=\"mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\">\r\n          <div className=\"flex items-center\">\r\n            <i className=\"ri-check-line text-green-600 dark:text-green-400 mr-2\"></i>\r\n            <span className=\"text-green-700 dark:text-green-300 text-sm\">{successMessage}</span>\r\n            <button\r\n              onClick={() => setSuccessMessage(null)}\r\n              className=\"ml-auto text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200\"\r\n            >\r\n              <i className=\"ri-close-line\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Error Message */}\r\n      {errorMessage && (\r\n        <div className=\"mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\">\r\n          <div className=\"flex items-center\">\r\n            <i className=\"ri-error-warning-line text-red-600 dark:text-red-400 mr-2\"></i>\r\n            <span className=\"text-red-700 dark:text-red-300 text-sm\">{errorMessage}</span>\r\n            <button\r\n              onClick={() => setErrorMessage(null)}\r\n              className=\"ml-auto text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200\"\r\n            >\r\n              <i className=\"ri-close-line\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <div className={`p-4 rounded-lg border ${getStatusColor()} dark:bg-gray-700 dark:border-gray-600`}>\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center\">\r\n            <i className={`${getStatusIcon()} text-xl mr-3`}></i>\r\n            <div>\r\n              <p className=\"font-medium\">{getStatusText()}</p>\r\n              {invoiceStatus.invoice && (\r\n                <div className=\"text-sm opacity-75 mt-1\">\r\n                  <p>Invoice #{invoiceStatus.invoice.invoice_number}</p>\r\n                  <p>Amount: ${invoiceStatus.invoice.amount}</p>\r\n                  {invoiceStatus.invoice.due_date && (\r\n                    <p>Due: {new Date(invoiceStatus.invoice.due_date).toLocaleDateString()}</p>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {!invoiceStatus.hasInvoice && application.status === 'pass_evaluation' && (\r\n            <button\r\n              onClick={() => {\r\n                setShowGenerateForm(true);\r\n                setErrorMessage(null); // Clear any previous errors\r\n                setSuccessMessage(null); // Clear any success messages\r\n                loadDefaultInvoiceData();\r\n              }}\r\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors\"\r\n            >\r\n              <i className=\"ri-add-line mr-2\"></i>\r\n              Generate Invoice\r\n            </button>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Application Invoices List */}\r\n      {allInvoices.length > 0 && (\r\n        <div className=\"mt-6\">\r\n          <h5 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center\">\r\n            <i className=\"ri-file-list-3-line mr-2 text-blue-600\"></i>\r\n            Application Invoices ({allInvoices.length})\r\n          </h5>\r\n\r\n          {loadingInvoices ? (\r\n            <div className=\"flex items-center justify-center py-4\">\r\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3\"></div>\r\n              <span className=\"text-gray-600 dark:text-gray-400\">Loading invoices...</span>\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-3\">\r\n              {allInvoices.map((invoice, index) => (\r\n                <div\r\n                  key={invoice.invoice_id || index}\r\n                  className=\"p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600\"\r\n                >\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"flex items-center justify-between mb-2\">\r\n                        <h6 className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                          Invoice #{invoice.invoice_number || 'N/A'}\r\n                        </h6>\r\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\r\n                          invoice.status === 'paid'\r\n                            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'\r\n                            : invoice.status === 'overdue'\r\n                            ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'\r\n                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'\r\n                        }`}>\r\n                          { formatStatus(invoice.status)}\r\n                        </span>\r\n                      </div>\r\n\r\n                      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600 dark:text-gray-400\">\r\n                        <div>\r\n                          <span className=\"font-medium\">Amount:</span> ${invoice.amount || '0.00'}\r\n                        </div>\r\n                        <div>\r\n                          <span className=\"font-medium\">Created:</span> {\r\n                            invoice.created_at\r\n                              ? new Date(invoice.created_at).toLocaleDateString()\r\n                              : 'N/A'\r\n                          }\r\n                        </div>\r\n                        <div>\r\n                          <span className=\"font-medium\">Due Date:</span> {\r\n                            invoice.due_date\r\n                              ? new Date(invoice.due_date).toLocaleDateString()\r\n                              : 'N/A'\r\n                          }\r\n                        </div>\r\n                      </div>\r\n\r\n                      {invoice.description && (\r\n                        <div className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\r\n                          <span className=\"font-medium\">Description:</span> {invoice.description}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Generate Invoice Form */}\r\n      {showGenerateForm && (\r\n        <div className=\"mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border overflow-hidden\">\r\n          <h5 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">\r\n            Generate Invoice\r\n          </h5>\r\n\r\n          {loadingDefaults ? (\r\n            <div className=\"flex items-center justify-center py-4\">\r\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3\"></div>\r\n              <span className=\"text-gray-600 dark:text-gray-400\">Loading default values...</span>\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-6\">\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                  Description <span className=\"text-red-500\">*</span>\r\n                </label>\r\n                <textarea\r\n                  value={formData.description}\r\n                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\r\n                  rows={3}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100\"\r\n                  placeholder=\"License application processing fee...\"\r\n                />\r\n              </div>\r\n\r\n              {/* Invoice Items */}\r\n              <div>\r\n                <div className=\"flex items-center justify-between mb-3\">\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                    Invoice Items\r\n                  </label>\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={addInvoiceItem}\r\n                    className=\"px-3 py-1 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500\"\r\n                  >\r\n                    <i className=\"ri-add-line mr-1\"></i>\r\n                    Add Item\r\n                  </button>\r\n                </div>\r\n\r\n                <div className=\"space-y-3 overflow-x-auto\">\r\n                  {formData.items.map((item, index) => (\r\n                    <div key={item.item_id} className=\"p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 min-w-0\">\r\n                      {/* Mobile Layout */}\r\n                      <div className=\"block md:hidden space-y-3\">\r\n                        <div>\r\n                          <label className=\"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1\">\r\n                            Description\r\n                          </label>\r\n                          <input\r\n                            type=\"text\"\r\n                            value={item.description}\r\n                            onChange={(e) => updateInvoiceItem(index, 'description', e.target.value)}\r\n                            className=\"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100\"\r\n                            placeholder=\"Item description\"\r\n                          />\r\n                        </div>\r\n                        <div className=\"grid grid-cols-2 gap-2\">\r\n                          <div>\r\n                            <label className=\"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1\">\r\n                              Quantity\r\n                            </label>\r\n                            <input\r\n                              type=\"number\"\r\n                              min=\"1\"\r\n                              value={item.quantity}\r\n                              onChange={(e) => updateInvoiceItem(index, 'quantity', parseInt(e.target.value) || 1)}\r\n                              className=\"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100\"\r\n                            />\r\n                          </div>\r\n                          <div>\r\n                            <label className=\"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1\">\r\n                              Unit Price\r\n                            </label>\r\n                            <input\r\n                              type=\"number\"\r\n                              step=\"0.01\"\r\n                              min=\"0\"\r\n                              value={item.unit_price}\r\n                              onChange={(e) => updateInvoiceItem(index, 'unit_price', parseFloat(e.target.value) || 0)}\r\n                              className=\"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100\"\r\n                            />\r\n                          </div>\r\n                        </div>\r\n                        <div className=\"flex justify-between items-center\">\r\n                          <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                            Subtotal: ${(item.quantity * item.unit_price).toFixed(2)}\r\n                          </span>\r\n                          <button\r\n                            type=\"button\"\r\n                            onClick={() => removeInvoiceItem(index)}\r\n                            className=\"px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-1 focus:ring-red-500\"\r\n                          >\r\n                            <i className=\"ri-delete-bin-line text-xs mr-1\"></i>\r\n                            Remove\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Desktop Layout */}\r\n                      <div className=\"hidden md:block\">\r\n                        <div className=\"grid grid-cols-12 gap-3 items-end\">\r\n                          <div className=\"col-span-5\">\r\n                            <label className=\"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1\">\r\n                              Description\r\n                            </label>\r\n                            <input\r\n                              type=\"text\"\r\n                              value={item.description}\r\n                              onChange={(e) => updateInvoiceItem(index, 'description', e.target.value)}\r\n                              className=\"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100\"\r\n                              placeholder=\"Item description\"\r\n                            />\r\n                          </div>\r\n                          <div className=\"col-span-2\">\r\n                            <label className=\"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1\">\r\n                              Qty\r\n                            </label>\r\n                            <input\r\n                              type=\"number\"\r\n                              min=\"1\"\r\n                              value={item.quantity}\r\n                              onChange={(e) => updateInvoiceItem(index, 'quantity', parseInt(e.target.value) || 1)}\r\n                              className=\"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100\"\r\n                            />\r\n                          </div>\r\n                          <div className=\"col-span-2\">\r\n                            <label className=\"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1\">\r\n                              Price\r\n                            </label>\r\n                            <input\r\n                              type=\"number\"\r\n                              step=\"0.01\"\r\n                              min=\"0\"\r\n                              value={item.unit_price}\r\n                              onChange={(e) => updateInvoiceItem(index, 'unit_price', parseFloat(e.target.value) || 0)}\r\n                              className=\"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100\"\r\n                            />\r\n                          </div>\r\n                          <div className=\"col-span-2\">\r\n                            <label className=\"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1\">\r\n                              Subtotal\r\n                            </label>\r\n                            <div className=\"px-2 py-1 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 rounded border\">\r\n                              ${(item.quantity * item.unit_price).toFixed(2)}\r\n                            </div>\r\n                          </div>\r\n                          <div className=\"col-span-1\">\r\n                            <button\r\n                              type=\"button\"\r\n                              onClick={() => removeInvoiceItem(index)}\r\n                              className=\"w-full px-2 py-1 bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-1 focus:ring-red-500\"\r\n                              title=\"Remove item\"\r\n                            >\r\n                              <i className=\"ri-delete-bin-line text-xs\"></i>\r\n                            </button>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n\r\n                {/* Total Amount */}\r\n                <div className=\"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800\">\r\n                  <div className=\"flex justify-between items-center\">\r\n                    <span className=\"text-lg font-semibold text-blue-800 dark:text-blue-200\">\r\n                      Total Amount:\r\n                    </span>\r\n                    <span className=\"text-xl font-bold text-blue-900 dark:text-blue-100\">\r\n                      ${formData.items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0).toFixed(2)}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex justify-end space-x-3\">\r\n                <button\r\n                  onClick={() => {\r\n                    setShowGenerateForm(false);\r\n                    setFormData({ amount: '', description: '', items: [] });\r\n                    setErrorMessage(null); // Clear any errors\r\n                    setSuccessMessage(null); // Clear any success messages\r\n                  }}\r\n                  className=\"px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\"\r\n                >\r\n                  Cancel\r\n                </button>\r\n                <button\r\n                  onClick={handleGenerateInvoice}\r\n                  disabled={generating || !formData.description || formData.items.length === 0}\r\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                >\r\n                  {generating ? (\r\n                    <>\r\n                      <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\r\n                      Generating...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <i className=\"ri-save-line mr-2\"></i>\r\n                      Generate Invoice and Close evaluation task\r\n                    </>\r\n                  )}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InvoiceStatusCard;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AAAA;AACA;;;AAPA;;;;;;AAkBA,MAAM,oBAAsD;QAAC,EAC3D,WAAW,EACX,kBAAkB,EAClB,cAAc,EACf;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAI9C;QAAE,YAAY;QAAO,QAAQ;IAAO;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAC5D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,QAAQ;QACR,aAAa;QACb,OAAO,EAAE;IACX;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,4DAA4D;IAC5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,wBAAA,kCAAA,YAAa,cAAc,EAAE;gBAC/B,QAAQ,GAAG,CAAC,4CAA4C,YAAY,cAAc;gBAClF;gBACA;YACF;QACF;sCAAG;QAAC,wBAAA,kCAAA,YAAa,cAAc;KAAC;IAEhC,0EAA0E;IAC1E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,wBAAA,kCAAA,YAAa,cAAc,EAAE;gBAC/B,QAAQ,GAAG,CAAC,+DAA+D,YAAY,cAAc;gBACrG;gBACA;YACF;QACF;sCAAG,EAAE,GAAG,uDAAuD;IAE/D,MAAM,oBAAoB;QACxB,QAAQ,GAAG,CAAC,gDAAgD,YAAY,cAAc;QACtF,WAAW;QACX,IAAI;YACF,MAAM,SAAS,MAAM,oIAAA,CAAA,iBAAc,CAAC,2BAA2B,CAAC,YAAY,cAAc;YAC1F,QAAQ,GAAG,CAAC,4BAA4B;YACxC,iBAAiB;YACjB,2CAA2C;YAC3C,2BAAA,qCAAA,eAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,mCAAmC;YAC/C,MAAM,cAAc;gBAAE,YAAY;gBAAO,QAAQ;YAAgB;YACjE,iBAAiB;YACjB,2CAA2C;YAC3C,2BAAA,qCAAA,eAAiB;QACnB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,QAAQ,GAAG,CAAC,8CAA8C,YAAY,cAAc;QACpF,mBAAmB;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,oIAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,eAAe,YAAY,cAAc;YACnG,QAAQ,GAAG,CAAC,wBAAwB;YACpC,MAAM,WAAW,SAAS,IAAI,IAAI,YAAY,EAAE;YAChD,QAAQ,GAAG,CAAC,0BAA0B,SAAS,MAAM,EAAE;YACvD,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,iCAAiC;YAC7C,eAAe,EAAE;QACnB,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,yBAAyB;QAC7B,mBAAmB;QACnB,IAAI;YACF,MAAM,EAAE,kBAAkB,EAAE,GAAG,MAAM,oIAAA,CAAA,iBAAc,CAAC,+BAA+B,CAAC,YAAY,cAAc;YAC9G,YAAY;gBACV,QAAQ,mBAAmB,MAAM,CAAC,QAAQ;gBAC1C,aAAa,mBAAmB,WAAW;gBAC3C,OAAO,mBAAmB,KAAK;YACjC;QACF,EAAE,OAAO,OAAO;YACd,kCAAkC;YAClC,YAAY;gBACV,QAAQ;gBACR,aAAa;gBACb,OAAO;oBAAC;wBACN,SAAS,AAAC,eAAyB,OAAX,KAAK,GAAG;wBAChC,aAAa;wBACb,UAAU;wBACV,YAAY;oBACd;iBAAE;YACJ;QACF,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,WAAW,EAAE;YAC7C,gBAAgB;YAChB,kBAAkB;YAClB;QACF;QAEA,oCAAoC;QACpC,MAAM,cAAc,SAAS,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,QAAQ,GAAG,KAAK,UAAU,EAAG;QAElG,cAAc;QACd,IAAI;YACF,MAAM,UAAU,MAAM,oIAAA,CAAA,iBAAc,CAAC,0BAA0B,CAAC,YAAY,cAAc,EAAE;gBAC1F,QAAQ;gBACR,aAAa,SAAS,WAAW;gBACjC,OAAO,SAAS,KAAK,CAAC,MAAM,GAAG,IAAI,SAAS,KAAK,GAAG;YACtD;YAEA,MAAM,YAAY;gBAChB,YAAY;gBACZ;gBACA,QAAQ;YACV;YAEA,iBAAiB;YACjB,2CAA2C;YAC3C,2BAAA,qCAAA,eAAiB;YAEjB,oBAAoB;YACpB,YAAY;gBAAE,QAAQ;gBAAI,aAAa;gBAAI,OAAO,EAAE;YAAC;YACrD,gBAAgB,OAAO,4BAA4B;YAEnD,uBAAuB;YACvB,MAAM,aAAa,CAAA,oBAAA,8BAAA,QAAS,cAAc,IACtC,AAAC,WAAiC,OAAvB,QAAQ,cAAc,EAAC,8BAClC;YAEJ,kBAAkB;YAElB,4CAA4C;YAC5C,WAAW;gBACT,kBAAkB;YACpB,GAAG;YAEH,IAAI,oBAAoB;gBACtB,mBAAmB;YACrB;YAEA,4BAA4B;YAC5B,MAAM;YAEN,sCAAsC;YACtC,MAAM;QACR,EAAE,OAAO,OAAY;gBAKf,sBAAA;YAJJ,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,0CAA0C;YAC1C,IAAI,eAAe;YACnB,IAAI,kBAAA,6BAAA,kBAAA,MAAO,QAAQ,cAAf,uCAAA,uBAAA,gBAAiB,IAAI,cAArB,2CAAA,qBAAuB,OAAO,EAAE;gBAClC,eAAe,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC5C,OAAO,IAAI,kBAAA,4BAAA,MAAO,OAAO,EAAE;gBACzB,eAAe,MAAM,OAAO;YAC9B;YAEA,gBAAgB;YAChB,kBAAkB,OAAO,4BAA4B;QACvD,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,QAAQ,GAAG,CAAC,gDAAgD,YAAY,YAAY;YAEpF,oCAAoC;YACpC,MAAM,OAAO,MAAM,wIAAA,CAAA,cAAW,CAAC,qBAAqB,CAAC,YAAY,YAAY;YAE7E,IAAI,QAAQ,KAAK,MAAM,KAAK,uHAAA,CAAA,aAAU,CAAC,SAAS,EAAE;gBAChD,QAAQ,GAAG,CAAC,2BAA2B,KAAK,OAAO;gBAEnD,sCAAsC;gBACtC,MAAM,wIAAA,CAAA,cAAW,CAAC,UAAU,CAAC,KAAK,OAAO,EAAE;oBACzC,QAAQ,uHAAA,CAAA,aAAU,CAAC,SAAS;oBAC5B,kBAAkB;gBACpB;gBAEA,QAAQ,GAAG,CAAC,+BAA+B,KAAK,OAAO;YACzD,OAAO,IAAI,QAAQ,KAAK,MAAM,KAAK,uHAAA,CAAA,aAAU,CAAC,SAAS,EAAE;gBACvD,QAAQ,GAAG,CAAC,iCAAiC,KAAK,OAAO;YAC3D,OAAO;gBACL,QAAQ,GAAG,CAAC,qCAAqC,YAAY,YAAY;YAC3E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,8FAA8F;QAChG;IACF;IAEA,MAAM,iBAAiB;QACrB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,OAAO;uBACF,KAAK,KAAK;oBACb;wBACE,SAAS,AAAC,QAAkB,OAAX,KAAK,GAAG;wBACzB,aAAa;wBACb,UAAU;wBACV,YAAY;oBACd;iBACD;YACH,CAAC;IACH;IAEA,MAAM,oBAAoB,CAAC,OAAe,OAA0B;QAClE,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,IAC3B,MAAM,QAAQ;wBAAE,GAAG,IAAI;wBAAE,CAAC,MAAM,EAAE;oBAAM,IAAI;YAEhD,CAAC;IACH;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAC3C,CAAC;IACH;IAEA,MAAM,gBAAgB;QACpB,OAAQ,cAAc,MAAM;YAC1B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ,cAAc,MAAM;YAC1B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ,cAAc,MAAM;YAC1B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAK,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAI3D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;;sCACZ,6LAAC;4BAAE,WAAU;;;;;;wBAAsC;;;;;;;;;;;;YAMtD,gCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;;;;;sCACb,6LAAC;4BAAK,WAAU;sCAA8C;;;;;;sCAC9D,6LAAC;4BACC,SAAS,IAAM,kBAAkB;4BACjC,WAAU;sCAEV,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;YAOpB,8BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;;;;;sCACb,6LAAC;4BAAK,WAAU;sCAA0C;;;;;;sCAC1D,6LAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAU;sCAEV,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMrB,6LAAC;gBAAI,WAAW,AAAC,yBAAyC,OAAjB,kBAAiB;0BACxD,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAW,AAAC,GAAkB,OAAhB,iBAAgB;;;;;;8CACjC,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAe;;;;;;wCAC3B,cAAc,OAAO,kBACpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAE;wDAAU,cAAc,OAAO,CAAC,cAAc;;;;;;;8DACjD,6LAAC;;wDAAE;wDAAU,cAAc,OAAO,CAAC,MAAM;;;;;;;gDACxC,cAAc,OAAO,CAAC,QAAQ,kBAC7B,6LAAC;;wDAAE;wDAAM,IAAI,KAAK,cAAc,OAAO,CAAC,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;wBAO7E,CAAC,cAAc,UAAU,IAAI,YAAY,MAAM,KAAK,mCACnD,6LAAC;4BACC,SAAS;gCACP,oBAAoB;gCACpB,gBAAgB,OAAO,4BAA4B;gCACnD,kBAAkB,OAAO,6BAA6B;gCACtD;4BACF;4BACA,WAAU;;8CAEV,6LAAC;oCAAE,WAAU;;;;;;gCAAuB;;;;;;;;;;;;;;;;;;YAQ3C,YAAY,MAAM,GAAG,mBACpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAE,WAAU;;;;;;4BAA6C;4BACnC,YAAY,MAAM;4BAAC;;;;;;;oBAG3C,gCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAmC;;;;;;;;;;;iFAGrD,6LAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,SAAS,sBACzB,6LAAC;gCAEC,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;;4DAA+C;4DACjD,QAAQ,cAAc,IAAI;;;;;;;kEAEtC,6LAAC;wDAAK,WAAW,AAAC,2EAMjB,OALC,QAAQ,MAAM,KAAK,SACf,yEACA,QAAQ,MAAM,KAAK,YACnB,iEACA;kEAEF,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,MAAM;;;;;;;;;;;;0DAIjC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAc;4DAAG,QAAQ,MAAM,IAAI;;;;;;;kEAEnE,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAe;4DAC3C,QAAQ,UAAU,GACd,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB,KAC/C;;;;;;;kEAGR,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAgB;4DAC5C,QAAQ,QAAQ,GACZ,IAAI,KAAK,QAAQ,QAAQ,EAAE,kBAAkB,KAC7C;;;;;;;;;;;;;4CAKT,QAAQ,WAAW,kBAClB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAc;;;;;;oDAAmB;oDAAE,QAAQ,WAAW;;;;;;;;;;;;;;;;;;+BA1CzE,QAAQ,UAAU,IAAI;;;;;;;;;;;;;;;;YAuDtC,kCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA4D;;;;;;oBAIzE,gCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAmC;;;;;;;;;;;iFAGrD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAAkE;0DACrE,6LAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAE7C,6LAAC;wCACC,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC9E,MAAM;wCACN,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAKhB,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAA6D;;;;;;0DAG9E,6LAAC;gDACC,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC;wDAAE,WAAU;;;;;;oDAAuB;;;;;;;;;;;;;kDAKxC,6LAAC;wCAAI,WAAU;kDACZ,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACzB,6LAAC;gDAAuB,WAAU;;kEAEhC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,6LAAC;wEACC,MAAK;wEACL,OAAO,KAAK,WAAW;wEACvB,UAAU,CAAC,IAAM,kBAAkB,OAAO,eAAe,EAAE,MAAM,CAAC,KAAK;wEACvE,WAAU;wEACV,aAAY;;;;;;;;;;;;0EAGhB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;0FACC,6LAAC;gFAAM,WAAU;0FAAkE;;;;;;0FAGnF,6LAAC;gFACC,MAAK;gFACL,KAAI;gFACJ,OAAO,KAAK,QAAQ;gFACpB,UAAU,CAAC,IAAM,kBAAkB,OAAO,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gFAClF,WAAU;;;;;;;;;;;;kFAGd,6LAAC;;0FACC,6LAAC;gFAAM,WAAU;0FAAkE;;;;;;0FAGnF,6LAAC;gFACC,MAAK;gFACL,MAAK;gFACL,KAAI;gFACJ,OAAO,KAAK,UAAU;gFACtB,UAAU,CAAC,IAAM,kBAAkB,OAAO,cAAc,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gFACtF,WAAU;;;;;;;;;;;;;;;;;;0EAIhB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;;4EAAuD;4EACzD,CAAC,KAAK,QAAQ,GAAG,KAAK,UAAU,EAAE,OAAO,CAAC;;;;;;;kFAExD,6LAAC;wEACC,MAAK;wEACL,SAAS,IAAM,kBAAkB;wEACjC,WAAU;;0FAEV,6LAAC;gFAAE,WAAU;;;;;;4EAAsC;;;;;;;;;;;;;;;;;;;kEAOzD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAM,WAAU;sFAAkE;;;;;;sFAGnF,6LAAC;4EACC,MAAK;4EACL,OAAO,KAAK,WAAW;4EACvB,UAAU,CAAC,IAAM,kBAAkB,OAAO,eAAe,EAAE,MAAM,CAAC,KAAK;4EACvE,WAAU;4EACV,aAAY;;;;;;;;;;;;8EAGhB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAM,WAAU;sFAAkE;;;;;;sFAGnF,6LAAC;4EACC,MAAK;4EACL,KAAI;4EACJ,OAAO,KAAK,QAAQ;4EACpB,UAAU,CAAC,IAAM,kBAAkB,OAAO,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4EAClF,WAAU;;;;;;;;;;;;8EAGd,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAM,WAAU;sFAAkE;;;;;;sFAGnF,6LAAC;4EACC,MAAK;4EACL,MAAK;4EACL,KAAI;4EACJ,OAAO,KAAK,UAAU;4EACtB,UAAU,CAAC,IAAM,kBAAkB,OAAO,cAAc,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4EACtF,WAAU;;;;;;;;;;;;8EAGd,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAM,WAAU;sFAAkE;;;;;;sFAGnF,6LAAC;4EAAI,WAAU;;gFAA4G;gFACvH,CAAC,KAAK,QAAQ,GAAG,KAAK,UAAU,EAAE,OAAO,CAAC;;;;;;;;;;;;;8EAGhD,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEACC,MAAK;wEACL,SAAS,IAAM,kBAAkB;wEACjC,WAAU;wEACV,OAAM;kFAEN,cAAA,6LAAC;4EAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CAhHb,KAAK,OAAO;;;;;;;;;;kDA0H1B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAyD;;;;;;8DAGzE,6LAAC;oDAAK,WAAU;;wDAAqD;wDACjE,SAAS,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,QAAQ,GAAG,KAAK,UAAU,EAAG,GAAG,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAMnG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;4CACP,oBAAoB;4CACpB,YAAY;gDAAE,QAAQ;gDAAI,aAAa;gDAAI,OAAO,EAAE;4CAAC;4CACrD,gBAAgB,OAAO,mBAAmB;4CAC1C,kBAAkB,OAAO,6BAA6B;wCACxD;wCACA,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS;wCACT,UAAU,cAAc,CAAC,SAAS,WAAW,IAAI,SAAS,KAAK,CAAC,MAAM,KAAK;wCAC3E,WAAU;kDAET,2BACC;;8DACE,6LAAC;oDAAE,WAAU;;;;;;gDAAyC;;yEAIxD;;8DACE,6LAAC;oDAAE,WAAU;;;;;;gDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3D;GAvoBM;KAAA;uCAyoBS", "debugId": null}}, {"offset": {"line": 9469, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/tasks/ApplicationTaskComponent.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { applicationService } from '@/services/applicationService';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { Application, Task } from '@/types';\r\nimport ActivityHistory from '../common/ActivityHistory';\r\nimport StatusCard from '../customer/StatusCard';\r\nimport { ApplicantInfoCard, LegalHistoryCard, ManagementCard, EquipmentDetailsCard, ContactInfoCard, AddressInfoCard, DocumentCard } from '../evaluation';\r\nimport ApplicationStatusButtons from '../evaluation/ApplicationStatusButtons';\r\nimport InvoiceStatusCard from '../evaluation/InvoiceStatusCard';\r\n\r\ninterface ApplicationTaskComponentProps {\r\n  task: Task;\r\n  onTaskUpdate: () => void;\r\n}\r\n\r\nconst ApplicationTaskComponent: React.FC<ApplicationTaskComponentProps> = ({ task, onTaskUpdate }) => {\r\n  const { showToast } = useToast();\r\n  const [application, setApplication] = useState<Application | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    if (task.entity_type === 'application' && task.entity_id) {\r\n      loadApplication();\r\n    }\r\n  }, [task]);\r\n\r\n  const loadApplication = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await applicationService.getApplication(task.entity_id!);\r\n      if (response) {\r\n        setApplication(response);\r\n      } else {\r\n        throw new Error('Failed to load application');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading application:', error);\r\n      showToast('Failed to load application details', 'error');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleOpenEvaluation = () => {\r\n    if (application) {\r\n      // Open the evaluation interface in a new tab\r\n      const evaluationUrl = `/evaluation/${application.id}`;\r\n      window.open(evaluationUrl, '_blank');\r\n    }\r\n  };\r\n\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center py-8\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mr-3\"></div>\r\n        <span className=\"text-gray-600 dark:text-gray-400\">Loading application details...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!application) {\r\n    return (\r\n      <div className=\"text-center py-8\">\r\n        <div className=\"bg-yellow-100 dark:bg-yellow-900/20 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center\">\r\n          <i className=\"ri-error-warning-line text-2xl text-yellow-600 dark:text-yellow-400\"></i>\r\n        </div>\r\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">Application Not Found</h3>\r\n        <p className=\"text-gray-600 dark:text-gray-400\">\r\n          The application associated with this task could not be found.\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <StatusCard title={'Application Status'} value={application.status} bgColor={'text-blue-200'} iconBgColor={'bg-blue-200'} iconTextColor={''} linkText={''} linkHref={''} icon={undefined} ></StatusCard>\r\n\r\n      {/* Applicant Information Card */}\r\n      <ApplicantInfoCard\r\n        applicant={application.applicant ?? null}\r\n        className=\"mb-6\"\r\n        showEmptyFields={false}\r\n      />\r\n\r\n      <ContactInfoCard application={application}></ContactInfoCard>\r\n      <AddressInfoCard application={application}></AddressInfoCard>\r\n      <ManagementCard application={application}></ManagementCard>\r\n      <LegalHistoryCard application={application}></LegalHistoryCard>\r\n      <EquipmentDetailsCard application={application}></EquipmentDetailsCard>\r\n      <DocumentCard application={application}></DocumentCard>\r\n\r\n      {/* Invoice Status - Always visible for invoice generation */}\r\n      <div className=\"mb-6\">\r\n        <InvoiceStatusCard\r\n          application={application!}\r\n          onInvoiceGenerated={(invoice) => {\r\n            console.log('Invoice generated:', invoice);\r\n          }}\r\n          onStatusChange={(status) => {\r\n            // Update invoice status when it changes\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      {/* Activity History */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\r\n        <ActivityHistory\r\n          entityType=\"application\"\r\n          entityId={application.application_id ?? \"\"}\r\n          title=\"Activity History\"\r\n          showSearch={true}\r\n          showFilters={false}\r\n          maxHeight=\"max-h-96\"\r\n          className=\"border-0 rounded-none\"\r\n        />\r\n      </div>\r\n\r\n      <ApplicationStatusButtons\r\n        application={application}\r\n        onStatusChange={(newStatus, updatedApplication) => {\r\n          // Update the application state with new status\r\n          setApplication(updatedApplication);\r\n          loadApplication();\r\n        }}\r\n      />\r\n              \r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ApplicationTaskComponent;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAVA;;;;;;;;;AAiBA,MAAM,2BAAoE;QAAC,EAAE,IAAI,EAAE,YAAY,EAAE;;IAC/F,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,IAAI,KAAK,WAAW,KAAK,iBAAiB,KAAK,SAAS,EAAE;gBACxD;YACF;QACF;6CAAG;QAAC;KAAK;IAET,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,wIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,KAAK,SAAS;YACvE,IAAI,UAAU;gBACZ,eAAe;YACjB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,UAAU,sCAAsC;QAClD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,aAAa;YACf,6CAA6C;YAC7C,MAAM,gBAAgB,AAAC,eAA6B,OAAf,YAAY,EAAE;YACnD,OAAO,IAAI,CAAC,eAAe;QAC7B;IACF;IAGA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BAAmC;;;;;;;;;;;;IAGzD;IAEA,IAAI,CAAC,aAAa;QAChB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;;;;;;;;;;8BAEf,6LAAC;oBAAG,WAAU;8BAA4D;;;;;;8BAC1E,6LAAC;oBAAE,WAAU;8BAAmC;;;;;;;;;;;;IAKtD;QAQiB,wBA6BC;IAnClB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+IAAA,CAAA,UAAU;gBAAC,OAAO;gBAAsB,OAAO,YAAY,MAAM;gBAAE,SAAS;gBAAiB,aAAa;gBAAe,eAAe;gBAAI,UAAU;gBAAI,UAAU;gBAAI,MAAM;;;;;;0BAG/K,6LAAC,wMAAA,CAAA,oBAAiB;gBAChB,WAAW,CAAA,yBAAA,YAAY,SAAS,cAArB,oCAAA,yBAAyB;gBACpC,WAAU;gBACV,iBAAiB;;;;;;0BAGnB,6LAAC,oMAAA,CAAA,kBAAe;gBAAC,aAAa;;;;;;0BAC9B,6LAAC,oMAAA,CAAA,kBAAe;gBAAC,aAAa;;;;;;0BAC9B,6LAAC,kMAAA,CAAA,iBAAc;gBAAC,aAAa;;;;;;0BAC7B,6LAAC,sMAAA,CAAA,mBAAgB;gBAAC,aAAa;;;;;;0BAC/B,6LAAC,8MAAA,CAAA,uBAAoB;gBAAC,aAAa;;;;;;0BACnC,6LAAC,8LAAA,CAAA,eAAY;gBAAC,aAAa;;;;;;0BAG3B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,wJAAA,CAAA,UAAiB;oBAChB,aAAa;oBACb,oBAAoB,CAAC;wBACnB,QAAQ,GAAG,CAAC,sBAAsB;oBACpC;oBACA,gBAAgB,CAAC;oBACf,wCAAwC;oBAC1C;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,kJAAA,CAAA,UAAe;oBACd,YAAW;oBACX,UAAU,CAAA,8BAAA,YAAY,cAAc,cAA1B,yCAAA,8BAA8B;oBACxC,OAAM;oBACN,YAAY;oBACZ,aAAa;oBACb,WAAU;oBACV,WAAU;;;;;;;;;;;0BAId,6LAAC,+JAAA,CAAA,UAAwB;gBACvB,aAAa;gBACb,gBAAgB,CAAC,WAAW;oBAC1B,+CAA+C;oBAC/C,eAAe;oBACf;gBACF;;;;;;;;;;;;AAKR;GAnHM;;QACkB,mIAAA,CAAA,WAAQ;;;KAD1B;uCAqHS", "debugId": null}}, {"offset": {"line": 9750, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/tasks/GeneralTaskComponent.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { Task } from '@/services/task-assignment';\r\nimport { taskAssignmentService } from '@/services/task-assignment';\r\nimport { useToast } from '@/contexts/ToastContext';\r\n\r\ninterface GeneralTaskComponentProps {\r\n  task: Task;\r\n  onTaskUpdate: () => void;\r\n}\r\n\r\nconst GeneralTaskComponent: React.FC<GeneralTaskComponentProps> = ({ task, onTaskUpdate }) => {\r\n  const { showToast } = useToast();\r\n  const [updating, setUpdating] = useState(false);\r\n  const [notes, setNotes] = useState('');\r\n\r\n  const handleCompleteTask = async () => {\r\n    try {\r\n      setUpdating(true);\r\n      \r\n      const response = await taskAssignmentService.updateTask(task.task_id, {\r\n        status: 'completed',\r\n        completion_notes: notes || undefined,\r\n      });\r\n      \r\n      if (response.success) {\r\n        showToast('Task completed successfully', 'success');\r\n        onTaskUpdate();\r\n      } else {\r\n        throw new Error(response.message || 'Failed to complete task');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error completing task:', error);\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to complete task';\r\n      showToast(errorMessage, 'error');\r\n    } finally {\r\n      setUpdating(false);\r\n    }\r\n  };\r\n\r\n  const handleUpdateStatus = async (status: string) => {\r\n    try {\r\n      setUpdating(true);\r\n      \r\n      const response = await taskAssignmentService.updateTask(task.task_id, {\r\n        status,\r\n        review_notes: notes || undefined,\r\n      });\r\n      \r\n      if (response.success) {\r\n        showToast(`Task status updated to ${status.replace('_', ' ')}`, 'success');\r\n        onTaskUpdate();\r\n      } else {\r\n        throw new Error(response.message || 'Failed to update task status');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating task status:', error);\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update task status';\r\n      showToast(errorMessage, 'error');\r\n    } finally {\r\n      setUpdating(false);\r\n    }\r\n  };\r\n\r\n  const getTaskTypeIcon = (taskType: string) => {\r\n    switch (taskType) {\r\n      case 'complaint': return 'ri-feedback-line';\r\n      case 'data_breach': return 'ri-shield-cross-line';\r\n      case 'evaluation': return 'ri-search-line';\r\n      case 'inspection': return 'ri-search-eye-line';\r\n      case 'document_review': return 'ri-file-text-line';\r\n      case 'compliance_check': return 'ri-shield-check-line';\r\n      case 'follow_up': return 'ri-phone-line';\r\n      default: return 'ri-task-line';\r\n    }\r\n  };\r\n\r\n  const getTaskTypeColor = (taskType: string) => {\r\n    switch (taskType) {\r\n      case 'complaint': return 'text-red-600 bg-red-100';\r\n      case 'data_breach': return 'text-red-600 bg-red-100';\r\n      case 'evaluation': return 'text-purple-600 bg-purple-100';\r\n      case 'inspection': return 'text-orange-600 bg-orange-100';\r\n      case 'document_review': return 'text-blue-600 bg-blue-100';\r\n      case 'compliance_check': return 'text-green-600 bg-green-100';\r\n      case 'follow_up': return 'text-yellow-600 bg-yellow-100';\r\n      default: return 'text-gray-600 bg-gray-100';\r\n    }\r\n  };\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status.toLowerCase()) {\r\n      case 'pending': return 'bg-yellow-100 text-yellow-800';\r\n      case 'in_progress': return 'bg-blue-100 text-blue-800';\r\n      case 'completed': return 'bg-green-100 text-green-800';\r\n      case 'cancelled': return 'bg-red-100 text-red-800';\r\n      case 'on_hold': return 'bg-gray-100 text-gray-800';\r\n      default: return 'bg-gray-100 text-gray-800';\r\n    }\r\n  };\r\n\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority.toLowerCase()) {\r\n      case 'low': return 'bg-green-100 text-green-800';\r\n      case 'medium': return 'bg-yellow-100 text-yellow-800';\r\n      case 'high': return 'bg-orange-100 text-orange-800';\r\n      case 'urgent': return 'bg-red-100 text-red-800';\r\n      default: return 'bg-gray-100 text-gray-800';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Task Overview */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm\">\r\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\r\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\">\r\n            <div className={`p-2 rounded-lg mr-3 ${getTaskTypeColor(task.task_type)}`}>\r\n              <i className={`${getTaskTypeIcon(task.task_type)} text-lg`}></i>\r\n            </div>\r\n            Task Details\r\n          </h2>\r\n        </div>\r\n        <div className=\"p-6\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n            <div>\r\n              <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400 mb-3\">Task Information</h3>\r\n              <div className=\"space-y-3\">\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Task Number:</span>\r\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{task.task_number}</span>\r\n                </div>\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Type:</span>\r\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100 capitalize\">\r\n                    {task.task_type.replace('_', ' ')}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Status:</span>\r\n                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>\r\n                    {task.status.replace('_', ' ').toUpperCase()}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Priority:</span>\r\n                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>\r\n                    {task.priority.toUpperCase()}\r\n                  </span>\r\n                </div>\r\n                {task.due_date && (\r\n                  <div className=\"flex justify-between\">\r\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Due Date:</span>\r\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                      {new Date(task.due_date).toLocaleDateString()}\r\n                    </span>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n            \r\n            <div>\r\n              <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400 mb-3\">Assignment Information</h3>\r\n              <div className=\"space-y-3\">\r\n                {task.assignee && (\r\n                  <div className=\"flex justify-between\">\r\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Assigned To:</span>\r\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                      {task.assignee.first_name} {task.assignee.last_name}\r\n                    </span>\r\n                  </div>\r\n                )}\r\n                {task.assigner && (\r\n                  <div className=\"flex justify-between\">\r\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Assigned By:</span>\r\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                      {task.assigner.first_name} {task.assigner.last_name}\r\n                    </span>\r\n                  </div>\r\n                )}\r\n                {task.assigned_at && (\r\n                  <div className=\"flex justify-between\">\r\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Assigned At:</span>\r\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                      {new Date(task.assigned_at).toLocaleDateString()}\r\n                    </span>\r\n                  </div>\r\n                )}\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Created:</span>\r\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                    {new Date(task.created_at).toLocaleDateString()}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Task Description */}\r\n          {task.description && (\r\n            <div className=\"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700\">\r\n              <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400 mb-2\">Description</h3>\r\n              <p className=\"text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap\">\r\n                {task.description}\r\n              </p>\r\n            </div>\r\n          )}\r\n\r\n          {/* Entity Information */}\r\n          {task.entity_type && task.entity_id && (\r\n            <div className=\"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700\">\r\n              <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400 mb-2\">Related Entity</h3>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Type:</span>\r\n                <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100 capitalize\">\r\n                  {task.entity_type}\r\n                </span>\r\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">•</span>\r\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">ID:</span>\r\n                <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                  {task.entity_id}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Task Actions */}\r\n      {task.status !== 'completed' && task.status !== 'cancelled' && (\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm\">\r\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\r\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\">\r\n              <i className=\"ri-tools-line mr-2 text-purple-600\"></i>\r\n              Task Actions\r\n            </h2>\r\n          </div>\r\n          <div className=\"p-6\">\r\n            {/* Notes/Comments */}\r\n            <div className=\"mb-6\">\r\n              <label htmlFor=\"notes\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Notes/Comments\r\n              </label>\r\n              <textarea\r\n                id=\"notes\"\r\n                rows={4}\r\n                value={notes}\r\n                onChange={(e) => setNotes(e.target.value)}\r\n                className=\"block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-100\"\r\n                placeholder=\"Add notes or comments about this task...\"\r\n              />\r\n            </div>\r\n\r\n            {/* Action Buttons */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n              {task.status === 'pending' && (\r\n                <button\r\n                  onClick={() => handleUpdateStatus('in_progress')}\r\n                  disabled={updating}\r\n                  className=\"flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\"\r\n                >\r\n                  {updating ? (\r\n                    <>\r\n                      <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                      Starting...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <i className=\"ri-play-line mr-2\"></i>\r\n                      Start Task\r\n                    </>\r\n                  )}\r\n                </button>\r\n              )}\r\n\r\n              {task.status === 'in_progress' && (\r\n                <>\r\n                  <button\r\n                    onClick={handleCompleteTask}\r\n                    disabled={updating}\r\n                    className=\"flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50\"\r\n                  >\r\n                    {updating ? (\r\n                      <>\r\n                        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                        Completing...\r\n                      </>\r\n                    ) : (\r\n                      <>\r\n                        <i className=\"ri-check-line mr-2\"></i>\r\n                        Complete Task\r\n                      </>\r\n                    )}\r\n                  </button>\r\n\r\n                  <button\r\n                    onClick={() => handleUpdateStatus('on_hold')}\r\n                    disabled={updating}\r\n                    className=\"flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\"\r\n                  >\r\n                    <i className=\"ri-pause-line mr-2\"></i>\r\n                    Put On Hold\r\n                  </button>\r\n                </>\r\n              )}\r\n\r\n              {task.status === 'on_hold' && (\r\n                <button\r\n                  onClick={() => handleUpdateStatus('in_progress')}\r\n                  disabled={updating}\r\n                  className=\"flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\"\r\n                >\r\n                  <i className=\"ri-play-line mr-2\"></i>\r\n                  Resume Task\r\n                </button>\r\n              )}\r\n\r\n              <button\r\n                onClick={() => handleUpdateStatus('cancelled')}\r\n                disabled={updating}\r\n                className=\"flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50\"\r\n              >\r\n                <i className=\"ri-close-line mr-2\"></i>\r\n                Cancel Task\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Task History/Notes */}\r\n      {(task.review || task.review_notes || task.completion_notes) && (\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm\">\r\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\r\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\">\r\n              <i className=\"ri-history-line mr-2 text-gray-600\"></i>\r\n              Task History\r\n            </h2>\r\n          </div>\r\n          <div className=\"p-6 space-y-4\">\r\n            {task.review && (\r\n              <div>\r\n                <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400 mb-2\">Review</h3>\r\n                <p className=\"text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap\">\r\n                  {task.review}\r\n                </p>\r\n              </div>\r\n            )}\r\n            {task.review_notes && (\r\n              <div>\r\n                <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400 mb-2\">Review Notes</h3>\r\n                <p className=\"text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap\">\r\n                  {task.review_notes}\r\n                </p>\r\n              </div>\r\n            )}\r\n            {task.completion_notes && (\r\n              <div>\r\n                <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400 mb-2\">Completion Notes</h3>\r\n                <p className=\"text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap\">\r\n                  {task.completion_notes}\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default GeneralTaskComponent;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;;;AALA;;;;AAYA,MAAM,uBAA4D;QAAC,EAAE,IAAI,EAAE,YAAY,EAAE;;IACvF,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,qBAAqB;QACzB,IAAI;YACF,YAAY;YAEZ,MAAM,WAAW,MAAM,wIAAA,CAAA,wBAAqB,CAAC,UAAU,CAAC,KAAK,OAAO,EAAE;gBACpE,QAAQ;gBACR,kBAAkB,SAAS;YAC7B;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,UAAU,+BAA+B;gBACzC;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,OAAO,IAAI;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,UAAU,cAAc;QAC1B,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,YAAY;YAEZ,MAAM,WAAW,MAAM,wIAAA,CAAA,wBAAqB,CAAC,UAAU,CAAC,KAAK,OAAO,EAAE;gBACpE;gBACA,cAAc,SAAS;YACzB;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,UAAU,AAAC,0BAAkD,OAAzB,OAAO,OAAO,CAAC,KAAK,OAAQ;gBAChE;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,OAAO,IAAI;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,UAAU,cAAc;QAC1B,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAmB,OAAO;YAC/B,KAAK;gBAAoB,OAAO;YAChC,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAmB,OAAO;YAC/B,KAAK;gBAAoB,OAAO;YAChC,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,OAAO,WAAW;YACxB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAI,WAAW,AAAC,uBAAuD,OAAjC,iBAAiB,KAAK,SAAS;8CACpE,cAAA,6LAAC;wCAAE,WAAW,AAAC,GAAkC,OAAhC,gBAAgB,KAAK,SAAS,GAAE;;;;;;;;;;;gCAC7C;;;;;;;;;;;;kCAIV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAC1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAU;0EAAwD,KAAK,WAAW;;;;;;;;;;;;kEAE1F,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAU;0EACb,KAAK,SAAS,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;kEAGjC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAW,AAAC,2EAAsG,OAA5B,eAAe,KAAK,MAAM;0EACnH,KAAK,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;;;;;;;kEAG9C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAW,AAAC,2EAA0G,OAAhC,iBAAiB,KAAK,QAAQ;0EACvH,KAAK,QAAQ,CAAC,WAAW;;;;;;;;;;;;oDAG7B,KAAK,QAAQ,kBACZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAU;0EACb,IAAI,KAAK,KAAK,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;kDAOrD,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAC1E,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,QAAQ,kBACZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAU;;oEACb,KAAK,QAAQ,CAAC,UAAU;oEAAC;oEAAE,KAAK,QAAQ,CAAC,SAAS;;;;;;;;;;;;;oDAIxD,KAAK,QAAQ,kBACZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAU;;oEACb,KAAK,QAAQ,CAAC,UAAU;oEAAC;oEAAE,KAAK,QAAQ,CAAC,SAAS;;;;;;;;;;;;;oDAIxD,KAAK,WAAW,kBACf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAU;0EACb,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB;;;;;;;;;;;;kEAIpD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAU;0EACb,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQtD,KAAK,WAAW,kBACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAC1E,6LAAC;wCAAE,WAAU;kDACV,KAAK,WAAW;;;;;;;;;;;;4BAMtB,KAAK,WAAW,IAAI,KAAK,SAAS,kBACjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAC1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,6LAAC;gDAAK,WAAU;0DACb,KAAK,WAAW;;;;;;0DAEnB,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,6LAAC;gDAAK,WAAU;0DACb,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS1B,KAAK,MAAM,KAAK,eAAe,KAAK,MAAM,KAAK,6BAC9C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAE,WAAU;;;;;;gCAAyC;;;;;;;;;;;;kCAI1D,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,SAAQ;wCAAQ,WAAU;kDAAkE;;;;;;kDAGnG,6LAAC;wCACC,IAAG;wCACH,MAAM;wCACN,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAKhB,6LAAC;gCAAI,WAAU;;oCACZ,KAAK,MAAM,KAAK,2BACf,6LAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,UAAU;wCACV,WAAU;kDAET,yBACC;;8DACE,6LAAC;oDAAI,WAAU;;;;;;gDAAuE;;yEAIxF;;8DACE,6LAAC;oDAAE,WAAU;;;;;;gDAAwB;;;;;;;;oCAO5C,KAAK,MAAM,KAAK,+BACf;;0DACE,6LAAC;gDACC,SAAS;gDACT,UAAU;gDACV,WAAU;0DAET,yBACC;;sEACE,6LAAC;4DAAI,WAAU;;;;;;wDAAuE;;iFAIxF;;sEACE,6LAAC;4DAAE,WAAU;;;;;;wDAAyB;;;;;;;;0DAM5C,6LAAC;gDACC,SAAS,IAAM,mBAAmB;gDAClC,UAAU;gDACV,WAAU;;kEAEV,6LAAC;wDAAE,WAAU;;;;;;oDAAyB;;;;;;;;;oCAM3C,KAAK,MAAM,KAAK,2BACf,6LAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,UAAU;wCACV,WAAU;;0DAEV,6LAAC;gDAAE,WAAU;;;;;;4CAAwB;;;;;;;kDAKzC,6LAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,UAAU;wCACV,WAAU;;0DAEV,6LAAC;gDAAE,WAAU;;;;;;4CAAyB;;;;;;;;;;;;;;;;;;;;;;;;;YAS/C,CAAC,KAAK,MAAM,IAAI,KAAK,YAAY,IAAI,KAAK,gBAAgB,mBACzD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAE,WAAU;;;;;;gCAAyC;;;;;;;;;;;;kCAI1D,6LAAC;wBAAI,WAAU;;4BACZ,KAAK,MAAM,kBACV,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAC1E,6LAAC;wCAAE,WAAU;kDACV,KAAK,MAAM;;;;;;;;;;;;4BAIjB,KAAK,YAAY,kBAChB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAC1E,6LAAC;wCAAE,WAAU;kDACV,KAAK,YAAY;;;;;;;;;;;;4BAIvB,KAAK,gBAAgB,kBACpB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAC1E,6LAAC;wCAAE,WAAU;kDACV,KAAK,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC;GAtWM;;QACkB,mIAAA,CAAA,WAAQ;;;KAD1B;uCAwWS", "debugId": null}}, {"offset": {"line": 10661, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/tasks/%5BtaskId%5D/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useParams, useRouter } from 'next/navigation';\r\nimport { taskAssignmentService, taskService } from '@/services/task-assignment';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport PaymentTaskComponent from '@/components/tasks/PaymentTaskComponent';\r\nimport ApplicationTaskComponent from '@/components/tasks/ApplicationTaskComponent';\r\nimport GeneralTaskComponent from '@/components/tasks/GeneralTaskComponent';\r\nimport ConfirmationModal from '@/components/common/ConfirmationModal';\r\nimport { Task, TaskType } from '@/types';\r\nimport ActivityNotesModal from '@/components/evaluation/ActivityNotesModal';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\n\r\nexport default function TaskViewPage() {\r\n  const params = useParams();\r\n  const router = useRouter();\r\n  const { showToast } = useToast();\r\n  const { user } = useAuth();\r\n  const taskId = params.taskId as string;\r\n\r\n  const [task, setTask] = useState<Task | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [closingTask, setClosingTask] = useState(false);\r\n  const [isCommunicationModalOpen, setIsCommunicationModalOpen] = useState(false);\r\n  const [showCloseTaskModal, setShowCloseTaskModal] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (taskId) {\r\n      loadTask();\r\n    }\r\n  }, [taskId]);\r\n\r\n  const loadTask = async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);      \r\n      const task = await taskAssignmentService.getTaskById(taskId);\r\n      setTask(task);\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to load task';\r\n      showToast(errorMessage, 'error');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleTaskUpdate = async () => {\r\n    // Reload task data after updates\r\n    await loadTask();\r\n  };\r\n\r\n  const handleCloseTaskClick = () => {\r\n    setShowCloseTaskModal(true);\r\n  };\r\n\r\n  const handleCloseTask = async () => {\r\n    if (!task) return;\r\n\r\n    try {\r\n      setClosingTask(true);\r\n\r\n      const response = await taskService.updateTask(task.task_id, {\r\n        status: 'completed'\r\n      });\r\n\r\n      if (response) {\r\n        showToast('Task closed successfully', 'success');\r\n        await loadTask(); // Reload to show updated status\r\n        setShowCloseTaskModal(false);\r\n      } else {\r\n        throw new Error('Failed to close task');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error closing task:', error);\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to close task';\r\n      showToast(errorMessage, 'error');\r\n    } finally {\r\n      setClosingTask(false);\r\n    }\r\n  };\r\n\r\n  const handleEmailClient = () => {\r\n    setIsCommunicationModalOpen(true);\r\n  };\r\n\r\n  const renderTaskComponent = () => {\r\n    if (!task) return null;\r\n\r\n    switch (task.task_type) {\r\n      case TaskType.PAYMENT_VERIFICATION:\r\n        return (\r\n          <PaymentTaskComponent\r\n            task={task}\r\n            onTaskUpdate={handleTaskUpdate}\r\n          />\r\n        );\r\n\r\n      case 'application':\r\n      case 'evaluation':\r\n        return (\r\n          <ApplicationTaskComponent\r\n            task={task}\r\n            onTaskUpdate={handleTaskUpdate}\r\n          />\r\n        );\r\n\r\n      default:\r\n        return (\r\n          <GeneralTaskComponent\r\n            task={task}\r\n            onTaskUpdate={handleTaskUpdate}\r\n          />\r\n        );\r\n    }\r\n  };\r\n\r\n  const getTaskTypeIcon = (taskType: string) => {\r\n    switch (taskType) {\r\n      case 'payment_verification': return 'ri-money-dollar-circle-line';\r\n      case 'application': return 'ri-file-list-line';\r\n      case 'evaluation': return 'ri-search-line';\r\n      case 'document_review': return 'ri-file-text-line';\r\n      case 'compliance_check': return 'ri-shield-check-line';\r\n      default: return 'ri-task-line';\r\n    }\r\n  };\r\n\r\n  const getTaskTypeColor = (taskType: string) => {\r\n    switch (taskType) {\r\n      case 'payment_verification': return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';\r\n      case 'application': return 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900/20';\r\n      case 'evaluation': return 'text-purple-600 bg-purple-100 dark:text-purple-400 dark:bg-purple-900/20';\r\n      case 'document_review': return 'text-orange-600 bg-orange-100 dark:text-orange-400 dark:bg-orange-900/20';\r\n      case 'compliance_check': return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';\r\n      default: return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-700';\r\n    }\r\n  };\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status.toLowerCase()) {\r\n      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';\r\n      case 'in_progress': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';\r\n      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';\r\n      case 'cancelled': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';\r\n      case 'on_hold': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';\r\n    }\r\n  };\r\n\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority.toLowerCase()) {\r\n      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';\r\n      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';\r\n      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';\r\n      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center py-20\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4\"></div>\r\n          <p className=\"text-gray-600 dark:text-gray-400\">Loading task...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error || !task) {\r\n    return (\r\n      <div className=\"flex items-center justify-center py-20\">\r\n        <div className=\"text-center max-w-md mx-auto\">\r\n          <div className=\"bg-red-100 dark:bg-red-900/20 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center\">\r\n            <i className=\"ri-error-warning-line text-2xl text-red-600 dark:text-red-400\"></i>\r\n          </div>\r\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2\">Task Not Found</h2>\r\n          <p className=\"text-gray-600 dark:text-gray-400 mb-6\">\r\n            {error || 'The requested task could not be found.'}\r\n          </p>\r\n          <button\r\n            onClick={() => router.back()}\r\n            className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors\"\r\n          >\r\n            <i className=\"ri-arrow-left-line mr-2\"></i>\r\n            Go Back\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"p-6\">\r\n      {/* Breadcrumb */}\r\n      <nav className=\"flex mb-6\" aria-label=\"Breadcrumb\">\r\n        <ol className=\"inline-flex items-center space-x-1 md:space-x-3\">\r\n          <li className=\"inline-flex items-center\">\r\n            <a\r\n              href=\"/\"\r\n              className=\"inline-flex items-center text-sm font-medium text-gray-700 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400\"\r\n            >\r\n              <i className=\"ri-home-line mr-2\"></i>\r\n              Admin\r\n            </a>\r\n          </li>\r\n          <li>\r\n            <div className=\"flex items-center\">\r\n              <i className=\"ri-arrow-right-s-line text-gray-400\"></i>\r\n              <a\r\n                href=\"/tasks\"\r\n                className=\"ml-1 text-sm font-medium text-gray-700 hover:text-red-600 md:ml-2 dark:text-gray-400 dark:hover:text-red-400\"\r\n              >\r\n                Tasks\r\n              </a>\r\n            </div>\r\n          </li>\r\n          <li aria-current=\"page\">\r\n            <div className=\"flex items-center\">\r\n              <i className=\"ri-arrow-right-s-line text-gray-400\"></i>\r\n              <span className=\"ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400\">\r\n                {task.title}\r\n              </span>\r\n            </div>\r\n          </li>\r\n        </ol>\r\n      </nav>\r\n\r\n      {/* Task Header Card */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6\">\r\n        <div className=\"px-6 py-4\">\r\n          {/* Mobile-first responsive layout */}\r\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\">\r\n            {/* Left section - Task info */}\r\n            <div className=\"flex items-center space-x-4\">\r\n              <button\r\n                onClick={() => router.back()}\r\n                className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                title=\"Go back\"\r\n              >\r\n                <i className=\"ri-arrow-left-line text-xl\"></i>\r\n              </button>\r\n              <div className=\"flex items-center space-x-4\">\r\n                <div className={`p-3 rounded-lg ${getTaskTypeColor(task.task_type)}`}>\r\n                  <i className={`${getTaskTypeIcon(task.task_type)} text-xl`}></i>\r\n                </div>\r\n                <div>\r\n                  <h1 className=\"text-xl lg:text-2xl font-bold text-gray-900 dark:text-gray-100\">\r\n                    {task.title}\r\n                  </h1>\r\n                  <div className=\"flex flex-wrap items-center gap-2 lg:gap-4 mt-1\">\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                      Task #{task.task_number}\r\n                    </p>\r\n                    {task.assigned_to && (\r\n                      <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                        Assigned to: {task.assignee?.first_name}\r\n                      </p>\r\n                    )}\r\n                    {task.due_date && (\r\n                      <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                        Due: {new Date(task.due_date).toLocaleDateString()}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Right section - Status, badges, and actions */}\r\n            <div className=\"flex flex-col sm:flex-row items-start sm:items-center gap-3\">\r\n              {/* Status and Priority badges */}\r\n              <div className=\"flex items-center space-x-2\">\r\n                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(task.status)}`}>\r\n                  {task.status.replace('_', ' ').toUpperCase()}\r\n                </span>\r\n              </div>\r\n\r\n              {/* Action buttons */}\r\n              <div className=\"flex items-center space-x-2\">\r\n                {/* Email Client Button - Show for application, invoice, and payment tasks */}\r\n                {(task.entity_type === 'application' || task.entity_type === 'invoice' || task.entity_type === 'payment' ||\r\n                  task.task_type === 'application' || task.task_type === 'payment_verification') && task.entity_id && (\r\n                  <button\r\n                    onClick={handleEmailClient}\r\n                    className=\"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\"\r\n                    title={`Email ${task.entity_type === 'application' ? 'Applicant' : 'Client'}`}\r\n                  >\r\n                    <i className=\"ri-mail-line mr-2\"></i>\r\n                  </button>\r\n                )}\r\n\r\n                {/* Close Task Button - Only show if task is not already completed */}\r\n                {task.status.toLowerCase() !== 'completed' && user?.isAdmin && (\r\n                  <button\r\n                    onClick={handleCloseTaskClick}\r\n                    disabled={closingTask}\r\n                    className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                    title=\"Close Task\"\r\n                  >\r\n                    {closingTask ? (\r\n                      <>\r\n                        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-1\"></div>\r\n                        <span className=\"hidden sm:inline ml-1\">Closing...</span>\r\n                      </>\r\n                    ) : (\r\n                      <>\r\n                        <span className=\"hidden sm:inline\">Close Task</span>\r\n                      </>\r\n                    )}\r\n                  </button>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {task.description && (\r\n            <div className=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\r\n              <p className=\"text-gray-600 dark:text-gray-400 leading-relaxed\">{task.description}</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Task Content */}\r\n      <div className=\"space-y-6\">\r\n        {renderTaskComponent()}\r\n      </div>\r\n\r\n      {/* Communication Center Modal */}\r\n      {task && task.entity_id && (\r\n        <ActivityNotesModal\r\n          isOpen={isCommunicationModalOpen}\r\n          onClose={() => setIsCommunicationModalOpen(false)}\r\n          entityType={task.entity_type || 'task'}\r\n          entityId={task.entity_id}\r\n          initialEmails={task.assigner?.email}\r\n        />  \r\n      )}\r\n      {/* Close Task Confirmation Modal */}\r\n      <ConfirmationModal\r\n        isOpen={showCloseTaskModal}\r\n        onClose={() => setShowCloseTaskModal(false)}\r\n        onConfirm={handleCloseTask}\r\n        title=\"Close Task\"\r\n        message={\r\n          <div className=\"space-y-3\">\r\n            <p>Are you sure you want to close this task?</p>\r\n            <div className=\"bg-green-50 dark:bg-green-900/20 rounded-lg p-3 text-sm\">\r\n              <p className=\"font-medium text-green-900 dark:text-green-100 mb-2\">This action will:</p>\r\n              <ul className=\"text-green-700 dark:text-green-300 space-y-1\">\r\n                <li>• Mark the task as completed</li>\r\n                <li>• Update the task status permanently</li>\r\n                <li>• Create an activity log entry</li>\r\n                <li>• Notify relevant stakeholders</li>\r\n              </ul>\r\n            </div>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              This action cannot be undone. Make sure all work is completed before closing.\r\n            </p>\r\n          </div>\r\n        }\r\n        confirmText=\"Close Task\"\r\n        confirmVariant=\"primary\"\r\n        loading={closingTask}\r\n        icon={\r\n          <div className=\"flex-shrink-0\">\r\n            <div className=\"h-10 w-10 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center\">\r\n              <i className=\"ri-check-line text-green-600 dark:text-green-400 text-xl\"></i>\r\n            </div>\r\n          </div>\r\n        }\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AAZA;;;;;;;;;;;;AAce,SAAS;QAqPc,gBAgFb;;IApUvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,SAAS,OAAO,MAAM;IAE5B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,QAAQ;gBACV;YACF;QACF;iCAAG;QAAC;KAAO;IAEX,MAAM,WAAW;QACf,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,OAAO,MAAM,wIAAA,CAAA,wBAAqB,CAAC,WAAW,CAAC;YACrD,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,UAAU,cAAc;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,iCAAiC;QACjC,MAAM;IACR;IAEA,MAAM,uBAAuB;QAC3B,sBAAsB;IACxB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,eAAe;YAEf,MAAM,WAAW,MAAM,wIAAA,CAAA,cAAW,CAAC,UAAU,CAAC,KAAK,OAAO,EAAE;gBAC1D,QAAQ;YACV;YAEA,IAAI,UAAU;gBACZ,UAAU,4BAA4B;gBACtC,MAAM,YAAY,gCAAgC;gBAClD,sBAAsB;YACxB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,UAAU,cAAc;QAC1B,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB;QACxB,4BAA4B;IAC9B;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM,OAAO;QAElB,OAAQ,KAAK,SAAS;YACpB,KAAK,uHAAA,CAAA,WAAQ,CAAC,oBAAoB;gBAChC,qBACE,6LAAC,sJAAA,CAAA,UAAoB;oBACnB,MAAM;oBACN,cAAc;;;;;;YAIpB,KAAK;YACL,KAAK;gBACH,qBACE,6LAAC,0JAAA,CAAA,UAAwB;oBACvB,MAAM;oBACN,cAAc;;;;;;YAIpB;gBACE,qBACE,6LAAC,sJAAA,CAAA,UAAoB;oBACnB,MAAM;oBACN,cAAc;;;;;;QAGtB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAwB,OAAO;YACpC,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAmB,OAAO;YAC/B,KAAK;gBAAoB,OAAO;YAChC;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAwB,OAAO;YACpC,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAmB,OAAO;YAC/B,KAAK;gBAAoB,OAAO;YAChC;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,OAAO,WAAW;YACxB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAIxD;IAEA,IAAI,SAAS,CAAC,MAAM;QAClB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;kCAEf,6LAAC;wBAAG,WAAU;kCAA8D;;;;;;kCAC5E,6LAAC;wBAAE,WAAU;kCACV,SAAS;;;;;;kCAEZ,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI;wBAC1B,WAAU;;0CAEV,6LAAC;gCAAE,WAAU;;;;;;4BAA8B;;;;;;;;;;;;;;;;;;IAMrD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;gBAAY,cAAW;0BACpC,cAAA,6LAAC;oBAAG,WAAU;;sCACZ,6LAAC;4BAAG,WAAU;sCACZ,cAAA,6LAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,6LAAC;wCAAE,WAAU;;;;;;oCAAwB;;;;;;;;;;;;sCAIzC,6LAAC;sCACC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;;;;;kDACb,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAKL,6LAAC;4BAAG,gBAAa;sCACf,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;;;;;kDACb,6LAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,OAAO,IAAI;4CAC1B,WAAU;4CACV,OAAM;sDAEN,cAAA,6LAAC;gDAAE,WAAU;;;;;;;;;;;sDAEf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,AAAC,kBAAkD,OAAjC,iBAAiB,KAAK,SAAS;8DAC/D,cAAA,6LAAC;wDAAE,WAAW,AAAC,GAAkC,OAAhC,gBAAgB,KAAK,SAAS,GAAE;;;;;;;;;;;8DAEnD,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAEb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEAA2C;wEAC/C,KAAK,WAAW;;;;;;;gEAExB,KAAK,WAAW,kBACf,6LAAC;oEAAE,WAAU;;wEAA2C;yEACxC,iBAAA,KAAK,QAAQ,cAAb,qCAAA,eAAe,UAAU;;;;;;;gEAG1C,KAAK,QAAQ,kBACZ,6LAAC;oEAAE,WAAU;;wEAA2C;wEAChD,IAAI,KAAK,KAAK,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAS5D,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAW,AAAC,uEAAkG,OAA5B,eAAe,KAAK,MAAM;0DAC/G,KAAK,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;;;;;;sDAK9C,6LAAC;4CAAI,WAAU;;gDAEZ,CAAC,KAAK,WAAW,KAAK,iBAAiB,KAAK,WAAW,KAAK,aAAa,KAAK,WAAW,KAAK,aAC7F,KAAK,SAAS,KAAK,iBAAiB,KAAK,SAAS,KAAK,sBAAsB,KAAK,KAAK,SAAS,kBAChG,6LAAC;oDACC,SAAS;oDACT,WAAU;oDACV,OAAO,AAAC,SAAoE,OAA5D,KAAK,WAAW,KAAK,gBAAgB,cAAc;8DAEnE,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;gDAKhB,KAAK,MAAM,CAAC,WAAW,OAAO,gBAAe,iBAAA,2BAAA,KAAM,OAAO,mBACzD,6LAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAU;oDACV,OAAM;8DAEL,4BACC;;0EACE,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;qFAG1C;kEACE,cAAA,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAShD,KAAK,WAAW,kBACf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAoD,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;0BAOzF,6LAAC;gBAAI,WAAU;0BACZ;;;;;;YAIF,QAAQ,KAAK,SAAS,kBACrB,6LAAC,yJAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,4BAA4B;gBAC3C,YAAY,KAAK,WAAW,IAAI;gBAChC,UAAU,KAAK,SAAS;gBACxB,aAAa,GAAE,iBAAA,KAAK,QAAQ,cAAb,qCAAA,eAAe,KAAK;;;;;;0BAIvC,6LAAC,oJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,sBAAsB;gBACrC,WAAW;gBACX,OAAM;gBACN,uBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAE;;;;;;sCACH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAsD;;;;;;8CACnE,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAGR,6LAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;gBAK5D,aAAY;gBACZ,gBAAe;gBACf,SAAS;gBACT,oBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;GA5WwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QACF,mIAAA,CAAA,WAAQ;QACb,kIAAA,CAAA,UAAO;;;KAJF", "debugId": null}}]}