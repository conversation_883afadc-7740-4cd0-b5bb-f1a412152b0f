import { processApiResponse } from '@/lib/authUtils';
import { apiClient } from '../lib/apiClient';
import { License, PaginatedResponse } from '@/types';


export const licenseService = {
  // Get all licenses with pagination
  async getLicenses(params?: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
    status?: string;
    licenseType?: string;
    dateRange?: string;
  }): Promise<PaginatedResponse<License> > {
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    if (params?.status) queryParams.append('filter.status', params.status);
    if (params?.licenseType) queryParams.append('filter.licenseType', params.licenseType);
    if (params?.dateRange) queryParams.append('filter.dateRange', params.dateRange);

    const response = await apiClient.get(`/licenses?${queryParams.toString()}`);
    return processApiResponse(response);
  },

  // Get single license by ID
  async getLicense(id: string): Promise<License> {
    const response = await apiClient.get(`/licenses/${id}`);
    return processApiResponse(response);
  },

  // Get license by application ID
  async getLicenseByApplication(applicationId: string): Promise<License | null> {
    try {
      // Since there's no direct endpoint, we'll get all licenses and filter by application_id
      // In a real implementation, you'd want to add this endpoint to the backend
      const response = await apiClient.get(`/licenses?filter.application_id=${applicationId}`);
      const result = processApiResponse(response);
      
      if (result.data && result.data.length > 0) {
        return result.data[0]; // Return the first (and should be only) license for this application
      }
      
      return null;
    } catch (error) {
      console.error('Error getting license by application:', error);
      return null;
    }
  },

  // Get licenses by applicant
  async getLicensesByApplicant(applicantId: string): Promise<License[]> {
    const response = await apiClient.get(`/licenses/by-applicant/${applicantId}`);
    return processApiResponse(response);
  },

  // Get license by license number
  async getLicenseByNumber(licenseNumber: string): Promise<License> {
    const response = await apiClient.get(`/licenses/by-number/${licenseNumber}`);
    return processApiResponse(response);
  },

  // Download license PDF
  async downloadLicensePDF(licenseId: string): Promise<Blob> {
    try {
      const response = await apiClient.get(`/licenses/${licenseId}/pdf`, {
        responseType: 'blob',
        headers: {
          'Accept': 'application/pdf',
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error downloading license PDF:', error);
      throw error;
    }
  },

  // Get license statistics
  async getLicenseStats(): Promise<Record<string, number>> {
    const response = await apiClient.get('/licenses/stats');
    return processApiResponse(response);
  },

  // Get licenses expiring soon
  async getExpiringSoon(days: number = 30): Promise<License[]> {
    const response = await apiClient.get(`/licenses/expiring-soon?days=${days}`);
    return processApiResponse(response);
  },

  // Create new license (admin only)
  async createLicense(data: {
    license_number: string;
    application_id: string;
    applicant_id: string;
    license_type_id: string;
    status?: string;
    issue_date: string;
    expiry_date: string;
    issued_by: string;
    conditions?: string;
  }): Promise<License> {
    const response = await apiClient.post('/licenses', data);
    return processApiResponse(response);
  },

  // Update license (admin only)
  async updateLicense(id: string, data: Partial<License>): Promise<License> {
    const response = await apiClient.put(`/licenses/${id}`, data);
    return processApiResponse(response);
  },

  // Delete license (admin only)
  async deleteLicense(id: string): Promise<{ message: string }> {
    const response = await apiClient.delete(`/licenses/${id}`);
    return processApiResponse(response);
  },
};
