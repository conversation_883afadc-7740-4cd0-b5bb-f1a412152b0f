'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useLicenseTypes, useLicenseCategories } from '@/hooks/useLicenseData';
import { LicenseCategory, LicenseType } from '@/types';

// Helper function to get icon and styling based on license type name
const getLicenseTypeIcon = (name: string) => {
  const nameLower = name.toLowerCase();

  if (nameLower.includes('postal') || nameLower.includes('courier') || nameLower.includes('mail')) {
    return {
      icon: 'ri-mail-send-line',
      iconBg: 'bg-blue-100',
      iconColor: 'text-blue-600',
      category: 'Postal'
    };
  } else if (nameLower.includes('telecom') || nameLower.includes('spectrum') || nameLower.includes('radio')) {
    return {
      icon: 'ri-signal-tower-line',
      iconBg: 'bg-indigo-100',
      iconColor: 'text-indigo-600',
      category: 'Telecommunications'
    };
  } else if (nameLower.includes('standard') || nameLower.includes('approval') || nameLower.includes('certificate')) {
    return {
      icon: 'ri-shield-check-line',
      iconBg: 'bg-emerald-100',
      iconColor: 'text-emerald-600',
      category: 'Standards'
    };
  } else if (nameLower.includes('clf') || nameLower.includes('converged') || nameLower.includes('framework')) {
    return {
      icon: 'ri-stack-line',
      iconBg: 'bg-purple-100',
      iconColor: 'text-purple-600',
      category: 'CLF'
    };
  } else if (nameLower.includes('internet') || nameLower.includes('isp') || nameLower.includes('network')) {
    return {
      icon: 'ri-wifi-line',
      iconBg: 'bg-green-100',
      iconColor: 'text-green-600',
      category: 'Internet'
    };
  } else if (nameLower.includes('broadcast') || nameLower.includes('media') || nameLower.includes('content')) {
    return {
      icon: 'ri-broadcast-line',
      iconBg: 'bg-orange-100',
      iconColor: 'text-orange-600',
      category: 'Broadcasting'
    };
  } else {
    return {
      icon: 'ri-file-text-line',
      iconBg: 'bg-gray-100',
      iconColor: 'text-gray-600',
      category: 'General'
    };
  }
};

const CustomerApplicationsPage = () => {
  const router = useRouter();
  const { licenseTypes, loading: licenseLoading, error: licenseError } = useLicenseTypes();
  const { loading: categoriesLoading, error: categoriesError, getCategoriesByType } = useLicenseCategories();

  const loading = licenseLoading || categoriesLoading;
  const error = licenseError || categoriesError;

  // Transform backend license types to display format
  const transformedLicenseTypes = licenseTypes.map((licenseType): LicenseType =>{
    const iconData = getLicenseTypeIcon(licenseType.name);
    const relatedCategories = getCategoriesByType(licenseType.license_type_id);

    licenseType.categories = relatedCategories
    return {
      ...licenseType,
      ...iconData
    };
  });

  const handleApplyForLicense = (licenseType: LicenseType) => {
    // Debug logging
    console.log('Navigating to license type:', {
      id: licenseType.license_type_id,
      name: licenseType.name,
      url: `/customer/applications/${licenseType.license_type_id}`
    });
    // Navigate to license category selection page for the selected license type
    router.push(`/customer/applications/${licenseType.license_type_id}`);
  };

  if (loading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="text-red-600 dark:text-red-400 mb-4">
              <i className="ri-error-warning-line text-4xl"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Failed to load license types
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
            <button
              type="button"
              onClick={() => window.location.reload()}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <div className="min-h-screen flex flex-col">
        {/* Header Section */}
        <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  New License Applications
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Choose a license type to start your application
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/customer/my-licenses')}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                >
                  <i className="ri-file-list-line mr-2"></i>
                  My Applications
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-grow mt-2">
          <div className="max-w-7xl mx-auto">
            {/* License Cards Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {transformedLicenseTypes.map((license) => (
                <div
                  key={license.license_type_id}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:border-primary group h-full"
                >
                  <div className="p-4 h-full flex flex-col">
                    {/* License Icon and Category */}
                    <div className="flex items-center justify-between mb-3">
                      <div className={`w-10 h-10 ${license.iconBg} rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                        <i className={`${license.icon} text-lg ${license.iconColor}`}></i>
                      </div>
                      <span className="text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
                        {license.code}
                      </span>
                    </div>

                    {/* License Name */}
                    <h3 className="text-base font-semibold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-primary transition-colors duration-300 line-clamp-2">
                      {license.name}
                    </h3>

                    {/* License Details - Compact */}
                    <div className="space-y-1 mb-3">
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-gray-500 dark:text-gray-400">Categories:</span>
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                          {license.categories?.length || 0}
                        </span>
                      </div>
                    </div>

                    {/* Description */}
                    <div className="mb-3 flex-grow">
                      <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-3">
                        {license.description || 'No description available'}
                      </p>
                    </div>

                    {/* Apply Button */}
                    <button
                      type="button"
                      onClick={() => handleApplyForLicense(license)}
                      className="w-full bg-primary text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300"
                    >
                      <i className="ri-file-add-line mr-1"></i>
                      Apply Now
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Footer - Help Section */}
        <footer className="mt-8 bg-blue-50 dark:bg-blue-900/20 border-t border-blue-200 dark:border-blue-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-start">
              <i className="ri-information-line text-blue-600 dark:text-blue-400 text-xl mr-3 mt-0.5"></i>
              <div>
                <h4 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-2">
                  Need Help with Your Application?
                </h4>
                <p className="text-blue-800 dark:text-blue-200 mb-4">
                  Our team is here to assist you throughout the application process. Contact us for guidance on requirements, 
                  documentation, or any questions about the licensing process.
                </p>
                <div className="flex flex-wrap gap-4">
                  <a
                    href="mailto:<EMAIL>"
                    className="inline-flex items-center text-blue-700 dark:text-blue-300 hover:text-blue-900 dark:hover:text-blue-100 font-medium"
                  >
                    <i className="ri-mail-line mr-2"></i>
                    <EMAIL>
                  </a>
                  <a
                    href="tel:+************"
                    className="inline-flex items-center text-blue-700 dark:text-blue-300 hover:text-blue-900 dark:hover:text-blue-100 font-medium"
                  >
                    <i className="ri-phone-line mr-2"></i>
                    +265 123 456 789
                  </a>
                </div>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </CustomerLayout>
  );
};

export default CustomerApplicationsPage;