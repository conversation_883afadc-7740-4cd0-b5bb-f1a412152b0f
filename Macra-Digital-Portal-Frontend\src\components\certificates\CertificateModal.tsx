'use client';

import React, { useState } from 'react';
import { License } from '@/types';
import { licenseService } from '@/services/licenseService';
import PostalServicesCertificate from './PostalServicesCertificate';
import TypeApprovalCertificate from './TypeApprovalCertificate';
import StandardsCertificate from './StandardsCertificate';
import toast from 'react-hot-toast';

interface CertificateModalProps {
  license: License;
  isOpen: boolean;
  onClose: () => void;
}

export default function CertificateModal({ license, isOpen, onClose }: CertificateModalProps) {
  const [isDownloading, setIsDownloading] = useState(false);

  if (!isOpen) return null;

  const renderCertificate = () => {
    // Determine which certificate to show based on license code
    switch (license.code) {
      case 'postal_services':
        return <PostalServicesCertificate license={license} />;
      case 'standards_compliance':
        return <TypeApprovalCertificate license={license} />;
      case 'telecommunications':
      case 'broadcasting':
      case 'spectrum_management':
      default:
        // Fallback to standards certificate for other types
        return <StandardsCertificate license={license} />;
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownloadPDF = async () => {
    try {
      setIsDownloading(true);
      const blob = await licenseService.downloadLicensePDF(license.license_id);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${license.license_number}_certificate.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success('Certificate PDF downloaded successfully');
    } catch (error) {
      console.error('Error downloading certificate PDF:', error);
      toast.error('Failed to download certificate PDF');
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block w-full max-w-6xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
          {/* Modal header */}
          <div className="flex items-center justify-between mb-4 print:hidden">
            <h3 className="text-lg font-medium text-gray-900">
              License Certificate - {license.license_number}
            </h3>
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={handleDownloadPDF}
                disabled={isDownloading}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isDownloading ? (
                  <>
                    <i className="ri-loader-4-line mr-2 animate-spin"></i>
                    Downloading...
                  </>
                ) : (
                  <>
                    <i className="ri-download-line mr-2"></i>
                    Download PDF
                  </>
                )}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <i className="ri-close-line mr-2"></i>
                Close
              </button>
            </div>
          </div>

          {/* Certificate content */}
          <div className="certificate-container">
            {renderCertificate()}
          </div>
        </div>
      </div>

      {/* Print styles */}
      <style jsx global>{`
        @media print {
          body * {
            visibility: hidden;
          }
          .certificate-container,
          .certificate-container * {
            visibility: visible;
          }
          .certificate-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }
          .print\\:hidden {
            display: none !important;
          }
        }
      `}</style>
    </div>
  );
}
