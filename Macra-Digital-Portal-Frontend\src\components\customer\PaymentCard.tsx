import React from 'react';
import Link from 'next/link';

interface PaymentCardProps {
  id: string;
  title: string;
  amount: string;
  dueDate: string;
  status: 'Due' | 'Overdue' | 'Paid';
  description?: string;
}

const PaymentCard: React.FC<PaymentCardProps> = ({
  id,
  title,
  amount,
  dueDate,
  status,
  description
}) => {
  const getStatusStyles = (status: string) => {
    switch (status) {
      case 'Due':
        return {
          bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
          borderColor: 'border-yellow-100 dark:border-yellow-800',
          iconBg: 'bg-yellow-100 dark:bg-yellow-900/30',
          iconColor: 'text-yellow-600 dark:text-yellow-400',
          badgeBg: 'bg-yellow-100 dark:bg-yellow-900/30',
          badgeText: 'text-yellow-800 dark:text-yellow-200'
        };
      case 'Overdue':
        return {
          bgColor: 'bg-red-50 dark:bg-red-900/20',
          borderColor: 'border-red-100 dark:border-red-800',
          iconBg: 'bg-red-100 dark:bg-red-900/30',
          iconColor: 'text-red-600 dark:text-red-400',
          badgeBg: 'bg-red-100 dark:bg-red-900/30',
          badgeText: 'text-red-800 dark:text-red-200'
        };
      case 'Paid':
        return {
          bgColor: 'bg-green-50 dark:bg-green-900/20',
          borderColor: 'border-green-100 dark:border-green-800',
          iconBg: 'bg-green-100 dark:bg-green-900/30',
          iconColor: 'text-green-600 dark:text-green-400',
          badgeBg: 'bg-green-100 dark:bg-green-900/30',
          badgeText: 'text-green-800 dark:text-green-200'
        };
      default:
        return {
          bgColor: 'bg-gray-50 dark:bg-gray-800',
          borderColor: 'border-gray-100 dark:border-gray-700',
          iconBg: 'bg-gray-100 dark:bg-gray-700',
          iconColor: 'text-gray-600 dark:text-gray-400',
          badgeBg: 'bg-gray-100 dark:bg-gray-700',
          badgeText: 'text-gray-800 dark:text-gray-200'
        };
    }
  };

  const statusStyles = getStatusStyles(status);

  return (
    <div className={`${statusStyles.bgColor} border ${statusStyles.borderColor} rounded-lg p-4`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className={`w-8 h-8 flex items-center justify-center rounded-full ${statusStyles.iconBg} ${statusStyles.iconColor}`}>
            <i className="ri-calendar-line"></i>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{title}</p>
            <p className="text-xs text-gray-500 dark:text-gray-400">{description || dueDate}</p>
          </div>
        </div>
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusStyles.badgeBg} ${statusStyles.badgeText}`}>
          {amount}
        </span>
      </div>
      {status !== 'Paid' && (
        <div className="mt-2 text-right">
          <Link 
            href={`/customer/payments/${id}`} 
            className="text-xs font-medium text-primary hover:text-primary"
          >
            Pay Now
          </Link>
        </div>
      )}
    </div>
  );
};

export default PaymentCard;
