import React, { useState, useEffect } from 'react';
import DataDisplayCard from './DataDisplayCard';
import { Application } from '@/types/license';
import { ApplicationDevice, applicationDeviceService } from '@/services/applicationDeviceService';

interface EquipmentDetailsCardProps {
  application: Application | null;
  className?: string;
  showEmptyFields?: boolean;
  defaultCollapsed?: boolean;
}

const EquipmentDetailsCard: React.FC<EquipmentDetailsCardProps> = ({
  application,
  className = '',
  showEmptyFields = true,
  defaultCollapsed = false
}) => {
  const [devices, setDevices] = useState<ApplicationDevice[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchDevices = async () => {
      if (!application?.application_id) return;

      try {
        setLoading(true);
        const response = await applicationDeviceService.getDevicesByApplication(application.application_id);
        const deviceData = response.data || [];
        setDevices(deviceData);
      } catch (err) {
        console.warn('Could not load devices:', err);
        setDevices([]);
      } finally {
        setLoading(false);
      }
    };

    fetchDevices();
  }, [application?.application_id]);

  // Return empty fragment if no application or no devices
  if (!application || loading || !devices || devices.length === 0) {
    return <></>;
  }

  return (
    <>
      {devices.map((device, index) => (
        <DataDisplayCard
          key={device.device_id || index}
          title={`Equipment Details ${devices.length > 1 ? `#${index + 1}` : ''}`}
          icon="ri-smartphone-line"
          className={className}
          showEmptyFields={showEmptyFields}
          defaultCollapsed={defaultCollapsed}
          creatorEmail={application?.applicant?.email}
          creatorName={application?.applicant?.name}
          showEmailButton={true}
          fields={[
            {
              label: 'IMEI',
              value: device.imei,
              icon: 'ri-barcode-line'
            },
            {
              label: 'Device Type',
              value: device.device_type,
              icon: 'ri-device-line'
            },
            {
              label: 'Model Name',
              value: device.equipment_model || device.model_name,
              icon: 'ri-smartphone-line'
            },
            {
              label: 'Device Serial Number',
              value: device.device_serial_number,
              icon: 'ri-hashtag'
            },
            {
              label: 'Manufacturer Name',
              value: device.manufacturer_name,
              icon: 'ri-building-line'
            },
            {
              label: 'Manufacturer Country',
              value: device.manufacturer_country,
              icon: 'ri-earth-line'
            },
            {
              label: 'Manufacturer Address',
              value: device.manufacturer_address,
              icon: 'ri-map-pin-line',
              fullWidth: true
            },
            {
              label: 'Brand/Trade Name',
              value: device.brand_trade_name,
              icon: 'ri-price-tag-line'
            },
            {
              label: 'Product Type',
              value: device.product_type_name,
              icon: 'ri-product-hunt-line'
            },
            {
              label: 'Equipment Category',
              value: device.equipment_category?.name || 'N/A',
              icon: 'ri-folder-line'
            },
            {
              label: 'Approval Status',
              value: device.approval_status,
              icon: 'ri-checkbox-circle-line'
            },
            {
              label: 'Device Approval Number',
              value: device.device_approval_number,
              icon: 'ri-file-list-line'
            },
            {
              label: 'Device Approval Date',
              value: device.device_approval_date ? new Date(device.device_approval_date).toLocaleDateString() : null,
              type: 'date' as const,
              icon: 'ri-calendar-check-line'
            },
            {
              label: 'Approval Notes',
              value: device.approval_notes,
              icon: 'ri-sticky-note-line',
              fullWidth: true
            },
            {
              label: 'Created Date',
              value: device.created_at ? new Date(device.created_at).toLocaleDateString() : null,
              type: 'date' as const,
              icon: 'ri-calendar-line'
            }
          ]}
        />
      ))}
    </>
  );
};

export default EquipmentDetailsCard;
