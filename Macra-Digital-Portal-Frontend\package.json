{"name": "macra-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo -p 3000", "build": "next build", "start": "next start -p 3000", "start:staff": "next start -p 3000", "start:customer": "next start -p 3000", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@heroicons/react": "^2.2.0", "@tailwindcss/postcss": "^4.1.8", "@types/js-cookie": "^3.0.6", "@types/qrcode": "^1.5.5", "axios": "^1.9.0", "debounce": "^2.2.0", "echarts": "^5.6.0", "form-data": "^4.0.4", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "lucide-react": "^0.525.0", "next": "^15.3.3", "qrcode": "^1.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "remixicon": "^4.6.0", "use-debounce": "^10.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.5.3", "tailwindcss": "^3.4.0", "typescript": "^5"}}