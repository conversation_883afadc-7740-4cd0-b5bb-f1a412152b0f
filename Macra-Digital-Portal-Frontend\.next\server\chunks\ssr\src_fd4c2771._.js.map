{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/license.ts"], "sourcesContent": ["import { LicenseCategory } from \".\";\r\nimport { User } from \"./user\";\r\n\r\nexport interface LicenseType {\r\n  license_type_id: string;\r\n  name: string;\r\n  validity?: number;\r\n  code?: string;\r\n  description?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  categories?: LicenseCategory[];\r\n  iconColor?: string;\r\n  icon?: string;\r\n  iconBg?: string;\r\n}\r\n\r\nexport interface StatusAction {\r\n  status: ApplicationStatus;\r\n  label: string;\r\n  icon: string;\r\n  color: string;\r\n  hoverColor: string;\r\n  roles?: string[],\r\n  description: string;\r\n  confirmMessage?: string;\r\n}\r\n\r\n\r\nexport interface Applicant {\r\n  applicant_id: string;\r\n  name: string;\r\n  business_registration_number: string;\r\n  tpin: string;\r\n  website: string;\r\n  email: string;\r\n  phone: string;\r\n  fax?: string;\r\n  level_of_insurance_cover?: string;\r\n  address_id?: string;\r\n  contact_id?: string;\r\n  date_incorporation: string;\r\n  place_incorporation: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Application extends Record<string, unknown> {\r\n  application_id: string;\r\n  application_number: string;\r\n  applicant_id: string;\r\n  license_category_id: string;\r\n  status: string;\r\n  current_step: number;\r\n  progress_percentage: number;\r\n  submitted_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  assigned_to?: string;\r\n  assigned_at?: string;\r\n  application_data?: any;\r\n  applicant?: Applicant;\r\n  license_category?:LicenseCategory;\r\n  assignee?:User;\r\n}\r\n\r\nexport enum ApplicationStatus {\r\n  DRAFT = 'draft',\r\n  PENDING = 'pending',\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  EVALUATION = 'evaluation',\r\n  PENDING_PAYMENT = 'pending_payment',\r\n  APPROVED = 'approved',\r\n  REJECTED = 'rejected',\r\n  WITHDRAWN = 'withdrawn',\r\n  PASS_EVALUATION = 'pass_evaluation',\r\n  WAITING_FOR_APPROVAL = 'waiting_for_approval',\r\n}\r\n\r\nexport interface ApplicationFilters {\r\n  licenseTypeId?: string;\r\n  licenseCategoryId?: string;\r\n  status?: ApplicationStatus | '';\r\n  dateRange?: string;\r\n  search?: string;\r\n}\r\n\r\nexport interface License extends Record<string, unknown> {\r\n  license_id: string;\r\n  license_number: string;\r\n  description?: string;\r\n  application_id: string;\r\n  applicant_id: string;\r\n  license_type_id: string;\r\n  status: LicenseStatus;\r\n  issue_date: string;\r\n  expiry_date: string;\r\n  issued_by: string;\r\n  code?: string;\r\n  conditions?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  application: Application;\r\n  issuer?:User;\r\n}\r\n\r\nexport enum LicenseStatus {\r\n  ACTIVE = 'active',\r\n  EXPIRED = 'expired',\r\n  SUSPENDED = 'suspended',\r\n  REVOKED = 'revoked',\r\n  UNDER_REVIEW = 'under_review',\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAkEO,IAAA,AAAK,2CAAA;;;;;;;;;;;;WAAA;;AAyCL,IAAA,AAAK,uCAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/notificationService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { ApplicationStatus } from '../types/license';\r\nimport { AppNotification, NotificationSummary } from '@/types/notification';\r\n\r\n\r\nexport const notificationService = {\r\n  // Get user notifications\r\n  async getUserNotifications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    type?: string;\r\n    status?: 'read' | 'unread';\r\n  }): Promise<NotificationSummary> {\r\n    try {\r\n      const queryParams = new URLSearchParams();\r\n      \r\n      if (params?.page) queryParams.append('page', params.page.toString());\r\n      if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n      if (params?.type) queryParams.append('type', params.type);\r\n      if (params?.status) queryParams.append('status', params.status);\r\n\r\n      const endpoint = `/notifications/my-notifications?${queryParams.toString()}`;\r\n      const response = await apiClient.get(endpoint);\r\n      const data = processApiResponse(response).data;\r\n      \r\n      // Validate response structure\r\n      if (!data || typeof data !== 'object') {\r\n        console.warn('Invalid notification response format:', data);\r\n        return {\r\n          total_count: 0,\r\n          unread_count: 0,\r\n          notifications: []\r\n        };\r\n      }\r\n      \r\n      return {\r\n        total_count: data.length || 0,\r\n        unread_count: data.length || 0,\r\n        notifications: Array.isArray(data) ? data : []\r\n      };\r\n    } catch (error: any) {\r\n      // Convert params to string format for URLSearchParams\r\n      const stringParams: Record<string, string> = {};\r\n      if (params) {\r\n        if (params.page) stringParams.page = params.page.toString();\r\n        if (params.limit) stringParams.limit = params.limit.toString();\r\n        if (params.type) stringParams.type = params.type;\r\n        if (params.status) stringParams.status = params.status;\r\n      }\r\n\r\n      console.error('Error fetching user notifications:', {\r\n        error: error.message || 'Unknown error',\r\n        response: error.response?.data || null,\r\n        status: error.response?.status || null,\r\n        code: error.code || null,\r\n        params,\r\n        endpoint: `/notifications/my-notifications?${new URLSearchParams(stringParams).toString()}`\r\n      });\r\n      \r\n      // Return empty result on error instead of throwing\r\n      return {\r\n        total_count: 0,\r\n        unread_count: 0,\r\n        notifications: []\r\n      };\r\n    }\r\n  },\r\n\r\n  // Mark notification as read\r\n  async markAsRead(notificationId: string): Promise<void> {\r\n    const response = await apiClient.patch(`/notifications/${notificationId}/mark-read`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Mark all notifications as read (implemented client-side by marking each notification individually)\r\n  async markAllAsRead(): Promise<void> {\r\n    // This is handled by the useNotifications hook by calling markAsRead for each unread notification\r\n    // We don't need a separate backend endpoint for this\r\n    throw new Error('markAllAsRead should be handled by useNotifications hook');\r\n  },\r\n\r\n  // Create a status change notification (usually called from backend)\r\n  async createStatusChangeNotification(\r\n    applicationId: string,\r\n    oldStatus: ApplicationStatus,\r\n    newStatus: ApplicationStatus,\r\n    step?: number,\r\n    progressPercentage?: number\r\n  ): Promise<Notification> {\r\n    const response = await apiClient.post('/notifications/status-change', {\r\n      application_id: applicationId,\r\n      old_status: oldStatus,\r\n      new_status: newStatus,\r\n      step,\r\n      progress_percentage: progressPercentage\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete notification\r\n  async deleteNotification(notificationId: string): Promise<void> {\r\n    const response = await apiClient.delete(`/notifications/${notificationId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get notification count\r\n  async getNotificationCount(): Promise<{ total: number; unread: number }> {\r\n    const response = await apiClient.get('/notifications/count');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Manual notification trigger for testing (will be removed in production)\r\n  async triggerTestNotification(\r\n    applicationId: string,\r\n    applicationNumber: string,\r\n    licenseCategoryName: string,\r\n    type: 'status_change' | 'reminder' | 'document_required' | 'approval' | 'rejection',\r\n    status: ApplicationStatus\r\n  ): Promise<Notification> {\r\n    const response = await apiClient.post('/notifications/test', {\r\n      application_id: applicationId,\r\n      application_number: applicationNumber,\r\n      license_category_name: licenseCategoryName,\r\n      type,\r\n      status\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Send activity note notification (creates notification which auto-creates activity note)\r\n  async sendActivityNoteNotification(data: {\r\n    applicationId: string;\r\n    message: string;\r\n    noteType?: string;\r\n    category?: string;\r\n    priority?: 'low' | 'normal' | 'high';\r\n    attachmentsCount?: number;\r\n  }): Promise<any> {\r\n    const response = await apiClient.post('/notifications/activity-note', {\r\n      application_id: data.applicationId,\r\n      message: data.message,\r\n      note_type: data.noteType || 'evaluation_comment',\r\n      category: data.category || 'communication',\r\n      priority: data.priority || 'normal',\r\n      attachments_count: data.attachmentsCount || 0,\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n};\r\n\r\n// Status change message templates\r\nexport const getStatusChangeMessage = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  oldStatus: string,\r\n  newStatus: string,\r\n  step?: number,\r\n  progressPercentage?: number\r\n): { title: string; message: string; type: 'info' | 'success' | 'warning' | 'error' } => {\r\n  const progressText = progressPercentage ? ` (${progressPercentage}% complete)` : '';\r\n  const stepText = step ? ` - Step ${step}` : '';\r\n  \r\n  const messages = {\r\n    [ApplicationStatus.DRAFT]: {\r\n      title: 'Application Draft Saved',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been saved as a draft. You can continue editing and submit it when ready.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.PENDING]: {\r\n      title: 'Application Pending',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is pending initial review. We'll notify you once the review begins.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.SUBMITTED]: {\r\n      title: 'Application Submitted',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been successfully submitted and is now being processed.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.UNDER_REVIEW]: {\r\n      title: 'Application Under Review',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is now under review by our team${progressText}${stepText}. We'll notify you of any updates.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.EVALUATION]: {\r\n      title: 'Application Being Evaluated',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is currently being evaluated${progressText}${stepText}. This may take several business days.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.PENDING_PAYMENT]: {\r\n      title: 'Payment Required',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been approved for payment. Please complete the payment to proceed with license issuance.`,\r\n      type: 'warning' as const\r\n    },\r\n    [ApplicationStatus.APPROVED]: {\r\n      title: 'Application Approved! 🎉',\r\n      message: `Congratulations! Your ${licenseCategoryName} application (${applicationNumber}) has been approved. You can now download your license.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.REJECTED]: {\r\n      title: 'Application Status Update',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) requires attention. Please review the feedback and resubmit if needed.`,\r\n      type: 'warning' as const\r\n    },\r\n    [ApplicationStatus.WITHDRAWN]: {\r\n      title: 'Application Withdrawn',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been withdrawn as requested.`,\r\n      type: 'info' as const\r\n    }\r\n  };\r\n\r\n  const defaultMessage = {\r\n    title: 'Application Status Update',\r\n    message: `Your ${licenseCategoryName} application (${applicationNumber}) status has been updated to ${newStatus.replace('_', ' ')}.`,\r\n    type: 'info' as const\r\n  };\r\n\r\n  return messages[newStatus as keyof typeof messages] || defaultMessage;\r\n};\r\n\r\n// Helper function to manually trigger notification for testing\r\nexport const createTestNotification = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  status: ApplicationStatus\r\n): AppNotification => {\r\n  const now = new Date().toISOString();\r\n  const notificationId = `test-${Date.now()}`;\r\n  \r\n  return {\r\n    notification_id: notificationId,\r\n    user_id: 'current-user',\r\n    application_id: `app-${applicationNumber}`,\r\n    application_number: applicationNumber,\r\n    license_category_name: licenseCategoryName,\r\n    title: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).title,\r\n    message: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).message,\r\n    type: 'status_change',\r\n    status: 'unread',\r\n    priority: 'medium',\r\n    created_at: now,\r\n    metadata: {\r\n      old_status: ApplicationStatus.SUBMITTED,\r\n      new_status: status,\r\n      step: 2,\r\n      progress_percentage: 50\r\n    }\r\n  };\r\n};"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAIO,MAAM,sBAAsB;IACjC,yBAAyB;IACzB,MAAM,sBAAqB,MAK1B;QACC,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;YACxD,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAE9D,MAAM,WAAW,CAAC,gCAAgC,EAAE,YAAY,QAAQ,IAAI;YAC5E,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI;YAE9C,8BAA8B;YAC9B,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;gBACrC,QAAQ,IAAI,CAAC,yCAAyC;gBACtD,OAAO;oBACL,aAAa;oBACb,cAAc;oBACd,eAAe,EAAE;gBACnB;YACF;YAEA,OAAO;gBACL,aAAa,KAAK,MAAM,IAAI;gBAC5B,cAAc,KAAK,MAAM,IAAI;gBAC7B,eAAe,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;YAChD;QACF,EAAE,OAAO,OAAY;YACnB,sDAAsD;YACtD,MAAM,eAAuC,CAAC;YAC9C,IAAI,QAAQ;gBACV,IAAI,OAAO,IAAI,EAAE,aAAa,IAAI,GAAG,OAAO,IAAI,CAAC,QAAQ;gBACzD,IAAI,OAAO,KAAK,EAAE,aAAa,KAAK,GAAG,OAAO,KAAK,CAAC,QAAQ;gBAC5D,IAAI,OAAO,IAAI,EAAE,aAAa,IAAI,GAAG,OAAO,IAAI;gBAChD,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,GAAG,OAAO,MAAM;YACxD;YAEA,QAAQ,KAAK,CAAC,sCAAsC;gBAClD,OAAO,MAAM,OAAO,IAAI;gBACxB,UAAU,MAAM,QAAQ,EAAE,QAAQ;gBAClC,QAAQ,MAAM,QAAQ,EAAE,UAAU;gBAClC,MAAM,MAAM,IAAI,IAAI;gBACpB;gBACA,UAAU,CAAC,gCAAgC,EAAE,IAAI,gBAAgB,cAAc,QAAQ,IAAI;YAC7F;YAEA,mDAAmD;YACnD,OAAO;gBACL,aAAa;gBACb,cAAc;gBACd,eAAe,EAAE;YACnB;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,YAAW,cAAsB;QACrC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,eAAe,UAAU,CAAC;QACnF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qGAAqG;IACrG,MAAM;QACJ,kGAAkG;QAClG,qDAAqD;QACrD,MAAM,IAAI,MAAM;IAClB;IAEA,oEAAoE;IACpE,MAAM,gCACJ,aAAqB,EACrB,SAA4B,EAC5B,SAA4B,EAC5B,IAAa,EACb,kBAA2B;QAE3B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC;YACpE,gBAAgB;YAChB,YAAY;YACZ,YAAY;YACZ;YACA,qBAAqB;QACvB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,oBAAmB,cAAsB;QAC7C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,gBAAgB;QAC1E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0EAA0E;IAC1E,MAAM,yBACJ,aAAqB,EACrB,iBAAyB,EACzB,mBAA2B,EAC3B,IAAmF,EACnF,MAAyB;QAEzB,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;YAC3D,gBAAgB;YAChB,oBAAoB;YACpB,uBAAuB;YACvB;YACA;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0FAA0F;IAC1F,MAAM,8BAA6B,IAOlC;QACC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC;YACpE,gBAAgB,KAAK,aAAa;YAClC,SAAS,KAAK,OAAO;YACrB,WAAW,KAAK,QAAQ,IAAI;YAC5B,UAAU,KAAK,QAAQ,IAAI;YAC3B,UAAU,KAAK,QAAQ,IAAI;YAC3B,mBAAmB,KAAK,gBAAgB,IAAI;QAC9C;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA,WACA,WACA,MACA;IAEA,MAAM,eAAe,qBAAqB,CAAC,EAAE,EAAE,mBAAmB,WAAW,CAAC,GAAG;IACjF,MAAM,WAAW,OAAO,CAAC,QAAQ,EAAE,MAAM,GAAG;IAE5C,MAAM,WAAW;QACf,CAAC,uHAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,EAAE;YACzB,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,+EAA+E,CAAC;YACvJ,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,OAAO,CAAC,EAAE;YAC3B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,qEAAqE,CAAC;YAC7I,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,6DAA6D,CAAC;YACrI,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,EAAE;YAChC,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,iCAAiC,EAAE,eAAe,SAAS,kCAAkC,CAAC;YACrK,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,EAAE;YAC9B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,8BAA8B,EAAE,eAAe,SAAS,sCAAsC,CAAC;YACtK,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,eAAe,CAAC,EAAE;YACnC,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,8FAA8F,CAAC;YACtK,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,CAAC,sBAAsB,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,uDAAuD,CAAC;YAChJ,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,wEAAwE,CAAC;YAChJ,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,kCAAkC,CAAC;YAC1G,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAO;QACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,6BAA6B,EAAE,UAAU,OAAO,CAAC,KAAK,KAAK,CAAC,CAAC;QACpI,MAAM;IACR;IAEA,OAAO,QAAQ,CAAC,UAAmC,IAAI;AACzD;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA;IAEA,MAAM,MAAM,IAAI,OAAO,WAAW;IAClC,MAAM,iBAAiB,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IAE3C,OAAO;QACL,iBAAiB;QACjB,SAAS;QACT,gBAAgB,CAAC,IAAI,EAAE,mBAAmB;QAC1C,oBAAoB;QACpB,uBAAuB;QACvB,OAAO,uBAAuB,mBAAmB,qBAAqB,uHAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,KAAK;QAChH,SAAS,uBAAuB,mBAAmB,qBAAqB,uHAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,OAAO;QACpH,MAAM;QACN,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,UAAU;YACR,YAAY,uHAAA,CAAA,oBAAiB,CAAC,SAAS;YACvC,YAAY;YACZ,MAAM;YACN,qBAAqB;QACvB;IACF;AACF", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Modal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ReactNode, useEffect } from 'react';\r\n\r\ninterface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string;\r\n  children: ReactNode;\r\n  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';\r\n  showCloseButton?: boolean;\r\n  closeOnOverlayClick?: boolean;\r\n}\r\n\r\nexport default function Modal({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  size = 'md',\r\n  showCloseButton = true,\r\n  closeOnOverlayClick = true,\r\n}: ModalProps) {\r\n  // Handle escape key press\r\n  useEffect(() => {\r\n    const handleEscape = (event: KeyboardEvent) => {\r\n      if (event.key === 'Escape' && isOpen) {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      // Prevent body scroll when modal is open\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [isOpen, onClose]);\r\n\r\n  if (!isOpen) return null;\r\n\r\n  const getSizeClasses = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'sm:max-w-sm';\r\n      case 'md':\r\n        return 'sm:max-w-md';\r\n      case 'lg':\r\n        return 'sm:max-w-lg';\r\n      case 'xl':\r\n        return 'sm:max-w-xl';\r\n      case '2xl':\r\n        return 'sm:max-w-2xl';\r\n      default:\r\n        return 'sm:max-w-md';\r\n    }\r\n  };\r\n\r\n  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {\r\n    if (closeOnOverlayClick && e.target === e.currentTarget) {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4\">\r\n      <div\r\n        className=\"fixed inset-0\"\r\n        onClick={handleOverlayClick}\r\n        aria-hidden=\"true\"\r\n      />\r\n      \r\n      <div className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full ${getSizeClasses()} mx-4 transform transition-all max-h-[90vh] flex flex-col`}>\r\n        {/* Header - Fixed */}\r\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n            {title}\r\n          </h3>\r\n          {showCloseButton && (\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 rounded-md p-1\"\r\n              aria-label=\"Close modal\"\r\n            >\r\n              <i className=\"ri-close-line text-xl\"></i>\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {/* Scrollable Content */}\r\n        <div className=\"flex-1 overflow-hidden\">\r\n          <div className=\"overflow-y-auto h-full\">\r\n            <div className=\"p-6\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAce,SAAS,MAAM,EAC5B,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACX,kBAAkB,IAAI,EACtB,sBAAsB,IAAI,EACf;IACX,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,MAAM,GAAG,KAAK,YAAY,QAAQ;gBACpC;YACF;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,WAAW;YACrC,yCAAyC;YACzC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,uBAAuB,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YACvD;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAU;gBACV,SAAS;gBACT,eAAY;;;;;;0BAGd,8OAAC;gBAAI,WAAW,CAAC,+DAA+D,EAAE,iBAAiB,yDAAyD,CAAC;;kCAE3J,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX;;;;;;4BAEF,iCACC,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;kCAMnB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/notifications/NotificationItem.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { Notification } from '@/hooks/useNotifications';\r\n\r\ninterface NotificationItemProps {\r\n  notification: Notification;\r\n  onMarkAsRead: (id: string) => void;\r\n  onNotificationClick?: (notification: Notification) => void;\r\n}\r\n\r\nconst NotificationItem: React.FC<NotificationItemProps> = ({\r\n  notification,\r\n  onMarkAsRead,\r\n  onNotificationClick,\r\n}) => {\r\n  const formatTimeAgo = (dateString: string) => {\r\n    const date = new Date(dateString);\r\n    const now = new Date();\r\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n    if (diffInSeconds < 60) {\r\n      return 'Just now';\r\n    } else if (diffInSeconds < 3600) {\r\n      const minutes = Math.floor(diffInSeconds / 60);\r\n      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\r\n    } else if (diffInSeconds < 86400) {\r\n      const hours = Math.floor(diffInSeconds / 3600);\r\n      return `${hours} hour${hours > 1 ? 's' : ''} ago`;\r\n    } else if (diffInSeconds < 604800) {\r\n      const days = Math.floor(diffInSeconds / 86400);\r\n      return `${days} day${days > 1 ? 's' : ''} ago`;\r\n    } else {\r\n      return date.toLocaleDateString();\r\n    }\r\n  };\r\n\r\n  const getNotificationIcon = (type: string) => {\r\n    switch (type.toLowerCase()) {\r\n      case 'email':\r\n        return 'ri-mail-line';\r\n      case 'sms':\r\n        return 'ri-message-3-line';\r\n      case 'in_app':\r\n        return 'ri-notification-3-line';\r\n      case 'application_status':\r\n        return 'ri-file-list-3-line';\r\n      case 'evaluation_assigned':\r\n        return 'ri-user-settings-line';\r\n      case 'payment_due':\r\n        return 'ri-money-dollar-circle-line';\r\n      case 'license_expiry':\r\n        return 'ri-calendar-event-line';\r\n      case 'system_alert':\r\n        return 'ri-alert-line';\r\n      default:\r\n        return 'ri-notification-3-line';\r\n    }\r\n  };\r\n\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority.toLowerCase()) {\r\n      case 'urgent':\r\n        return 'text-red-600 dark:text-red-400';\r\n      case 'high':\r\n        return 'text-orange-600 dark:text-orange-400';\r\n      case 'medium':\r\n        return 'text-yellow-600 dark:text-yellow-400';\r\n      case 'low':\r\n        return 'text-green-600 dark:text-green-400';\r\n      default:\r\n        return 'text-gray-600 dark:text-gray-400';\r\n    }\r\n  };\r\n\r\n  const handleItemClick = () => {\r\n    if (!notification.is_read) {\r\n      onMarkAsRead(notification.notification_id);\r\n    }\r\n    if (onNotificationClick) {\r\n      onNotificationClick(notification);\r\n    }\r\n  };\r\n\r\n  const handleMarkAsReadClick = (e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    onMarkAsRead(notification.notification_id);\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors duration-200 ${\r\n        !notification.is_read ? 'bg-blue-50 dark:bg-blue-900/20' : ''\r\n      }`}\r\n      onClick={handleItemClick}\r\n    >\r\n      <div className=\"flex items-start space-x-3\">\r\n        {/* Notification Icon */}\r\n        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${\r\n          !notification.is_read \r\n            ? 'bg-blue-100 dark:bg-blue-900/50' \r\n            : 'bg-gray-100 dark:bg-gray-700'\r\n        }`}>\r\n          <i className={`${getNotificationIcon(notification.type)} text-sm ${\r\n            !notification.is_read \r\n              ? 'text-blue-600 dark:text-blue-400' \r\n              : 'text-gray-600 dark:text-gray-400'\r\n          }`}></i>\r\n        </div>\r\n\r\n        {/* Notification Content */}\r\n        <div className=\"flex-1 min-w-0\">\r\n          <div className=\"flex items-start justify-between\">\r\n            <div className=\"flex-1\">\r\n              <h4 className={`text-sm font-medium ${\r\n                !notification.is_read \r\n                  ? 'text-gray-900 dark:text-gray-100' \r\n                  : 'text-gray-700 dark:text-gray-300'\r\n              }`}>\r\n                {notification.subject}\r\n              </h4>\r\n              <p className={`mt-1 text-sm ${\r\n                !notification.is_read \r\n                  ? 'text-gray-700 dark:text-gray-300' \r\n                  : 'text-gray-500 dark:text-gray-400'\r\n              }`}>\r\n                {notification.message}\r\n              </p>\r\n            </div>\r\n\r\n            {/* Priority Indicator */}\r\n            {notification.priority && notification.priority !== 'medium' && (\r\n              <div className={`flex-shrink-0 ml-2 ${getPriorityColor(notification.priority)}`}>\r\n                <i className=\"ri-flag-line text-xs\"></i>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Metadata */}\r\n          <div className=\"mt-2 flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400\">\r\n              <span>{formatTimeAgo(notification.created_at)}</span>\r\n              {notification.entity_type && (\r\n                <span className=\"capitalize\">{notification.entity_type}</span>\r\n              )}\r\n            </div>\r\n\r\n            {/* Actions */}\r\n            <div className=\"flex items-center space-x-2\">\r\n              {!notification.is_read && (\r\n                <button\r\n                  onClick={handleMarkAsReadClick}\r\n                  className=\"inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/50 hover:text-blue-800 dark:hover:text-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-colors duration-200\"\r\n                  title=\"Mark as read\"\r\n                >\r\n                  <i className=\"ri-check-line mr-1\"></i>\r\n                  Mark as Read\r\n                </button>\r\n              )}\r\n              {notification.is_read && (\r\n                <span className=\"text-xs text-green-600 dark:text-green-400 flex items-center\">\r\n                  <i className=\"ri-check-line mr-1\"></i>\r\n                  Read\r\n                </span>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Unread Indicator */}\r\n        {!notification.is_read && (\r\n          <div className=\"flex-shrink-0 w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full\"></div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotificationItem;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAWA,MAAM,mBAAoD,CAAC,EACzD,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACpB;IACC,MAAM,gBAAgB,CAAC;QACrB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;QAEpE,IAAI,gBAAgB,IAAI;YACtB,OAAO;QACT,OAAO,IAAI,gBAAgB,MAAM;YAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;YAC3C,OAAO,GAAG,QAAQ,OAAO,EAAE,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC;QACzD,OAAO,IAAI,gBAAgB,OAAO;YAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;YACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;QACnD,OAAO,IAAI,gBAAgB,QAAQ;YACjC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;YACxC,OAAO,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC;QAChD,OAAO;YACL,OAAO,KAAK,kBAAkB;QAChC;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ,KAAK,WAAW;YACtB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,aAAa,OAAO,EAAE;YACzB,aAAa,aAAa,eAAe;QAC3C;QACA,IAAI,qBAAqB;YACvB,oBAAoB;QACtB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,EAAE,eAAe;QACjB,aAAa,aAAa,eAAe;IAC3C;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,wIAAwI,EAClJ,CAAC,aAAa,OAAO,GAAG,mCAAmC,IAC3D;QACF,SAAS;kBAET,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAW,CAAC,oEAAoE,EACnF,CAAC,aAAa,OAAO,GACjB,oCACA,gCACJ;8BACA,cAAA,8OAAC;wBAAE,WAAW,GAAG,oBAAoB,aAAa,IAAI,EAAE,SAAS,EAC/D,CAAC,aAAa,OAAO,GACjB,qCACA,oCACJ;;;;;;;;;;;8BAIJ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAW,CAAC,oBAAoB,EAClC,CAAC,aAAa,OAAO,GACjB,qCACA,oCACJ;sDACC,aAAa,OAAO;;;;;;sDAEvB,8OAAC;4CAAE,WAAW,CAAC,aAAa,EAC1B,CAAC,aAAa,OAAO,GACjB,qCACA,oCACJ;sDACC,aAAa,OAAO;;;;;;;;;;;;gCAKxB,aAAa,QAAQ,IAAI,aAAa,QAAQ,KAAK,0BAClD,8OAAC;oCAAI,WAAW,CAAC,mBAAmB,EAAE,iBAAiB,aAAa,QAAQ,GAAG;8CAC7E,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;sCAMnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAM,cAAc,aAAa,UAAU;;;;;;wCAC3C,aAAa,WAAW,kBACvB,8OAAC;4CAAK,WAAU;sDAAc,aAAa,WAAW;;;;;;;;;;;;8CAK1D,8OAAC;oCAAI,WAAU;;wCACZ,CAAC,aAAa,OAAO,kBACpB,8OAAC;4CACC,SAAS;4CACT,WAAU;4CACV,OAAM;;8DAEN,8OAAC;oDAAE,WAAU;;;;;;gDAAyB;;;;;;;wCAIzC,aAAa,OAAO,kBACnB,8OAAC;4CAAK,WAAU;;8DACd,8OAAC;oDAAE,WAAU;;;;;;gDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;gBAS/C,CAAC,aAAa,OAAO,kBACpB,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzB;uCAEe", "debugId": null}}, {"offset": {"line": 882, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useNotifications.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { notificationService } from '@/services/notificationService';\r\nimport { AppNotification } from '@/types/notification';\r\n\r\nexport interface NotificationCounts {\r\n  total: number;\r\n  unread: number;\r\n}\r\n\r\nexport interface UseNotificationsReturn {\r\n  notifications: AppNotification[];\r\n  unreadCount: number;\r\n  totalCount: number;\r\n  loading: boolean;\r\n  error: string | null;\r\n  fetchNotifications: () => Promise<void>;\r\n  markAsRead: (id: string) => Promise<void>;\r\n  markAllAsRead: () => Promise<void>;\r\n  refreshNotifications: () => Promise<void>;\r\n  getNotificationCounts: () => Promise<void>;\r\n}\r\n\r\nexport const useNotifications = (): UseNotificationsReturn => {\r\n  const [notifications, setNotifications] = useState<AppNotification[]>([]);\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [totalCount, setTotalCount] = useState(0);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const { user, isAuthenticated } = useAuth();\r\n  const { showError } = useToast();\r\n\r\n  const fetchNotifications = useCallback(async () => {\r\n    if (!isAuthenticated || !user) {\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      console.log('🔍 Fetching notifications for user:', {\r\n        userId: user.user_id,\r\n        email: user.email\r\n      });\r\n\r\n      const data = await notificationService.getUserNotifications({\r\n        page: 1,\r\n        limit: 50 // Get more notifications for better UX\r\n      });\r\n\r\n      if (data && data.notifications) {\r\n        setNotifications(data.notifications);\r\n        setUnreadCount(data.unread_count);\r\n        setTotalCount(data.total_count);\r\n      } else {\r\n        setNotifications([]);\r\n        setUnreadCount(0);\r\n        setTotalCount(0);\r\n      }\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch notifications';\r\n      setError(errorMessage);\r\n      console.error('Error fetching notifications:', err);\r\n      // Don't show toast error for initial load failures\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [isAuthenticated, user]);\r\n\r\n  const markAsRead = useCallback(async (id: string) => {\r\n    if (!isAuthenticated) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await notificationService.markAsRead(id);\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notification =>\r\n          notification.notification_id === id\r\n            ? { ...notification, status: 'read', read_at: new Date().toISOString() }\r\n            : notification\r\n        )\r\n      );\r\n\r\n      // Update unread count\r\n      setUnreadCount(prev => Math.max(0, prev - 1));\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to mark notification as read';\r\n      setError(errorMessage);\r\n      showError('Failed to mark notification as read');\r\n      console.error('Error marking notification as read:', err);\r\n    }\r\n  }, [isAuthenticated, showError]);\r\n\r\n  const markAllAsRead = useCallback(async () => {\r\n    if (!isAuthenticated || notifications.length === 0) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Mark all unread notifications as read\r\n      const unreadNotifications = notifications.filter(n => n.status === 'unread');\r\n\r\n      for (const notification of unreadNotifications) {\r\n        await markAsRead(notification.notification_id);\r\n      }\r\n\r\n      setUnreadCount(0);\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to mark all notifications as read';\r\n      setError(errorMessage);\r\n      showError('Failed to mark all notifications as read');\r\n      console.error('Error marking all notifications as read:', err);\r\n    }\r\n  }, [isAuthenticated, notifications, markAsRead, showError]);\r\n\r\n  const getNotificationCounts = useCallback(async () => {\r\n    if (!isAuthenticated) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const counts = await notificationService.getNotificationCount();\r\n      setUnreadCount(counts.unread);\r\n      setTotalCount(counts.total);\r\n    } catch (err) {\r\n      console.error('Error getting notification counts:', err);\r\n    }\r\n  }, [isAuthenticated]);\r\n\r\n  const refreshNotifications = useCallback(async () => {\r\n    await fetchNotifications();\r\n  }, [fetchNotifications]);\r\n\r\n  // Initial fetch when component mounts and user is authenticated\r\n  useEffect(() => {\r\n    if (isAuthenticated && user) {\r\n      fetchNotifications();\r\n    }\r\n  }, [isAuthenticated, user, fetchNotifications]);\r\n\r\n  // Set up polling for new notifications every 30 seconds\r\n  useEffect(() => {\r\n    if (!isAuthenticated || !user) {\r\n      return;\r\n    }\r\n\r\n    const interval = setInterval(() => {\r\n      fetchNotifications();\r\n    }, 30000); // 30 seconds\r\n\r\n    return () => clearInterval(interval);\r\n  }, [isAuthenticated, user, fetchNotifications]);\r\n\r\n  return {\r\n    notifications,\r\n    unreadCount,\r\n    totalCount,\r\n    loading,\r\n    error,\r\n    fetchNotifications,\r\n    markAsRead,\r\n    markAllAsRead,\r\n    refreshNotifications,\r\n    getNotificationCounts,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AALA;;;;;AA0BO,MAAM,mBAAmB;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACxC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAE7B,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,CAAC,mBAAmB,CAAC,MAAM;YAC7B;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,QAAQ,GAAG,CAAC,uCAAuC;gBACjD,QAAQ,KAAK,OAAO;gBACpB,OAAO,KAAK,KAAK;YACnB;YAEA,MAAM,OAAO,MAAM,sIAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC;gBAC1D,MAAM;gBACN,OAAO,GAAG,uCAAuC;YACnD;YAEA,IAAI,QAAQ,KAAK,aAAa,EAAE;gBAC9B,iBAAiB,KAAK,aAAa;gBACnC,eAAe,KAAK,YAAY;gBAChC,cAAc,KAAK,WAAW;YAChC,OAAO;gBACL,iBAAiB,EAAE;gBACnB,eAAe;gBACf,cAAc;YAChB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,mDAAmD;QACrD,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAiB;KAAK;IAE1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpC,IAAI,CAAC,iBAAiB;YACpB;QACF;QAEA,IAAI;YACF,MAAM,sIAAA,CAAA,sBAAmB,CAAC,UAAU,CAAC;YAErC,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,eAAe,KAAK,KAC7B;wBAAE,GAAG,YAAY;wBAAE,QAAQ;wBAAQ,SAAS,IAAI,OAAO,WAAW;oBAAG,IACrE;YAIR,sBAAsB;YACtB,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;QAC5C,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,UAAU;YACV,QAAQ,KAAK,CAAC,uCAAuC;QACvD;IACF,GAAG;QAAC;QAAiB;KAAU;IAE/B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,CAAC,mBAAmB,cAAc,MAAM,KAAK,GAAG;YAClD;QACF;QAEA,IAAI;YACF,wCAAwC;YACxC,MAAM,sBAAsB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;YAEnE,KAAK,MAAM,gBAAgB,oBAAqB;gBAC9C,MAAM,WAAW,aAAa,eAAe;YAC/C;YAEA,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,UAAU;YACV,QAAQ,KAAK,CAAC,4CAA4C;QAC5D;IACF,GAAG;QAAC;QAAiB;QAAe;QAAY;KAAU;IAE1D,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxC,IAAI,CAAC,iBAAiB;YACpB;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,sIAAA,CAAA,sBAAmB,CAAC,oBAAoB;YAC7D,eAAe,OAAO,MAAM;YAC5B,cAAc,OAAO,KAAK;QAC5B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,MAAM;IACR,GAAG;QAAC;KAAmB;IAEvB,gEAAgE;IAChE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,mBAAmB,MAAM;YAC3B;QACF;IACF,GAAG;QAAC;QAAiB;QAAM;KAAmB;IAE9C,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB,CAAC,MAAM;YAC7B;QACF;QAEA,MAAM,WAAW,YAAY;YAC3B;QACF,GAAG,QAAQ,aAAa;QAExB,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAiB;QAAM;KAAmB;IAE9C,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1045, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/notifications/NotificationModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Modal from '@/components/common/Modal';\r\nimport NotificationItem from './NotificationItem';\r\nimport Loader from '@/components/Loader';\r\nimport { useNotifications } from '@/hooks/useNotifications';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { AppNotification } from '@/types/notification';\r\n\r\ninterface NotificationModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nconst NotificationModal: React.FC<NotificationModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n}) => {\r\n  const {\r\n    notifications,\r\n    unreadCount,\r\n    loading,\r\n    error,\r\n    markAsRead,\r\n    markAllAsRead,\r\n    refreshNotifications,\r\n  } = useNotifications();\r\n  \r\n  const { showSuccess, showError } = useToast();\r\n  const [filter, setFilter] = useState<'all' | 'unread'>('unread');\r\n  const [localLoading, setLocalLoading] = useState(false);\r\n\r\n  // Filter notifications based on selected filter\r\n  const filteredNotifications = notifications.filter(notification => {\r\n    if (filter === 'unread') {\r\n      return !notification.read_at;\r\n    }\r\n    return true;\r\n  });\r\n\r\n\r\n  // Refresh notifications when modal opens\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      refreshNotifications();\r\n    }\r\n  }, [isOpen, refreshNotifications]);\r\n\r\n  const handleMarkAsRead = async (id: string) => {\r\n    try {\r\n      await markAsRead(id);\r\n      showSuccess('Notification marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking notification as read:', error);\r\n      showError('Failed to mark notification as read');\r\n    }\r\n  };\r\n\r\n  const handleMarkAllAsRead = async () => {\r\n    if (unreadCount === 0) {\r\n      showError('No unread notifications to mark');\r\n      return;\r\n    }\r\n\r\n    setLocalLoading(true);\r\n    try {\r\n      await markAllAsRead();\r\n      showSuccess('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      showError('Failed to mark all notifications as read');\r\n    } finally {\r\n      setLocalLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleNotificationClick = (notification: AppNotification) => {\r\n    // Handle notification click - could navigate to related page\r\n    if (notification.entity_type === 'application' && notification.entity_id) {\r\n      // Navigate to application details\r\n      window.location.href = `/customer/my-licenses?application_id=${notification.entity_id}`;\r\n    }\r\n  };\r\n\r\n  const handleRefresh = async () => {\r\n    setLocalLoading(true);\r\n    try {\r\n      await refreshNotifications();\r\n    } catch (error) {\r\n      console.error('Error refreshing notifications:', error);\r\n      showError('Failed to refresh notifications');\r\n    } finally {\r\n      setLocalLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      title=\"Notifications\"\r\n      size=\"lg\"\r\n    >\r\n      <div className=\"flex flex-col h-96\">\r\n        {/* Header with filters and actions */}\r\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center space-x-4\">\r\n            {/* Filter buttons */}\r\n            <div className=\"flex space-x-2\">\r\n              <button\r\n                onClick={() => setFilter('all')}\r\n                className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${\r\n                  filter === 'all'\r\n                    ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'\r\n                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'\r\n                }`}\r\n              >\r\n                All ({notifications.length})\r\n              </button>\r\n              <button\r\n                onClick={() => setFilter('unread')}\r\n                className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${\r\n                  filter === 'unread'\r\n                    ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'\r\n                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'\r\n                }`}\r\n              >\r\n                Unread ({unreadCount})\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Action buttons */}\r\n          <div className=\"flex items-center space-x-2\">\r\n            <button\r\n              onClick={handleRefresh}\r\n              disabled={loading || localLoading}\r\n              className=\"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 disabled:opacity-50\"\r\n              title=\"Refresh notifications\"\r\n            >\r\n              <i className={`ri-refresh-line ${(loading || localLoading) ? 'animate-spin' : ''}`}></i>\r\n            </button>\r\n            \r\n            {unreadCount > 0 && (\r\n              <button\r\n                onClick={handleMarkAllAsRead}\r\n                disabled={loading || localLoading}\r\n                className=\"px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 disabled:opacity-50\"\r\n              >\r\n                Mark all as read\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Notifications list */}\r\n        <div className=\"flex-1 overflow-y-auto\">\r\n          {loading && notifications.length === 0 ? (\r\n            <div className=\"flex items-center justify-center h-full\">\r\n              <Loader message=\"Loading notifications...\" />\r\n            </div>\r\n          ) : error ? (\r\n            <div className=\"flex flex-col items-center justify-center h-full text-center p-4\">\r\n              <i className=\"ri-error-warning-line text-4xl text-red-500 mb-2\"></i>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-1\">\r\n                Error Loading Notifications\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\r\n                {error}\r\n              </p>\r\n              <button\r\n                onClick={handleRefresh}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200\"\r\n              >\r\n                Try Again\r\n              </button>\r\n            </div>\r\n          ) : filteredNotifications.length === 0 ? (\r\n            <div className=\"flex flex-col items-center justify-center h-full text-center p-4\">\r\n              <i className=\"ri-notification-off-line text-4xl text-gray-400 mb-2\"></i>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-1\">\r\n                {filter === 'unread' ? 'No Unread Notifications' : 'No Notifications'}\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                {filter === 'unread' \r\n                  ? 'All caught up! You have no unread notifications.'\r\n                  : 'You have no notifications at this time.'\r\n                }\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"divide-y divide-gray-200 dark:divide-gray-700\">\r\n              {filteredNotifications.map((notification) => (\r\n                <NotificationItem\r\n                  key={notification.notification_id}\r\n                  notification={notification}\r\n                  onMarkAsRead={handleMarkAsRead}\r\n                  onNotificationClick={handleNotificationClick}\r\n                />\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        {filteredNotifications.length > 0 && (\r\n          <div className=\"p-4 border-t border-gray-200 dark:border-gray-700 text-center\">\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Showing {filteredNotifications.length} of {notifications.length} notifications\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default NotificationModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAeA,MAAM,oBAAsD,CAAC,EAC3D,MAAM,EACN,OAAO,EACR;IACC,MAAM,EACJ,aAAa,EACb,WAAW,EACX,OAAO,EACP,KAAK,EACL,UAAU,EACV,aAAa,EACb,oBAAoB,EACrB,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gDAAgD;IAChD,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAA;QACjD,IAAI,WAAW,UAAU;YACvB,OAAO,CAAC,aAAa,OAAO;QAC9B;QACA,OAAO;IACT;IAGA,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;QAAQ;KAAqB;IAEjC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW;YACjB,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,UAAU;QACZ;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,gBAAgB,GAAG;YACrB,UAAU;YACV;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM;YACN,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,6DAA6D;QAC7D,IAAI,aAAa,WAAW,KAAK,iBAAiB,aAAa,SAAS,EAAE;YACxE,kCAAkC;YAClC,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,qCAAqC,EAAE,aAAa,SAAS,EAAE;QACzF;IACF;IAEA,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC,qIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,OAAM;QACN,MAAK;kBAEL,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAEb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,4DAA4D,EACtE,WAAW,QACP,qEACA,iFACJ;;4CACH;4CACO,cAAc,MAAM;4CAAC;;;;;;;kDAE7B,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,4DAA4D,EACtE,WAAW,WACP,qEACA,iFACJ;;4CACH;4CACU;4CAAY;;;;;;;;;;;;;;;;;;sCAM3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,UAAU,WAAW;oCACrB,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC;wCAAE,WAAW,CAAC,gBAAgB,EAAE,AAAC,WAAW,eAAgB,iBAAiB,IAAI;;;;;;;;;;;gCAGnF,cAAc,mBACb,8OAAC;oCACC,SAAS;oCACT,UAAU,WAAW;oCACrB,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAQP,8OAAC;oBAAI,WAAU;8BACZ,WAAW,cAAc,MAAM,KAAK,kBACnC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4HAAA,CAAA,UAAM;4BAAC,SAAQ;;;;;;;;;;mEAEhB,sBACF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAG1E,8OAAC;gCAAE,WAAU;0CACV;;;;;;0CAEH,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;mEAID,sBAAsB,MAAM,KAAK,kBACnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;gCAAG,WAAU;0CACX,WAAW,WAAW,4BAA4B;;;;;;0CAErD,8OAAC;gCAAE,WAAU;0CACV,WAAW,WACR,qDACA;;;;;;;;;;;iFAKR,8OAAC;wBAAI,WAAU;kCACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,8OAAC,uJAAA,CAAA,UAAgB;gCAEf,cAAc;gCACd,cAAc;gCACd,qBAAqB;+BAHhB,aAAa,eAAe;;;;;;;;;;;;;;;gBAW1C,sBAAsB,MAAM,GAAG,mBAC9B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAA2C;4BAC7C,sBAAsB,MAAM;4BAAC;4BAAK,cAAc,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;;;;;AAO9E;uCAEe", "debugId": null}}, {"offset": {"line": 1367, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/NotificationBell.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { notificationService } from '@/services/notificationService';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport NotificationModal from '../notifications/NotificationModal';\r\n\r\ninterface NotificationBellProps {\r\n  className?: string;\r\n}\r\n\r\nconst NotificationBell: React.FC<NotificationBellProps> = ({ className = '' }) => {\r\n  const { user } = useAuth();\r\n  const { showError } = useToast();\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n\r\n  // Fetch notification count\r\n  const fetchNotificationCount = async () => {\r\n    if (!user) return;\r\n    \r\n    try {\r\n      const data = await notificationService.getNotificationCount();\r\n      setUnreadCount(data.unread);\r\n    } catch (error) {\r\n      console.error('Error fetching notification count:', error);\r\n      showError('Failed to load notification count');\r\n    }\r\n  };\r\n\r\n  // Initial fetch\r\n  useEffect(() => {\r\n    fetchNotificationCount();\r\n  }, [user]);\r\n\r\n  // Poll for new notifications every 30 seconds\r\n  useEffect(() => {\r\n    const interval = setInterval(fetchNotificationCount, 30000);\r\n    return () => clearInterval(interval);\r\n  }, [user]);\r\n\r\n  // Handle modal close and refresh count\r\n  const handleModalClose = () => {\r\n    setIsModalOpen(false);\r\n    fetchNotificationCount(); // Refresh count when modal closes\r\n  };\r\n\r\n  if (!user) return null;\r\n\r\n  return (\r\n    <>\r\n      <div className={`relative ${className}`}>\r\n        {/* Notification Bell */}\r\n        <button\r\n          onClick={() => setIsModalOpen(true)}\r\n          className=\"relative p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800\"\r\n          title=\"Notifications\"\r\n        >\r\n          <i className=\"ri-notification-line text-xl\"></i>\r\n          {unreadCount > 0 && (\r\n            <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[1.25rem] h-5 flex items-center justify-center px-1\">\r\n              {unreadCount > 99 ? '99+' : unreadCount}\r\n            </span>\r\n          )}\r\n        </button>\r\n      </div>\r\n\r\n      {/* Notification Modal */}\r\n      <NotificationModal\r\n        isOpen={isModalOpen}\r\n        onClose={handleModalClose}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default NotificationBell;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAYA,MAAM,mBAAoD,CAAC,EAAE,YAAY,EAAE,EAAE;IAC3E,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2BAA2B;IAC3B,MAAM,yBAAyB;QAC7B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,OAAO,MAAM,sIAAA,CAAA,sBAAmB,CAAC,oBAAoB;YAC3D,eAAe,KAAK,MAAM;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,UAAU;QACZ;IACF;IAEA,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAK;IAET,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY,wBAAwB;QACrD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAK;IAET,uCAAuC;IACvC,MAAM,mBAAmB;QACvB,eAAe;QACf,0BAA0B,kCAAkC;IAC9D;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;;0BACE,8OAAC;gBAAI,WAAW,CAAC,SAAS,EAAE,WAAW;0BAErC,cAAA,8OAAC;oBACC,SAAS,IAAM,eAAe;oBAC9B,WAAU;oBACV,OAAM;;sCAEN,8OAAC;4BAAE,WAAU;;;;;;wBACZ,cAAc,mBACb,8OAAC;4BAAK,WAAU;sCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAOpC,8OAAC,wJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS;;;;;;;;AAIjB;uCAEe", "debugId": null}}, {"offset": {"line": 1469, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\nimport NotificationBell from '../common/NotificationBell';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'My Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Invoices & Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Data Protection',\r\n      href: '/customer/data-protection',\r\n      icon: 'ri-shield-keyhole-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/data-protection',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/my-licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/apply/': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/data-protection': 'Loading Data Protection...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <NotificationBell className=\"mr-4\" />\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAqBA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACpC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;SACD,EAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACjC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;SACD,EAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,MAAM,gBAAgB;gBACpB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,cAAc,OAAO,CAAC,CAAA;gBACpB,OAAO,QAAQ,CAAC;YAClB;QACF;QAEA,4DAA4D;QAC5D,MAAM,QAAQ,WAAW,eAAe;QACxC,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,uBAAuB,CAAC;IAC1B,GAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc;QAChD,MAAM,eAA0C;YAC9C,aAAa;YACb,yBAAyB;YACzB,0BAA0B;YAC1B,iCAAiC;YACjC,sBAAsB;YACtB,uBAAuB;YACvB,yBAAyB;YACzB,uBAAuB;YACvB,6BAA6B;YAC7B,kBAAkB;YAClB,qBAAqB;YACrB,sBAAsB;QACxB;QAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;QAC1D,WAAW;QACX,uBAAuB;IACzB,GAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,2CAA2C;QAC3C,OAAO,QAAQ,CAAC;IAClB,GAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,sBAAsB,CAAC;IACzB,GAAG;QAAC;KAAmB;IAEvB,qBACE,8OAAC;QAAI,WAAU;;YAEZ,qCACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,8OAAC;gBAAM,WAAW,CAAC;;QAEjB,EAAE,sBAAsB,kBAAkB,qCAAqC;MACjF,CAAC;0BACC,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,8GACA,wHACH;kBACH,CAAC;;8DAED,8OAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,OAAO,GAAG,mCAAmC,IAAI;8DACrH,cAAA,8OAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAqB5B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,8OAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;iHAGb,8OAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gJAAA,CAAA,UAAgB;4CAAC,WAAU;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC,6HAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,MAAM,iBAAiB;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,8OAAC,kIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;uCAEe", "debugId": null}}, {"offset": {"line": 1995, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/config/licenseTypeStepConfig.ts"], "sourcesContent": ["/**\r\n * License Type Step Configuration System\r\n *\r\n * SINGLE SOURCE OF TRUTH for all license type step configurations\r\n *\r\n * This consolidated configuration system defines:\r\n * - Form steps required for each license type\r\n * - Step order and navigation flow\r\n * - Validation requirements and estimated times\r\n * - Fallback configurations for unknown license types\r\n *\r\n * Supported license type codes:\r\n * - telecommunications\r\n * - postal_services\r\n * - standards_compliance\r\n * - broadcasting\r\n * - spectrum_management\r\n * - clf (Consumer Lending and Finance)\r\n * - shortcode\r\n *\r\n * Features:\r\n * - Optimized step loading based on license type codes\r\n * - Automatic fallback for unsupported types\r\n * - Smart license type resolution (UUID, code, name mapping)\r\n * - Comprehensive helper functions for navigation and progress tracking\r\n */\r\n\r\nimport { LicenseCategory } from \"@/types\";\r\n\r\n// ============================================================================\r\n// TYPE DEFINITIONS\r\n// ============================================================================\r\n\r\nexport interface StepConfig {\r\n  id: string;\r\n  name: string;\r\n  component: string;\r\n  route: string;\r\n  required: boolean;\r\n  description: string;\r\n  estimatedTime: string; // in minutes\r\n}\r\n\r\nexport interface LicenseTypeStepConfig {\r\n  licenseTypeId: string;\r\n  name: string;\r\n  description: string;\r\n  steps: StepConfig[];\r\n  estimatedTotalTime: string;\r\n  requirements: string[];\r\n}\r\n\r\n// ============================================================================\r\n// BASE STEP DEFINITIONS\r\n// ============================================================================\r\n\r\n/**\r\n * Reusable step configurations that can be composed into different license types\r\n */\r\nconst BASE_STEPS: Record<string, StepConfig> = {\r\n\r\n    // Core Information Steps\r\n  applicantInfo: {\r\n    id: 'applicant-info',\r\n    name: 'Applicant Information',\r\n    component: 'ApplicantInfo',\r\n    route: 'applicant-info',\r\n    required: true,\r\n    description: 'Personal or company information of the applicant',\r\n    estimatedTime: '5'\r\n  },\r\n\r\n  // Equipment-related Steps\r\n  equipmentDetails: {\r\n    id: 'equipment-details',\r\n    name: 'Details for Equipment',\r\n    component: 'EquipmentDetails',\r\n    route: 'equipment-details',\r\n    required: true,\r\n    description: 'Equipment details including IMEI, brand, model, and manufacturer information',\r\n    estimatedTime: '10'\r\n  },\r\n\r\n\r\n\r\n  addressInfo: {\r\n    id: 'address-info',\r\n    name: 'Address Information',\r\n    component: 'AddressInfo',\r\n    route: 'address-info',\r\n    required: true,\r\n    description: 'Physical and postal address details',\r\n    estimatedTime: '3'\r\n  },\r\n\r\n  contactInfo: {\r\n    id: 'contact-info',\r\n    name: 'Contact Information',\r\n    component: 'ContactInfo',\r\n    route: 'contact-info',\r\n    required: true,\r\n    description: 'Contact details and communication preferences',\r\n    estimatedTime: '5'\r\n  },\r\n\r\n  // Organizational Steps\r\n  management: {\r\n    id: 'management',\r\n    name: 'Management Structure',\r\n    component: 'Management',\r\n    route: 'management',\r\n    required: false,\r\n    description: 'Management team and organizational structure',\r\n    estimatedTime: '8'\r\n  },\r\n\r\n  professionalServices: {\r\n    id: 'professional-services',\r\n    name: 'Professional Services',\r\n    component: 'ProfessionalServices',\r\n    route: 'professional-services',\r\n    required: false,\r\n    description: 'External consultants and service providers',\r\n    estimatedTime: '6'\r\n  },\r\n\r\n  // Service and Compliance Steps\r\n  serviceScope: {\r\n    id: 'service-scope',\r\n    name: 'Service Scope',\r\n    component: 'ServiceScope',\r\n    route: 'service-scope',\r\n    required: true,\r\n    description: 'Services offered and geographic coverage',\r\n    estimatedTime: '8'\r\n  },\r\n\r\n  legalHistory: {\r\n    id: 'legal-history',\r\n    name: 'Legal History',\r\n    component: 'LegalHistory',\r\n    route: 'legal-history',\r\n    required: true,\r\n    description: 'Legal compliance and regulatory history',\r\n    estimatedTime: '5'\r\n  },\r\n\r\n  // Shortcode-specific Steps\r\n  shortCodeUsage: {\r\n    id: 'short-code-usage',\r\n    name: 'Short Code Usage',\r\n    component: 'ShortCodeUsage',\r\n    route: 'short-code-usage',\r\n    required: true,\r\n    description: 'Short code usage and service description',\r\n    estimatedTime: '5'\r\n  },\r\n\r\n  // Final Steps\r\n  documents: {\r\n    id: 'documents',\r\n    name: 'Required Documents',\r\n    component: 'Documents',\r\n    route: 'documents',\r\n    required: true,\r\n    description: 'Upload required documents for license application',\r\n    estimatedTime: '10'\r\n  },\r\n\r\n  submit: {\r\n    id: 'submit',\r\n    name: 'Finalise Application',\r\n    component: 'Submit',\r\n    route: 'submit',\r\n    required: true,\r\n    description: 'Final review and submission of application',\r\n    estimatedTime: '5'\r\n  }\r\n};\r\n\r\n// ============================================================================\r\n// LICENSE TYPE CONFIGURATIONS\r\n// ============================================================================\r\n\r\n/**\r\n * License type specific step configurations\r\n * Each configuration defines the complete application flow for a specific license type\r\n */\r\nexport const LICENSE_TYPE_STEP_CONFIGS: Record<string, LicenseTypeStepConfig> = {\r\n  telecommunications: {\r\n    licenseTypeId: 'telecommunications',\r\n    name: 'Telecommunications License',\r\n    description: 'License for telecommunications service providers including ISPs, mobile operators, and fixed-line services',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.serviceScope,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '97 minutes',\r\n    requirements: [\r\n      'Business registration certificate',\r\n      'Tax compliance certificate',\r\n      'Technical specifications',\r\n      'Financial statements',\r\n      'Management CVs',\r\n      'Network coverage plans'\r\n    ]\r\n  },\r\n\r\n  postal_services: {\r\n    licenseTypeId: 'postal_services',\r\n    name: 'Postal Services License',\r\n    description: 'License for postal and courier service providers',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '65 minutes',\r\n    requirements: [\r\n      'Business registration certificate',\r\n      'Fleet inventory',\r\n      'Service coverage map',\r\n      'Insurance certificates',\r\n      'Premises documentation'\r\n    ]\r\n  },\r\n\r\n  standards_compliance: {\r\n    licenseTypeId: 'standards_compliance',\r\n    name: 'Standards Compliance License',\r\n    description: 'License for standards compliance and certification services',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.equipmentDetails,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.professionalServices,\r\n      BASE_STEPS.serviceScope,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '87 minutes',\r\n    requirements: [\r\n      'Device IMEI number',\r\n      'Accreditation certificates',\r\n      'Technical competency proof',\r\n      'Quality management system',\r\n      'Laboratory facilities documentation',\r\n      'Staff qualifications'\r\n    ]\r\n  },\r\n\r\n  type_approval_certificate: {\r\n    licenseTypeId: 'type_approval_certificate',\r\n    name: 'Type Approval Certificate',\r\n    description: 'Certificate for equipment type approval and standards compliance',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.equipmentDetails,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '25 minutes',\r\n    requirements: [\r\n      'Proof of Payment',\r\n      'ETSI Documents',\r\n      'Test Reports from accredited Labs',\r\n      'Technical Specifications',\r\n      'Authorization Letter (Power of Attorney)',\r\n      'Declaration of Conformity',\r\n      'Any Copies of Approval from ITU Region 1'\r\n    ]\r\n  },\r\n\r\n  broadcasting: {\r\n    licenseTypeId: 'broadcasting',\r\n    name: 'Broadcasting License',\r\n    description: 'License for radio and television broadcasting services',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.serviceScope,\r\n      BASE_STEPS.professionalServices,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '86 minutes',\r\n    requirements: [\r\n      'Broadcasting equipment specifications',\r\n      'Content programming plan',\r\n      'Studio facility documentation',\r\n      'Transmission coverage maps',\r\n      'Local content compliance plan'\r\n    ]\r\n  },\r\n\r\n  spectrum_management: {\r\n    licenseTypeId: 'spectrum_management',\r\n    name: 'Spectrum Management License',\r\n    description: 'License for radio frequency spectrum management and allocation',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.serviceScope,\r\n      BASE_STEPS.professionalServices,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '89 minutes',\r\n    requirements: [\r\n      'Spectrum usage plan',\r\n      'Technical interference analysis',\r\n      'Equipment type approval',\r\n      'Frequency coordination agreements',\r\n      'Monitoring capabilities documentation'\r\n    ]\r\n  },\r\n\r\n  shortcode: {\r\n    licenseTypeId: 'shortcode',\r\n    name: 'Short Code License',\r\n    description: 'Apply for an SMS short code allocation',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.shortCodeUsage,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '15 minutes',\r\n    requirements: [\r\n      'Any other required documents'\r\n    ]\r\n  }, \r\n\r\n  clf: {\r\n    licenseTypeId: 'clf',\r\n    name: 'Consumer Lending and Finance License',\r\n    description: 'License for consumer lending and finance services',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.professionalServices,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '50 minutes',\r\n    requirements: [\r\n      'Financial institution license',\r\n      'Capital adequacy documentation',\r\n      'Risk management framework',\r\n      'Consumer protection policies',\r\n      'Anti-money laundering procedures'\r\n    ]\r\n  } \r\n};\r\n\r\n// ============================================================================\r\n// MAPPING AND FALLBACK CONFIGURATIONS\r\n// ============================================================================\r\n\r\n/**\r\n * License type name to configuration key mapping\r\n * Supports various naming conventions and aliases\r\n */\r\nconst LICENSE_TYPE_NAME_MAPPING: Record<string, string> = {\r\n  'telecommunications': 'telecommunications',\r\n  'postal_services': 'postal_services',\r\n  'standards_compliance': 'standards_compliance',\r\n  'broadcasting': 'broadcasting',\r\n  'spectrum_management': 'spectrum_management',\r\n  'clf': 'clf',\r\n  'consumer lending and finance': 'clf',\r\n  'short_code_allocation': 'shortcode',\r\n  'shortcode': 'shortcode',\r\n  'short_code': 'shortcode',\r\n  'short code': 'shortcode',\r\n  'Short Code License': 'shortcode',\r\n  'type_approval_certificate': 'type_approval_certificate',\r\n  'Type Approval Certificate': 'type_approval_certificate'\r\n};\r\n\r\n/**\r\n * Default fallback configuration for unknown license types\r\n * Includes all possible steps to ensure comprehensive coverage\r\n */\r\nconst DEFAULT_FALLBACK_CONFIG: LicenseTypeStepConfig = {\r\n  licenseTypeId: 'default',\r\n  name: 'License Application',\r\n  description: 'Comprehensive application process with all required steps',\r\n  steps: [\r\n    BASE_STEPS.applicantInfo,\r\n    BASE_STEPS.addressInfo,\r\n    BASE_STEPS.contactInfo,\r\n    BASE_STEPS.management,\r\n    BASE_STEPS.professionalServices,\r\n    BASE_STEPS.serviceScope,\r\n    BASE_STEPS.legalHistory,\r\n    BASE_STEPS.documents,\r\n    BASE_STEPS.submit\r\n  ],\r\n  estimatedTotalTime: '55 minutes',\r\n  requirements: [\r\n    'Business registration certificate',\r\n    'Tax compliance certificate',\r\n    'Financial statements',\r\n    'Management CVs',\r\n    'Professional qualifications',\r\n    'Service documentation'\r\n  ]\r\n};\r\n\r\nvar  licenseCategory:LicenseCategory| null = null\r\n\r\n\r\n// ============================================================================\r\n// CORE HELPER FUNCTIONS\r\n// ============================================================================\r\n\r\n/**\r\n * Get license type step configuration with intelligent fallback\r\n * Supports multiple lookup strategies for maximum compatibility\r\n */\r\nexport const getLicenseTypeStepConfig = (\r\n  licenseTypeCode: string,\r\n  category: LicenseCategory | null = null\r\n): LicenseTypeStepConfig => {\r\n\r\n\r\n  if (category) {\r\n  licenseCategory =category;\r\n  console.log('In License steps config, license category exists', licenseCategory);\r\n  }\r\n\r\n  // Handle license category override logic\r\n  if (licenseCategory && licenseCategory.name) {\r\n    console.log('Checking license category for specific overrides...');\r\n\r\n    // Special case: Check if this is a shortcode category\r\n    const categoryLowerName = licenseCategory.name;\r\n    const categoryCode = categoryLowerName.replace(/\\s+/g, '_');\r\n    console.log('Generated category code:', categoryCode);\r\n\r\n    if (isLicenseTypeCodeSupported(categoryCode)) {\r\n      console.log(`Using category-based code: ${categoryCode}`);\r\n      licenseTypeCode = categoryCode;\r\n    }\r\n    // Fallback: check if the category name directly matches\r\n    else if (isLicenseTypeCodeSupported(licenseCategory.name)) {\r\n      console.log(`Using category name directly: ${licenseCategory.name}`);\r\n      licenseTypeCode = licenseCategory.name;\r\n    }\r\n    // Otherwise keep the original licenseTypeCode\r\n    else {\r\n      console.log(`No category override found, keeping original: ${licenseTypeCode}`);\r\n    }\r\n  }\r\n\r\n  // Validate input\r\n  if (!licenseTypeCode || typeof licenseTypeCode !== 'string') {\r\n    return DEFAULT_FALLBACK_CONFIG;\r\n  }\r\n\r\n  // Strategy 1: Direct exact match\r\n  let config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode];\r\n  if (config) {\r\n    console.log(`✅ Found direct match for '${licenseTypeCode}':`, config.name, `(${config.steps.length} steps)`);\r\n    return config;\r\n  }\r\n\r\n  // Strategy 2: Normalized lookup (lowercase with underscores)\r\n  const normalizedId = licenseTypeCode.toLowerCase().replace(/[^a-z0-9]/g, '_');\r\n  config = LICENSE_TYPE_STEP_CONFIGS[normalizedId];\r\n  if (config) {\r\n    return config;\r\n  }\r\n\r\n  // Strategy 3: Name mapping for common variations\r\n  const mappedKey = LICENSE_TYPE_NAME_MAPPING[normalizedId];\r\n  if (mappedKey) {\r\n    return LICENSE_TYPE_STEP_CONFIGS[mappedKey];\r\n  }\r\n\r\n  // Strategy 4: Partial matching for known license type codes\r\n  const knownCodes = Object.keys(LICENSE_TYPE_STEP_CONFIGS);\r\n  const partialMatch = knownCodes.find(code =>\r\n    licenseTypeCode.toLowerCase().includes(code) ||\r\n    code.includes(licenseTypeCode.toLowerCase())\r\n  );\r\n\r\n  if (partialMatch) {\r\n    return LICENSE_TYPE_STEP_CONFIGS[partialMatch];\r\n  }\r\n\r\n  // Fallback to default configuration\r\n  return DEFAULT_FALLBACK_CONFIG;\r\n};\r\n\r\n/**\r\n * Check if a license type code is supported\r\n */\r\nexport const isLicenseTypeCodeSupported = (licenseTypeCode: string): boolean => {\r\n  // Check direct configuration match\r\n  if (LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode]) {\r\n    return true;\r\n  }\r\n\r\n  // Check name mapping\r\n  if (LICENSE_TYPE_NAME_MAPPING[licenseTypeCode]) {\r\n    return true;\r\n  }\r\n\r\n  // Check normalized version\r\n  const normalizedId = licenseTypeCode.toLowerCase().replace(/[^a-z0-9]/g, '_');\r\n  return LICENSE_TYPE_STEP_CONFIGS[normalizedId] !== undefined ||\r\n         LICENSE_TYPE_NAME_MAPPING[normalizedId] !== undefined;\r\n};\r\n\r\n/**\r\n * Get steps array for a specific license type\r\n */\r\nexport const getStepsByLicenseTypeCode = (\r\n  licenseTypeCode: string\r\n): StepConfig[] => {\r\n  return getLicenseTypeStepConfig(licenseTypeCode).steps;\r\n};\r\n\r\n// ============================================================================\r\n// STEP NAVIGATION FUNCTIONS\r\n// ============================================================================\r\n\r\n/**\r\n * Find a step by its route within a license type configuration\r\n */\r\nexport const getStepByRoute = (\r\n  licenseTypeCode: string,\r\n  stepRoute: string\r\n): StepConfig | null => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeCode);\r\n  return config.steps.find(step => step.route === stepRoute) || null;\r\n};\r\n\r\n/**\r\n * Get a step by its index position\r\n */\r\nexport const getStepByIndex = (\r\n  licenseTypeCode: string,\r\n  stepIndex: number\r\n): StepConfig | null => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeCode);\r\n  if (stepIndex < 0 || stepIndex >= config.steps.length) return null;\r\n  return config.steps[stepIndex];\r\n};\r\n\r\n/**\r\n * Get the index of a step by its route\r\n */\r\nexport const getStepIndex = (\r\n  licenseTypeCode: string,\r\n  stepRoute: string\r\n): number => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeCode);\r\n  return config.steps.findIndex(step => step.route === stepRoute);\r\n};\r\n\r\n/**\r\n * Get the next step in the sequence\r\n */\r\nexport const getNextStep = (\r\n  licenseTypeCode: string,\r\n  currentStepRoute: string\r\n): StepConfig | null => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeCode);\r\n  const currentIndex = getStepIndex(licenseTypeCode, currentStepRoute);\r\n  if (currentIndex === -1 || currentIndex >= config.steps.length - 1) return null;\r\n  return config.steps[currentIndex + 1];\r\n};\r\n\r\n/**\r\n * Get the previous step in the sequence\r\n */\r\nexport const getPreviousStep = (\r\n  licenseTypeCode: string,\r\n  currentStepRoute: string\r\n): StepConfig | null => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeCode);\r\n  const currentIndex = getStepIndex(licenseTypeCode, currentStepRoute);\r\n  if (currentIndex <= 0) return null;\r\n  return config.steps[currentIndex - 1];\r\n};\r\n\r\n// ============================================================================\r\n// UTILITY FUNCTIONS\r\n// ============================================================================\r\n\r\n/**\r\n * Get total number of steps for a license type\r\n */\r\nexport const getTotalSteps = (\r\n  licenseTypeCode: string\r\n): number => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeCode);\r\n  return config.steps.length;\r\n};\r\n\r\n/**\r\n * Get only required steps\r\n */\r\nexport const getRequiredSteps = (\r\n  licenseTypeCode: string\r\n): StepConfig[] => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeCode);\r\n  return config.steps.filter(step => step.required);\r\n};\r\n\r\n/**\r\n * Get only optional steps\r\n */\r\nexport const getOptionalSteps = (\r\n  licenseTypeCode: string\r\n): StepConfig[] => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeCode);\r\n  return config.steps.filter(step => !step.required);\r\n};\r\n\r\n/**\r\n * Calculate completion progress as percentage\r\n */\r\nexport const calculateProgress = (\r\n  licenseTypeCode: string,\r\n  completedSteps: string[]\r\n): number => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeCode);\r\n  const totalSteps = config.steps.length;\r\n  const completed = completedSteps.length;\r\n  return Math.round((completed / totalSteps) * 100);\r\n};\r\n\r\n/**\r\n * Get optimized step configuration with validation\r\n * @deprecated Use getLicenseTypeStepConfig instead\r\n */\r\nexport const getOptimizedStepConfig = (licenseTypeCode: string): LicenseTypeStepConfig => {\r\n  if (isLicenseTypeCodeSupported(licenseTypeCode)) {\r\n    return LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode];\r\n  }\r\n  return DEFAULT_FALLBACK_CONFIG;\r\n};"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC;;;;;;;;;;;;;;;;AA2BD,+EAA+E;AAC/E,wBAAwB;AACxB,+EAA+E;AAE/E;;CAEC,GACD,MAAM,aAAyC;IAE3C,yBAAyB;IAC3B,eAAe;QACb,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IAEA,0BAA0B;IAC1B,kBAAkB;QAChB,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IAIA,aAAa;QACX,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IAEA,aAAa;QACX,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IAEA,uBAAuB;IACvB,YAAY;QACV,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IAEA,sBAAsB;QACpB,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IAEA,+BAA+B;IAC/B,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IAEA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IAEA,2BAA2B;IAC3B,gBAAgB;QACd,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IAEA,cAAc;IACd,WAAW;QACT,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IAEA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;AACF;AAUO,MAAM,4BAAmE;IAC9E,oBAAoB;QAClB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,iBAAiB;QACf,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,sBAAsB;QACpB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,gBAAgB;YAC3B,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,2BAA2B;QACzB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,gBAAgB;YAC3B,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,cAAc;QACZ,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,qBAAqB;QACnB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;QACT,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;SACD;IACH;IAEA,KAAK;QACH,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAEA,+EAA+E;AAC/E,sCAAsC;AACtC,+EAA+E;AAE/E;;;CAGC,GACD,MAAM,4BAAoD;IACxD,sBAAsB;IACtB,mBAAmB;IACnB,wBAAwB;IACxB,gBAAgB;IAChB,uBAAuB;IACvB,OAAO;IACP,gCAAgC;IAChC,yBAAyB;IACzB,aAAa;IACb,cAAc;IACd,cAAc;IACd,sBAAsB;IACtB,6BAA6B;IAC7B,6BAA6B;AAC/B;AAEA;;;CAGC,GACD,MAAM,0BAAiD;IACrD,eAAe;IACf,MAAM;IACN,aAAa;IACb,OAAO;QACL,WAAW,aAAa;QACxB,WAAW,WAAW;QACtB,WAAW,WAAW;QACtB,WAAW,UAAU;QACrB,WAAW,oBAAoB;QAC/B,WAAW,YAAY;QACvB,WAAW,YAAY;QACvB,WAAW,SAAS;QACpB,WAAW,MAAM;KAClB;IACD,oBAAoB;IACpB,cAAc;QACZ;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,IAAK,kBAAwC;AAWtC,MAAM,2BAA2B,CACtC,iBACA,WAAmC,IAAI;IAIvC,IAAI,UAAU;QACd,kBAAiB;QACjB,QAAQ,GAAG,CAAC,oDAAoD;IAChE;IAEA,yCAAyC;IACzC,IAAI,mBAAmB,gBAAgB,IAAI,EAAE;QAC3C,QAAQ,GAAG,CAAC;QAEZ,sDAAsD;QACtD,MAAM,oBAAoB,gBAAgB,IAAI;QAC9C,MAAM,eAAe,kBAAkB,OAAO,CAAC,QAAQ;QACvD,QAAQ,GAAG,CAAC,4BAA4B;QAExC,IAAI,2BAA2B,eAAe;YAC5C,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,cAAc;YACxD,kBAAkB;QACpB,OAEK,IAAI,2BAA2B,gBAAgB,IAAI,GAAG;YACzD,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,gBAAgB,IAAI,EAAE;YACnE,kBAAkB,gBAAgB,IAAI;QACxC,OAEK;YACH,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,iBAAiB;QAChF;IACF;IAEA,iBAAiB;IACjB,IAAI,CAAC,mBAAmB,OAAO,oBAAoB,UAAU;QAC3D,OAAO;IACT;IAEA,iCAAiC;IACjC,IAAI,SAAS,yBAAyB,CAAC,gBAAgB;IACvD,IAAI,QAAQ;QACV,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,gBAAgB,EAAE,CAAC,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC;QAC3G,OAAO;IACT;IAEA,6DAA6D;IAC7D,MAAM,eAAe,gBAAgB,WAAW,GAAG,OAAO,CAAC,cAAc;IACzE,SAAS,yBAAyB,CAAC,aAAa;IAChD,IAAI,QAAQ;QACV,OAAO;IACT;IAEA,iDAAiD;IACjD,MAAM,YAAY,yBAAyB,CAAC,aAAa;IACzD,IAAI,WAAW;QACb,OAAO,yBAAyB,CAAC,UAAU;IAC7C;IAEA,4DAA4D;IAC5D,MAAM,aAAa,OAAO,IAAI,CAAC;IAC/B,MAAM,eAAe,WAAW,IAAI,CAAC,CAAA,OACnC,gBAAgB,WAAW,GAAG,QAAQ,CAAC,SACvC,KAAK,QAAQ,CAAC,gBAAgB,WAAW;IAG3C,IAAI,cAAc;QAChB,OAAO,yBAAyB,CAAC,aAAa;IAChD;IAEA,oCAAoC;IACpC,OAAO;AACT;AAKO,MAAM,6BAA6B,CAAC;IACzC,mCAAmC;IACnC,IAAI,yBAAyB,CAAC,gBAAgB,EAAE;QAC9C,OAAO;IACT;IAEA,qBAAqB;IACrB,IAAI,yBAAyB,CAAC,gBAAgB,EAAE;QAC9C,OAAO;IACT;IAEA,2BAA2B;IAC3B,MAAM,eAAe,gBAAgB,WAAW,GAAG,OAAO,CAAC,cAAc;IACzE,OAAO,yBAAyB,CAAC,aAAa,KAAK,aAC5C,yBAAyB,CAAC,aAAa,KAAK;AACrD;AAKO,MAAM,4BAA4B,CACvC;IAEA,OAAO,yBAAyB,iBAAiB,KAAK;AACxD;AASO,MAAM,iBAAiB,CAC5B,iBACA;IAEA,MAAM,SAAS,yBAAyB;IACxC,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,cAAc;AAChE;AAKO,MAAM,iBAAiB,CAC5B,iBACA;IAEA,MAAM,SAAS,yBAAyB;IACxC,IAAI,YAAY,KAAK,aAAa,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO;IAC9D,OAAO,OAAO,KAAK,CAAC,UAAU;AAChC;AAKO,MAAM,eAAe,CAC1B,iBACA;IAEA,MAAM,SAAS,yBAAyB;IACxC,OAAO,OAAO,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;AACvD;AAKO,MAAM,cAAc,CACzB,iBACA;IAEA,MAAM,SAAS,yBAAyB;IACxC,MAAM,eAAe,aAAa,iBAAiB;IACnD,IAAI,iBAAiB,CAAC,KAAK,gBAAgB,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG,OAAO;IAC3E,OAAO,OAAO,KAAK,CAAC,eAAe,EAAE;AACvC;AAKO,MAAM,kBAAkB,CAC7B,iBACA;IAEA,MAAM,SAAS,yBAAyB;IACxC,MAAM,eAAe,aAAa,iBAAiB;IACnD,IAAI,gBAAgB,GAAG,OAAO;IAC9B,OAAO,OAAO,KAAK,CAAC,eAAe,EAAE;AACvC;AASO,MAAM,gBAAgB,CAC3B;IAEA,MAAM,SAAS,yBAAyB;IACxC,OAAO,OAAO,KAAK,CAAC,MAAM;AAC5B;AAKO,MAAM,mBAAmB,CAC9B;IAEA,MAAM,SAAS,yBAAyB;IACxC,OAAO,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AAClD;AAKO,MAAM,mBAAmB,CAC9B;IAEA,MAAM,SAAS,yBAAyB;IACxC,OAAO,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ;AACnD;AAKO,MAAM,oBAAoB,CAC/B,iBACA;IAEA,MAAM,SAAS,yBAAyB;IACxC,MAAM,aAAa,OAAO,KAAK,CAAC,MAAM;IACtC,MAAM,YAAY,eAAe,MAAM;IACvC,OAAO,KAAK,KAAK,CAAC,AAAC,YAAY,aAAc;AAC/C;AAMO,MAAM,yBAAyB,CAAC;IACrC,IAAI,2BAA2B,kBAAkB;QAC/C,OAAO,yBAAyB,CAAC,gBAAgB;IACnD;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2498, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/applications/ApplicationProgress.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport { useRouter, useSearchParams, usePathname } from 'next/navigation';\r\nimport {\r\n  getLicenseTypeStepConfig,\r\n  StepConfig\r\n} from '@/config/licenseTypeStepConfig';\r\nimport { CustomerApiService } from '@/lib/customer-api';\r\n\r\n// Cache for license data to avoid repeated API calls\r\nconst licenseDataCache = new Map<string, {\r\n  category: any;\r\n  licenseType: any;\r\n  steps: StepConfig[];\r\n  categoryName: string;\r\n  timestamp: number;\r\n}>();\r\n\r\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\r\n\r\ninterface ApplicationProgressProps {\r\n  className?: string;\r\n  // Note: licenseCategoryId is obtained from URL searchParams, not as a direct prop\r\n  // This maintains backward compatibility while displaying the category name\r\n}\r\n\r\nconst ApplicationProgress: React.FC<ApplicationProgressProps> = ({ className = '' }) => {\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const pathname = usePathname();\r\n\r\n  // Create customer API service instance\r\n  const customerApi = useMemo(() => new CustomerApiService(), []);\r\n\r\n  // Get query parameters\r\n  const licenseCategoryId = searchParams.get('license_category_id');\r\n  const applicationId = searchParams.get('application_id');\r\n\r\n  // State\r\n  const [applicationSteps, setApplicationSteps] = useState<StepConfig[]>([]);\r\n  const [licenseCategoryName, setLicenseCategoryName] = useState<string>('');\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // Get current step from pathname (memoized)\r\n  const currentStepIndex = useMemo(() => {\r\n    if (!applicationSteps.length) return -1;\r\n    const pathSegments = pathname.split('/');\r\n    const currentStepId = pathSegments[pathSegments.length - 1];\r\n    return applicationSteps.findIndex(step => step.id === currentStepId);\r\n  }, [pathname, applicationSteps]);\r\n\r\n  // Check cache for license data\r\n  const getCachedLicenseData = useCallback((licenseCategoryId: string) => {\r\n    const cached = licenseDataCache.get(licenseCategoryId);\r\n    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\r\n      return cached;\r\n    }\r\n    return null;\r\n  }, []);\r\n\r\n  // Cache license data\r\n  const cacheLicenseData = useCallback((licenseCategoryId: string, data: any) => {\r\n    licenseDataCache.set(licenseCategoryId, {\r\n      ...data,\r\n      timestamp: Date.now()\r\n    });\r\n  }, []);\r\n\r\n  // Load data with caching and optimization\r\n  useEffect(() => {\r\n    const loadData = async () => {\r\n      try {\r\n        if (!licenseCategoryId) {\r\n          setLoading(false);\r\n          return;\r\n        }\r\n\r\n        setError(null);\r\n\r\n        // Check cache first\r\n        const cachedData = getCachedLicenseData(licenseCategoryId);\r\n        if (cachedData) {\r\n          console.log('Using cached license data for:', licenseCategoryId);\r\n          setApplicationSteps(cachedData.steps);\r\n          setLicenseCategoryName(cachedData.categoryName);\r\n          setLoading(false);\r\n          return;\r\n        }\r\n\r\n        // Load license category\r\n        const category = await customerApi.getLicenseCategory(licenseCategoryId);\r\n        if (!category?.license_type_id) {\r\n          throw new Error('License category does not have a license type ID');\r\n        }\r\n\r\n        const licenseType = await customerApi.getLicenseType(category.license_type_id);\r\n        if (!licenseType) {\r\n          throw new Error('License type not found');\r\n        }\r\n\r\n        // Use optimized step configuration\r\n        let steps: StepConfig[] = [];\r\n        steps = getLicenseTypeStepConfig(licenseType.code, category).steps;\r\n\r\n        // Extract category name\r\n        const categoryName = category.name || 'Unknown Category';\r\n\r\n        // Cache the data\r\n        cacheLicenseData(licenseCategoryId, {\r\n          category,\r\n          licenseType,\r\n          steps,\r\n          categoryName\r\n        });\r\n\r\n        setApplicationSteps(steps);\r\n        setLicenseCategoryName(categoryName);\r\n        setLoading(false);\r\n\r\n      } catch (err: any) {\r\n        console.error('Error loading application steps:', err);\r\n        setError(err.message || 'Failed to load license information');\r\n        setApplicationSteps([]);\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadData();\r\n  }, [licenseCategoryId, applicationId, customerApi, getCachedLicenseData, cacheLicenseData]);\r\n\r\n  // Navigation handlers\r\n  const handleStepClick = (stepIndex: number) => {\r\n    // Prevent navigation to future steps if not editing an existing application\r\n    if (!applicationId && stepIndex > currentStepIndex) {\r\n      return;\r\n    }\r\n\r\n    const step = applicationSteps[stepIndex];\r\n    const params = new URLSearchParams();\r\n    params.set('license_category_id', licenseCategoryId!);\r\n    if (applicationId) {\r\n      params.set('application_id', applicationId);\r\n    }\r\n    router.push(`/customer/applications/apply/${step.id}?${params.toString()}`);\r\n  };\r\n\r\n  // Loading state\r\n  if (loading) {\r\n    return (\r\n      <div className={`mb-8 ${className}`}>\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4\">\r\n          <div className=\"animate-pulse\">\r\n            <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4\"></div>\r\n            <div className=\"space-y-2\">\r\n              {[...Array(4)].map((_, i) => (\r\n                <div key={i} className=\"flex items-center p-2\">\r\n                  <div className=\"w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded-full mr-3\"></div>\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-1\"></div>\r\n                    <div className=\"h-2 bg-gray-200 dark:bg-gray-700 rounded w-1/2\"></div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error) {\r\n    return (\r\n      <div className={`mb-8 ${className}`}>\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-red-200 dark:border-red-800 p-4\">\r\n          <div className=\"flex items-center text-red-600 dark:text-red-400\">\r\n            <i className=\"ri-error-warning-line mr-2\"></i>\r\n            <span className=\"text-sm font-medium\">Failed to load progress</span>\r\n          </div>\r\n          <p className=\"text-xs text-red-500 dark:text-red-400 mt-1\">{error}</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // No steps available\r\n  if (!applicationSteps.length) {\r\n    return (\r\n      <div className={`mb-8 ${className}`}>\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4\">\r\n          <div className=\"text-center text-gray-500 dark:text-gray-400\">\r\n            <i className=\"ri-file-list-line text-2xl mb-2\"></i>\r\n            <p className=\"text-sm\">No application steps available</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={`mb-8 ${className}`}>\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4\">\r\n        <h3 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-4\">\r\n          <div className='mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md'><span className='text-blue-900 dark:text-blue-400 text-lg'>{licenseCategoryName || 'License Application'}</span> <span className='text-blue-900 dark:text-blue-400 text-lg'>- Step ({currentStepIndex + 1} of {applicationSteps.length})</span></div>\r\n        </h3>\r\n        <div className=\"space-y-2\">\r\n          {applicationSteps.map((step, index) => {\r\n            const isAccessible = applicationId || index <= currentStepIndex;\r\n            return (\r\n              <div\r\n                key={step.id}\r\n                className={`flex items-center p-2 rounded-md transition-colors ${\r\n                  isAccessible ? 'cursor-pointer' : 'cursor-not-allowed opacity-60'\r\n                } ${\r\n                  index === currentStepIndex\r\n                    ? 'bg-primary/10 border border-primary/20'\r\n                    : index < currentStepIndex\r\n                    ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'\r\n                    : 'bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600'\r\n                }`}\r\n                onClick={() => isAccessible && handleStepClick(index)}\r\n              >\r\n                <div\r\n                  className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium mr-3 ${\r\n                    index === currentStepIndex\r\n                      ? 'bg-primary text-white'\r\n                      : index < currentStepIndex\r\n                      ? 'bg-green-500 text-white'\r\n                      : 'bg-gray-300 text-gray-600 dark:bg-gray-600 dark:text-gray-300'\r\n                  }`}\r\n                >\r\n                  {index < currentStepIndex ? (\r\n                    <i className=\"ri-check-line\"></i>\r\n                  ) : (\r\n                    index + 1\r\n                  )}\r\n                </div>\r\n                <div className=\"flex-1\">\r\n                  <div className={`text-sm font-medium ${\r\n                    index === currentStepIndex\r\n                      ? 'text-primary'\r\n                      : index < currentStepIndex\r\n                      ? 'text-green-700 dark:text-green-300'\r\n                      : 'text-gray-600 dark:text-gray-400'\r\n                  }`}>\r\n                    {step.name}\r\n                  </div>\r\n                  {step.description && (\r\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                      {step.description}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n                {step.required && (\r\n                  <span className=\"text-xs text-red-500 ml-2\">*</span>\r\n                )}\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ApplicationProgress;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAIA;AARA;;;;;;AAUA,qDAAqD;AACrD,MAAM,mBAAmB,IAAI;AAQ7B,MAAM,iBAAiB,IAAI,KAAK,MAAM,YAAY;AAQlD,MAAM,sBAA0D,CAAC,EAAE,YAAY,EAAE,EAAE;IACjF,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,uCAAuC;IACvC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,IAAI,6HAAA,CAAA,qBAAkB,IAAI,EAAE;IAE9D,uBAAuB;IACvB,MAAM,oBAAoB,aAAa,GAAG,CAAC;IAC3C,MAAM,gBAAgB,aAAa,GAAG,CAAC;IAEvC,QAAQ;IACR,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACzE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,4CAA4C;IAC5C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,IAAI,CAAC,iBAAiB,MAAM,EAAE,OAAO,CAAC;QACtC,MAAM,eAAe,SAAS,KAAK,CAAC;QACpC,MAAM,gBAAgB,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;QAC3D,OAAO,iBAAiB,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACxD,GAAG;QAAC;QAAU;KAAiB;IAE/B,+BAA+B;IAC/B,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,MAAM,SAAS,iBAAiB,GAAG,CAAC;QACpC,IAAI,UAAU,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,gBAAgB;YAC5D,OAAO;QACT;QACA,OAAO;IACT,GAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,mBAA2B;QAC/D,iBAAiB,GAAG,CAAC,mBAAmB;YACtC,GAAG,IAAI;YACP,WAAW,KAAK,GAAG;QACrB;IACF,GAAG,EAAE;IAEL,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI;gBACF,IAAI,CAAC,mBAAmB;oBACtB,WAAW;oBACX;gBACF;gBAEA,SAAS;gBAET,oBAAoB;gBACpB,MAAM,aAAa,qBAAqB;gBACxC,IAAI,YAAY;oBACd,QAAQ,GAAG,CAAC,kCAAkC;oBAC9C,oBAAoB,WAAW,KAAK;oBACpC,uBAAuB,WAAW,YAAY;oBAC9C,WAAW;oBACX;gBACF;gBAEA,wBAAwB;gBACxB,MAAM,WAAW,MAAM,YAAY,kBAAkB,CAAC;gBACtD,IAAI,CAAC,UAAU,iBAAiB;oBAC9B,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,cAAc,MAAM,YAAY,cAAc,CAAC,SAAS,eAAe;gBAC7E,IAAI,CAAC,aAAa;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,mCAAmC;gBACnC,IAAI,QAAsB,EAAE;gBAC5B,QAAQ,CAAA,GAAA,sIAAA,CAAA,2BAAwB,AAAD,EAAE,YAAY,IAAI,EAAE,UAAU,KAAK;gBAElE,wBAAwB;gBACxB,MAAM,eAAe,SAAS,IAAI,IAAI;gBAEtC,iBAAiB;gBACjB,iBAAiB,mBAAmB;oBAClC;oBACA;oBACA;oBACA;gBACF;gBAEA,oBAAoB;gBACpB,uBAAuB;gBACvB,WAAW;YAEb,EAAE,OAAO,KAAU;gBACjB,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,SAAS,IAAI,OAAO,IAAI;gBACxB,oBAAoB,EAAE;gBACtB,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;QAAmB;QAAe;QAAa;QAAsB;KAAiB;IAE1F,sBAAsB;IACtB,MAAM,kBAAkB,CAAC;QACvB,4EAA4E;QAC5E,IAAI,CAAC,iBAAiB,YAAY,kBAAkB;YAClD;QACF;QAEA,MAAM,OAAO,gBAAgB,CAAC,UAAU;QACxC,MAAM,SAAS,IAAI;QACnB,OAAO,GAAG,CAAC,uBAAuB;QAClC,IAAI,eAAe;YACjB,OAAO,GAAG,CAAC,kBAAkB;QAC/B;QACA,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC5E;IAEA,gBAAgB;IAChB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAW,CAAC,KAAK,EAAE,WAAW;sBACjC,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oCAAY,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;mCAJT;;;;;;;;;;;;;;;;;;;;;;;;;;IAaxB;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAW,CAAC,KAAK,EAAE,WAAW;sBACjC,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAsB;;;;;;;;;;;;kCAExC,8OAAC;wBAAE,WAAU;kCAA+C;;;;;;;;;;;;;;;;;IAIpE;IAEA,qBAAqB;IACrB,IAAI,CAAC,iBAAiB,MAAM,EAAE;QAC5B,qBACE,8OAAC;YAAI,WAAW,CAAC,KAAK,EAAE,WAAW;sBACjC,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;4BAAE,WAAU;sCAAU;;;;;;;;;;;;;;;;;;;;;;IAKjC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,KAAK,EAAE,WAAW;kBACjC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BACZ,cAAA,8OAAC;wBAAI,WAAU;;0CAAqD,8OAAC;gCAAK,WAAU;0CAA4C,uBAAuB;;;;;;4BAA6B;0CAAC,8OAAC;gCAAK,WAAU;;oCAA2C;oCAAS,mBAAmB;oCAAE;oCAAK,iBAAiB,MAAM;oCAAC;;;;;;;;;;;;;;;;;;8BAE7S,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,MAAM;wBAC3B,MAAM,eAAe,iBAAiB,SAAS;wBAC/C,qBACE,8OAAC;4BAEC,WAAW,CAAC,mDAAmD,EAC7D,eAAe,mBAAmB,gCACnC,CAAC,EACA,UAAU,mBACN,2CACA,QAAQ,mBACR,mFACA,8EACJ;4BACF,SAAS,IAAM,gBAAgB,gBAAgB;;8CAE/C,8OAAC;oCACC,WAAW,CAAC,+EAA+E,EACzF,UAAU,mBACN,0BACA,QAAQ,mBACR,4BACA,iEACJ;8CAED,QAAQ,iCACP,8OAAC;wCAAE,WAAU;;;;;mFAEb,QAAQ;;;;;;8CAGZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,oBAAoB,EACnC,UAAU,mBACN,iBACA,QAAQ,mBACR,uCACA,oCACJ;sDACC,KAAK,IAAI;;;;;;wCAEX,KAAK,WAAW,kBACf,8OAAC;4CAAI,WAAU;sDACZ,KAAK,WAAW;;;;;;;;;;;;gCAItB,KAAK,QAAQ,kBACZ,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;;2BA5CzC,KAAK,EAAE;;;;;oBAgDlB;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 2926, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/applications/ApplicationLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { Suspense } from 'react';\r\nimport ApplicationProgress from './ApplicationProgress';\r\n\r\ninterface ApplicationLayoutProps {\r\n  children: React.ReactNode;\r\n  onSubmit?: () => void;\r\n  onSave?: () => void;\r\n  onNext?: () => void;\r\n  onPrevious?: () => void;\r\n  isSubmitting?: boolean;\r\n  isSaving?: boolean;\r\n  showNextButton?: boolean;\r\n  showPreviousButton?: boolean;\r\n  showSaveButton?: boolean;\r\n  showSubmitButton?: boolean;\r\n  nextButtonText?: string;\r\n  previousButtonText?: string;\r\n  saveButtonText?: string;\r\n  submitButtonText?: string;\r\n  nextButtonDisabled?: boolean;\r\n  previousButtonDisabled?: boolean;\r\n  saveButtonDisabled?: boolean;\r\n  submitButtonDisabled?: boolean;\r\n  className?: string;\r\n  showProgress?: boolean; // Allow disabling progress for better performance\r\n  progressFallback?: React.ReactNode; // Custom loading fallback\r\n\r\n  // Enhanced props for optimized step configuration\r\n  licenseTypeCode?: string; // For step validation and navigation\r\n  currentStepRoute?: string; // Current step identifier\r\n  stepValidationErrors?: string[]; // Step-specific validation errors\r\n  showStepInfo?: boolean; // Show step information and requirements\r\n}\r\n\r\n// Progress Loading Fallback Component\r\nconst ProgressLoadingFallback: React.FC = () => (\r\n  <div className=\"mb-8\">\r\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4\">\r\n      <div className=\"animate-pulse\">\r\n        <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4\"></div>\r\n        <div className=\"space-y-2\">\r\n          {[...Array(4)].map((_, i) => (\r\n            <div key={i} className=\"flex items-center p-2\">\r\n              <div className=\"w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded-full mr-3\"></div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-1\"></div>\r\n                <div className=\"h-2 bg-gray-200 dark:bg-gray-700 rounded w-1/2\"></div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nconst ApplicationLayout: React.FC<ApplicationLayoutProps> = ({\r\n  children,\r\n  onSubmit,\r\n  onSave,\r\n  onNext,\r\n  onPrevious,\r\n  isSubmitting = false,\r\n  isSaving = false,\r\n  showNextButton = true,\r\n  showPreviousButton = true,\r\n  showSaveButton = false,\r\n  showSubmitButton = false,\r\n  nextButtonText = 'Next',\r\n  previousButtonText = 'Previous',\r\n  saveButtonText = 'Save',\r\n  submitButtonText = 'Submit',\r\n  nextButtonDisabled = false,\r\n  previousButtonDisabled = false,\r\n  saveButtonDisabled = false,\r\n  submitButtonDisabled = false,\r\n  className = '',\r\n  showProgress = true,\r\n  progressFallback,\r\n  licenseTypeCode,\r\n  currentStepRoute,\r\n  stepValidationErrors = [],\r\n  showStepInfo = false\r\n}) => {\r\n  return (\r\n    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${className}`}>\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\r\n          {/* Progress Steps - Left Sidebar */}\r\n          {showProgress && (\r\n            <div className=\"lg:col-span-1\">\r\n              <div className=\"sticky top-8\">\r\n                <Suspense fallback={progressFallback || <ProgressLoadingFallback />}>\r\n                  <ApplicationProgress />\r\n                </Suspense>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Main Content Area */}\r\n          <div className={showProgress ? \"lg:col-span-3\" : \"lg:col-span-4\"}>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\r\n              {/* Step Information Banner */}\r\n              {showStepInfo && licenseTypeCode && currentStepRoute && (\r\n                <div className=\"border-b border-gray-200 dark:border-gray-700 p-4 bg-blue-50 dark:bg-blue-900/20\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div>\r\n                      <h3 className=\"text-sm font-medium text-blue-900 dark:text-blue-100\">\r\n                        License Type: {licenseTypeCode.replace(/_/g, ' ').toUpperCase()}\r\n                      </h3>\r\n                      <p className=\"text-xs text-blue-700 dark:text-blue-300 mt-1\">\r\n                        Current Step: {currentStepRoute.replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"text-xs text-blue-600 dark:text-blue-400\">\r\n                      <i className=\"ri-information-line mr-1\"></i>\r\n                      Optimized Configuration Active\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Validation Errors */}\r\n              {stepValidationErrors.length > 0 && (\r\n                <div className=\"border-b border-gray-200 dark:border-gray-700 p-4 bg-red-50 dark:bg-red-900/20\">\r\n                  <div className=\"flex items-start\">\r\n                    <i className=\"ri-error-warning-line text-red-500 mr-2 mt-0.5\"></i>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-red-900 dark:text-red-100 mb-2\">\r\n                        Please fix the following issues:\r\n                      </h4>\r\n                      <ul className=\"text-xs text-red-700 dark:text-red-300 space-y-1\">\r\n                        {stepValidationErrors.map((error, index) => (\r\n                          <li key={index}>• {error}</li>\r\n                        ))}\r\n                      </ul>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Form Content */}\r\n              <div className=\"p-6\">\r\n                {children}\r\n              </div>\r\n\r\n              {/* Footer with Action Buttons */}\r\n              <div className=\"px-6 py-4 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-600 rounded-b-lg\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  {/* Left Side - Previous Button */}\r\n                  <div>\r\n                    {showPreviousButton && onPrevious && (\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={onPrevious}\r\n                        disabled={previousButtonDisabled}\r\n                        className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      >\r\n                        <i className=\"ri-arrow-left-line mr-2\"></i>\r\n                        {previousButtonText}\r\n                      </button>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Right Side - Action Buttons */}\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    {/* Save Button */}\r\n                    {showSaveButton && onSave && (\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => {\r\n                          console.log('🔘 Save button clicked in ApplicationLayout');\r\n                          onSave();\r\n                        }}\r\n                        disabled={saveButtonDisabled || isSaving}\r\n                        className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      >\r\n                        {isSaving ? (\r\n                          <>\r\n                            <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\r\n                            Saving...\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <i className=\"ri-save-line mr-2\"></i>\r\n                            {saveButtonText}\r\n                          </>\r\n                        )}\r\n                      </button>\r\n                    )}\r\n\r\n                    {/* Submit Button */}\r\n                    {showSubmitButton && onSubmit && (\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={onSubmit}\r\n                        disabled={submitButtonDisabled || isSubmitting}\r\n                        className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      >\r\n                        {isSubmitting ? (\r\n                          <>\r\n                            <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\r\n                            Submitting...\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <i className=\"ri-send-plane-line mr-2\"></i>\r\n                            {submitButtonText}\r\n                          </>\r\n                        )}\r\n                      </button>\r\n                    )}\r\n\r\n                    {/* Next Button */}\r\n                    {showNextButton && onNext && (\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => {\r\n                          console.log('🔘 Next button clicked in ApplicationLayout');\r\n                          onNext();\r\n                        }}\r\n                        disabled={nextButtonDisabled}\r\n                        className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      >\r\n                        {nextButtonText}\r\n                        <i className=\"ri-arrow-right-line ml-2\"></i>\r\n                      </button>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ApplicationLayout;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAoCA,sCAAsC;AACtC,MAAM,0BAAoC,kBACxC,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;+BAJT;;;;;;;;;;;;;;;;;;;;;;;;;;AActB,MAAM,oBAAsD,CAAC,EAC3D,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,UAAU,EACV,eAAe,KAAK,EACpB,WAAW,KAAK,EAChB,iBAAiB,IAAI,EACrB,qBAAqB,IAAI,EACzB,iBAAiB,KAAK,EACtB,mBAAmB,KAAK,EACxB,iBAAiB,MAAM,EACvB,qBAAqB,UAAU,EAC/B,iBAAiB,MAAM,EACvB,mBAAmB,QAAQ,EAC3B,qBAAqB,KAAK,EAC1B,yBAAyB,KAAK,EAC9B,qBAAqB,KAAK,EAC1B,uBAAuB,KAAK,EAC5B,YAAY,EAAE,EACd,eAAe,IAAI,EACnB,gBAAgB,EAChB,eAAe,EACf,gBAAgB,EAChB,uBAAuB,EAAE,EACzB,eAAe,KAAK,EACrB;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,yCAAyC,EAAE,WAAW;kBACrE,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;oBAEZ,8BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,qMAAA,CAAA,WAAQ;gCAAC,UAAU,kCAAoB,8OAAC;;;;;0CACvC,cAAA,8OAAC,yJAAA,CAAA,UAAmB;;;;;;;;;;;;;;;;;;;;kCAO5B,8OAAC;wBAAI,WAAW,eAAe,kBAAkB;kCAC/C,cAAA,8OAAC;4BAAI,WAAU;;gCAEZ,gBAAgB,mBAAmB,kCAClC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;;4DAAuD;4DACpD,gBAAgB,OAAO,CAAC,MAAM,KAAK,WAAW;;;;;;;kEAE/D,8OAAC;wDAAE,WAAU;;4DAAgD;4DAC5C,iBAAiB,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;;;;;;;;;;;;;0DAG1F,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;;;;;oDAA+B;;;;;;;;;;;;;;;;;;gCAQnD,qBAAqB,MAAM,GAAG,mBAC7B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA0D;;;;;;kEAGxE,8OAAC;wDAAG,WAAU;kEACX,qBAAqB,GAAG,CAAC,CAAC,OAAO,sBAChC,8OAAC;;oEAAe;oEAAG;;+DAAV;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASrB,8OAAC;oCAAI,WAAU;8CACZ;;;;;;8CAIH,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;0DACE,sBAAsB,4BACrB,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAE,WAAU;;;;;;wDACZ;;;;;;;;;;;;0DAMP,8OAAC;gDAAI,WAAU;;oDAEZ,kBAAkB,wBACjB,8OAAC;wDACC,MAAK;wDACL,SAAS;4DACP,QAAQ,GAAG,CAAC;4DACZ;wDACF;wDACA,UAAU,sBAAsB;wDAChC,WAAU;kEAET,yBACC;;8EACE,8OAAC;oEAAE,WAAU;;;;;;gEAAyC;;yFAIxD;;8EACE,8OAAC;oEAAE,WAAU;;;;;;gEACZ;;;;;;;;oDAOR,oBAAoB,0BACnB,8OAAC;wDACC,MAAK;wDACL,SAAS;wDACT,UAAU,wBAAwB;wDAClC,WAAU;kEAET,6BACC;;8EACE,8OAAC;oEAAE,WAAU;;;;;;gEAAyC;;yFAIxD;;8EACE,8OAAC;oEAAE,WAAU;;;;;;gEACZ;;;;;;;;oDAOR,kBAAkB,wBACjB,8OAAC;wDACC,MAAK;wDACL,SAAS;4DACP,QAAQ,GAAG,CAAC;4DACZ;wDACF;wDACA,UAAU;wDACV,WAAU;;4DAET;0EACD,8OAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrC;uCAEe", "debugId": null}}, {"offset": {"line": 3361, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/FileUpload.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useRef } from 'react';\r\n\r\ninterface FileUploadProps {\r\n  id: string;\r\n  label: string;\r\n  accept?: string;\r\n  maxSize?: number; // in MB\r\n  required?: boolean;\r\n  value?: File | null;\r\n  onChange: (file: File | null) => void;\r\n  description?: string;\r\n  className?: string;\r\n}\r\n\r\nconst FileUpload: React.FC<FileUploadProps> = ({\r\n  id,\r\n  label,\r\n  accept = '.pdf',\r\n  maxSize = 10,\r\n  required = false,\r\n  value,\r\n  onChange,\r\n  description,\r\n  className = ''\r\n}) => {\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const [error, setError] = React.useState<string | null>(null);\r\n\r\n  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event.target.files?.[0] || null;\r\n    setError(null);\r\n    \r\n    if (file) {\r\n      // Check file size\r\n      if (file.size > maxSize * 1024 * 1024) {\r\n        setError(`File size exceeds the maximum limit of ${maxSize}MB. Please select a smaller file.`);\r\n        if (fileInputRef.current) {\r\n          fileInputRef.current.value = '';\r\n        }\r\n        return;\r\n      }\r\n      \r\n      // Check file type\r\n      if (accept && !accept.split(',').some(type => file.name.toLowerCase().endsWith(type.trim().replace('*', '')))) {\r\n        setError(`Invalid file type. Accepted formats: ${accept.replace(/\\./g, '').toUpperCase()}`);\r\n        if (fileInputRef.current) {\r\n          fileInputRef.current.value = '';\r\n        }\r\n        return;\r\n      }\r\n    }\r\n    \r\n    onChange(file);\r\n  };\r\n\r\n  const handleClick = () => {\r\n    fileInputRef.current?.click();\r\n  };\r\n\r\n  const handleRemove = (e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    onChange(null);\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <label htmlFor={id} className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n        {label} {required && <span className=\"text-red-500\">*</span>}\r\n      </label>\r\n      \r\n      <div\r\n        onClick={handleClick}\r\n        className=\"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-3 text-center cursor-pointer hover:border-primary hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors\"\r\n      >\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          accept={accept}\r\n          onChange={handleFileChange}\r\n          className=\"hidden\"\r\n          id={id}\r\n          required={required}\r\n        />\r\n        \r\n        {value ? (\r\n          <div className=\"space-y-1\">\r\n            <div className=\"flex items-center justify-center\">\r\n              <i className=\"ri-file-text-line text-2xl text-green-500\"></i>\r\n            </div>\r\n            <p className=\"text-sm font-medium text-green-600 dark:text-green-400\">\r\n              {value.name}\r\n            </p>\r\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n              {(value.size / 1024 / 1024).toFixed(2)} MB\r\n            </p>\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleRemove}\r\n              className=\"inline-flex items-center px-3 py-1 bg-red-100 text-red-700 hover:bg-red-200 rounded-md text-xs font-medium transition-colors\"\r\n            >\r\n              <i className=\"ri-delete-bin-line mr-1\"></i>\r\n              Remove\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div className=\"space-y-1\">\r\n            <div className=\"flex items-center justify-center\">\r\n              <i className=\"ri-upload-cloud-2-line text-2xl text-gray-400\"></i>\r\n            </div>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Click to upload {label.toLowerCase()}\r\n            </p>\r\n            {description && (\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-500\">\r\n                {description}\r\n              </p>\r\n            )}\r\n            <div className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\">\r\n              <i className=\"ri-folder-upload-line mr-2\"></i>\r\n              Choose File\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n      \r\n      {description && !value && !error && (\r\n        <p className=\"mt-2 text-xs text-gray-500 dark:text-gray-400\">\r\n          {description}\r\n        </p>\r\n      )}\r\n      \r\n      {error && (\r\n        <div className=\"mt-2 p-2 bg-red-50 dark:bg-red-900/30 rounded border border-red-200 dark:border-red-800\">\r\n          <p className=\"text-xs text-red-600 dark:text-red-400 flex items-start\">\r\n            <i className=\"ri-error-warning-line mr-1 mt-0.5 flex-shrink-0\"></i>\r\n            <span>{error}</span>\r\n          </p>\r\n        </div>\r\n      )}\r\n      \r\n      <p className=\"mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center\">\r\n        <i className=\"ri-information-line mr-1\"></i>\r\n        Maximum file size: {maxSize}MB\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FileUpload;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAgBA,MAAM,aAAwC,CAAC,EAC7C,EAAE,EACF,KAAK,EACL,SAAS,MAAM,EACf,UAAU,EAAE,EACZ,WAAW,KAAK,EAChB,KAAK,EACL,QAAQ,EACR,WAAW,EACX,YAAY,EAAE,EACf;IACC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,CAAC,OAAO,SAAS,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAgB;IAExD,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;QACxC,SAAS;QAET,IAAI,MAAM;YACR,kBAAkB;YAClB,IAAI,KAAK,IAAI,GAAG,UAAU,OAAO,MAAM;gBACrC,SAAS,CAAC,uCAAuC,EAAE,QAAQ,iCAAiC,CAAC;gBAC7F,IAAI,aAAa,OAAO,EAAE;oBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;gBAC/B;gBACA;YACF;YAEA,kBAAkB;YAClB,IAAI,UAAU,CAAC,OAAO,KAAK,CAAC,KAAK,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK,IAAI,GAAG,OAAO,CAAC,KAAK,OAAO;gBAC7G,SAAS,CAAC,qCAAqC,EAAE,OAAO,OAAO,CAAC,OAAO,IAAI,WAAW,IAAI;gBAC1F,IAAI,aAAa,OAAO,EAAE;oBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;gBAC/B;gBACA;YACF;QACF;QAEA,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,aAAa,OAAO,EAAE;IACxB;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,eAAe;QACjB,SAAS;QACT,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBAAM,SAAS;gBAAI,WAAU;;oBAC3B;oBAAM;oBAAE,0BAAY,8OAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAGtD,8OAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,UAAU;wBACV,WAAU;wBACV,IAAI;wBACJ,UAAU;;;;;;oBAGX,sBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;0CAEf,8OAAC;gCAAE,WAAU;0CACV,MAAM,IAAI;;;;;;0CAEb,8OAAC;gCAAE,WAAU;;oCACV,CAAC,MAAM,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;oCAAG;;;;;;;0CAEzC,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAA8B;;;;;;;;;;;;iFAK/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;0CAEf,8OAAC;gCAAE,WAAU;;oCAA2C;oCACrC,MAAM,WAAW;;;;;;;4BAEnC,6BACC,8OAAC;gCAAE,WAAU;0CACV;;;;;;0CAGL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;;;;;oCAAiC;;;;;;;;;;;;;;;;;;;YAOrD,eAAe,CAAC,SAAS,CAAC,uBACzB,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;sCACX,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;sCAAM;;;;;;;;;;;;;;;;;0BAKb,8OAAC;gBAAE,WAAU;;kCACX,8OAAC;wBAAE,WAAU;;;;;;oBAA+B;oBACxB;oBAAQ;;;;;;;;;;;;;AAIpC;uCAEe", "debugId": null}}, {"offset": {"line": 3639, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';\r\n\r\nexport const applicationService = {\r\n  // Get all applications with pagination and filters\r\n  async getApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filters?: ApplicationFilters;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    \r\n    // Add filters\r\n    if (params?.filters?.licenseTypeId) {\r\n      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);\r\n    }\r\n    if (params?.filters?.licenseCategoryId) {\r\n      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);\r\n    }\r\n    if (params?.filters?.status) {\r\n      queryParams.append('filter.status', params.filters.status);\r\n    }\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by license type (through license category)\r\n  async getApplicationsByLicenseType(licenseTypeId: string, params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    status?: ApplicationStatus;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    \r\n    // Filter by license type through license category\r\n    queryParams.append('filter.license_category.license_type_id', licenseTypeId);\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get single application by ID\r\n  async getApplication(id: string): Promise<Application> {\r\n    const response = await apiClient.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by applicant\r\n  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by status\r\n  async getApplicationsByStatus(status: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-status/${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application status\r\n  async updateApplicationStatus(id: string, status: string): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application progress\r\n  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {\r\n    const response = await apiClient.put(\r\n      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`\r\n    );\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get application statistics\r\n  async getApplicationStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/applications/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application\r\n  async createApplication(data: {\r\n    application_number: string;\r\n    applicant_id: string;\r\n    license_category_id: string;\r\n    status?: ApplicationStatus;\r\n    current_step?: number;\r\n    progress_percentage?: number;\r\n    submitted_at?: Date;\r\n  }): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.post('/applications', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application with improved error handling\r\n  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {\r\n    try {\r\n      console.log('Updating application:', id, 'with data:', data);\r\n      const response = await apiClient.put(`/applications/${id}`, data, {\r\n        timeout: 30000, // 30 second timeout\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('Error updating application:', error);\r\n\r\n      // Handle specific error cases\r\n      if (error.code === 'ECONNABORTED') {\r\n        throw new Error('Request timeout - please try again');\r\n      }\r\n\r\n      if (error.response?.status === 400) {\r\n        const message = error.response?.data?.message || 'Invalid application data';\r\n        console.error('400 Bad Request details:', error.response?.data);\r\n        throw new Error(`Bad Request: ${message}`);\r\n      }\r\n\r\n      if (error.response?.status === 429) {\r\n        throw new Error('Too many requests - please wait a moment and try again');\r\n      }\r\n\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete application\r\n  async deleteApplication(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application with applicant data\r\n  async createApplicationWithApplicant(data: {\r\n    license_type_id: string;\r\n    license_category_id: string;\r\n    applicant_data: Record<string, any>;\r\n    user_id: string;\r\n  }): Promise<Application> {\r\n    try {\r\n      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)\r\n      const now = new Date();\r\n      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');\r\n      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');\r\n      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();\r\n      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;\r\n\r\n      // Validate user_id is a proper UUID\r\n      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n      if (!uuidRegex.test(data.user_id)) {\r\n        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);\r\n      }\r\n\r\n      // Create application using user_id as applicant_id\r\n      // In most systems, the authenticated user is the applicant\r\n      const application = await this.createApplication({\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id, // Use user_id as applicant_id\r\n        license_category_id: data.license_category_id,\r\n        current_step: 1,\r\n        progress_percentage: 0 // Start with 0% progress\r\n      });\r\n\r\n      return application;\r\n    } catch (error) {\r\n      console.error('Error creating application with applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application section data\r\n  async saveApplicationSection(\r\n    applicationId: string,\r\n    sectionName: string,\r\n    sectionData: Record<string, any>\r\n  ): Promise<void> {\r\n    try {\r\n      // Try to save using form data service, but continue if it fails\r\n      let completedSections = 1; // At least one section is being saved\r\n\r\n      // Estimate progress based on section name\r\n      const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'];\r\n      const sectionIndex = sectionOrder.indexOf(sectionName);\r\n      completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;\r\n\r\n      // Calculate progress based on completed sections (excluding reviewSubmit from total)\r\n      const totalSections = 6; // Total number of form sections (excluding reviewSubmit)\r\n      const progressPercentage = Math.min(Math.round((completedSections / totalSections) * 100), 100);\r\n\r\n      // Update the application progress\r\n      await this.updateApplication(applicationId, {\r\n        progress_percentage: progressPercentage,\r\n        current_step: completedSections\r\n      });\r\n\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get application section data\r\n  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Submit application for review\r\n  async submitApplication(applicationId: string): Promise<Application> {\r\n    try {\r\n      // Update application status to submitted and set submission date\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        status: 'submitted',\r\n        submitted_at: new Date().toISOString(),\r\n        progress_percentage: 100,\r\n        current_step: 7\r\n      });\r\n\r\n      console.log('Application submitted successfully:', processApiResponse(response));\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error submitting application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get user's applications (filtered by authenticated user)\r\n  async getUserApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filter?: Record<string, string>;\r\n  }): Promise<any> {\r\n    try {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (params?.page) queryParams.append('page', params.page.toString());\r\n      if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n      if (params?.search) queryParams.append('search', params.search);\r\n      if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n      if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n\r\n      // Add filters\r\n      if (params?.filter) {\r\n        Object.entries(params.filter).forEach(([key, value]) => {\r\n          if (value) queryParams.append(`filter.${key}`, value);\r\n        });\r\n      }\r\n\r\n      const url = `/applications/user-applications${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const response = await apiClient.get(url);\r\n      const processedResponse = processApiResponse(response);\r\n\r\n      // Return paginated response structure for DataTable compatibility\r\n      if (processedResponse?.data && processedResponse?.meta) {\r\n        return processedResponse;\r\n      }\r\n\r\n      // Handle legacy non-paginated response\r\n      let applications = [];\r\n      if (processedResponse?.data) {\r\n        applications = Array.isArray(processedResponse.data) ? processedResponse.data : [];\r\n      } else if (Array.isArray(processedResponse)) {\r\n        applications = processedResponse;\r\n      } else if (processedResponse) {\r\n        applications = [processedResponse];\r\n      }\r\n\r\n      // Return in paginated format for backward compatibility\r\n      return {\r\n        data: applications,\r\n        meta: {\r\n          itemsPerPage: params?.limit || 10,\r\n          totalItems: applications.length,\r\n          currentPage: params?.page || 1,\r\n          totalPages: Math.ceil(applications.length / (params?.limit || 10)),\r\n          sortBy: [],\r\n          searchBy: [],\r\n          search: params?.search || '',\r\n          select: [],\r\n        },\r\n        links: { current: '' },\r\n      };\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application as draft\r\n  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        form_data: formData,\r\n        status: 'draft'\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error saving application as draft:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Validate application before submission\r\n  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {\r\n    try {\r\n      // Get data from entity-specific APIs for validation\r\n      let formData: Record<string, any> = {};\r\n\r\n      const errors: any[] = [];\r\n      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];\r\n\r\n      // Check if all required sections are completed\r\n      for (const section of requiredSections) {\r\n        if (!formData[section] || Object.keys(formData[section]).length === 0) {\r\n          errors.push(`${section} section is incomplete`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application status\r\n  async updateStatus(applicationId: string, status: string): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.patch(`/applications/${applicationId}/status`, { status });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error updating application status:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Assign application to an officer\r\n  async assignApplication(applicationId: string, assignedTo: string): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.patch(`/applications/${applicationId}/assign`, { assignedTo });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error assigning application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,qBAAqB;IAChC,mDAAmD;IACnD,MAAM,iBAAgB,MAOrB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,cAAc;QACd,IAAI,QAAQ,SAAS,eAAe;YAClC,YAAY,MAAM,CAAC,2CAA2C,OAAO,OAAO,CAAC,aAAa;QAC5F;QACA,IAAI,QAAQ,SAAS,mBAAmB;YACtC,YAAY,MAAM,CAAC,8BAA8B,OAAO,OAAO,CAAC,iBAAiB;QACnF;QACA,IAAI,QAAQ,SAAS,QAAQ;YAC3B,YAAY,MAAM,CAAC,iBAAiB,OAAO,OAAO,CAAC,MAAM;QAC3D;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8DAA8D;IAC9D,MAAM,8BAA6B,aAAqB,EAAE,MAKzD;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QAErE,kDAAkD;QAClD,YAAY,MAAM,CAAC,2CAA2C;QAE9D,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,4BAA2B,WAAmB;QAClD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa;QAChF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM,yBAAwB,MAAc;QAC1C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;QACxE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,4BAA4B;IAC5B,MAAM,yBAAwB,EAAU,EAAE,MAAc;QACtD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,eAAe,EAAE,QAAQ;QAClF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,2BAA0B,EAAU,EAAE,WAAmB,EAAE,kBAA0B;QACzF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAC,cAAc,EAAE,GAAG,sBAAsB,EAAE,YAAY,oBAAoB,EAAE,oBAAoB;QAEpG,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,mBAAkB,IAQvB;QACC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,kDAAkD;IAClD,MAAM,mBAAkB,EAAU,EAAE,IAA0B;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC,yBAAyB,IAAI,cAAc;YACvD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE,MAAM;gBAChE,SAAS;YACX;YACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,8BAA8B;YAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,WAAW;gBACjD,QAAQ,KAAK,CAAC,4BAA4B,MAAM,QAAQ,EAAE;gBAC1D,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,SAAS;YAC3C;YAEA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6CAA6C;IAC7C,MAAM,gCAA+B,IAKpC;QACC,IAAI;YACF,uEAAuE;YACvE,MAAM,MAAM,IAAI;YAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;YAC7D,MAAM,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;YAC7D,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;YACrE,MAAM,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;YAElE,oCAAoC;YACpC,MAAM,YAAY;YAClB,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,GAAG;gBACjC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,OAAO,CAAC,uBAAuB,CAAC;YAClF;YAEA,mDAAmD;YACnD,2DAA2D;YAC3D,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/C,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,cAAc;gBACd,qBAAqB,EAAE,yBAAyB;YAClD;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,wBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,IAAI;YACF,gEAAgE;YAChE,IAAI,oBAAoB,GAAG,sCAAsC;YAEjE,0CAA0C;YAC1C,MAAM,eAAe;gBAAC;gBAAiB;gBAAkB;gBAAgB;gBAAgB;gBAAgB;gBAAgB;aAAe;YACxI,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,oBAAoB,gBAAgB,IAAI,eAAe,IAAI;YAE3D,qFAAqF;YACrF,MAAM,gBAAgB,GAAG,yDAAyD;YAClF,MAAM,qBAAqB,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB,MAAM;YAE3F,kCAAkC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAC1C,qBAAqB;gBACrB,cAAc;YAChB;QAEF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,uBAAsB,aAAqB,EAAE,WAAmB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,UAAU,EAAE,aAAa;YAC7F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAkB,aAAqB;QAC3C,IAAI;YACF,iEAAiE;YACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,uCAAuC,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YACtE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,2DAA2D;IAC3D,MAAM,qBAAoB,MAOzB;QACC,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;YAEvE,cAAc;YACd,IAAI,QAAQ,QAAQ;gBAClB,OAAO,OAAO,CAAC,OAAO,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;oBACjD,IAAI,OAAO,YAAY,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACjD;YACF;YAEA,MAAM,MAAM,CAAC,+BAA+B,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;YAC1G,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,oBAAoB,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7C,kEAAkE;YAClE,IAAI,mBAAmB,QAAQ,mBAAmB,MAAM;gBACtD,OAAO;YACT;YAEA,uCAAuC;YACvC,IAAI,eAAe,EAAE;YACrB,IAAI,mBAAmB,MAAM;gBAC3B,eAAe,MAAM,OAAO,CAAC,kBAAkB,IAAI,IAAI,kBAAkB,IAAI,GAAG,EAAE;YACpF,OAAO,IAAI,MAAM,OAAO,CAAC,oBAAoB;gBAC3C,eAAe;YACjB,OAAO,IAAI,mBAAmB;gBAC5B,eAAe;oBAAC;iBAAkB;YACpC;YAEA,wDAAwD;YACxD,OAAO;gBACL,MAAM;gBACN,MAAM;oBACJ,cAAc,QAAQ,SAAS;oBAC/B,YAAY,aAAa,MAAM;oBAC/B,aAAa,QAAQ,QAAQ;oBAC7B,YAAY,KAAK,IAAI,CAAC,aAAa,MAAM,GAAG,CAAC,QAAQ,SAAS,EAAE;oBAChE,QAAQ,EAAE;oBACV,UAAU,EAAE;oBACZ,QAAQ,QAAQ,UAAU;oBAC1B,QAAQ,EAAE;gBACZ;gBACA,OAAO;oBAAE,SAAS;gBAAG;YACvB;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAY,aAAqB,EAAE,QAA6B;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,WAAW;gBACX,QAAQ;YACV;YACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAoB,aAAqB;QAC7C,IAAI;YACF,oDAAoD;YACpD,IAAI,WAAgC,CAAC;YAErC,MAAM,SAAgB,EAAE;YACxB,MAAM,mBAAmB;gBAAC;gBAAiB;gBAAkB;gBAAgB;aAAe;YAE5F,+CAA+C;YAC/C,KAAK,MAAM,WAAW,iBAAkB;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG;oBACrE,OAAO,IAAI,CAAC,GAAG,QAAQ,sBAAsB,CAAC;gBAChD;YACF;YAEA,OAAO;gBACL,SAAS,OAAO,MAAM,KAAK;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,cAAa,aAAqB,EAAE,MAAc;QACtD,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,cAAc,EAAE,cAAc,OAAO,CAAC,EAAE;gBAAE;YAAO;YACzF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,mCAAmC;IACnC,MAAM,mBAAkB,aAAqB,EAAE,UAAkB;QAC/D,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,cAAc,EAAE,cAAc,OAAO,CAAC,EAAE;gBAAE;YAAW;YAC7F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 3956, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/documentService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { LicenseCategoryDocument, Document, CreateDocument, UploadDocumentResponse } from '@/types';\r\n\r\n// Cache for preventing duplicate requests\r\nconst requestCache = new Map<string, Promise<any>>();\r\n\r\nexport const documentService = {\r\n  // Get all documents with pagination and filtering\r\n  async getDocuments(params?: { page?: number; limit?: number; search?: string; sortBy?: string }): Promise<any> {\r\n    try {\r\n      const queryParams = new URLSearchParams();\r\n      if (params?.page) queryParams.append('page', params.page.toString());\r\n      if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n      if (params?.search) queryParams.append('search', params.search);\r\n      if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n\r\n      const cacheKey = `/documents?${queryParams.toString()}`;\r\n\r\n      // Check if we already have a pending request for this exact query\r\n      if (requestCache.has(cacheKey)) {\r\n        console.log('Returning cached request for:', cacheKey);\r\n        return await requestCache.get(cacheKey);\r\n      }\r\n\r\n      // Create new request and cache it\r\n      const requestPromise = apiClient.get(cacheKey).then(response => {\r\n        // Remove from cache after completion\r\n        requestCache.delete(cacheKey);\r\n        return processApiResponse(response);\r\n      }).catch(error => {\r\n        // Remove from cache on error too\r\n        requestCache.delete(cacheKey);\r\n        throw error;\r\n      });\r\n\r\n      requestCache.set(cacheKey, requestPromise);\r\n      return await requestPromise;\r\n    } catch (error) {\r\n      console.error('DocumentService.getDocuments error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get documents by entity (polymorphic relationship)\r\n  async getDocumentsByEntity(entityType: string, entityId: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/documents/by-entity/${entityType}/${entityId}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('DocumentService.getDocumentsByEntity error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get documents by application\r\n  async getDocumentsByApplication(applicationId: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/documents/by-application/${applicationId}`);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get required documents for license category\r\n  async getRequiredDocumentsForLicenseCategory(licenseCategoryId: string): Promise<LicenseCategoryDocument[]> {\r\n    try {\r\n      const response = await apiClient.get(`/license-category-documents/category/${licenseCategoryId}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('DocumentService.getRequiredDocumentsForLicenseCategory error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Upload document (using the dedicated upload endpoint)\r\n  async uploadDocument(file: File, documentData: Omit<CreateDocument, 'file_name' | 'file_path' | 'file_size' | 'mime_type'>): Promise<UploadDocumentResponse> {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n      formData.append('document_type', documentData.document_type);\r\n      formData.append('entity_type', documentData.entity_type);\r\n      formData.append('entity_id', documentData.entity_id);\r\n      formData.append('is_required', (documentData.is_required || false).toString());\r\n      formData.append('file_name', file.name);\r\n\r\n      const response = await apiClient.post('/documents/upload', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n\r\n      const result = processApiResponse(response);\r\n      return {\r\n        document: result.data,\r\n        message: result.message || 'Document uploaded successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('DocumentService.uploadDocument error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Create document record (without file upload)\r\n  async createDocument(documentData: CreateDocument): Promise<Document> {\r\n    try {\r\n      const response = await apiClient.post('/documents', documentData);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('DocumentService.createDocument error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update document\r\n  async updateDocument(documentId: string, updateData: Partial<CreateDocument>): Promise<Document> {\r\n    try {\r\n      const response = await apiClient.put(`/documents/${documentId}`, updateData);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('DocumentService.updateDocument error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete document\r\n  async deleteDocument(documentId: string): Promise<void> {\r\n    try {\r\n      await apiClient.delete(`/documents/${documentId}`);\r\n    } catch (error) {\r\n      console.error('DocumentService.deleteDocument error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get document by ID\r\n  async getDocument(documentId: string): Promise<Document> {\r\n    try {\r\n      const response = await apiClient.get(`/documents/${documentId}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('DocumentService.getDocument error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Download document\r\n  async downloadDocument(documentId: string): Promise<Blob> {\r\n    try {\r\n      const response = await apiClient.get(`/documents/${documentId}/download`, {\r\n        responseType: 'blob',\r\n      });\r\n      return response.data; // Return blob directly, not processed\r\n    } catch (error) {\r\n      console.error('DocumentService.downloadDocument error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Preview document\r\n  async previewDocument(documentId: string): Promise<Blob> {\r\n    try {\r\n      const response = await apiClient.get(`/documents/${documentId}/preview`, {\r\n        responseType: 'blob',\r\n      });\r\n      return response.data; // Return blob directly for preview\r\n    } catch (error) {\r\n      console.error('DocumentService.previewDocument error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Check if document type is previewable\r\n  isPreviewable(mimeType: string = \"\"): boolean {\r\n    if (!mimeType) return false;\r\n    const previewableMimeTypes = [\r\n      'application/pdf',\r\n      'image/jpeg',\r\n      'image/jpg',\r\n      'image/png',\r\n      'image/gif',\r\n      'image/webp',\r\n      'text/plain',\r\n      'text/html',\r\n      'text/css',\r\n      'text/javascript',\r\n      'application/json',\r\n    ];\r\n\r\n    return previewableMimeTypes.includes(mimeType.toLowerCase());\r\n  },\r\n\r\n  // Check if all required documents are uploaded for an application\r\n  async checkRequiredDocuments(applicationId: string, licenseCategoryId: string): Promise<{\r\n    allUploaded: boolean;\r\n    missing: LicenseCategoryDocument[];\r\n    uploaded: Document[];\r\n  }> {\r\n    try {\r\n      // Get required documents for license category\r\n      const requiredDocs = await this.getRequiredDocumentsForLicenseCategory(licenseCategoryId);\r\n      \r\n      // Get uploaded documents for application\r\n      const data = await this.getDocumentsByApplication(applicationId);\r\n      const uploadedDocs: Document[] = data.data\r\n      \r\n      // Check which required documents are missing\r\n      const uploadedTypes = uploadedDocs.map(doc => doc.document_type);\r\n      const missing = requiredDocs.filter(reqDoc => \r\n        reqDoc.is_required && !uploadedTypes.includes(reqDoc.name.toLowerCase().replace(/\\s+/g, '_'))\r\n      );\r\n      \r\n      return {\r\n        allUploaded: missing.length === 0,\r\n        missing,\r\n        uploaded: uploadedDocs\r\n      };\r\n    } catch (error) {\r\n      console.error('DocumentService.checkRequiredDocuments error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get document types enum\r\n  getDocumentTypes(): string[] {\r\n    return [\r\n      'certificate_incorporation',\r\n      'memorandum_association',\r\n      'shareholding_structure',\r\n      'business_plan',\r\n      'financial_statements',\r\n      'technical_proposal',\r\n      'coverage_plan',\r\n      'network_diagram',\r\n      'equipment_specifications',\r\n      'insurance_certificate',\r\n      'tax_clearance',\r\n      'audited_accounts',\r\n      'bank_statement',\r\n      'cv_document',\r\n      'proof_of_payment',\r\n      'other'\r\n    ];\r\n  },\r\n\r\n  // Format document type for display\r\n  formatDocumentType(type: string): string {\r\n    return type\r\n      .split('_')\r\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\r\n      .join(' ');\r\n  },\r\n\r\n  // Map document name to DocumentType enum value\r\n  mapDocumentNameToType(documentName: string): string {\r\n    const nameToTypeMap: Record<string, string> = {\r\n      'Certificate of Incorporation': 'certificate_incorporation',\r\n      'Memorandum of Association': 'memorandum_association',\r\n      'Shareholding Structure': 'shareholding_structure',\r\n      'Business Plan': 'business_plan',\r\n      'Financial Statements': 'financial_statements',\r\n      'Technical Proposal': 'technical_proposal',\r\n      'Coverage Plan': 'coverage_plan',\r\n      'Network Diagram': 'network_diagram',\r\n      'Equipment Specifications': 'equipment_specifications',\r\n      'Insurance Certificate': 'insurance_certificate',\r\n      'Tax Clearance Certificate': 'tax_clearance',\r\n      'Tax Clearance': 'tax_clearance',\r\n      'Audited Accounts': 'audited_accounts',\r\n      'Bank Statement': 'bank_statement',\r\n      'CV Document': 'cv_document',\r\n      'Other': 'other'\r\n    };\r\n\r\n    // Try exact match first\r\n    if (nameToTypeMap[documentName]) {\r\n      return nameToTypeMap[documentName];\r\n    }\r\n\r\n    // Try case-insensitive match\r\n    const lowerName = documentName.toLowerCase();\r\n    for (const [name, type] of Object.entries(nameToTypeMap)) {\r\n      if (name.toLowerCase() === lowerName) {\r\n        return type;\r\n      }\r\n    }\r\n\r\n    // Fallback: convert name to snake_case\r\n    return documentName.toLowerCase().replace(/\\s+/g, '_');\r\n  },\r\n\r\n  // Validate file type and size\r\n  validateFile(file: File, maxSizeMB: number = 10, allowedTypes: string[] = []): {\r\n    isValid: boolean;\r\n    error?: string;\r\n  } {\r\n    // Check file size (convert MB to bytes)\r\n    const maxSizeBytes = maxSizeMB * 1024 * 1024;\r\n    if (file.size > maxSizeBytes) {\r\n      return {\r\n        isValid: false,\r\n        error: `File size must be less than ${maxSizeMB}MB`\r\n      };\r\n    }\r\n\r\n    // Check file type if specified\r\n    if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {\r\n      return {\r\n        isValid: false,\r\n        error: `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`\r\n      };\r\n    }\r\n\r\n    return { isValid: true };\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGA,0CAA0C;AAC1C,MAAM,eAAe,IAAI;AAElB,MAAM,kBAAkB;IAC7B,kDAAkD;IAClD,MAAM,cAAa,MAA4E;QAC7F,IAAI;YACF,MAAM,cAAc,IAAI;YACxB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAE9D,MAAM,WAAW,CAAC,WAAW,EAAE,YAAY,QAAQ,IAAI;YAEvD,kEAAkE;YAClE,IAAI,aAAa,GAAG,CAAC,WAAW;gBAC9B,QAAQ,GAAG,CAAC,iCAAiC;gBAC7C,OAAO,MAAM,aAAa,GAAG,CAAC;YAChC;YAEA,kCAAkC;YAClC,MAAM,iBAAiB,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC,CAAA;gBAClD,qCAAqC;gBACrC,aAAa,MAAM,CAAC;gBACpB,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAC5B,GAAG,KAAK,CAAC,CAAA;gBACP,iCAAiC;gBACjC,aAAa,MAAM,CAAC;gBACpB,MAAM;YACR;YAEA,aAAa,GAAG,CAAC,UAAU;YAC3B,OAAO,MAAM;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;IAEA,qDAAqD;IACrD,MAAM,sBAAqB,UAAkB,EAAE,QAAgB;QAC7D,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,WAAW,CAAC,EAAE,UAAU;YACrF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,2BAA0B,aAAqB;QACnD,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,0BAA0B,EAAE,eAAe;YACjF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,MAAM;QACR;IACF;IAEA,8CAA8C;IAC9C,MAAM,wCAAuC,iBAAyB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,qCAAqC,EAAE,mBAAmB;YAChG,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iEAAiE;YAC/E,MAAM;QACR;IACF;IAEA,wDAAwD;IACxD,MAAM,gBAAe,IAAU,EAAE,YAAyF;QACxH,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,iBAAiB,aAAa,aAAa;YAC3D,SAAS,MAAM,CAAC,eAAe,aAAa,WAAW;YACvD,SAAS,MAAM,CAAC,aAAa,aAAa,SAAS;YACnD,SAAS,MAAM,CAAC,eAAe,CAAC,aAAa,WAAW,IAAI,KAAK,EAAE,QAAQ;YAC3E,SAAS,MAAM,CAAC,aAAa,KAAK,IAAI;YAEtC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,qBAAqB,UAAU;gBACnE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAClC,OAAO;gBACL,UAAU,OAAO,IAAI;gBACrB,SAAS,OAAO,OAAO,IAAI;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,+CAA+C;IAC/C,MAAM,gBAAe,YAA4B;QAC/C,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,cAAc;YACpD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAe,UAAkB,EAAE,UAAmC;QAC1E,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,YAAY,EAAE;YACjE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAe,UAAkB;QACrC,IAAI;YACF,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,YAAY;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,aAAY,UAAkB;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,YAAY;YAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,oBAAoB;IACpB,MAAM,kBAAiB,UAAkB;QACvC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,SAAS,CAAC,EAAE;gBACxE,cAAc;YAChB;YACA,OAAO,SAAS,IAAI,EAAE,sCAAsC;QAC9D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,UAAkB;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,QAAQ,CAAC,EAAE;gBACvE,cAAc;YAChB;YACA,OAAO,SAAS,IAAI,EAAE,mCAAmC;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,MAAM;QACR;IACF;IAEA,wCAAwC;IACxC,eAAc,WAAmB,EAAE;QACjC,IAAI,CAAC,UAAU,OAAO;QACtB,MAAM,uBAAuB;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,OAAO,qBAAqB,QAAQ,CAAC,SAAS,WAAW;IAC3D;IAEA,kEAAkE;IAClE,MAAM,wBAAuB,aAAqB,EAAE,iBAAyB;QAK3E,IAAI;YACF,8CAA8C;YAC9C,MAAM,eAAe,MAAM,IAAI,CAAC,sCAAsC,CAAC;YAEvE,yCAAyC;YACzC,MAAM,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC;YAClD,MAAM,eAA2B,KAAK,IAAI;YAE1C,6CAA6C;YAC7C,MAAM,gBAAgB,aAAa,GAAG,CAAC,CAAA,MAAO,IAAI,aAAa;YAC/D,MAAM,UAAU,aAAa,MAAM,CAAC,CAAA,SAClC,OAAO,WAAW,IAAI,CAAC,cAAc,QAAQ,CAAC,OAAO,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ;YAG1F,OAAO;gBACL,aAAa,QAAQ,MAAM,KAAK;gBAChC;gBACA,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iDAAiD;YAC/D,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B;QACE,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,mCAAmC;IACnC,oBAAmB,IAAY;QAC7B,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;IACV;IAEA,+CAA+C;IAC/C,uBAAsB,YAAoB;QACxC,MAAM,gBAAwC;YAC5C,gCAAgC;YAChC,6BAA6B;YAC7B,0BAA0B;YAC1B,iBAAiB;YACjB,wBAAwB;YACxB,sBAAsB;YACtB,iBAAiB;YACjB,mBAAmB;YACnB,4BAA4B;YAC5B,yBAAyB;YACzB,6BAA6B;YAC7B,iBAAiB;YACjB,oBAAoB;YACpB,kBAAkB;YAClB,eAAe;YACf,SAAS;QACX;QAEA,wBAAwB;QACxB,IAAI,aAAa,CAAC,aAAa,EAAE;YAC/B,OAAO,aAAa,CAAC,aAAa;QACpC;QAEA,6BAA6B;QAC7B,MAAM,YAAY,aAAa,WAAW;QAC1C,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,OAAO,OAAO,CAAC,eAAgB;YACxD,IAAI,KAAK,WAAW,OAAO,WAAW;gBACpC,OAAO;YACT;QACF;QAEA,uCAAuC;QACvC,OAAO,aAAa,WAAW,GAAG,OAAO,CAAC,QAAQ;IACpD;IAEA,8BAA8B;IAC9B,cAAa,IAAU,EAAE,YAAoB,EAAE,EAAE,eAAyB,EAAE;QAI1E,wCAAwC;QACxC,MAAM,eAAe,YAAY,OAAO;QACxC,IAAI,KAAK,IAAI,GAAG,cAAc;YAC5B,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,4BAA4B,EAAE,UAAU,EAAE,CAAC;YACrD;QACF;QAEA,+BAA+B;QAC/B,IAAI,aAAa,MAAM,GAAG,KAAK,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YAChE,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,sCAAsC,EAAE,aAAa,IAAI,CAAC,OAAO;YAC3E;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;AACF", "debugId": null}}, {"offset": {"line": 4238, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/licenseCategoryDocumentService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { PaginatedResponse, PaginateQuery, BaseEntity, UserReference, LicenseCategoryDocument, CreateLicenseCategoryDocumentDto, UpdateLicenseCategoryDocumentDto } from '@/types';\r\n\r\n\r\nexport type LicenseCategoryDocumentsResponse = PaginatedResponse<LicenseCategoryDocument>;\r\n\r\nexport const licenseCategoryDocumentService = {\r\n  // Get all license category documents with pagination\r\n  async getLicenseCategoryDocuments(query: PaginateQuery = {}): Promise<LicenseCategoryDocumentsResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/license-category-documents?${params.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get license category document by ID\r\n  async getLicenseCategoryDocument(id: string): Promise<LicenseCategoryDocument> {\r\n    const response = await apiClient.get(`/license-category-documents/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get license category documents by license category\r\n  async getLicenseCategoryDocumentsByCategory(licenseCategoryId: string): Promise<LicenseCategoryDocument[]> {\r\n    const response = await apiClient.get(`/license-category-documents/by-license-category/${licenseCategoryId}`);\r\n    return processApiResponse(response).data;\r\n  },\r\n\r\n  // Create new license category document\r\n  async createLicenseCategoryDocument(documentData: CreateLicenseCategoryDocumentDto): Promise<LicenseCategoryDocument> {\r\n    const response = await apiClient.post('/license-category-documents', documentData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update license category document\r\n  async updateLicenseCategoryDocument(id: string, documentData: UpdateLicenseCategoryDocumentDto): Promise<LicenseCategoryDocument> {\r\n    const response = await apiClient.patch(`/license-category-documents/${id}`, documentData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete license category document\r\n  async deleteLicenseCategoryDocument(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/license-category-documents/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get all license category documents (simple list for dropdowns)\r\n  async getAllLicenseCategoryDocuments(): Promise<LicenseCategoryDocument[]> {\r\n    const response = await this.getLicenseCategoryDocuments({ limit: 1000 });\r\n    return processApiResponse(response);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAMO,MAAM,iCAAiC;IAC5C,qDAAqD;IACrD,MAAM,6BAA4B,QAAuB,CAAC,CAAC;QACzD,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,OAAO,QAAQ,IAAI;QACvF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sCAAsC;IACtC,MAAM,4BAA2B,EAAU;QACzC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,IAAI;QACxE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qDAAqD;IACrD,MAAM,uCAAsC,iBAAyB;QACnE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,gDAAgD,EAAE,mBAAmB;QAC3G,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI;IAC1C;IAEA,uCAAuC;IACvC,MAAM,+BAA8B,YAA8C;QAChF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,+BAA+B;QACrE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,mCAAmC;IACnC,MAAM,+BAA8B,EAAU,EAAE,YAA8C;QAC5F,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,4BAA4B,EAAE,IAAI,EAAE;QAC5E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,mCAAmC;IACnC,MAAM,+BAA8B,EAAU;QAC5C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,4BAA4B,EAAE,IAAI;QAC3E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,iEAAiE;IACjE,MAAM;QACJ,MAAM,WAAW,MAAM,IAAI,CAAC,2BAA2B,CAAC;YAAE,OAAO;QAAK;QACtE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 4307, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useDynamicNavigation.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { \r\n  getStepsByLicenseTypeCode,\r\n  isLicenseTypeCodeSupported,\r\n  getLicenseTypeStepConfig,\r\n  StepConfig \r\n} from '@/config/licenseTypeStepConfig';\r\nimport { CustomerApiService } from '@/lib/customer-api';\r\n\r\ninterface NavigationParams {\r\n  licenseCategoryId: string;\r\n  applicationId?: string;\r\n}\r\n\r\ninterface UseDynamicNavigationProps {\r\n  currentStepRoute: string;\r\n  licenseCategoryId: string | null;\r\n  applicationId: string | null;\r\n  context?: 'apply' | 'evaluate'; // Add context to determine path\r\n  licenseType?: string; // For evaluation context\r\n}\r\n\r\ninterface UseDynamicNavigationReturn {\r\n  // Navigation functions\r\n  handleNext: (saveFunction?: () => Promise<boolean>) => Promise<void>;\r\n  handlePrevious: () => void;\r\n  navigateToStep: (stepRoute: string) => void;\r\n  \r\n  // Step information\r\n  currentStep: StepConfig | null;\r\n  nextStep: StepConfig | null;\r\n  previousStep: StepConfig | null;\r\n  currentStepIndex: number;\r\n  totalSteps: number;\r\n  \r\n  // State\r\n  loading: boolean;\r\n  error: string | null;\r\n  licenseTypeCode: string | null;\r\n  \r\n  // Utility functions\r\n  isFirstStep: boolean;\r\n  isLastStep: boolean;\r\n  canNavigateNext: boolean;\r\n  canNavigatePrevious: boolean;\r\n}\r\n\r\nexport const useDynamicNavigation = ({\r\n  currentStepRoute,\r\n  licenseCategoryId,\r\n  applicationId,\r\n  context = 'apply',\r\n  licenseType\r\n}: UseDynamicNavigationProps): UseDynamicNavigationReturn => {\r\n  const router = useRouter();\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [licenseTypeCode, setLicenseTypeCode] = useState<string | null>(null);\r\n  const [steps, setSteps] = useState<StepConfig[]>([]);\r\n\r\n  // Create customer API service instance\r\n  const customerApi = useMemo(() => new CustomerApiService(), []);\r\n\r\n  // Load license type and steps\r\n  const loadLicenseTypeSteps = useCallback(async () => {\r\n    if (!licenseCategoryId) {\r\n      setError('License category ID is required');\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      // Get license category and type\r\n      const category = await customerApi.getLicenseCategory(licenseCategoryId);\r\n      if (!category?.license_type_id) {\r\n        throw new Error('License category does not have a license type ID');\r\n      }\r\n\r\n      // Add small delay to prevent rate limiting\r\n      await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n      const licenseType = await customerApi.getLicenseType(category.license_type_id);\r\n      if (!licenseType) {\r\n        throw new Error('License type not found');\r\n      }\r\n\r\n      const typeCode = licenseType.code || licenseType.license_type_id;\r\n      setLicenseTypeCode(typeCode);\r\n\r\n      // Get steps based on license type code\r\n      let licenseSteps: StepConfig[] = [];\r\n      \r\n      if (isLicenseTypeCodeSupported(typeCode)) {\r\n        licenseSteps = getLicenseTypeStepConfig(typeCode, category).steps;\r\n      } else {\r\n        const config = getLicenseTypeStepConfig(typeCode, category);\r\n        licenseSteps = config.steps;\r\n      }\r\n      setSteps(licenseSteps);\r\n    } catch (err: any) {\r\n      console.error('Error loading license type steps:', err);\r\n      setError(err.message || 'Failed to load navigation configuration');\r\n      \r\n      // Use default fallback steps\r\n      const fallbackConfig = getLicenseTypeStepConfig('default');\r\n      setSteps(fallbackConfig.steps);\r\n      setLicenseTypeCode('default');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [licenseCategoryId, customerApi]);\r\n\r\n  // Load steps when dependencies change\r\n  useEffect(() => {\r\n    loadLicenseTypeSteps();\r\n  }, [loadLicenseTypeSteps]);\r\n\r\n  // Computed values\r\n  const currentStepIndex = useMemo(() => {\r\n    return steps.findIndex(step => step.route === currentStepRoute);\r\n  }, [steps, currentStepRoute]);\r\n\r\n  const currentStep = useMemo(() => {\r\n    return steps[currentStepIndex] || null;\r\n  }, [steps, currentStepIndex]);\r\n\r\n  const nextStep = useMemo(() => {\r\n    return currentStepIndex >= 0 && currentStepIndex < steps.length - 1 \r\n      ? steps[currentStepIndex + 1] \r\n      : null;\r\n  }, [steps, currentStepIndex]);\r\n\r\n  const previousStep = useMemo(() => {\r\n    return currentStepIndex > 0\r\n      ? steps[currentStepIndex - 1]\r\n      : null;\r\n  }, [steps, currentStepIndex]);\r\n\r\n  const totalSteps = steps.length;\r\n  const isFirstStep = currentStepIndex === 0;\r\n  const isLastStep = currentStepIndex === steps.length - 1;\r\n  const canNavigateNext = !isLastStep && nextStep !== null;\r\n  const canNavigatePrevious = !isFirstStep && previousStep !== null;\r\n\r\n  // Navigation functions\r\n  const createNavigationUrl = useCallback((stepRoute: string) => {\r\n    const params = new URLSearchParams();\r\n\r\n    if (context === 'evaluate') {\r\n      // For evaluation context, only include application_id\r\n      if (applicationId) {\r\n        params.set('application_id', applicationId);\r\n      }\r\n      return `/applications/${licenseType}/evaluate/${stepRoute}?${params.toString()}`;\r\n    } else {\r\n      // For apply context, include license_category_id\r\n      params.set('license_category_id', licenseCategoryId || '');\r\n      if (applicationId) {\r\n        params.set('application_id', applicationId);\r\n      }\r\n      return `/customer/applications/apply/${stepRoute}?${params.toString()}`;\r\n    }\r\n  }, [context, licenseType, licenseCategoryId, applicationId]);\r\n\r\n  const navigateToStep = useCallback((stepRoute: string) => {\r\n    const url = createNavigationUrl(stepRoute);\r\n    console.log('🧭 Navigating to step:', stepRoute, 'URL:', url);\r\n    router.push(url);\r\n  }, [createNavigationUrl, router]);\r\n\r\n  const handleNext = useCallback(async (saveFunction?: () => Promise<boolean>) => {\r\n    if (!canNavigateNext || !nextStep) {\r\n      return;\r\n    }\r\n\r\n    // If save function is provided, save first\r\n    if (saveFunction) {\r\n      console.log('💾 Saving current step before navigation...');\r\n      try {\r\n        const saved = await saveFunction();\r\n        if (!saved) {\r\n          console.warn('⚠️ Save failed, not navigating');\r\n          return;\r\n        }\r\n      } catch (error: any) {\r\n        console.error('❌ Error during save operation:', error);\r\n\r\n        // Handle specific error types\r\n        if (error.message?.includes('timeout')) {\r\n          console.error('Save operation timed out');\r\n        } else if (error.message?.includes('Bad Request')) {\r\n          console.error('Invalid data provided for save operation');\r\n        } else if (error.message?.includes('Too many requests')) {\r\n          console.error('Rate limit exceeded, please wait and try again');\r\n        }\r\n\r\n        // Don't navigate if save failed\r\n        return;\r\n      }\r\n    }\r\n\r\n    navigateToStep(nextStep.route);\r\n  }, [canNavigateNext, nextStep, navigateToStep]);\r\n\r\n  const handlePrevious = useCallback(() => {\r\n    if (!canNavigatePrevious || !previousStep) {\r\n      console.warn('⚠️ Cannot navigate to previous step');\r\n      return;\r\n    }\r\n\r\n    navigateToStep(previousStep.route);\r\n  }, [canNavigatePrevious, previousStep, navigateToStep]);\r\n\r\n  return {\r\n    // Navigation functions\r\n    handleNext,\r\n    handlePrevious,\r\n    navigateToStep,\r\n    \r\n    // Step information\r\n    currentStep,\r\n    nextStep,\r\n    previousStep,\r\n    currentStepIndex,\r\n    totalSteps,\r\n    \r\n    // State\r\n    loading,\r\n    error,\r\n    licenseTypeCode,\r\n    \r\n    // Utility functions\r\n    isFirstStep,\r\n    isLastStep,\r\n    canNavigateNext,\r\n    canNavigatePrevious,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAMA;AAVA;;;;;AAkDO,MAAM,uBAAuB,CAAC,EACnC,gBAAgB,EAChB,iBAAiB,EACjB,aAAa,EACb,UAAU,OAAO,EACjB,WAAW,EACe;IAC1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAEnD,uCAAuC;IACvC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,IAAI,6HAAA,CAAA,qBAAkB,IAAI,EAAE;IAE9D,8BAA8B;IAC9B,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,IAAI,CAAC,mBAAmB;YACtB,SAAS;YACT,WAAW;YACX;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YACT,gCAAgC;YAChC,MAAM,WAAW,MAAM,YAAY,kBAAkB,CAAC;YACtD,IAAI,CAAC,UAAU,iBAAiB;gBAC9B,MAAM,IAAI,MAAM;YAClB;YAEA,2CAA2C;YAC3C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,cAAc,MAAM,YAAY,cAAc,CAAC,SAAS,eAAe;YAC7E,IAAI,CAAC,aAAa;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,YAAY,IAAI,IAAI,YAAY,eAAe;YAChE,mBAAmB;YAEnB,uCAAuC;YACvC,IAAI,eAA6B,EAAE;YAEnC,IAAI,CAAA,GAAA,sIAAA,CAAA,6BAA0B,AAAD,EAAE,WAAW;gBACxC,eAAe,CAAA,GAAA,sIAAA,CAAA,2BAAwB,AAAD,EAAE,UAAU,UAAU,KAAK;YACnE,OAAO;gBACL,MAAM,SAAS,CAAA,GAAA,sIAAA,CAAA,2BAAwB,AAAD,EAAE,UAAU;gBAClD,eAAe,OAAO,KAAK;YAC7B;YACA,SAAS;QACX,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,SAAS,IAAI,OAAO,IAAI;YAExB,6BAA6B;YAC7B,MAAM,iBAAiB,CAAA,GAAA,sIAAA,CAAA,2BAAwB,AAAD,EAAE;YAChD,SAAS,eAAe,KAAK;YAC7B,mBAAmB;QACrB,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAmB;KAAY;IAEnC,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAqB;IAEzB,kBAAkB;IAClB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,OAAO,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;IAChD,GAAG;QAAC;QAAO;KAAiB;IAE5B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,OAAO,KAAK,CAAC,iBAAiB,IAAI;IACpC,GAAG;QAAC;QAAO;KAAiB;IAE5B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,OAAO,oBAAoB,KAAK,mBAAmB,MAAM,MAAM,GAAG,IAC9D,KAAK,CAAC,mBAAmB,EAAE,GAC3B;IACN,GAAG;QAAC;QAAO;KAAiB;IAE5B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,OAAO,mBAAmB,IACtB,KAAK,CAAC,mBAAmB,EAAE,GAC3B;IACN,GAAG;QAAC;QAAO;KAAiB;IAE5B,MAAM,aAAa,MAAM,MAAM;IAC/B,MAAM,cAAc,qBAAqB;IACzC,MAAM,aAAa,qBAAqB,MAAM,MAAM,GAAG;IACvD,MAAM,kBAAkB,CAAC,cAAc,aAAa;IACpD,MAAM,sBAAsB,CAAC,eAAe,iBAAiB;IAE7D,uBAAuB;IACvB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,MAAM,SAAS,IAAI;QAEnB,IAAI,YAAY,YAAY;YAC1B,sDAAsD;YACtD,IAAI,eAAe;gBACjB,OAAO,GAAG,CAAC,kBAAkB;YAC/B;YACA,OAAO,CAAC,cAAc,EAAE,YAAY,UAAU,EAAE,UAAU,CAAC,EAAE,OAAO,QAAQ,IAAI;QAClF,OAAO;YACL,iDAAiD;YACjD,OAAO,GAAG,CAAC,uBAAuB,qBAAqB;YACvD,IAAI,eAAe;gBACjB,OAAO,GAAG,CAAC,kBAAkB;YAC/B;YACA,OAAO,CAAC,6BAA6B,EAAE,UAAU,CAAC,EAAE,OAAO,QAAQ,IAAI;QACzE;IACF,GAAG;QAAC;QAAS;QAAa;QAAmB;KAAc;IAE3D,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,MAAM,MAAM,oBAAoB;QAChC,QAAQ,GAAG,CAAC,0BAA0B,WAAW,QAAQ;QACzD,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;QAAqB;KAAO;IAEhC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpC,IAAI,CAAC,mBAAmB,CAAC,UAAU;YACjC;QACF;QAEA,2CAA2C;QAC3C,IAAI,cAAc;YAChB,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,MAAM,QAAQ,MAAM;gBACpB,IAAI,CAAC,OAAO;oBACV,QAAQ,IAAI,CAAC;oBACb;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,kCAAkC;gBAEhD,8BAA8B;gBAC9B,IAAI,MAAM,OAAO,EAAE,SAAS,YAAY;oBACtC,QAAQ,KAAK,CAAC;gBAChB,OAAO,IAAI,MAAM,OAAO,EAAE,SAAS,gBAAgB;oBACjD,QAAQ,KAAK,CAAC;gBAChB,OAAO,IAAI,MAAM,OAAO,EAAE,SAAS,sBAAsB;oBACvD,QAAQ,KAAK,CAAC;gBAChB;gBAEA,gCAAgC;gBAChC;YACF;QACF;QAEA,eAAe,SAAS,KAAK;IAC/B,GAAG;QAAC;QAAiB;QAAU;KAAe;IAE9C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,uBAAuB,CAAC,cAAc;YACzC,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,eAAe,aAAa,KAAK;IACnC,GAAG;QAAC;QAAqB;QAAc;KAAe;IAEtD,OAAO;QACL,uBAAuB;QACvB;QACA;QACA;QAEA,mBAAmB;QACnB;QACA;QACA;QACA;QACA;QAEA,QAAQ;QACR;QACA;QACA;QAEA,oBAAoB;QACpB;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 4510, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/applications/apply/documents/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useSearchParams } from 'next/navigation';\r\nimport CustomerLayout from '@/components/customer/CustomerLayout';\r\nimport ApplicationLayout from '@/components/applications/ApplicationLayout';\r\nimport DocumentPreviewModal from '@/components/documents/DocumentPreviewModal';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport FileUpload from '@/components/forms/FileUpload';\r\nimport { applicationService } from '@/services/applicationService';\r\nimport { documentService } from '@/services/documentService';\r\nimport { licenseCategoryDocumentService } from '@/services/licenseCategoryDocumentService';\r\nimport { useDynamicNavigation } from '@/hooks/useDynamicNavigation';\r\nimport { Application, LicenseCategoryDocument, Document } from '@/types';\r\n\r\nconst DocumentsPage: React.FC = () => {\r\n  const searchParams = useSearchParams();\r\n  const { isAuthenticated, loading: authLoading } = useAuth();\r\n\r\n  // URL parameters\r\n  const licenseCategoryId = searchParams.get('license_category_id');\r\n  const applicationId = searchParams.get('application_id');\r\n\r\n  // State management\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isSaving, setIsSaving] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\r\n\r\n  // Document data\r\n  const [requiredDocuments, setRequiredDocuments] = useState<LicenseCategoryDocument[]>([]);\r\n  const [uploadedDocuments, setUploadedDocuments] = useState<Document[]>([]);\r\n  const [documentFiles, setDocumentFiles] = useState<Record<string, File>>({});\r\n  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});\r\n  const [application, setApplication] = useState<Application | null>(null);\r\n\r\n  // Preview modal state\r\n  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);\r\n  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);\r\n\r\n  // Dynamic navigation hook\r\n  const {\r\n    handleNext: dynamicHandleNext,\r\n    handlePrevious: dynamicHandlePrevious,\r\n    nextStep\r\n  } = useDynamicNavigation({\r\n    currentStepRoute: 'documents',\r\n    licenseCategoryId,\r\n    applicationId\r\n  });\r\n\r\n  // Load required documents and existing uploads\r\n  useEffect(() => {\r\n    const loadData = async () => {\r\n      if (!applicationId || !licenseCategoryId || !isAuthenticated || authLoading) return;\r\n\r\n      const application = await applicationService.getApplication(applicationId);\r\n      setApplication(application)\r\n      \r\n      try {\r\n        setIsLoading(true);\r\n        setError(null);\r\n        let requiredDocsArray: LicenseCategoryDocument[] = [];\r\n\r\n        try {\r\n          const requiredDocs = await licenseCategoryDocumentService.getLicenseCategoryDocumentsByCategory(licenseCategoryId);\r\n          requiredDocsArray = Array.isArray(requiredDocs) ? requiredDocs : [];\r\n          setRequiredDocuments(requiredDocsArray);\r\n        } catch (docError: any) {\r\n          console.log('📝 No required documents found for this license category');\r\n          requiredDocsArray = [];\r\n          setRequiredDocuments([]);\r\n        }\r\n\r\n        try {\r\n          const data = await documentService.getDocumentsByApplication(applicationId);\r\n          const uploadedDocs = data.data;\r\n          const uploadedDocsArray = Array.isArray(uploadedDocs) ? uploadedDocs : [];\r\n          setUploadedDocuments(uploadedDocsArray);\r\n\r\n          if (uploadedDocsArray.length === 0) {\r\n            console.log('📝 No existing documents found - this is normal for new applications');\r\n          }\r\n        } catch (uploadError: any) {\r\n          console.error('❌ Error loading uploaded documents:', uploadError);\r\n          setUploadedDocuments([]);\r\n\r\n          if (uploadError.response?.status === 404) {\r\n            console.log('📝 No documents found for this application - this is normal for new applications');\r\n          }\r\n        }\r\n\r\n      } catch (err: any) {\r\n        setError('Failed to load documents data');\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    loadData();\r\n  }, [applicationId, licenseCategoryId, isAuthenticated, authLoading]);\r\n\r\n  // Handle file selection\r\n  const handleFileChange = (documentType: string, file: File | null) => {\r\n    setDocumentFiles(prev => {\r\n      const newFiles = { ...prev };\r\n      if (file) {\r\n        newFiles[documentType] = file;\r\n      } else {\r\n        delete newFiles[documentType];\r\n      }\r\n      return newFiles;\r\n    });\r\n\r\n    // Clear validation error for this document\r\n    if (validationErrors[documentType]) {\r\n      setValidationErrors(prev => {\r\n        const newErrors = { ...prev };\r\n        delete newErrors[documentType];\r\n        return newErrors;\r\n      });\r\n    }\r\n  };\r\n\r\n  // Upload a single document\r\n  const uploadDocument = async (documentType: string, file: File): Promise<boolean> => {\r\n    try {\r\n      setUploadProgress(prev => ({ ...prev, [documentType]: 0 }));\r\n\r\n      // Map the document name to the correct DocumentType enum value\r\n      const requiredDoc = requiredDocuments.find(doc =>\r\n        doc.name.toLowerCase().replace(/\\s+/g, '_') === documentType\r\n      );\r\n      const mappedDocumentType = requiredDoc ?\r\n        documentService.mapDocumentNameToType(requiredDoc.name) :\r\n        documentType;\r\n\r\n      const uploadData = {\r\n        document_type: mappedDocumentType,\r\n        entity_type: 'application',\r\n        entity_id: applicationId!,\r\n        is_required: requiredDoc?.is_required || false\r\n      };\r\n\r\n      const result = await documentService.uploadDocument(file, uploadData);\r\n      setUploadProgress(prev => ({ ...prev, [documentType]: 100 }));\r\n      setUploadedDocuments(prev => [...prev, result.document]);\r\n\r\n      // Remove from pending files\r\n      setDocumentFiles(prev => {\r\n        const newFiles = { ...prev };\r\n        delete newFiles[documentType];\r\n        return newFiles;\r\n      });\r\n\r\n      return true;\r\n\r\n    } catch (error: any) {\r\n      console.error('❌ Error uploading document:', error);\r\n      setValidationErrors(prev => ({\r\n        ...prev,\r\n        [documentType]: 'Failed to upload document. Please try again.'\r\n      }));\r\n      setUploadProgress(prev => {\r\n        const newProgress = { ...prev };\r\n        delete newProgress[documentType];\r\n        return newProgress;\r\n      });\r\n      return false;\r\n    }\r\n  };\r\n\r\n  // Save all documents\r\n  const handleSave = async (): Promise<boolean> => {\r\n    if (!applicationId) {\r\n      setValidationErrors({ save: 'Application ID is required' });\r\n      return false;\r\n    }\r\n\r\n    setIsSaving(true);\r\n    try {\r\n      setValidationErrors({});\r\n\r\n      // Upload all pending documents\r\n      if (Object.keys(documentFiles).length > 0) {\r\n        const uploadPromises = Object.entries(documentFiles).map(([docType, file]) =>\r\n          uploadDocument(docType, file)\r\n        );\r\n\r\n        const uploadResults = await Promise.all(uploadPromises);\r\n        const allUploaded = uploadResults.every(result => result);\r\n\r\n        if (!allUploaded) {\r\n          throw new Error('Some documents failed to upload');\r\n        }\r\n      }\r\n\r\n      // Update application progress\r\n      try {\r\n        await applicationService.updateApplication(applicationId, {\r\n          current_step: 7,\r\n          progress_percentage: 86\r\n        });\r\n      } catch (progressError) {\r\n        console.warn('Failed to update application progress:', progressError);\r\n      }\r\n\r\n      return true;\r\n\r\n    } catch (error: any) {\r\n      console.error('❌ Error saving documents:', error);\r\n      setValidationErrors({ save: 'Failed to process documents. Please try again.' });\r\n      return false;\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  // Navigation functions using dynamic navigation\r\n  const handleNext = async () => {\r\n    await dynamicHandleNext(handleSave);\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    dynamicHandlePrevious();\r\n  };\r\n\r\n  // Remove uploaded document\r\n  const handleRemoveDocument = async (documentId: string) => {\r\n    try {\r\n      await documentService.deleteDocument(documentId);\r\n      setUploadedDocuments(prev => prev.filter(doc => doc.document_id !== documentId));\r\n    } catch (error) {\r\n      console.error('❌ Error removing document:', error);\r\n      setValidationErrors(prev => ({\r\n        ...prev,\r\n        remove: 'Failed to remove document. Please try again.'\r\n      }));\r\n    }\r\n  };\r\n\r\n  // Create comprehensive document list combining required and uploaded documents\r\n  const getComprehensiveDocumentList = () => {\r\n    const documentMap = new Map();\r\n\r\n    // Add all required documents first\r\n    if (Array.isArray(requiredDocuments)) {\r\n      requiredDocuments.forEach(reqDoc => {\r\n        const docType = reqDoc.name.toLowerCase().replace(/\\s+/g, '_');\r\n        documentMap.set(docType, {\r\n          type: 'required',\r\n          requiredDoc: reqDoc,\r\n          uploadedDoc: null,\r\n          docType,\r\n          isRequired: reqDoc.is_required,\r\n          isUploaded: false\r\n        });\r\n      });\r\n    }\r\n\r\n    // Add/update with uploaded documents\r\n    uploadedDocuments.forEach(uploadedDoc => {\r\n      const docType = uploadedDoc.document_type;\r\n      const existing = documentMap.get(docType);\r\n\r\n      if (existing) {\r\n        // Update existing required document with uploaded info\r\n        documentMap.set(docType, {\r\n          ...existing,\r\n          uploadedDoc,\r\n          isUploaded: true\r\n        });\r\n      } else {\r\n        // Add uploaded document that's not in required list\r\n        documentMap.set(docType, {\r\n          type: 'uploaded',\r\n          requiredDoc: null,\r\n          uploadedDoc,\r\n          docType,\r\n          isRequired: false,\r\n          isUploaded: true\r\n        });\r\n      }\r\n    });\r\n\r\n    return Array.from(documentMap.values());\r\n  };\r\n\r\n  const comprehensiveDocumentList = getComprehensiveDocumentList();\r\n\r\n  // Handle document preview with modal\r\n  const handlePreviewDocument = (doc: Document) => {\r\n    setSelectedDocument(doc);\r\n    setIsPreviewModalOpen(true);\r\n  };\r\n\r\n  // Handle closing preview modal\r\n  const handleClosePreview = () => {\r\n    setIsPreviewModalOpen(false);\r\n    setSelectedDocument(null);\r\n  };\r\n\r\n  if (authLoading || isLoading) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"flex items-center justify-center min-h-96\">\r\n          <div className=\"text-center\">\r\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\r\n            <p className=\"text-gray-600 dark:text-gray-400\">Loading required documents...</p>\r\n          </div>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"max-w-4xl mx-auto p-6\">\r\n          <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6\">\r\n            <div className=\"flex items-center\">\r\n              <i className=\"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4\"></i>\r\n              <div>\r\n                <h3 className=\"text-lg font-medium text-red-800 dark:text-red-200\">Error Loading Documents</h3>\r\n                <p className=\"text-red-700 dark:text-red-300 mt-1\">{error}</p>\r\n                <button\r\n                  onClick={() => dynamicHandlePrevious()}\r\n                  className=\"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40\"\r\n                >\r\n                  <i className=\"ri-arrow-left-line mr-2\"></i>\r\n                  Go Back\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <CustomerLayout>\r\n      <ApplicationLayout\r\n        onNext={handleNext}\r\n        onPrevious={handlePrevious}\r\n        onSave={handleSave}\r\n        showNextButton={true}\r\n        showPreviousButton={true}\r\n        showSaveButton={true}\r\n        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : \"Continue\"}\r\n        previousButtonText=\"Back to Previous Step\"\r\n        saveButtonText=\"Save Changes\"\r\n        nextButtonDisabled={false}\r\n        isSaving={isSaving}\r\n      >\r\n        {/* Header */}\r\n        <div className=\"mb-6\">\r\n          <h2 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n            Document Upload\r\n          </h2>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n            Upload required documents for your license application.\r\n          </p>\r\n          {application && (\r\n            <div className=\"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\r\n              <p className=\"text-sm text-blue-700 dark:text-blue-300\">\r\n                <i className=\"ri-file-upload-line mr-1\"></i>\r\n                Application: {application.application_number}\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Validation Errors */}\r\n        {Object.keys(validationErrors).length > 0 && (\r\n          <div className=\"mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\">\r\n            <h3 className=\"text-sm font-medium text-red-800 dark:text-red-200 mb-2\">Please fix the following errors:</h3>\r\n            <ul className=\"text-sm text-red-700 dark:text-red-300 list-disc list-inside\">\r\n              {Object.entries(validationErrors).map(([field, error]) => (\r\n                <li key={field}>{error}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        )}\r\n\r\n        {/* File Upload Info */}\r\n        <div className=\"mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800\">\r\n          <p className=\"text-sm font-medium flex items-center text-yellow-700 dark:text-yellow-300\">\r\n            <i className=\"ri-information-line text-lg mr-2\"></i>\r\n            <span>Accepted formats: PDF, DOC, DOCX, JPG, PNG. Maximum file size: <span className=\"font-bold\">10MB</span> per document.</span>\r\n          </p>\r\n        </div>\r\n\r\n        {/* Documents Upload Section */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6\">\r\n          <div className=\"space-y-6\">\r\n\r\n            {/* Required Documents */}\r\n            <div className=\"space-y-4\">\r\n              {comprehensiveDocumentList.map((docItem) => {\r\n                const { docType, requiredDoc, uploadedDoc, isUploaded } = docItem;\r\n                const isUploading = uploadProgress[docType] !== undefined;\r\n\r\n                return (\r\n                  <div key={requiredDoc?.license_category_document_id || docType} className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\r\n                    <div className=\"flex items-center justify-between mb-3\">\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                          {requiredDoc?.name || docType}\r\n                          {requiredDoc?.is_required && <span className=\"text-red-500 ml-1\">*</span>}\r\n                        </h4>\r\n                        {isUploaded && uploadedDoc && (\r\n                          <p className=\"text-xs text-green-600 dark:text-green-400 mt-1\">\r\n                            ✅ {uploadedDoc.file_name}\r\n                          </p>\r\n                        )}\r\n                      </div>\r\n                      {isUploaded && uploadedDoc && (\r\n                        <div className=\"flex items-center space-x-2\">\r\n                          <button\r\n                            onClick={() => handlePreviewDocument(uploadedDoc)}\r\n                            className=\"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm\"\r\n                          >\r\n                            <i className=\"ri-eye-line mr-1\"></i>\r\n                            Preview\r\n                          </button>\r\n                          <button\r\n                            onClick={() => handleRemoveDocument(uploadedDoc.document_id!)}\r\n                            className=\"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 text-sm\"\r\n                          >\r\n                            <i className=\"ri-delete-bin-line mr-1\"></i>\r\n                            Remove\r\n                          </button>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                    {!isUploaded && (\r\n                      <FileUpload\r\n                        id={`document-${docType}`}\r\n                        label=\"\"\r\n                        accept=\".pdf,.doc,.docx,.jpg,.jpeg,.png\"\r\n                        required={false}\r\n                        maxSize={10}\r\n                        value={documentFiles[docType] || null}\r\n                        onChange={(file) => handleFileChange(docType, file)}\r\n                        description={`Upload ${requiredDoc?.name?.toLowerCase() || docType}`}\r\n                      />\r\n                    )}\r\n\r\n                    {isUploading && (\r\n                      <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\r\n                        <div\r\n                          className=\"bg-primary h-2 rounded-full transition-all duration-300\"\r\n                          style={{ width: `${uploadProgress[docType]}%` }}\r\n                        ></div>\r\n                      </div>\r\n                    )}\r\n\r\n                    {validationErrors[docType] && (\r\n                      <p className=\"text-sm text-red-600 dark:text-red-400\">\r\n                        {validationErrors[docType]}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                );\r\n              })}\r\n\r\n            {/* General Supporting Documents */}\r\n            <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\r\n              <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3\">\r\n                Additional Supporting Documents\r\n              </h4>\r\n              <FileUpload\r\n                id=\"general-attachment\"\r\n                label=\"\"\r\n                accept=\".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt\"\r\n                maxSize={10}\r\n                value={documentFiles['general_attachment'] || null}\r\n                onChange={(file) => handleFileChange('general_attachment', file)}\r\n                description=\"Upload any additional supporting documents\"\r\n              />\r\n            </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Upload Summary */}\r\n        {requiredDocuments.length > 0 && (\r\n          <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\r\n            <div className=\"flex items-center justify-between text-sm\">\r\n              <span className=\"text-gray-600 dark:text-gray-400\">\r\n                Required: <span className=\"font-medium text-gray-900 dark:text-gray-100\">{requiredDocuments.filter(doc => doc.is_required).length}</span>\r\n              </span>\r\n              <span className=\"text-gray-600 dark:text-gray-400\">\r\n                Uploaded: <span className=\"font-medium text-green-600 dark:text-green-400\">{uploadedDocuments.length}</span>\r\n              </span>\r\n              <span className=\"text-gray-600 dark:text-gray-400\">\r\n                Pending: <span className=\"font-medium text-orange-600 dark:text-orange-400\">{Object.keys(documentFiles).length}</span>\r\n              </span>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n      </ApplicationLayout>\r\n    </CustomerLayout>\r\n  );\r\n};\r\n\r\nexport default DocumentsPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;AAeA,MAAM,gBAA0B;IAC9B,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAExD,iBAAiB;IACjB,MAAM,oBAAoB,aAAa,GAAG,CAAC;IAC3C,MAAM,gBAAgB,aAAa,GAAG,CAAC;IAEvC,mBAAmB;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAElF,gBAAgB;IAChB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B,EAAE;IACxF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB,CAAC;IAC1E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAEnE,sBAAsB;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,0BAA0B;IAC1B,MAAM,EACJ,YAAY,iBAAiB,EAC7B,gBAAgB,qBAAqB,EACrC,QAAQ,EACT,GAAG,CAAA,GAAA,oIAAA,CAAA,uBAAoB,AAAD,EAAE;QACvB,kBAAkB;QAClB;QACA;IACF;IAEA,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,mBAAmB,aAAa;YAE7E,MAAM,cAAc,MAAM,qIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;YAC5D,eAAe;YAEf,IAAI;gBACF,aAAa;gBACb,SAAS;gBACT,IAAI,oBAA+C,EAAE;gBAErD,IAAI;oBACF,MAAM,eAAe,MAAM,iJAAA,CAAA,iCAA8B,CAAC,qCAAqC,CAAC;oBAChG,oBAAoB,MAAM,OAAO,CAAC,gBAAgB,eAAe,EAAE;oBACnE,qBAAqB;gBACvB,EAAE,OAAO,UAAe;oBACtB,QAAQ,GAAG,CAAC;oBACZ,oBAAoB,EAAE;oBACtB,qBAAqB,EAAE;gBACzB;gBAEA,IAAI;oBACF,MAAM,OAAO,MAAM,kIAAA,CAAA,kBAAe,CAAC,yBAAyB,CAAC;oBAC7D,MAAM,eAAe,KAAK,IAAI;oBAC9B,MAAM,oBAAoB,MAAM,OAAO,CAAC,gBAAgB,eAAe,EAAE;oBACzE,qBAAqB;oBAErB,IAAI,kBAAkB,MAAM,KAAK,GAAG;wBAClC,QAAQ,GAAG,CAAC;oBACd;gBACF,EAAE,OAAO,aAAkB;oBACzB,QAAQ,KAAK,CAAC,uCAAuC;oBACrD,qBAAqB,EAAE;oBAEvB,IAAI,YAAY,QAAQ,EAAE,WAAW,KAAK;wBACxC,QAAQ,GAAG,CAAC;oBACd;gBACF;YAEF,EAAE,OAAO,KAAU;gBACjB,SAAS;YACX,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;QAAe;QAAmB;QAAiB;KAAY;IAEnE,wBAAwB;IACxB,MAAM,mBAAmB,CAAC,cAAsB;QAC9C,iBAAiB,CAAA;YACf,MAAM,WAAW;gBAAE,GAAG,IAAI;YAAC;YAC3B,IAAI,MAAM;gBACR,QAAQ,CAAC,aAAa,GAAG;YAC3B,OAAO;gBACL,OAAO,QAAQ,CAAC,aAAa;YAC/B;YACA,OAAO;QACT;QAEA,2CAA2C;QAC3C,IAAI,gBAAgB,CAAC,aAAa,EAAE;YAClC,oBAAoB,CAAA;gBAClB,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,aAAa;gBAC9B,OAAO;YACT;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB,OAAO,cAAsB;QAClD,IAAI;YACF,kBAAkB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,aAAa,EAAE;gBAAE,CAAC;YAEzD,+DAA+D;YAC/D,MAAM,cAAc,kBAAkB,IAAI,CAAC,CAAA,MACzC,IAAI,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,SAAS;YAElD,MAAM,qBAAqB,cACzB,kIAAA,CAAA,kBAAe,CAAC,qBAAqB,CAAC,YAAY,IAAI,IACtD;YAEF,MAAM,aAAa;gBACjB,eAAe;gBACf,aAAa;gBACb,WAAW;gBACX,aAAa,aAAa,eAAe;YAC3C;YAEA,MAAM,SAAS,MAAM,kIAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,MAAM;YAC1D,kBAAkB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,aAAa,EAAE;gBAAI,CAAC;YAC3D,qBAAqB,CAAA,OAAQ;uBAAI;oBAAM,OAAO,QAAQ;iBAAC;YAEvD,4BAA4B;YAC5B,iBAAiB,CAAA;gBACf,MAAM,WAAW;oBAAE,GAAG,IAAI;gBAAC;gBAC3B,OAAO,QAAQ,CAAC,aAAa;gBAC7B,OAAO;YACT;YAEA,OAAO;QAET,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,oBAAoB,CAAA,OAAQ,CAAC;oBAC3B,GAAG,IAAI;oBACP,CAAC,aAAa,EAAE;gBAClB,CAAC;YACD,kBAAkB,CAAA;gBAChB,MAAM,cAAc;oBAAE,GAAG,IAAI;gBAAC;gBAC9B,OAAO,WAAW,CAAC,aAAa;gBAChC,OAAO;YACT;YACA,OAAO;QACT;IACF;IAEA,qBAAqB;IACrB,MAAM,aAAa;QACjB,IAAI,CAAC,eAAe;YAClB,oBAAoB;gBAAE,MAAM;YAA6B;YACzD,OAAO;QACT;QAEA,YAAY;QACZ,IAAI;YACF,oBAAoB,CAAC;YAErB,+BAA+B;YAC/B,IAAI,OAAO,IAAI,CAAC,eAAe,MAAM,GAAG,GAAG;gBACzC,MAAM,iBAAiB,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,SAAS,KAAK,GACvE,eAAe,SAAS;gBAG1B,MAAM,gBAAgB,MAAM,QAAQ,GAAG,CAAC;gBACxC,MAAM,cAAc,cAAc,KAAK,CAAC,CAAA,SAAU;gBAElD,IAAI,CAAC,aAAa;oBAChB,MAAM,IAAI,MAAM;gBAClB;YACF;YAEA,8BAA8B;YAC9B,IAAI;gBACF,MAAM,qIAAA,CAAA,qBAAkB,CAAC,iBAAiB,CAAC,eAAe;oBACxD,cAAc;oBACd,qBAAqB;gBACvB;YACF,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,0CAA0C;YACzD;YAEA,OAAO;QAET,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,oBAAoB;gBAAE,MAAM;YAAiD;YAC7E,OAAO;QACT,SAAU;YACR,YAAY;QACd;IACF;IAEA,gDAAgD;IAChD,MAAM,aAAa;QACjB,MAAM,kBAAkB;IAC1B;IAEA,MAAM,iBAAiB;QACrB;IACF;IAEA,2BAA2B;IAC3B,MAAM,uBAAuB,OAAO;QAClC,IAAI;YACF,MAAM,kIAAA,CAAA,kBAAe,CAAC,cAAc,CAAC;YACrC,qBAAqB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,WAAW,KAAK;QACtE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,oBAAoB,CAAA,OAAQ,CAAC;oBAC3B,GAAG,IAAI;oBACP,QAAQ;gBACV,CAAC;QACH;IACF;IAEA,+EAA+E;IAC/E,MAAM,+BAA+B;QACnC,MAAM,cAAc,IAAI;QAExB,mCAAmC;QACnC,IAAI,MAAM,OAAO,CAAC,oBAAoB;YACpC,kBAAkB,OAAO,CAAC,CAAA;gBACxB,MAAM,UAAU,OAAO,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ;gBAC1D,YAAY,GAAG,CAAC,SAAS;oBACvB,MAAM;oBACN,aAAa;oBACb,aAAa;oBACb;oBACA,YAAY,OAAO,WAAW;oBAC9B,YAAY;gBACd;YACF;QACF;QAEA,qCAAqC;QACrC,kBAAkB,OAAO,CAAC,CAAA;YACxB,MAAM,UAAU,YAAY,aAAa;YACzC,MAAM,WAAW,YAAY,GAAG,CAAC;YAEjC,IAAI,UAAU;gBACZ,uDAAuD;gBACvD,YAAY,GAAG,CAAC,SAAS;oBACvB,GAAG,QAAQ;oBACX;oBACA,YAAY;gBACd;YACF,OAAO;gBACL,oDAAoD;gBACpD,YAAY,GAAG,CAAC,SAAS;oBACvB,MAAM;oBACN,aAAa;oBACb;oBACA;oBACA,YAAY;oBACZ,YAAY;gBACd;YACF;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,YAAY,MAAM;IACtC;IAEA,MAAM,4BAA4B;IAElC,qCAAqC;IACrC,MAAM,wBAAwB,CAAC;QAC7B,oBAAoB;QACpB,sBAAsB;IACxB;IAEA,+BAA+B;IAC/B,MAAM,qBAAqB;QACzB,sBAAsB;QACtB,oBAAoB;IACtB;IAEA,IAAI,eAAe,WAAW;QAC5B,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK1D;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqD;;;;;;kDACnE,8OAAC;wCAAE,WAAU;kDAAuC;;;;;;kDACpD,8OAAC;wCACC,SAAS,IAAM;wCACf,WAAU;;0DAEV,8OAAC;gDAAE,WAAU;;;;;;4CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS3D;IAEA,qBACE,8OAAC,gJAAA,CAAA,UAAc;kBACb,cAAA,8OAAC,uJAAA,CAAA,UAAiB;YAChB,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,gBAAgB;YAChB,oBAAoB;YACpB,gBAAgB;YAChB,gBAAgB,WAAW,CAAC,YAAY,EAAE,SAAS,IAAI,EAAE,GAAG;YAC5D,oBAAmB;YACnB,gBAAe;YACf,oBAAoB;YACpB,UAAU;;8BAGV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAGrE,8OAAC;4BAAE,WAAU;sCAAgD;;;;;;wBAG5D,6BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;wCAAE,WAAU;;;;;;oCAA+B;oCAC9B,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;gBAOnD,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG,mBACtC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0D;;;;;;sCACxE,8OAAC;4BAAG,WAAU;sCACX,OAAO,OAAO,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,iBACnD,8OAAC;8CAAgB;mCAAR;;;;;;;;;;;;;;;;8BAOjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;0CACX,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;;oCAAK;kDAA+D,8OAAC;wCAAK,WAAU;kDAAY;;;;;;oCAAW;;;;;;;;;;;;;;;;;;8BAKhH,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCAGb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,0BAA0B,GAAG,CAAC,CAAC;oCAC9B,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG;oCAC1D,MAAM,cAAc,cAAc,CAAC,QAAQ,KAAK;oCAEhD,qBACE,8OAAC;wCAA+D,WAAU;;0DACxE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;;oEACX,aAAa,QAAQ;oEACrB,aAAa,6BAAe,8OAAC;wEAAK,WAAU;kFAAoB;;;;;;;;;;;;4DAElE,cAAc,6BACb,8OAAC;gEAAE,WAAU;;oEAAkD;oEAC1D,YAAY,SAAS;;;;;;;;;;;;;oDAI7B,cAAc,6BACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS,IAAM,sBAAsB;gEACrC,WAAU;;kFAEV,8OAAC;wEAAE,WAAU;;;;;;oEAAuB;;;;;;;0EAGtC,8OAAC;gEACC,SAAS,IAAM,qBAAqB,YAAY,WAAW;gEAC3D,WAAU;;kFAEV,8OAAC;wEAAE,WAAU;;;;;;oEAA8B;;;;;;;;;;;;;;;;;;;4CAOlD,CAAC,4BACA,8OAAC,yIAAA,CAAA,UAAU;gDACT,IAAI,CAAC,SAAS,EAAE,SAAS;gDACzB,OAAM;gDACN,QAAO;gDACP,UAAU;gDACV,SAAS;gDACT,OAAO,aAAa,CAAC,QAAQ,IAAI;gDACjC,UAAU,CAAC,OAAS,iBAAiB,SAAS;gDAC9C,aAAa,CAAC,OAAO,EAAE,aAAa,MAAM,iBAAiB,SAAS;;;;;;4CAIvE,6BACC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;oDAAC;;;;;;;;;;;4CAKnD,gBAAgB,CAAC,QAAQ,kBACxB,8OAAC;gDAAE,WAAU;0DACV,gBAAgB,CAAC,QAAQ;;;;;;;uCAzDtB,aAAa,gCAAgC;;;;;gCA8D3D;8CAGF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAG1E,8OAAC,yIAAA,CAAA,UAAU;4CACT,IAAG;4CACH,OAAM;4CACN,QAAO;4CACP,SAAS;4CACT,OAAO,aAAa,CAAC,qBAAqB,IAAI;4CAC9C,UAAU,CAAC,OAAS,iBAAiB,sBAAsB;4CAC3D,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQnB,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;;oCAAmC;kDACvC,8OAAC;wCAAK,WAAU;kDAAgD,kBAAkB,MAAM,CAAC,CAAA,MAAO,IAAI,WAAW,EAAE,MAAM;;;;;;;;;;;;0CAEnI,8OAAC;gCAAK,WAAU;;oCAAmC;kDACvC,8OAAC;wCAAK,WAAU;kDAAkD,kBAAkB,MAAM;;;;;;;;;;;;0CAEtG,8OAAC;gCAAK,WAAU;;oCAAmC;kDACxC,8OAAC;wCAAK,WAAU;kDAAoD,OAAO,IAAI,CAAC,eAAe,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9H;uCAEe", "debugId": null}}]}