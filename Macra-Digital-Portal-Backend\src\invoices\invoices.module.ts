import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InvoicesService } from './invoices.service';
import { InvoicesController } from './invoices.controller';
import { CustomerInvoicesController } from './customer-invoices.controller';
import { InvoicePdfService } from './invoice-pdf.service';
import { Invoices } from '../entities/invoices.entity';
import { Applications } from '../entities/applications.entity';
import { Applicants } from '../entities/applicant.entity';
import { LicenseCategories } from '../entities/license-categories.entity';
import { Payment } from '../payments/entities/payment.entity';
import { NotificationsModule } from '../notifications/notifications.module';
import { ApplicationsModule } from '../applications/applications.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Invoices, Applications, Applicants, LicenseCategories, Payment]),
    NotificationsModule,
    forwardRef(() => ApplicationsModule),
  ],
  controllers: [InvoicesController, CustomerInvoicesController],
  providers: [InvoicesService, InvoicePdfService],
  exports: [InvoicesService, InvoicePdfService],
})
export class InvoicesModule {}
