import React from 'react';
import DataDisplayCard from './DataDisplayCard';
import { Applicant } from '@/types/license';

interface ApplicantInfoCardProps {
  applicant: Applicant | null;
  className?: string;
  showEmptyFields?: boolean;
}

const ApplicantInfoCard: React.FC<ApplicantInfoCardProps> = ({
  applicant,
  className = '',
  showEmptyFields = true
}) => {
  const fields = [
    {
      label: 'Organization Name',
      value: applicant?.name,
      icon: 'ri-building-line'
    },
    {
      label: 'Business Registration Number',
      value: applicant?.business_registration_number,
      icon: 'ri-file-list-line'
    },
    {
      label: 'TPIN',
      value: applicant?.tpin,
      icon: 'ri-number-1'
    },
    {
      label: 'Email Address',
      value: applicant?.email,
      type: 'email' as const,
      icon: 'ri-mail-line'
    },
    {
      label: 'Phone Number',
      value: applicant?.phone,
      type: 'phone' as const,
      icon: 'ri-phone-line'
    },
    {
      label: 'Website',
      value: applicant?.website,
      type: 'url' as const,
      icon: 'ri-global-line'
    },
    {
      label: 'Fax Number',
      value: applicant?.fax,
      type: 'phone' as const,
      icon: 'ri-printer-line'
    },
    {
      label: 'Date of Incorporation',
      value: applicant?.date_incorporation ? new Date(applicant.date_incorporation).toLocaleDateString() : null,
      type: 'date' as const,
      icon: 'ri-calendar-line'
    },
    {
      label: 'Place of Incorporation',
      value: applicant?.place_incorporation,
      icon: 'ri-map-pin-line'
    },
    {
      label: 'Level of Insurance Cover',
      value: applicant?.level_of_insurance_cover,
      icon: 'ri-shield-check-line',
      fullWidth: true
    }
  ];

  return (
    <DataDisplayCard
      title="Applicant Information"
      icon="ri-user-line"
      fields={fields}
      className={className}
      showEmptyFields={showEmptyFields}
      creatorEmail={applicant?.email}
      creatorName={applicant?.name}
      showEmailButton={true}
    />
  );
};

export default ApplicantInfoCard;
