import { AxiosError } from 'axios';
import { processApiResponse } from '@/lib/authUtils';
import { ChangePasswordDto, CreateUserDto, UpdateProfileDto, UpdateUserDto, User } from '@/types/user';
import { PaginatedResponse, PaginateQuery } from '@/types';
import { apiClient, usersApiClient } from '@/lib';

// Re-export for backward compatibility

export const userService = {
  // Get all users with pagination
  async getUsers(query: PaginateQuery = {}): Promise<PaginatedResponse<User>> {
    const params = new URLSearchParams();

    if (query.page) params.set('page', query.page.toString());
    if (query.limit) params.set('limit', query.limit.toString());
    if (query.search) params.set('search', query.search);
    if (query.sortBy) {
      query.sortBy.forEach(sort => params.append('sortBy', sort));
    }
    if (query.searchBy) {
      query.searchBy.forEach(search => params.append('searchBy', search));
    }
    if (query.filter) {
      Object.entries(query.filter).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(`filter.${key}`, v));
        } else {
          params.set(`filter.${key}`, value);
        }
      });
    }

    const response = await usersApiClient.get(`?${params.toString()}`);
    return processApiResponse(response);
  },

  // Get user by ID
  async getUser(id: string): Promise<User> {
    const response = await usersApiClient.get(`/${id}`);
    return processApiResponse(response);
  },

  // Get user emails for predictive input
  async getUserEmails(searchTerm?: string): Promise<string[]> {
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.set('search', searchTerm);
      params.set('limit', '20'); // Limit to 20 suggestions

      const response = await apiClient.get(`/users?${params.toString()}`);
      const usersData = processApiResponse(response);

      // Extract emails from users
      const emails = usersData.data?.map((user: User) => user.email).filter(Boolean) || [];
      return emails;
    } catch (error) {
      console.warn('Failed to fetch user emails:', error);
      return [];
    }
  },

  // Get user by ID (alias for consistency)
  async getUserById(id: string): Promise<User> {
    return this.getUser(id);
  },

  // Get current user profile
  async getProfile(): Promise<User> {
    const response = await usersApiClient.get('/profile');
    return processApiResponse(response);
  },

  // Create new user
  async createUser(userData: CreateUserDto): Promise<User> {
    const response = await usersApiClient.post('', userData);
    return processApiResponse(response);
  },

  // Update user
  async updateUser(id: string, userData: UpdateUserDto): Promise<User> {
    const response = await usersApiClient.put(`/${id}`, userData);
    return processApiResponse(response);
  },

  // Update current user profile
  async updateProfile(userData: UpdateProfileDto): Promise<User> {
    const response = await usersApiClient.put('/profile', userData);
    return processApiResponse(response);
  },

  // Change password
  async changePassword(passwordData: ChangePasswordDto): Promise<{ message: string }> {
    const response = await usersApiClient.put('/profile/password', passwordData);
    return processApiResponse(response);
  },

  // Upload avatar
  async uploadAvatar(file: File): Promise<User> {
    console.log('userService: uploadAvatar called', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type
    });

    const formData = new FormData();
    formData.append('avatar', file);


    try {
      const response = await usersApiClient.post('/profile/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return processApiResponse(response);
    } catch (error: unknown) {
      const axiosError = error as AxiosError;
      console.error('userService: Upload failed', {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message
      });
      throw error;
    }
  },

  // Remove avatar
  async removeAvatar(): Promise<User> {
    const response = await usersApiClient.delete('/profile/avatar');
    return processApiResponse(response);
  },

  // Delete user
  async deleteUser(id: string): Promise<void> {
    await usersApiClient.delete(`/${id}`);
  },
};
