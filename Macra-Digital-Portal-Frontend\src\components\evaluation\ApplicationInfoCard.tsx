import React from 'react';
import DataDisplayCard from './DataDisplayCard';
import { Application } from '@/types/license';


interface ApplicationInfoCardProps {
  application: Application | null;
  className?: string;
  showEmptyFields?: boolean;
}

const ApplicationInfoCard: React.FC<ApplicationInfoCardProps> = ({
  application,
  className = '',
  showEmptyFields = true
}) => {

  // Format status for display
  const formatStatus = (status: string) => {
    return status.replaceAll("_", "");
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'draft':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'submitted':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'under_review':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'evaluation':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200';
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'withdrawn':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const fields = [
    {
      label: 'Application Number',
      value: application?.application_number,
      icon: 'ri-hashtag'
    },
    {
      label: 'License Category',
      value: application?.license_category?.name,
      icon: 'ri-award-line'
    },
    {
      label: 'License Type',
      value: application?.license_category?.license_type?.name,
      icon: 'ri-bookmark-line'
    },
    {
      label: 'Status',
      value: application?.status ? formatStatus(application.status) : null,
      type: 'status' as const,
      icon: 'ri-flag-line'
    },
    {
      label: 'Current Step',
      value: application?.current_step,
      icon: 'ri-footprint-line'
    },
    {
      label: 'Submitted Date',
      value: application?.submitted_at ? new Date(application.submitted_at).toLocaleDateString() : 'Not submitted',
      type: 'date' as const,
      icon: 'ri-calendar-check-line'
    },
    {
      label: 'Created Date',
      value: application?.created_at ? new Date(application.created_at).toLocaleDateString() : null,
      type: 'date' as const,
      icon: 'ri-calendar-line'
    },
    {
      label: 'Assigned To',
      value: application?.assignee ? `${application.assignee.first_name} ${application.assignee.last_name}` : 'Unassigned',
      icon: 'ri-user-settings-line'
    },
    // Only show Assignment Date if there's an assignee
    ...(application?.assignee ? [{
      label: 'Assignment Date',
      value: application?.assigned_at ? new Date(application.assigned_at).toLocaleDateString() : null,
      type: 'date' as const,
      icon: 'ri-calendar-event-line'
    }] : [])
  ];

  return (
    <DataDisplayCard
      title="Application Information"
      icon="ri-file-text-line"
      fields={fields}
      className={className}
      showEmptyFields={showEmptyFields}
      creatorEmail={application?.applicant?.email}
      creatorName={application?.applicant?.name}
      showEmailButton={true}
    />
  );
};

export default ApplicationInfoCard;
