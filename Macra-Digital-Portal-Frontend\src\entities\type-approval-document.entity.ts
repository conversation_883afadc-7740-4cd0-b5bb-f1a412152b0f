/**
 * Document types specific to Type Approval applications
 * These correspond to the requirements defined in licenseTypeStepConfig.ts
 */
export enum DocumentType {
  PROOF_OF_PAYMENT = 'proof_of_payment',
  ETSI_DOCUMENTS = 'etsi_documents',
  TEST_REPORTS = 'test_reports_from_accredited_labs',
  TECHNICAL_SPECIFICATIONS = 'technical_specifications',
  AUTHORIZATION_LETTER = 'authorization_letter_power_of_attorney',
  DECLARATION_OF_CONFORMITY = 'declaration_of_conformity',
  ITU_REGION_1_APPROVAL = 'any_copies_of_approval_from_itu_region_1',
  OTHER = 'other'
}

/**
 * Type Approval Document interface
 */
export interface TypeApprovalDocument {
  document_id: string;
  document_type: DocumentType;
  file_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  is_required: boolean;
  entity_type: 'application';
  entity_id: string;
  created_at: Date;
  created_by: string;
  updated_at?: Date;
  updated_by?: string;
  deleted_at?: Date;
}

/**
 * Document upload data interface
 */
export interface DocumentUploadData {
  document_type: string;
  entity_type: 'application';
  entity_id: string;
  is_required: boolean;
}

/**
 * Document validation result interface
 */
export interface DocumentValidationResult {
  isValid: boolean;
  missingDocuments: DocumentType[];
  message: string;
}
